import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import FilterSortBar from '../../renderer/components/knowledge-base-view/FilterSortBar';

describe('FilterSortBar', () => {
  const mockAvailableTags = ['react', 'javascript', 'ui', 'testing'];
  const mockAvailableCategories = ['Work', 'Personal', 'Tech'];
  const mockOnFilterChange = jest.fn();
  const mockOnSortChange = jest.fn();

  const initialFilters = {
    tags: [],
    category: '',
    date: '',
    source: '',
  };

  const initialSort = {
    by: 'date',
    order: 'desc',
  };

  beforeEach(() => {
    mockOnFilterChange.mockClear();
    mockOnSortChange.mockClear();
  });

  const renderComponent = (props = {}) => {
    return render(
      <FilterSortBar
        availableTags={mockAvailableTags}
        availableCategories={mockAvailableCategories}
        onFilterChange={mockOnFilterChange}
        onSortChange={mockOnSortChange}
        initialFilters={initialFilters}
        initialSort={initialSort}
        {...props}
      />
    );
  };

  test('renders all filter and sort controls', () => {
    renderComponent();
    expect(screen.getByText('Filter by Tag')).toBeInTheDocument();
    mockAvailableTags.forEach(tag => {
      expect(screen.getByLabelText(tag)).toBeInTheDocument();
    });

    expect(screen.getByText('Filter by Category')).toBeInTheDocument();
    expect(screen.getByLabelText('Filter by category')).toBeInTheDocument();

    expect(screen.getByText('Filter by Date')).toBeInTheDocument();
    expect(screen.getByLabelText('Filter by date')).toBeInTheDocument();

    expect(screen.getByText('Filter by Source')).toBeInTheDocument();
    expect(screen.getByLabelText('Filter by source')).toBeInTheDocument();

    expect(screen.getByText('Sort By')).toBeInTheDocument();
    expect(screen.getByLabelText('Sort by criteria')).toBeInTheDocument();
    expect(screen.getByLabelText('Sort order')).toBeInTheDocument();
  });

  test('initializes with default/provided initial filters and sort', () => {
    renderComponent();
    // Check initial tag filter (none selected)
    mockAvailableTags.forEach(tag => {
      expect(screen.getByLabelText(tag)).not.toBeChecked();
    });
    // Check initial category filter
    expect(screen.getByLabelText('Filter by category')).toHaveValue('');
    // Check initial date filter
    expect(screen.getByLabelText('Filter by date')).toHaveValue('');
    // Check initial source filter
    expect(screen.getByLabelText('Filter by source')).toHaveValue('');
    // Check initial sort by
    expect(screen.getByLabelText('Sort by criteria')).toHaveValue('date');
    // Check initial sort order
    expect(screen.getByLabelText('Sort order')).toHaveValue('desc');

    // useEffect calls on mount
    expect(mockOnFilterChange).toHaveBeenCalledWith(initialFilters);
    expect(mockOnSortChange).toHaveBeenCalledWith(initialSort);
  });

  test('calls onFilterChange when a tag is selected or deselected', () => {
    renderComponent();
    mockOnFilterChange.mockClear(); // Clear initial call

    const reactTagCheckbox = screen.getByLabelText('react');
    fireEvent.click(reactTagCheckbox);
    expect(mockOnFilterChange).toHaveBeenCalledTimes(1);
    expect(mockOnFilterChange).toHaveBeenCalledWith({ ...initialFilters, tags: ['react'] });

    fireEvent.click(reactTagCheckbox); // Deselect
    expect(mockOnFilterChange).toHaveBeenCalledTimes(2);
    expect(mockOnFilterChange).toHaveBeenCalledWith({ ...initialFilters, tags: [] });

    const jsTagCheckbox = screen.getByLabelText('javascript');
    fireEvent.click(jsTagCheckbox);
    expect(mockOnFilterChange).toHaveBeenCalledTimes(3);
    expect(mockOnFilterChange).toHaveBeenCalledWith({ ...initialFilters, tags: ['javascript'] });
  });

  test('calls onFilterChange when category filter changes', () => {
    renderComponent();
    mockOnFilterChange.mockClear(); 

    const categorySelect = screen.getByLabelText('Filter by category');
    fireEvent.change(categorySelect, { target: { value: 'Work' } });
    expect(mockOnFilterChange).toHaveBeenCalledTimes(1);
    expect(mockOnFilterChange).toHaveBeenCalledWith({ ...initialFilters, category: 'Work' });
  });

  test('calls onFilterChange when date filter changes', () => {
    renderComponent();
    mockOnFilterChange.mockClear();

    const dateSelect = screen.getByLabelText('Filter by date');
    fireEvent.change(dateSelect, { target: { value: 'today' } });
    expect(mockOnFilterChange).toHaveBeenCalledTimes(1);
    expect(mockOnFilterChange).toHaveBeenCalledWith({ ...initialFilters, date: 'today' });
  });

  test('calls onFilterChange when source filter changes', () => {
    renderComponent();
    mockOnFilterChange.mockClear();

    const sourceInput = screen.getByLabelText('Filter by source');
    fireEvent.change(sourceInput, { target: { value: 'TestSource' } });
    expect(mockOnFilterChange).toHaveBeenCalledTimes(1);
    expect(mockOnFilterChange).toHaveBeenCalledWith({ ...initialFilters, source: 'TestSource' });
  });

  test('calls onSortChange when sort by criteria changes', () => {
    renderComponent();
    mockOnSortChange.mockClear();

    const sortBySelect = screen.getByLabelText('Sort by criteria');
    fireEvent.change(sortBySelect, { target: { value: 'title' } });
    expect(mockOnSortChange).toHaveBeenCalledTimes(1);
    expect(mockOnSortChange).toHaveBeenCalledWith({ by: 'title', order: 'desc' });
  });

  test('calls onSortChange when sort order changes', () => {
    renderComponent();
    mockOnSortChange.mockClear();

    const sortOrderSelect = screen.getByLabelText('Sort order');
    fireEvent.change(sortOrderSelect, { target: { value: 'asc' } });
    expect(mockOnSortChange).toHaveBeenCalledTimes(1);
    expect(mockOnSortChange).toHaveBeenCalledWith({ by: 'date', order: 'asc' });
  });

  test('initializes with specific initialFilters and initialSort', () => {
    const specificInitialFilters = {
      tags: ['react', 'testing'],
      category: 'Work',
      date: 'last7days',
      source: 'SpecificSource',
    };
    const specificInitialSort = {
      by: 'title',
      order: 'asc',
    };
    renderComponent({ initialFilters: specificInitialFilters, initialSort: specificInitialSort });

    expect(screen.getByLabelText('react')).toBeChecked();
    expect(screen.getByLabelText('testing')).toBeChecked();
    expect(screen.getByLabelText('javascript')).not.toBeChecked();
    expect(screen.getByLabelText('Filter by category')).toHaveValue('Work');
    expect(screen.getByLabelText('Filter by date')).toHaveValue('last7days');
    expect(screen.getByLabelText('Filter by source')).toHaveValue('SpecificSource');
    expect(screen.getByLabelText('Sort by criteria')).toHaveValue('title');
    expect(screen.getByLabelText('Sort order')).toHaveValue('asc');

    expect(mockOnFilterChange).toHaveBeenCalledWith(specificInitialFilters);
    expect(mockOnSortChange).toHaveBeenCalledWith(specificInitialSort);
  });

  test('handles empty availableTags and availableCategories gracefully', () => {
    renderComponent({ availableTags: [], availableCategories: [] });
    expect(screen.getByText('Filter by Tag')).toBeInTheDocument();
    // No checkboxes should be rendered if availableTags is empty
    expect(screen.queryByRole('checkbox')).not.toBeInTheDocument();
    
    expect(screen.getByText('Filter by Category')).toBeInTheDocument();
    const categorySelect = screen.getByLabelText('Filter by category');
    expect(categorySelect.options.length).toBe(1); // Only "All Categories"
    expect(categorySelect).toHaveValue('');
  });
});