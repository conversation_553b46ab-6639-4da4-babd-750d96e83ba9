// src/knowledge-base-interaction/ai-services-gateway/config/apiKeys.js

/**
 * @file Manages API keys for external AI services.
 *
 * IMPORTANT:
 * - DO NOT commit actual API keys to the repository.
 * - Use environment variables or a secure secrets management system in production.
 * - This file serves as a placeholder for the structure of API key configuration.
 * - For local development, you might use a .env file (added to .gitignore).
 */

// AI-verifiable: Check for the existence of this placeholder structure.

const getGeminiApiKey = () => {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
        const errorMessage = 'CRITICAL: GEMINI_API_KEY is not set in environment variables.';
        console.error(errorMessage);
        // In a server environment, you might throw an error to halt startup
        throw new Error(errorMessage);
        // For client-side or less critical scenarios, returning undefined or null
        // and handling it downstream might be an option, but for API keys, failure is often better.
    }
    return apiKey;
};

const apiKeys = {
    gemini: {
        // apiKey is now a getter to ensure it's fetched at runtime if needed,
        // or to consistently show undefined if not set.
        get apiKey() { return getGeminiApiKey(); }
    },
    qa: {
        defaultProvider: 'gemini',
        // Potentially, QA could use a different key, e.g., process.env.GEMINI_QA_API_KEY
        // For now, it defaults to the main Gemini key.
        get apiKey() { return getGeminiApiKey(); }
    },
    transform: {
        defaultProvider: 'gemini',
        get apiKey() { return getGeminiApiKey(); }
    },
    link: {
        defaultProvider: 'gemini',
        get apiKey() { return getGeminiApiKey(); }
    },
    // Add other services as needed, following the pattern of using environment variables.
};

/**
 * Loads or retrieves API keys.
 * In a real application, this might involve reading from environment variables,
 * a configuration file (not committed), or a secrets manager.
 *
 * @async
 * @returns {Promise<object>} The API keys configuration.
 */
async function loadApiKeys() {
    console.log('AI Services Gateway Config: Attempting to load API keys from environment variables.');

    // The keys are now dynamically fetched from process.env via getters in the apiKeys object.
    // We can check if the primary key is available as an example.
    if (!apiKeys.gemini.apiKey) { // Accessing the getter here
        // The error is already logged by getGeminiApiKey()
        // Depending on application design, could throw here to prevent startup/operation
        // For now, we allow it to proceed, and handlers should check for key presence.
        console.warn('AI Services Gateway Config: Gemini API key is missing. Services requiring it will fail.');
    } else {
        console.log('AI Services Gateway Config: Gemini API key found in environment variables.');
    }
    
    console.log('AI Services Gateway Config: API key configuration processed.');
    // The apiKeys object itself is returned. It contains getters that will resolve to env vars or undefined.
    return Promise.resolve(apiKeys);
}

// To be used by the gateway or handlers after loading.
// Example:
// import { loadApiKeys } from './apiKeys.js';
// let loadedKeys;
// loadApiKeys().then(keys => loadedKeys = keys);

export { apiKeys, loadApiKeys };