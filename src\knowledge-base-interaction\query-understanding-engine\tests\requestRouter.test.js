// AI-VERIFIABLE: Placeholder test file for RequestRouter.
// These tests will verify the logic for routing processed queries.

import RequestRouter from '../request-routing/requestRouter';

describe('RequestRouter - Unit Tests', () => {
    let router;
    // Mocks will be created within each test or describe block for clarity
    
    beforeEach(() => {
        router = new RequestRouter();
    });

    const createMocks = (intentType, entitiesData, originalQuery) => {
        const intent = { type: intentType, confidence: 0.9, details: 'test intent details' };
        const parsedQuery = {
            original: originalQuery,
            cleanedQuery: originalQuery.toLowerCase(), // simplified for mock
            tokens: originalQuery.toLowerCase().split(' '),
            keywords: entitiesData.keywords || []
        };
        const entities = {
            keywords: entitiesData.keywords || [],
            namedEntities: entitiesData.namedEntities || [],
            dates: entitiesData.dates || [],
            authors: entitiesData.authors || [],
            relationships: entitiesData.relationships || [],
            queryStructure: entitiesData.queryStructure || 'test structure'
        };
        return { intent, entities, parsedQuery };
    };

    test('AI-VERIFIABLE: should route "search" intent to SearchService with basic terms', () => {
        const { intent, entities, parsedQuery } = createMocks(router.knownIntents.SEARCH, { keywords: ['test', 'document'] }, "search for test document");
        const decision = router.routeRequest(intent, entities, parsedQuery);
        expect(decision.service).toBe(router.services.SEARCH_SERVICE);
        expect(decision.params.intent.type).toBe(router.knownIntents.SEARCH);
        expect(decision.params.searchTerms).toEqual(['test', 'document']);
        expect(decision.params.filters).toEqual({}); // No specific filters
    });

    test('AI-VERIFIABLE: should route "search" intent with filters for author and date', () => {
        const entitiesData = {
            keywords: ['articles', 'tdd'],
            authors: ['John Doe'],
            dates: ['2023-01-01'],
            namedEntities: [{text: 'TDD', type: 'CONCEPT'}],
            relationships: [{type: 'publishedAfter', date: '2023-01-01'}]
        };
        const { intent, entities, parsedQuery } = createMocks(router.knownIntents.SEARCH, entitiesData, "articles by John Doe on TDD after 2023-01-01");
        const decision = router.routeRequest(intent, entities, parsedQuery);
        expect(decision.service).toBe(router.services.SEARCH_SERVICE);
        expect(decision.params.searchTerms).toEqual(['articles', 'tdd']);
        expect(decision.params.filters.author).toBe('John Doe');
        expect(decision.params.filters.date).toBe('2023-01-01'); // Takes first date
        expect(decision.params.filters.concepts).toEqual(['TDD']);
        expect(decision.params.filters.publishedAfter).toBe('2023-01-01');
    });


    test('AI-VERIFIABLE: should route "question_answering" intent to AIServicesGateway', () => {
        const { intent, entities, parsedQuery } = createMocks(router.knownIntents.QUESTION_ANSWERING, { keywords: ['capital', 'france'], namedEntities: [{text: 'France', type: 'LOC'}]}, "what is the capital of France?");
        const decision = router.routeRequest(intent, entities, parsedQuery);
        expect(decision.service).toBe(router.services.AI_SERVICES_GATEWAY);
        expect(decision.params.intent.type).toBe(router.knownIntents.QUESTION_ANSWERING);
        expect(decision.params.task).toBe('qa');
        expect(decision.params.question).toBe(parsedQuery.original);
        expect(decision.params.contextEntities).toEqual(entities);
    });

    test('AI-VERIFIABLE: should route "summarize" intent to AIServicesGateway', () => {
        const { intent, entities, parsedQuery } = createMocks(router.knownIntents.SUMMARIZE, { keywords: ['summarize', 'article'] }, "summarize this article");
        const decision = router.routeRequest(intent, entities, parsedQuery);
        expect(decision.service).toBe(router.services.AI_SERVICES_GATEWAY);
        expect(decision.params.intent.type).toBe(router.knownIntents.SUMMARIZE);
        expect(decision.params.task).toBe('summarize');
        expect(decision.params.sourceQuery).toBe(parsedQuery.original);
    });

    test('AI-VERIFIABLE: should route "content_transformation" intent to AIServicesGateway', () => {
        const { intent, entities, parsedQuery } = createMocks(router.knownIntents.CONTENT_TRANSFORMATION, { keywords: ['extract', 'facts'] }, "extract facts from document");
        const decision = router.routeRequest(intent, entities, parsedQuery);
        expect(decision.service).toBe(router.services.AI_SERVICES_GATEWAY);
        expect(decision.params.intent.type).toBe(router.knownIntents.CONTENT_TRANSFORMATION);
        expect(decision.params.task).toBe('transform');
        expect(decision.params.transformationDetails).toEqual({ query: parsedQuery.original, entities: entities });
    });

    test('AI-VERIFIABLE: should route "conceptual_linking" intent to ConceptualLinkingEngine', () => {
        const entitiesData = { keywords: ['connect', 'ai', 'ml'], namedEntities: [{text: 'AI', type: 'CONCEPT'}, {text: 'ML', type: 'CONCEPT'}]};
        const { intent, entities, parsedQuery } = createMocks(router.knownIntents.CONCEPTUAL_LINKING, entitiesData, "connect AI and ML");
        const decision = router.routeRequest(intent, entities, parsedQuery);
        expect(decision.service).toBe(router.services.CONCEPTUAL_LINKING_ENGINE);
        expect(decision.params.intent.type).toBe(router.knownIntents.CONCEPTUAL_LINKING);
        expect(decision.params.linkFocus).toEqual(entitiesData.keywords);
        expect(decision.params.contextEntities).toEqual(entitiesData.namedEntities);
    });

    test('AI-VERIFIABLE: should handle unknown intent by falling back to SearchService if keywords exist', () => {
        const { intent, entities, parsedQuery } = createMocks(router.knownIntents.UNKNOWN, { keywords: ['some', 'random', 'terms'] }, "some random terms");
        const decision = router.routeRequest(intent, entities, parsedQuery);
        expect(decision.service).toBe(router.services.SEARCH_SERVICE);
        expect(decision.params.searchTerms).toEqual(['some', 'random', 'terms']);
        expect(decision.params.info).toBe('Fallback to general search due to unhandled intent.');
    });
    
    test('AI-VERIFIABLE: should handle unknown intent by routing to DefaultHandler if NO keywords exist', () => {
        const { intent, entities, parsedQuery } = createMocks(router.knownIntents.UNKNOWN, { keywords: [] }, "gibberish"); // No keywords
        const decision = router.routeRequest(intent, entities, parsedQuery);
        expect(decision.service).toBe(router.services.DEFAULT_HANDLER);
        expect(decision.params.error).toBeDefined();
    });


    test('AI-VERIFIABLE: should include original query, full intent, and entities in params', () => {
        const { intent, entities, parsedQuery } = createMocks(router.knownIntents.SEARCH, { keywords: ['generic'] }, "generic search");
        const decision = router.routeRequest(intent, entities, parsedQuery);
        expect(decision.params.originalQuery).toBe(parsedQuery.original);
        expect(decision.params.intent).toEqual(intent); // Check full intent object
        expect(decision.params.entities).toEqual(entities);
    });

    test('AI-VERIFIABLE: should throw an error if intent is invalid', () => {
        const { entities, parsedQuery } = createMocks(null, {}, "query");
        expect(() => router.routeRequest(null, entities, parsedQuery)).toThrow('Intent object with type is required for request routing.');
        expect(() => router.routeRequest({}, entities, parsedQuery)).toThrow('Intent object with type is required for request routing.');
    });

    test('AI-VERIFIABLE: should throw an error if entities object is invalid or missing keywords', () => {
        const { intent, parsedQuery } = createMocks(router.knownIntents.SEARCH, {}, "query");
        expect(() => router.routeRequest(intent, null, parsedQuery)).toThrow('Entities object with at least a keywords array is required for request routing.');
        expect(() => router.routeRequest(intent, { namedEntities: [] }, parsedQuery)).toThrow('Entities object with at least a keywords array is required for request routing.');
    });

    test('AI-VERIFIABLE: should throw an error if parsedQuery object is invalid or missing original', () => {
        const validIntent = { type: router.knownIntents.SEARCH, confidence: 0.9, details: 'test intent details' };
        const validEntities = {
            keywords: ['test'],
            namedEntities: [],
            dates: [],
            authors: [],
            relationships: [],
            queryStructure: 'test structure'
        };
        
        expect(() => router.routeRequest(validIntent, validEntities, null)).toThrow('Parsed query object with original query is required for request routing.');
        // Test for parsedQuery missing the 'original' property
        expect(() => router.routeRequest(validIntent, validEntities, { cleanedQuery: "test", tokens: ["test"], keywords: ["test"] })).toThrow('Parsed query object with original query is required for request routing.');
    });


    // Add more tests for complex routing rules, different parameter compositions,
    // and error handling as the router evolves.
});

// AI-VERIFIABLE: End of requestRouter.test.js