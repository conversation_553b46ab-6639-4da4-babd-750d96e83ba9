import React from 'react';
import './CaptureControls.css';

function CaptureControls({ status, onCapture, captureType }) {
  const getButtonText = () => {
    if (status === 'capturing') return 'Capturing...';
    if (status === 'success') return 'Capture More';
    return `Capture ${captureType === 'bookmark' ? 'Bookmark' : captureType === 'selection' ? 'Selection' : 'Full Page'}`;
  };

  return (
    <div className="capture-controls">
      <button
        onClick={onCapture}
        disabled={status === 'capturing'}
        className={`capture-button ${status === 'capturing' ? 'capturing' : ''}`}
      >
        {getButtonText()}
      </button>
      {status === 'success' && <p className="status-message success">Capture Successful!</p>}
      {status === 'error' && <p className="status-message error">Capture Failed. Please try again.</p>}
    </div>
  );
}

export default CaptureControls;