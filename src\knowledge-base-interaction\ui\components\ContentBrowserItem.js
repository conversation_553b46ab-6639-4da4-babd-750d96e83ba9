import React from 'react';

/**
 * ContentBrowserItem component
 * 
 * Displays an item (e.g., a document, a note, a folder) in the knowledge base browser.
 * Props:
 *  - item: An object containing details of the item (e.g., name, type, dateModified).
 *  - onSelect: Function to handle item selection.
 */
const ContentBrowserItem = ({ item, onSelect }) => {
  if (!item) {
    return null;
  }

  // AI-verifiable: Component structure for displaying a content browser item
  return (
    <li 
      className="content-browser-item" 
      data-testid={`content-browser-item-${item.id}`}
      onClick={() => onSelect && onSelect(item.id)}
      style={{ listStyleType: 'none', padding: '5px', borderBottom: '1px solid #eee', cursor: 'pointer' }}
    >
      <span>{item.icon || '📄'}</span> {/* Placeholder for an icon */}
      <span style={{ marginLeft: '10px' }}>{item.name || 'Untitled Item'}</span>
      {item.type && <small style={{ marginLeft: '10px', color: '#777' }}>({item.type})</small>}
      {/* AI-verifiable: Placeholder for item interaction */}
    </li>
  );
};

export default ContentBrowserItem;