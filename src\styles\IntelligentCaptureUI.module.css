.intelligentCaptureUI {
  font-family: sans-serif;
  padding: 1rem;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  max-width: 500px;
  margin: 1rem auto;
}

.section {
  margin-bottom: 1.5rem;
}

.section h3 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.listItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.listItem:last-child {
  border-bottom: none;
}

.listItem span {
  flex-grow: 1;
  color: #555;
}

.actions button {
  margin-left: 0.5rem;
  padding: 0.3rem 0.6rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  font-size: 0.9em;
}

.actions button:hover {
  background-color: #f0f0f0;
}

.actions button[aria-label*="Accept"] {
  border-color: #4CAF50;
  color: #4CAF50;
}
.actions button[aria-label*="Accept"]:hover {
  background-color: #e8f5e9;
}

.actions button[aria-label*="Reject"] {
  border-color: #f44336;
  color: #f44336;
}
.actions button[aria-label*="Reject"]:hover {
  background-color: #ffebee;
}

.actions button[aria-label*="Feedback"] {
  border-color: #2196F3;
  color: #2196F3;
}
.actions button[aria-label*="Feedback"]:hover {
  background-color: #e3f2fd;
}

.feedbackButtonPositive, .feedbackButtonNegative {
  background-color: transparent;
  border: 1px solid #ccc;
  padding: 0.2rem 0.4rem;
  margin-left: 4px;
  cursor: pointer;
}

.feedbackButtonPositive:hover {
  background-color: #e8f5e9; /* Light green */
  border-color: #4CAF50;
}

.feedbackButtonNegative:hover {
  background-color: #ffebee; /* Light red */
  border-color: #f44336;
}

.manualAdd {
  margin-top: 1rem;
  display: flex;
  align-items: center;
}

.manualAdd label {
  margin-right: 0.5rem;
  font-size: 0.9em;
  color: #555;
}

.manualAdd input[type="text"] {
  flex-grow: 1;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 0.5rem;
}

.manualAdd button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.manualAdd button:hover {
  background-color: #0056b3;
}

.summaryText {
  font-style: italic;
  color: #555;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-left: 3px solid #007bff;
}

.destinationSelect {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.destinationSelect label {
  margin-right: 10px;
  font-size: 0.9em;
  color: #555;
}

.selectDropdown {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  flex-grow: 1;
}

.notesLabel {
  display: block;
  margin-bottom: 5px;
  font-size: 0.9em;
  color: #555;
}

.notesTextarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box; /* Ensures padding doesn't add to width */
  min-height: 80px;
  font-family: sans-serif;
}

.contentTextarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  min-height: 150px; /* Larger for content preview */
  font-family: sans-serif;
  margin-bottom: 10px;
  background-color: #fff; /* Make it look like a preview area */
}

.highlightButton {
  padding: 8px 12px;
  background-color: #ffc107; /* A distinct color for highlight */
  color: #333;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: block; /* Make it a block to take full width or center easily if needed */
  margin-top: 5px;
}

.highlightButton:hover {
  background-color: #e0a800;
}