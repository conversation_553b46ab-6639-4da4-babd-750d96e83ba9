// Import required modules and components for testing
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DataManagement from '../renderer/components/DataManagement';
import useStore from '../renderer/store/useStore';

// Mock the Zustand store
jest.mock('../renderer/store/useStore');

// Mock Electron IPC and window.confirm
const mockInvoke = jest.fn();
const mockConfirm = jest.fn();

// Set up window object
global.window = {
  electron: {
    ipcRenderer: {
      invoke: mockInvoke,
    },
  },
};

// Set up window.confirm as a proper Jest mock function
Object.defineProperty(global.window, 'confirm', {
  writable: true,
  value: mockConfirm
});

// Mock API client functions (though they are called within store actions)
// We primarily test that the store actions are called.
// The store tests will cover the interaction with API client and IPC.

describe('DataManagement Component', () => {
  let mockExportData;
  let mockImportData;

  beforeEach(() => {
    // Reset mocks before each test
    mockExportData = jest.fn();
    mockImportData = jest.fn();
    mockInvoke.mockReset();
    mockConfirm.mockReset();

    useStore.mockReturnValue({
      exportData: mockExportData,
      importData: mockImportData,
      exportInProgress: false,
      importInProgress: false,
      exportError: null,
      importError: null,
    });
  });

  test('renders Data Management section with buttons', () => {
    render(<DataManagement />);
    expect(screen.getByText('Data Management')).toBeInTheDocument();
    expect(screen.getByText('Export Knowledge Base')).toBeInTheDocument();
    expect(screen.getByText('Import Knowledge Base')).toBeInTheDocument();
  });

  test('calls exportData action when Export button is clicked', async () => {
    mockExportData.mockResolvedValueOnce(); // Simulate successful export initiation
    render(<DataManagement />);
    fireEvent.click(screen.getByText('Export Knowledge Base'));

    expect(mockExportData).toHaveBeenCalledTimes(1);
    // Optional: Check for loading state if component re-renders based on it
    // await waitFor(() => expect(screen.getByText('Exporting...')).toBeInTheDocument());
  });

  test('calls importData action when Import button is clicked', async () => {
    mockImportData.mockResolvedValueOnce(); // Simulate successful import initiation
    render(<DataManagement />);
    fireEvent.click(screen.getByText('Import Knowledge Base'));

    expect(mockImportData).toHaveBeenCalledTimes(1);
    // Optional: Check for loading state
    // await waitFor(() => expect(screen.getByText('Importing...')).toBeInTheDocument());
  });

test('displays export loading state when exportInProgress is true', () => {
  useStore.mockReturnValue({
    exportData: mockExportData,
    importData: mockImportData,
    exportInProgress: true,
    importInProgress: false,
    exportError: null,
    importError: null,
  });
  render(<DataManagement />);
  expect(screen.getByText('Exporting...')).toBeInTheDocument();
  expect(screen.queryByText('Export Knowledge Base')).toBeNull();
});

test('displays import loading state when importInProgress is true', () => {
  useStore.mockReturnValue({
    exportData: mockExportData,
    importData: mockImportData,
    exportInProgress: false,
    importInProgress: true,
    exportError: null,
    importError: null,
  });
  render(<DataManagement />);
  expect(screen.getByText('Importing...')).toBeInTheDocument();
  expect(screen.queryByText('Import Knowledge Base')).toBeNull();
});

  test('displays export error message when exportError is present', () => {
    const errorMessage = 'Export failed miserably';
    useStore.mockReturnValue({
      exportData: mockExportData,
      importData: mockImportData,
      exportInProgress: false,
      importInProgress: false,
      exportError: errorMessage,
      importError: null,
    });
    render(<DataManagement />);
    expect(screen.getByText(`Export Error: ${errorMessage}`)).toBeInTheDocument();
  });

  test('displays import error message when importError is present', () => {
    const errorMessage = 'Import totally failed';
    useStore.mockReturnValue({
      exportData: mockExportData,
      importData: mockImportData,
      exportInProgress: false,
      importInProgress: false,
      exportError: null,
      importError: errorMessage,
    });
    render(<DataManagement />);
    expect(screen.getByText(`Import Error: ${errorMessage}`)).toBeInTheDocument();
  });

  test('alert is shown on successful exportData call', async () => {
    mockExportData.mockResolvedValueOnce();
    global.alert = jest.fn(); // Mock alert

    render(<DataManagement />);
    fireEvent.click(screen.getByText('Export Knowledge Base'));

    await waitFor(() => {
      expect(mockExportData).toHaveBeenCalledTimes(1);
    });
    await waitFor(() => {
      expect(global.alert).toHaveBeenCalledWith('Export initiated successfully. Check your chosen location.');
    });
  });

  test('alert is shown on successful importData call', async () => {
    mockImportData.mockResolvedValueOnce();
    global.alert = jest.fn(); // Mock alert

    render(<DataManagement />);
    fireEvent.click(screen.getByText('Import Knowledge Base'));

    await waitFor(() => {
      expect(mockImportData).toHaveBeenCalledTimes(1);
    });
    await waitFor(() => {
      expect(global.alert).toHaveBeenCalledWith('Import initiated successfully. The application may reload or update.');
    });
  });

  test('alert is shown with error message on failed exportData call', async () => {
    const error = new Error('Network Error');
    mockExportData.mockRejectedValueOnce(error);
    global.alert = jest.fn(); // Mock alert

    render(<DataManagement />);
    fireEvent.click(screen.getByText('Export Knowledge Base'));

    await waitFor(() => {
      expect(mockExportData).toHaveBeenCalledTimes(1);
    });
    await waitFor(() => {
      expect(global.alert).toHaveBeenCalledWith(`Export failed: ${error.message}`);
    });
  });

  test('alert is shown with error message on failed importData call', async () => {
    const error = new Error('File Read Error');
    mockImportData.mockRejectedValueOnce(error);
    global.alert = jest.fn(); // Mock alert

    render(<DataManagement />);
    fireEvent.click(screen.getByText('Import Knowledge Base'));

    await waitFor(() => {
      expect(mockImportData).toHaveBeenCalledTimes(1);
    });
    await waitFor(() => {
      expect(global.alert).toHaveBeenCalledWith(`Import failed: ${error.message}`);
    });
  });

});
