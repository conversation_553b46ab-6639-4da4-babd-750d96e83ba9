# High-Level Architecture: Browser Extension UI Module

**Version:** 1.0
**Date:** May 13, 2025
**Based on:** 
*   [`docs/specs/Browser_Extension_UI_overview.md`](docs/specs/Browser_Extension_UI_overview.md)
*   [`docs/PRD.md`](docs/PRD.md)
*   [`docs/architecture/Web_Content_Capture_Module_architecture.md`](docs/architecture/Web_Content_Capture_Module_architecture.md)
*   [`docs/architecture/Intelligent_Capture_Organization_Assistance_Module_architecture.md`](docs/architecture/Intelligent_Capture_Organization_Assistance_Module_architecture.md)

## 1. Overview

The Browser Extension UI Module provides the user-facing interface for the Personalized AI Knowledge Companion's web clipping functionality. It allows users to initiate content capture, select capture modes, preview content, view and interact with automatically extracted metadata and AI-generated suggestions (summaries, tags, categories), add personal annotations (notes, highlights), and confirm the capture process. This architecture focuses on creating a responsive, intuitive, and lightweight user experience, adhering to the non-functional requirements outlined in the PRD, while effectively orchestrating interactions with backend modules.

## 2. Architectural Goals

*   **Usability:** Provide a simple, clean, modern, and intuitive interface (NFR 6.6.1).
*   **Responsiveness:** Ensure the UI loads quickly and responds promptly to user interactions (NFR 6.3.1).
*   **Lightweight:** Maintain a minimal footprint to avoid hindering the user's browsing experience (NFR 6.6.2).
*   **Modularity:** Clearly define UI components and their interactions with backend services.
*   **Functionality:** Fully support all user stories and functional requirements outlined in the Feature Overview Specification ([`docs/specs/Browser_Extension_UI_overview.md`](docs/specs/Browser_Extension_UI_overview.md:21)).
*   **Integration:** Seamlessly communicate with the "Web Content Capture Module" and "Intelligent Capture & Organization Assistance Module".

## 3. Key Components (Browser Extension Standard Structure)

The UI module follows a standard browser extension architecture:

*   **3.1. Popup UI / Sidebar UI (`popup.html`, `popup.js`, `popup.css`)**
    *   **Purpose:** The primary visual interface activated by the user (e.g., via toolbar icon). Presents capture options, previews, metadata, AI suggestions, and user input fields.
    *   **Responsibilities:**
        *   Displaying capture mode options (Full Page, Article, Selection, Bookmark, PDF - FR 5.1.3-5.1.7).
        *   Rendering content previews provided by the Web Content Capture Module (FR 5.1.9).
        *   Displaying extracted metadata (Title, URL, etc.) and allowing edits (e.g., Title - FR 5.1.8, US6).
        *   Displaying AI suggestions (Summary, Tags, Categories) received from the Intelligent Capture & Organization Assistance Module (FR 5.2.1, 5.2.2, 5.2.3).
        *   Providing controls for user interaction:
            *   Editing tags (add, remove, modify - FR 5.2.4).
            *   Selecting/changing/creating categories (FR 5.2.5).
            *   Adding notes/comments (FR 5.2.6).
            *   Initiating highlighting (FR 5.2.7 - interaction might involve Content Script).
            *   Providing feedback on AI suggestions (FR 5.2.8).
        *   Sending user commands (initiate capture, save, cancel) and inputs to the Background Script.
        *   Displaying status updates and error messages received from the Background Script.

*   **3.2. Content Script(s) (`content_script.js`, `content_script.css`)**
    *   **Purpose:** JavaScript and CSS injected into the active web page to interact directly with the page's DOM.
    *   **Responsibilities:**
        *   Capturing user text/image selections (FR 5.1.5).
        *   Potentially assisting with highlighting functionality within the page context (FR 5.2.7).
        *   Communicating selections or other page-specific data back to the Background Script.
        *   Could potentially receive commands from the Background Script to modify the page (e.g., display selection tools), though the primary UI is the popup/sidebar.

*   **3.3. Background Script / Service Worker (`background.js`)**
    *   **Purpose:** Acts as the central controller and communication hub for the extension. Manages state, orchestrates workflows, and interfaces with backend modules.
    *   **Responsibilities:**
        *   Receiving commands and data from the Popup UI and Content Scripts.
        *   Maintaining the state of the current capture operation.
        *   Acting as the sole communication point with backend modules:
            *   Sending capture initiation requests (URL, mode, selection data) to the Web Content Capture Module (WCCM) API.
            *   Receiving preview content and extracted metadata from WCCM.
            *   Sending requests for AI assistance (content sample/context) to the Intelligent Capture & Organization Assistance Module (ICOAM) API.
            *   Receiving AI suggestions (summary, tags, categories) from ICOAM.
            *   Sending the final aggregated capture data (content reference, final metadata, user inputs, annotations, feedback) to the designated save endpoint (likely managed by WCCM, as per [`docs/architecture/Web_Content_Capture_Module_architecture.md:103`](docs/architecture/Web_Content_Capture_Module_architecture.md:103) and [`docs/specs/Browser_Extension_UI_overview.md:148`](docs/specs/Browser_Extension_UI_overview.md:148)).
        *   Forwarding data (previews, suggestions, status updates) back to the Popup UI for display.
        *   Handling errors from backend communication and relaying them to the UI.
        *   Managing extension lifecycle events.

## 4. Component Interactions & Data Flow

```mermaid
sequenceDiagram
    participant User
    participant PopupUI as Popup/Sidebar UI
    participant ContentScript as Content Script (CS)
    participant BackgroundScript as Background Script (BG)
    participant WCC_API as Web Content Capture Module API
    participant ICOA_API as Intelligent Capture Module API

    User->>PopupUI: Clicks Extension Icon
    PopupUI->>BackgroundScript: Request UI State/Activation
    BackgroundScript-->>PopupUI: UI Ready

    User->>PopupUI: Selects Capture Mode (e.g., Article)
    PopupUI->>BackgroundScript: Initiate Capture (Mode, URL)
    activate BackgroundScript

    %% --- Interaction with Web Content Capture Module ---
    BackgroundScript->>WCC_API: POST /capture/initiate (URL, Mode)
    activate WCC_API
    WCC_API-->>BackgroundScript: { previewHtml, extractedMetadata }
    deactivate WCC_API

    BackgroundScript->>PopupUI: Update Preview & Metadata
    
    %% --- Interaction with Intelligent Capture Module ---
    BackgroundScript->>ICOA_API: POST /assist/suggestions (contentSample/URL/Title)
    activate ICOA_API
    ICOA_API-->>BackgroundScript: { suggestedTags, suggestedCategories, summary }
    deactivate ICOA_API

    BackgroundScript->>PopupUI: Update AI Suggestions

    %% --- User Interaction within Popup ---
    User->>PopupUI: Edits Title, Adds Notes, Modifies Tags, Selects Category, Provides Feedback
    PopupUI->>BackgroundScript: Update Capture State (User Inputs)

    alt User Highlights Text (if applicable)
        User->>PopupUI: Initiates Highlight
        PopupUI->>BackgroundScript: Request Highlight Tool
        BackgroundScript->>ContentScript: Activate Highlighter on Page/Preview
        ContentScript-->>BackgroundScript: Highlight Data (offsets, text)
        BackgroundScript->>PopupUI: Confirm Highlight / Update State
    end

    User->>PopupUI: Clicks Save
    PopupUI->>BackgroundScript: Finalize Capture (All User Data)
    BackgroundScript->>WCC_API: POST /capture/save (Aggregated Data: contentRef, title, tags, category, notes, highlights, feedback, format, ...)
    activate WCC_API
    WCC_API-->>BackgroundScript: { success: boolean, itemId?: string, error?: string }
    deactivate WCC_API

    BackgroundScript->>PopupUI: Display Save Confirmation/Error
    deactivate BackgroundScript

```

**Key Data Flow Steps:**

1.  **Activation:** User triggers the Popup UI.
2.  **Initiation:** Popup UI sends the capture mode and current URL to the Background Script.
3.  **Content Fetch & Preview:** Background Script requests initial processing (preview, basic metadata) from the WCC Module API. Results are displayed in the Popup UI.
4.  **AI Assistance:** Background Script requests suggestions (summary, tags, categories) from the ICOA Module API using content context. Suggestions are displayed in the Popup UI.
5.  **User Refinement:** User interacts with the Popup UI to modify metadata, add notes, adjust tags/categories, highlight, and provide feedback. These changes are relayed to the Background Script to update the capture state.
6.  **Save:** User confirms the save. The Background Script aggregates all final data (content reference/details from WCCM, all user inputs, annotations, feedback) and sends it to a unified save endpoint (likely handled by WCCM).
7.  **Confirmation:** Background Script receives success/failure status and updates the Popup UI.

## 5. Technology Considerations

*   **UI Framework:** Standard HTML, CSS, and JavaScript. To ensure a lightweight and responsive UI (NFR 6.6.2), avoid heavy frameworks unless their performance impact is carefully evaluated. Vanilla JS or lightweight libraries (like Preact, Svelte) might be preferable over larger ones (like React, Vue).
*   **State Management:** The Background Script will manage the primary state of the capture process. Simple state management within the Popup UI might be needed.
*   **Communication:** Standard WebExtensions APIs (`browser.runtime.sendMessage`, `browser.tabs.sendMessage`) for internal communication. `fetch` API for communication between the Background Script and backend module APIs (assuming they are exposed via HTTP, potentially on `localhost` via Native Messaging bridge or similar).

## 6. Non-Functional Requirements Compliance

*   **Simple & Clean UI (NFR 6.6.1):** Achieved through careful design of `popup.html` and `popup.css`, focusing on clarity, minimalism, and intuitive layout.
*   **Lightweight Clipper (NFR 6.6.2):** Prioritizing efficient JavaScript and potentially avoiding large frameworks in the Popup UI. Background script operations are asynchronous. Content scripts are kept minimal.
*   **Performance & Reliability (NFR 6.3.1):** Asynchronous communication with backend modules prevents UI blocking. Clear error handling and feedback mechanisms in the Background Script and Popup UI enhance reliability perception. UI rendering optimized for speed.

## 7. Integration Points

The Browser Extension UI Module (specifically the Background Script) interacts with:

*   **Web Content Capture Module API:**
    *   `POST /capture/initiate`: To get preview and basic metadata.
    *   `POST /capture/save`: To send the final aggregated data for storage.
*   **Intelligent Capture & Organization Assistance Module API:**
    *   `POST /assist/suggestions`: To get AI-driven summary, tags, and categories.
    *   (Feedback is included in the `/capture/save` payload, implicitly reaching ICOAM via WCCM coordination or a dedicated feedback endpoint if needed).
*   **Browser APIs:** Standard WebExtensions APIs for UI display, tab interaction, content script injection, and messaging.

## 8. Future Considerations

*   **UI Customization:** Allow users basic customization options (e.g., theme).
*   **More Interactive Previews:** Enhance previews with more dynamic highlighting or annotation tools.
*   **Sidebar UI Option:** Offer a persistent sidebar as an alternative to the popup for users who prefer it.

This architecture provides a solid foundation for building the Browser Extension UI, ensuring it meets functional requirements while adhering to crucial non-functional constraints like performance and user experience.