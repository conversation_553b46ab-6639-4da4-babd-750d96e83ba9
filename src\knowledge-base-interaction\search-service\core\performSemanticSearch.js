import { generateEmbedding } from '../../../common/services/embeddingService';
import { searchSimilarItems } from '../../../common/services/annService';
import KbalService from '../../kbal/services/KbalService.mjs'; // Assuming KbalService is a class

/**
 * @typedef {object} FilterOptions
 * @property {string} [type] - Filter by content type (e.g., 'note', 'article').
 * @property {string[]} [tags] - Filter by tags.
 * @property {{startDate?: string, endDate?: string}} [dateRange] - Filter by creation date range.
 * @property {string[]} [sourceApplications] - Filter by source application.
 * @property {string} [textContains] - Filter by specific text within the content.
 */

/**
 * @typedef {object} SearchResultItem
 * @property {string} id - The ID of the knowledge item.
 * @property {string} title - The title of the knowledge item.
 * @property {string} snippet - A relevant snippet from the content.
 * @property {number} score - The relevance score of the search result.
 * @property {string} type - The type of the knowledge item (e.g., 'note', 'article').
 * @property {string[]} [tags] - Tags associated with the item.
 * @property {string} [createdAt] - ISO date string of when the item was created.
 * @property {string} [updatedAt] - ISO date string of when the item was last updated.
 * @property {string} [sourceUrl] - If applicable, the original URL of the content.
 */

const DEFAULT_K_RESULTS = 10;
const SNIPPET_MAX_LENGTH = 150; // Max length for generated snippets

/**
 * Generates a simple snippet from content.
 * For V1, this is a basic truncation. Could be improved to find query terms.
 * @param {string} content - The full content text.
 * @param {string} query - The search query (currently unused in V1 snippet).
 * @returns {string}
 */
function createSnippet(content, query) {
  if (!content) return '';
  // Basic snippet: first N characters.
  // A better snippet would find query terms or use summarization.
  let snippet = content.substring(0, SNIPPET_MAX_LENGTH);
  if (content.length > SNIPPET_MAX_LENGTH) {
    snippet += '...';
  }
  return snippet;
}


/**
 * Performs a semantic search against the knowledge base.
 *
 * @param {string} query - The natural language query.
 * @param {FilterOptions} [filters] - Optional filters to apply.
 * @param {number} [k=DEFAULT_K_RESULTS] - The number of top results to return.
 * @returns {Promise<SearchResultItem[]>} A promise that resolves to an array of search result items.
 */
export async function performSemanticSearch(query, filters, k = DEFAULT_K_RESULTS) {
  if (!query || query.trim() === '') {
    return [];
  }

  try {
    const queryEmbedding = await generateEmbedding(query);
    
    // Pass filters to ANN service if it supports them directly
    // Otherwise, ANN returns more results, and we filter afterwards (less efficient)
    // The mock annService handles filters.
    const similarItemIdsAndScores = await searchSimilarItems(queryEmbedding, k, filters);

    if (!similarItemIdsAndScores || similarItemIdsAndScores.length === 0) {
      return [];
    }

    // Consistently use getInstance() as KbalService is a singleton
    const kbal = KbalService.getInstance();
    // If KbalService's init is not automatically called by getInstance or its constructor,
    // and needs to be called explicitly, ensure it's done.
    // The mock provides an init, so if the real one needs it, this would be the place.
    // await kbal.init(); // Assuming init is handled by getInstance or constructor or not needed here for getContentById

    const searchResults = [];
    for (const item of similarItemIdsAndScores) {
      const fullItem = await kbal.getContentById(item.id);
      if (fullItem) {
        searchResults.push({
          id: fullItem.id,
          title: fullItem.title || 'Untitled',
          snippet: createSnippet(fullItem.content || '', query),
          score: item.score,
          type: fullItem.type || 'unknown',
          tags: fullItem.tags || [],
          createdAt: fullItem.createdAt,
          updatedAt: fullItem.updatedAt,
          sourceUrl: fullItem.sourceUrl,
        });
      }
    }
    
    // If ANN service doesn't filter, and we need to filter post-retrieval (less ideal)
    // This example assumes ANN service handles filtering based on the mock.
    // If not, additional filtering logic would be here based on `filters` and `fullItem` details.

    return searchResults.slice(0, k); // Ensure we don't exceed k if ANN returned more due to no pre-filtering

  } catch (error) {
    console.error('Error during semantic search:', error);
    // Avoid problematic fallbacks: fail clearly.
    // Depending on requirements, could throw error or return empty array.
    // For now, return empty on error to prevent breaking UI.
    return []; 
  }
}
