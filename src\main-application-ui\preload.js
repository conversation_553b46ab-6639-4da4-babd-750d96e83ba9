const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld(
  'electronAPI', { // Keep existing electronAPI structure if other things are exposed
    // Expose specific ipcRenderer functionalities needed by the renderer
    ipcRenderer: {
      invoke: (channel, ...args) => {
        // Whitelist channels that can be invoked
        const validChannels = [
          'show-save-dialog',
          'show-open-dialog',
          'kbalService:getContentById',
          'kbalService:queryContent',
          'kbalService:addContent',
          'kbalService:updateContent',
          'kbalService:deleteContent',
          'kbalService:clearAllItems',
          'kbalService:fetchTags',
          'kbalService:createTag',
          'kbalService:updateTag',
          'kbalService:deleteTag',
          'kbalService:fetchCategories',
          'kbalService:createCategory',
          'kbalService:updateCategory',
          'kbalService:deleteCategory',
          'kbalService:askQuestion',
          'kbalService:summarizeItem',
          'kbalService:getConceptualLinks',
          'kbalService:transformContent',
          'kbalService:getManualLinks',
          'kbalService:addManualLink',
          'kbalService:removeManualLink',
          'kbalService:getCaptureSettings',
          'kbalService:putCaptureSettings',
          'kbalService:fetchClippingTemplates',
          'kbalService:createClippingTemplate',
          'kbalService:updateClippingTemplate',
          'kbalService:deleteClippingTemplate',
          'kbalService:setDefaultClippingTemplate',
          'kbalService:triggerExport',
          'kbalService:triggerImport'
        ];
        if (validChannels.includes(channel)) {
          return ipcRenderer.invoke(channel, ...args);
        }
        console.error(`Attempted to invoke an invalid channel: ${channel}`);
        return Promise.reject(new Error(`Invalid IPC channel: ${channel}`));
      },
      // send: (channel, data) => {
      //   // Whitelist channels
      //   let validChannels = ['toMain'];
      //   if (validChannels.includes(channel)) {
      //     ipcRenderer.send(channel, data);
      //   }
      // },
      // on: (channel, func) => {
      //   // Whitelist channels
      //   let validChannels = ['fromMain'];
      //   if (validChannels.includes(channel)) {
      //     // Deliberately strip event as it includes `sender`
      //     ipcRenderer.on(channel, (event, ...args) => func(...args));
      //   }
      // }
      // Add other specific IPC methods if needed, e.g., send, on, once
    },
    // Expose kbalService methods via IPC
    kbalService: {
      getContentById: (contentId) => ipcRenderer.invoke('kbalService:getContentById', contentId),
      queryContent: (queryCriteria) => ipcRenderer.invoke('kbalService:queryContent', queryCriteria),
      addContent: (contentItemData) => ipcRenderer.invoke('kbalService:addContent', contentItemData),
      updateContent: (contentId, updates) => ipcRenderer.invoke('kbalService:updateContent', contentId, updates),
      deleteContent: (contentId) => ipcRenderer.invoke('kbalService:deleteContent', contentId),
      clearAllItems: () => ipcRenderer.invoke('kbalService:clearAllItems'),
      fetchTags: () => ipcRenderer.invoke('kbalService:fetchTags'),
      createTag: (tagData) => ipcRenderer.invoke('kbalService:createTag', tagData),
      updateTag: (tagId, tagData) => ipcRenderer.invoke('kbalService:updateTag', tagId, tagData),
      deleteTag: (tagId) => ipcRenderer.invoke('kbalService:deleteTag', tagId),
      fetchCategories: () => ipcRenderer.invoke('kbalService:fetchCategories'),
      createCategory: (categoryData) => ipcRenderer.invoke('kbalService:createCategory', categoryData),
      updateCategory: (categoryId, categoryData) => ipcRenderer.invoke('kbalService:updateCategory', categoryId, categoryData),
      deleteCategory: (categoryId) => ipcRenderer.invoke('kbalService:deleteCategory', categoryId),
      askQuestion: (itemId, question) => ipcRenderer.invoke('kbalService:askQuestion', itemId, question),
      summarizeItem: (itemId) => ipcRenderer.invoke('kbalService:summarizeItem', itemId),
      getConceptualLinks: (itemId) => ipcRenderer.invoke('kbalService:getConceptualLinks', itemId),
      transformContent: (itemId, transformationType, selectedText) => ipcRenderer.invoke('kbalService:transformContent', itemId, transformationType, selectedText),
      getManualLinks: (itemId) => ipcRenderer.invoke('kbalService:getManualLinks', itemId),
      addManualLink: (sourceItemId, linkData) => ipcRenderer.invoke('kbalService:addManualLink', sourceItemId, linkData),
      removeManualLink: (sourceItemId, linkId) => ipcRenderer.invoke('kbalService:removeManualLink', sourceItemId, linkId),
      getCaptureSettings: () => ipcRenderer.invoke('kbalService:getCaptureSettings'),
      putCaptureSettings: (settingsData) => ipcRenderer.invoke('kbalService:putCaptureSettings', settingsData),
      fetchClippingTemplates: () => ipcRenderer.invoke('kbalService:fetchClippingTemplates'),
      createClippingTemplate: (templateData) => ipcRenderer.invoke('kbalService:createClippingTemplate', templateData),
      updateClippingTemplate: (templateId, updates) => ipcRenderer.invoke('kbalService:updateClippingTemplate', templateId, updates),
      deleteClippingTemplate: (templateId) => ipcRenderer.invoke('kbalService:deleteClippingTemplate', templateId),
      setDefaultClippingTemplate: (templateId) => ipcRenderer.invoke('kbalService:setDefaultClippingTemplate', templateId),
      triggerExport: (filePath) => ipcRenderer.invoke('kbalService:triggerExport', filePath),
      triggerImport: (filePath) => ipcRenderer.invoke('kbalService:triggerImport', filePath)
    }
  }
);

console.log('Preload script loaded.');
