# KBAL Data Models

This directory defines the data structures (models) used by the Knowledge Base Access Layer (KBAL).

## contentItem.js

-   **Purpose**: Represents a single item of content within the knowledge base.
-   **Potential Attributes**:
    -   `id`: Unique identifier.
    -   `type`: Type of content (e.g., "note", "article", "bookmark").
    -   `title`: Title of the content.
    -   `sourceUrl`: Original URL if applicable.
    -   `content`: The actual content (text, markdown, etc.).
    -   `metadata`: Additional information like tags, creation date, modification date.
    -   `embeddings`: Vector embeddings for similarity search (if applicable).

## queryCriteria.js (Optional)

-   **Purpose**: Defines the structure for query parameters if complex querying is needed.
-   **Potential Attributes**:
    -   `keywords`: Search terms.
    -   `tags`: Filter by tags.
    -   `dateRange`: Filter by creation/modification date.
    -   `contentType`: Filter by content type.

AI-Verifiable Structure: Ensure `contentItem.js` exists and defines a basic structure.