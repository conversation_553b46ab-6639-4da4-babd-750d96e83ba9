require('dotenv').config();
import { GoogleGenerativeAI } from '@google/generative-ai';
import { logInfo, logError } from '../../knowledge-base-interaction/features/content-summarization/utils/logger';

const DEFAULT_MAX_CATEGORIES = 3;

// Initialize Gemini AI
const API_KEY = process.env.GEMINI_API_KEY; // Use a specific environment variable for Gemini API Key

if (!API_KEY) {
  logError("GEMINI_API_KEY environment variable is not set. Please set your Gemini API key.");
  // In a real application, you might want to throw an error or handle this more gracefully
  // For now, we'll proceed with a dummy model or return empty results if API_KEY is missing.
}

const genAI = API_KEY ? new GoogleGenerativeAI(API_KEY) : null;
const model = genAI ? genAI.getGenerativeModel({ model: "gemini-pro" }) : null;

/**
 * Cleans the input text for consistent processing.
 * @param {string} text
 * @returns {string} Cleaned text.
 */
function cleanText(text) {
  if (!text || typeof text !== 'string') return '';
  return text.toLowerCase().replace(/[^\w\s-]/g, "").replace(/\s+/g, " ").trim();
}

/**
 * Parses the AI's response to extract category suggestions.
 * Assumes the AI might return categories in a list format, e.g., "1. Category A, 2. Category B" or "Categories: A, B, C".
 * @param {string} aiResponse - The raw response string from the AI.
 * @returns {string[]} An array of extracted category strings.
 */
function parseAiCategories(aiResponse) {
  const categories = new Set();
  if (!aiResponse) return [];

  // Attempt to find categories in common list formats
  // e.g., "Category1, Category2, Category3"
  // or "1. Category1\n2. Category2"
  const commaSeparated = aiResponse.split(/,\s*/);
  const numberedList = aiResponse.split(/\d+\.\s*/).filter(Boolean);
  const bulletList = aiResponse.split(/-\s*/).filter(Boolean);

  [commaSeparated, numberedList, bulletList].forEach(parts => {
    parts.forEach(part => {
      const cleanedPart = cleanText(part);
      if (cleanedPart.length > 0) {
        categories.add(cleanedPart.split('\n')[0].trim()); // Take only the first line if multi-line
      }
    });
  });

  return Array.from(categories).filter(cat => cat.length > 1); // Filter out very short or empty strings
}

/**
 * Suggests relevant categories for a given piece of content using Gemini AI.
 *
 * @param {string} content - The content to suggest categories for.
 * @param {string[]} [existingCategories=[]] - An array of already existing categories for the content.
 * @param {number} [maxCategories=DEFAULT_MAX_CATEGORIES] - The maximum number of categories to suggest.
 * @returns {Promise<string[]>} A promise that resolves to an array of suggested category strings.
 */
export async function suggestCategories(content, existingCategories = [], maxCategories = DEFAULT_MAX_CATEGORIES) {
  logInfo('AI Suggestion Service: Requesting category suggestions from Gemini AI.', { contentSnippet: content.substring(0, 100) });

  if (!model) {
    logError('AI Suggestion Service: Gemini AI model not initialized. API_KEY might be missing or invalid.');
    return [];
  }

  if (!content || content.trim().length < 50) { // Increased minimum content length for AI
    logInfo('AI Suggestion Service: Content too short for meaningful AI category suggestions.');
    return [];
  }

  try {
    const prompt = `Given the following content, suggest up to ${maxCategories} relevant categories. Provide only the category names, separated by commas. Avoid categories already present: ${existingCategories.join(', ')}. Content: "${content}"`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const aiResponseText = response.text();

    if (aiResponseText) {
      const aiSuggestedCategories = parseAiCategories(aiResponseText);
      const lowerExistingCategories = existingCategories.map(cat => cleanText(cat));
      const finalSuggestions = new Set();

      for (const cat of aiSuggestedCategories) {
        if (!lowerExistingCategories.includes(cleanText(cat))) {
          finalSuggestions.add(cat);
        }
        if (finalSuggestions.size >= maxCategories) {
          break;
        }
      }

      const result = Array.from(finalSuggestions);
      logInfo('AI Suggestion Service: Generated category suggestions from Gemini AI.', { suggestions: result });
      return result;
    } else {
      logError('AI Suggestion Service: No valid response from Gemini AI for category suggestion.');
      return [];
    }
  } catch (error) {
    logError('AI Suggestion Service: Error suggesting categories with Gemini AI.', error);
    // Log more details about the error if available
    if (error.status) {
      logError(`HTTP Status: ${error.status}`);
    }
    if (error.message) {
      logError(`Error Message: ${error.message}`);
    }
    return [];
  }
}