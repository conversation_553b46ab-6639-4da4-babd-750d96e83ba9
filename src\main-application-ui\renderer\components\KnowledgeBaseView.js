import React from 'react';
import PropTypes from 'prop-types';
import ContentList from './knowledge-base-view/ContentList';
import FilterSortBar from './knowledge-base-view/FilterSortBar';
import PaginationControl from './knowledge-base-view/PaginationControl';

const KnowledgeBaseView = ({
  items,
  searchResults,
  currentSearchTerm,
  onSearchTermChange,
  onPerformSearch,
  availableTags, // Array of { id, name }
  availableCategories, // Array of { id, name }
  selectedTags,
  onSelectedTagsChange,
  selectedCategories,
  onSelectedCategoriesChange,
  selectedDateFilter,
  onDateFilterChange,
  selectedSourceFilter,
  onSourceFilterChange,
  selectedSortBy,
  onSortByChange,
  selectedSortOrder,
  onSortOrderChange,
  onSelectItem, // Receives itemId (string)
  selectedItemId, // Pass selectedItemId for styling
  currentPage,
  totalPages,
  onPageChange,
  itemsPerPage,
  totalItems,
}) => {

  const displayItems = currentSearchTerm ? searchResults : items;

  // Map availableTags and availableCategories from {id, name} to string arrays for FilterSortBar
  const tagOptions = availableTags.map(tag => tag.name);
  const categoryOptions = availableCategories.map(cat => cat.name);

  return (
    <div className="knowledge-base-view-container">
      <h1>Knowledge Base</h1>
      {/* SearchBar removed, search is handled by NavigationFiltersPane */}
      <FilterSortBar
        availableTags={tagOptions}
        selectedTags={selectedTags}
        onSelectedTagsChange={onSelectedTagsChange}
        availableCategories={categoryOptions}
        selectedCategories={selectedCategories}
        onSelectedCategoriesChange={onSelectedCategoriesChange}
        selectedDateFilter={selectedDateFilter}
        onDateFilterChange={onDateFilterChange}
        selectedSourceFilter={selectedSourceFilter}
        onSourceFilterChange={onSourceFilterChange}
        selectedSortBy={selectedSortBy}
        onSortByChange={onSortByChange}
        selectedSortOrder={selectedSortOrder}
        onSortOrderChange={onSortOrderChange}
      />
      <ContentList
        items={displayItems}
        onSelectItem={onSelectItem}
        selectedItemId={selectedItemId} // Pass selectedItemId for styling
      />
      <PaginationControl
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        itemsPerPage={itemsPerPage}
        totalItems={totalItems}
      />
    </div>
  );
};

KnowledgeBaseView.propTypes = {
  items: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    snippet: PropTypes.string,
    tags: PropTypes.arrayOf(PropTypes.string),
    timestamp: PropTypes.string,
    source: PropTypes.string,
  })).isRequired,
  searchResults: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    snippet: PropTypes.string,
    tags: PropTypes.arrayOf(PropTypes.string),
    timestamp: PropTypes.string,
    source: PropTypes.string,
  })),
  currentSearchTerm: PropTypes.string,
  onSearchTermChange: PropTypes.func.isRequired,
  onPerformSearch: PropTypes.func.isRequired,
  
  availableTags: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
  })).isRequired,
  availableCategories: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
  })).isRequired,
  
  selectedTags: PropTypes.arrayOf(PropTypes.string).isRequired,
  onSelectedTagsChange: PropTypes.func.isRequired,
  selectedCategories: PropTypes.arrayOf(PropTypes.string).isRequired,
  onSelectedCategoriesChange: PropTypes.func.isRequired,
  selectedDateFilter: PropTypes.string.isRequired,
  onDateFilterChange: PropTypes.func.isRequired,
  selectedSourceFilter: PropTypes.string.isRequired,
  onSourceFilterChange: PropTypes.func.isRequired,
  selectedSortBy: PropTypes.oneOf(['date', 'title', 'relevance']).isRequired,
  onSortByChange: PropTypes.func.isRequired,
  selectedSortOrder: PropTypes.oneOf(['asc', 'desc']).isRequired,
  onSortOrderChange: PropTypes.func.isRequired,

  onSelectItem: PropTypes.func.isRequired, // Expects itemId (string)
  selectedItemId: PropTypes.string, // ContentList uses this for styling selected state

  currentPage: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  itemsPerPage: PropTypes.number.isRequired,
  totalItems: PropTypes.number.isRequired,
};

KnowledgeBaseView.defaultProps = {
  searchResults: [],
  currentSearchTerm: '',
  selectedItemId: null, // Default to null
  availableTags: [],
  availableCategories: [],
  selectedTags: [],
  selectedCategories: [],
  selectedDateFilter: '',
  onDateFilterChange: () => {},
  selectedSourceFilter: '',
  onSourceFilterChange: () => {},
  selectedSortBy: 'date',
  onSortByChange: () => {},
  selectedSortOrder: 'desc',
  onSortOrderChange: () => {},
  currentPage: 1,
  totalPages: 1,
  itemsPerPage: 10,
  totalItems: 0,
};

export default KnowledgeBaseView;