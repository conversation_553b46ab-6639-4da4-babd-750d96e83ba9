# Operating System files
.DS_Store
Thumbs.db
ehthumbs.db

# IDE and editor files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.swp
*~
*.bak

# Node.js
/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
*.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Build artifacts
dist/
build/
out/
target/

# Log files
*.log
logs/

# Temporary files
tmp/
temp/

# Archives
*.zip
*.tar.gz
*.rar

# Local configuration files (example, adjust as needed)
# config.local.json
# secrets.json
extract.py
# Pheromone file (if it's not meant to be versioned)
.Pheromone

# Playwright
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
