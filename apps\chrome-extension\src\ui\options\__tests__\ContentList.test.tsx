import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ContentList from '../ContentList';
import { KnowledgeBaseEntry } from '@pkm-ai/knowledge-base-service/dist/types';
import { jest } from '@jest/globals';

const mockItems: KnowledgeBaseEntry[] = [
  { id: '1', title: 'Item 1', content: 'Content 1', createdAt: new Date(), updatedAt: new Date() },
  { id: '2', title: 'Item 2', content: 'Content 2', createdAt: new Date(), updatedAt: new Date() },
  { id: '3', title: 'Item 3', content: 'Content 3', createdAt: new Date(), updatedAt: new Date() },
];

// Mock react-window to simplify testing.
// We are not testing react-window itself, but our Row component and list integration.
jest.mock('react-window', () => ({
  // Use a type assertion to avoid the spread error
  ...(jest.requireActual('react-window') as object),
  FixedSizeList: jest.fn(({ height, itemCount, width, children, itemData }) => {
    // Render all items for testing purposes, ignoring virtualization for unit tests.
    // Playwright E2E tests will cover actual virtualization.
    return (
      <div data-testid="mocked-fixed-size-list" style={{ height, width }}>
        {Array.from({ length: itemCount }, (_, index) => {
          // Ensure a unique key for each child in the mock
          const item = itemData.items[index];
          const key = item ? item.id : index;
          return React.cloneElement(children({ index, style: {}, data: itemData }), { key });
        })}
      </div>
    );
  }),
}));


describe('ContentList', () => {
  const mockOnItemSelect = jest.fn();
  const mockOnDelete = jest.fn();

  beforeEach(() => {
    mockOnItemSelect.mockClear();
    mockOnDelete.mockClear();
  });

  test('renders a list of items with delete buttons', () => {
    render(<ContentList items={mockItems} onItemSelect={mockOnItemSelect} onDelete={mockOnDelete} />);
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Content 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
    // Check for delete buttons
    const deleteButtons = screen.getAllByRole('button', { name: /delete item/i }); // Assuming aria-label or text
    expect(deleteButtons.length).toBe(mockItems.length);
  });

  test('calls onItemSelect when an item is clicked (but not on delete button)', () => {
    render(<ContentList items={mockItems} onItemSelect={mockOnItemSelect} onDelete={mockOnDelete} />);
    fireEvent.click(screen.getByText('Item 2')); // Click the item text/container
    expect(mockOnItemSelect).toHaveBeenCalledTimes(1);
    expect(mockOnItemSelect).toHaveBeenCalledWith(mockItems[1]);
    expect(mockOnDelete).not.toHaveBeenCalled();
  });
  
  test('calls onDelete when a delete button is clicked and stops propagation', () => {
    render(<ContentList items={mockItems} onItemSelect={mockOnItemSelect} onDelete={mockOnDelete} />);
    // Find delete button for "Item 1" specifically
    const deleteButtonForItem1 = screen.getByRole('button', { name: 'Delete Item 1' });
    fireEvent.click(deleteButtonForItem1);
    
    expect(mockOnDelete).toHaveBeenCalledTimes(1);
    expect(mockOnDelete).toHaveBeenCalledWith('1'); // ID of Item 1
    expect(mockOnItemSelect).not.toHaveBeenCalled(); // Ensure item selection was not triggered
  });


  test('highlights the selected item', () => {
    const { rerender } = render(
      <ContentList items={mockItems} onItemSelect={mockOnItemSelect} onDelete={mockOnDelete} selectedItemId="2" />
    );
    const item2Element = screen.getByText('Item 2').closest('div[role="button"]'); // Target the clickable row div
    expect(item2Element).toHaveClass('bg-blue-100');

    const item1Element = screen.getByText('Item 1').closest('div[role="button"]');
    expect(item1Element).not.toHaveClass('bg-blue-100');

    rerender(<ContentList items={mockItems} onItemSelect={mockOnItemSelect} onDelete={mockOnDelete} selectedItemId="1" />);
    expect(screen.getByText('Item 1').closest('div[role="button"]')).toHaveClass('bg-blue-100');
    expect(screen.getByText('Item 2').closest('div[role="button"]')).not.toHaveClass('bg-blue-100');
  });

  test('renders "No items to display." when items array is empty', () => {
    render(<ContentList items={[]} onItemSelect={mockOnItemSelect} onDelete={mockOnDelete} />);
    expect(screen.getByText('No items to display.')).toBeInTheDocument();
  });

  test('renders "No items to display." when items prop is undefined', () => {
    // @ts-expect-error Testing undefined case
    render(<ContentList items={undefined} onItemSelect={mockOnItemSelect} onDelete={mockOnDelete} />);
    expect(screen.getByText('No items to display.')).toBeInTheDocument();
  });

  test('handles keyboard interaction (Enter key) for item selection', () => {
    render(<ContentList items={mockItems} onItemSelect={mockOnItemSelect} onDelete={mockOnDelete} />);
    const itemElement = screen.getByText('Item 3').closest('div[role="button"]') as HTMLElement;
    if (itemElement) {
      itemElement.focus();
      fireEvent.keyDown(itemElement, { key: 'Enter', code: 'Enter' });
      expect(mockOnItemSelect).toHaveBeenCalledTimes(1);
      expect(mockOnItemSelect).toHaveBeenCalledWith(mockItems[2]);
    } else {
      throw new Error("Could not find item element for keyboard test");
    }
  });

   test('handles keyboard interaction (Space key) for item selection', () => {
    render(<ContentList items={mockItems} onItemSelect={mockOnItemSelect} onDelete={mockOnDelete} />);
    const itemElement = screen.getByText('Item 1').closest('div[role="button"]') as HTMLElement;
    if (itemElement) {
      itemElement.focus();
      fireEvent.keyDown(itemElement, { key: ' ', code: 'Space' });
      expect(mockOnItemSelect).toHaveBeenCalledTimes(1);
      expect(mockOnItemSelect).toHaveBeenCalledWith(mockItems[0]);
    } else {
      throw new Error("Could not find item element for keyboard test");
    }
  });
});