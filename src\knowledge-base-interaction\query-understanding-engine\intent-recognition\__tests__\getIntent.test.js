import { getIntent, Intent } from '../getIntent'; // Assuming Intent enum/object is exported from getIntent.js

describe('getIntent', () => {
  it('should correctly identify a "search" intent for simple search queries', async () => {
    const query1 = "find documents about AI";
    expect(await getIntent(query1)).toBe(Intent.SEARCH);

    const query2 = "search for knowledge management techniques";
    expect(await getIntent(query2)).toBe(Intent.SEARCH);

    const query3 = "show me notes on project X";
    expect(await getIntent(query3)).toBe(Intent.SEARCH);
  });

  it('should correctly identify a "question_answering" intent for questions', async () => {
    const query1 = "what is the capital of France?";
    expect(await getIntent(query1)).toBe(Intent.QUESTION_ANSWERING);

    const query2 = "how does photosynthesis work?";
    expect(await getIntent(query2)).toBe(Intent.QUESTION_ANSWERING);

    const query3 = "explain the theory of relativity"; // Could also be summarization, but Q&A is primary for "explain"
    expect(await getIntent(query3)).toBe(Intent.QUESTION_ANSWERING);
  });

  it('should correctly identify a "summarization" intent for summarization requests', async () => {
    const query1 = "summarize the main points of this article"; // Needs context, but intent is clear
    expect(await getIntent(query1)).toBe(Intent.SUMMARIZATION);

    const query2 = "give me a summary of the latest meeting notes";
    expect(await getIntent(query2)).toBe(Intent.SUMMARIZATION);

    const query3 = "tl;dr on climate change impact";
    expect(await getIntent(query3)).toBe(Intent.SUMMARIZATION);
  });

  it('should identify "command" intent for specific action phrases', async () => {
    const query1 = "create a new note about TDD";
    expect(await getIntent(query1)).toBe(Intent.COMMAND); // Example: "create", "add", "delete"

    const query2 = "set a reminder for tomorrow's meeting";
    expect(await getIntent(query2)).toBe(Intent.COMMAND);
  });

  it('should default to "unknown" or "search" for ambiguous queries if no clear intent is found', async () => {
    const query1 = "artificial intelligence"; // Could be search, could be a topic for Q&A
    // Depending on implementation, this might default to SEARCH or a specific UNKNOWN intent
    // For now, let's assume it defaults to SEARCH if no other strong signals.
    expect(await getIntent(query1)).toBe(Intent.SEARCH); // Or Intent.UNKNOWN if that's preferred

    const query2 = "the concept of flow";
    expect(await getIntent(query2)).toBe(Intent.SEARCH); // Or Intent.UNKNOWN
  });

  it('should handle empty or very short queries gracefully', async () => {
    const query1 = "";
    expect(await getIntent(query1)).toBe(Intent.UNKNOWN); // Or throw error, TBD by implementation

    const query2 = "a";
    // This is ambiguous, likely UNKNOWN or a fallback like SEARCH
    expect(await getIntent(query2)).toBe(Intent.UNKNOWN);
  });

  it('should be case-insensitive', async () => {
    const query1 = "WHAT IS THE MEANING OF LIFE?";
    expect(await getIntent(query1)).toBe(Intent.QUESTION_ANSWERING);

    const query2 = "Summarize THIS Document";
    expect(await getIntent(query2)).toBe(Intent.SUMMARIZATION);
  });

  // Add more tests for edge cases, complex queries, and specific keywords
  it('should identify "search" intent with keywords like "look up"', async () => {
    const query = "look up information on quantum computing";
    expect(await getIntent(query)).toBe(Intent.SEARCH);
  });

  it('should identify "question_answering" intent with "tell me about"', async () => {
    const query = "tell me about the history of the internet";
    expect(await getIntent(query)).toBe(Intent.QUESTION_ANSWERING);
  });

  it('should identify "summarization" intent with "brief overview of"', async () => {
    const query = "brief overview of the attached report";
    expect(await getIntent(query)).toBe(Intent.SUMMARIZATION);
  });

  it('should handle queries with mixed signals, prioritizing stronger indicators', async () => {
    // Example: "Can you search for a summary of the latest AI research?"
    // This has "search" and "summary". The implementation will decide priority.
    // Let's assume for now that "summary of" is a stronger indicator for SUMMARIZATION.
    const query = "Can you search for a summary of the latest AI research?";
    // This is a tricky one. Could be SEARCH for a summary, or a SUMMARIZATION task.
    // For V1, let's assume "summary of" takes precedence.
    // A more advanced system might parse this as a multi-step task.
    expect(await getIntent(query)).toBe(Intent.SUMMARIZATION);

    const query2 = "What is the best way to find information about topic X?";
    // "What is" -> Q&A, "find information" -> SEARCH
    // Let's assume Q&A is stronger here.
    expect(await getIntent(query2)).toBe(Intent.QUESTION_ANSWERING);
  });
});