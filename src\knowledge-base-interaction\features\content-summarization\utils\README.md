# Utilities - Content Summarization

This directory contains shared utility functions used across the content summarization feature components.

## Responsibilities

*   Provide common logging functionalities.
*   Offer helper functions for error handling and formatting.
*   Include content processing utilities (e.g., HTML to text conversion, PDF text extraction).

## Key Files

*   `logger.js`: Centralized logging functions.
*   `errorHandler.js`: (Optional) Centralized error handling or error formatting utilities.
*   `contentProcessor.js`: Functions for processing different content types (HTML, PDF, etc.).