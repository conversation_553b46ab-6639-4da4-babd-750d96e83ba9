// test/e2e/e2e_scn_007_offlineAccess.e2e.test.js

// Mock KBAL (storage service) - for browsing, viewing, and basic search
const mockGetAllItems = jest.fn(); // For browsing
const mockGetItem = jest.fn();    // For viewing
const mockSearchItemsLocal = jest.fn(); // For basic keyword search
const mockStorage = {
  getAllItems: mockGetAllItems,
  getItem: mockGetItem,
  searchItemsLocal: mockSearchItemsLocal, // A dedicated local search method
};
jest.mock('../../src/knowledge-base-interaction/kbal/services/kbalService.js', () => mockStorage, { virtual: true });

// Mock a network status utility or navigator.onLine
// In a real E2E test with a browser, you might manipulate the browser's network settings.
// Here, we'll mock a simple utility that the application might use.
let onlineStatus = true;
const mockIsOnline = jest.fn(() => onlineStatus);
const mockOfflineAccessHandler = { // Assuming a handler that checks this
    isOnline: mockIsOnline,
    handleOfflineFeature: jest.fn((featureName) => {
        // Call the mockIsOnline function directly, which is what mockOfflineAccessHandler.isOnline points to
        if (!mockIsOnline()) {
            return { available: false, mode: 'offline' };
        }
        return { available: true, mode: 'online' };
    })
};
jest.mock('../../src/knowledge-base-interaction/offline-access-handler/offlineHandler.js', () => mockOfflineAccessHandler, { virtual: true });

// Mock AI services to ensure they are NOT called when offline for features that need them
const mockPerformQA = jest.fn();
const mockPerformSummarization = jest.fn();
jest.mock('../../src/knowledge-base-interaction/ai-services-gateway/gateway.js', () => ({
  performQuestionAnswering: mockPerformQA,
  performSummarization: mockPerformSummarization,
  // Other AI functions that should be gracefully disabled
}), { virtual: true });


// Helper function to simulate the workflow for E2E_SCN_007
async function simulateOfflineAccessWorkflow({
  preExistingLocalItems, // Array of items already in local storage
  itemToViewIdOffline,
  localSearchQuery,
  simulatedLocalSearchResults,
}) {
  // 0. Precondition: Items are in local storage
  mockGetAllItems.mockResolvedValue(preExistingLocalItems);
  mockGetItem.mockImplementation(itemId => {
    const item = preExistingLocalItems.find(it => it.id === itemId);
    return Promise.resolve(item);
  });
  mockSearchItemsLocal.mockResolvedValue(simulatedLocalSearchResults);

  // 1. Simulate network disconnection
  onlineStatus = false;
  mockIsOnline.mockReturnValue(false); // Ensure our mock reflects this

  // 2. User opens main application UI (implicit)
  // 3. User navigates to Knowledge Base view (implicit)
  
  // 4. User browses the list of locally stored content items
  const browsedItems = await mockStorage.getAllItems();

  // 5. User selects and views the full content of a stored item
  const viewedItemOffline = await mockStorage.getItem(itemToViewIdOffline);

  // 6. User performs a basic keyword search
  const localSearchResults = await mockStorage.searchItemsLocal(localSearchQuery);
  
  // 7. Verify features requiring internet are gracefully disabled
  //    Example: Attempting an AI feature that should be blocked by offlineHandler
  const qaAttempt = await mockOfflineAccessHandler.handleOfflineFeature('AI_Q&A');
  const summaryAttempt = await mockOfflineAccessHandler.handleOfflineFeature('AI_Summarization');


  // Restore online status for subsequent tests if any in the same file (though usually separate)
  onlineStatus = true;
  mockIsOnline.mockReturnValue(true);

  return { browsedItems, viewedItemOffline, localSearchResults, qaAttemptStatus: qaAttempt, summaryAttemptStatus: summaryAttempt };
}

describe('E2E_SCN_007: Offline Access to Knowledge Base', () => {
  const localItem1 = { id: 'offline-001', title: 'Offline Article 1', content: 'This content is available offline. Keywords: local, data, access' };
  const localItem2 = { id: 'offline-002', title: 'Notes on Offline Mode', content: 'Offline mode should allow basic search. Keywords: search, local, mode' };

  beforeEach(() => {
    mockGetAllItems.mockClear();
    mockGetItem.mockClear();
    mockSearchItemsLocal.mockClear();
    mockIsOnline.mockClear();
    mockOfflineAccessHandler.handleOfflineFeature.mockClear();
    mockPerformQA.mockClear();
    mockPerformSummarization.mockClear();
    
    onlineStatus = true; // Reset to online before each test
    mockIsOnline.mockReturnValue(true);
  });

  test('should allow browsing, viewing, and basic local search when offline', async () => {
    const testParams = {
      preExistingLocalItems: [localItem1, localItem2],
      itemToViewIdOffline: localItem1.id,
      localSearchQuery: 'local data', // Basic keyword search
      simulatedLocalSearchResults: [localItem1], // Assuming localItem1 matches "local data"
    };

    const { browsedItems, viewedItemOffline, localSearchResults, qaAttemptStatus, summaryAttemptStatus } = 
      await simulateOfflineAccessWorkflow(testParams);

    // Verify browsing
    expect(mockGetAllItems).toHaveBeenCalledTimes(1);
    expect(browsedItems).toEqual(testParams.preExistingLocalItems);

    // Verify viewing
    expect(mockGetItem).toHaveBeenCalledWith(testParams.itemToViewIdOffline);
    expect(viewedItemOffline).toEqual(localItem1);

    // Verify local search
    expect(mockSearchItemsLocal).toHaveBeenCalledWith(testParams.localSearchQuery);
    expect(localSearchResults).toEqual(testParams.simulatedLocalSearchResults);

    // Verify online-dependent features are "disabled"
    expect(mockOfflineAccessHandler.isOnline).toHaveBeenCalled(); // Check that online status was queried
    expect(qaAttemptStatus.available).toBe(false);
    expect(summaryAttemptStatus.available).toBe(false);
    
    // Ensure actual AI service calls were NOT made
    expect(mockPerformQA).not.toHaveBeenCalled();
    expect(mockPerformSummarization).not.toHaveBeenCalled();
  });
});