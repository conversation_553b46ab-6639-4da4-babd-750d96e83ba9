import React from 'react';
import PropTypes from 'prop-types';
import DOMPurify from 'dompurify';

const ALLOWED_PROTOCOLS = ['http:', 'https:', 'mailto:', 'ftp:'];

const isValidURL = (url) => {
  try {
    if (!url) return false;
    const parsedURL = new URL(url);
    return ALLOWED_PROTOCOLS.includes(parsedURL.protocol.toLowerCase());
  } catch (e) {
    return false; // Invalid URL structure
  }
};

const MetadataDisplay = ({ metadata }) => {
  if (!metadata) {
    return <div className="metadata-display-empty">No metadata available.</div>;
  }

  const { sourceURL, captureDate, tags, categories } = metadata;

  const hasAnyMetadata = sourceURL || captureDate || (tags && tags.length > 0) || (categories && categories.length > 0);

  if (!hasAnyMetadata) {
    return <div className="metadata-display-no-details">No metadata details provided.</div>;
  }

  const safeHrefSourceURL = isValidURL(sourceURL) ? sourceURL : '#';

  const sanitizedTagsString = tags && tags.length > 0
    ? tags.map(tag => DOMPurify.sanitize(tag || '')).join(', ')
    : '';

  const sanitizedCategoriesString = categories && categories.length > 0
    ? categories.map(category => DOMPurify.sanitize(category || '')).join(', ')
    : '';

  return (
    <div className="metadata-display">
      {sourceURL && (
        <div className="metadata-item metadata-source">
          <strong>Source:</strong>{' '}
          <a href={safeHrefSourceURL} target="_blank" rel="noopener noreferrer">
            {sourceURL} {/* React escapes this text content */}
          </a>
        </div>
      )}
      {captureDate && (
        <div className="metadata-item metadata-capture-date">
          <strong>Captured:</strong> {new Date(captureDate).toLocaleString()}
        </div>
      )}
      {tags && tags.length > 0 && (
        <div className="metadata-item metadata-tags">
          <strong>Tags:</strong> {sanitizedTagsString}
        </div>
      )}
      {categories && categories.length > 0 && (
        <div className="metadata-item metadata-categories">
          <strong>Categories:</strong> {sanitizedCategoriesString}
        </div>
      )}
    </div>
  );
};

MetadataDisplay.propTypes = {
  metadata: PropTypes.shape({
    sourceURL: PropTypes.string,
    captureDate: PropTypes.string, // ISO date string
    tags: PropTypes.arrayOf(PropTypes.string),
    categories: PropTypes.arrayOf(PropTypes.string),
  }),
};

MetadataDisplay.defaultProps = {
  metadata: null,
};

export default MetadataDisplay;