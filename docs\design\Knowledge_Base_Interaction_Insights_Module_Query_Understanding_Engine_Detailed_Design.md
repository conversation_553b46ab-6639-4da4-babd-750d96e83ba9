# Detailed Design: Query Understanding Engine

## 1. Component Overview

The Query Understanding Engine (QUE) is a critical component within the Knowledge Base Interaction & Insights Module. Its primary function is to process natural language queries submitted by users through the UI Layer. The QUE analyzes these queries to discern the user's intent, extract relevant keywords and entities, and determine the type of request being made (e.g., information retrieval, question answering, content transformation, or conceptual linking). Based on this analysis, the QUE structures the query into a machine-readable format and routes it to the appropriate downstream component—either the Search Service for knowledge base lookups or the AI Services Gateway for more complex AI-driven processing. This component explicitly excludes query understanding functionalities related to Content Summarization, which are handled by a dedicated engine.

## 2. Key Responsibilities and Features

The QUE has the following key responsibilities and features:

*   **Query Parsing:**
    *   Accepts raw natural language text strings from the UI Layer.
    *   Performs initial linguistic processing, such as tokenization, stemming/lemmatization, and part-of-speech tagging if necessary.
*   **Intent Recognition:**
    *   Identifies the user's primary goal or intention behind the query.
    *   Categorizes intent into predefined types:
        *   `SEARCH`: User is looking for specific documents or information within the knowledge base.
        *   `QA`: User is asking a specific question expecting a direct answer.
        *   `TRANSFORM`: User is requesting a transformation of existing knowledge (e.g., "explain X in simpler terms," "compare A and B").
        *   `LINK`: User is trying to establish or explore conceptual connections between pieces of information.
        *   `UNKNOWN`: The intent cannot be reliably determined.
*   **Entity Extraction:**
    *   Identifies and extracts key entities (e.g., nouns, noun phrases, named entities like persons, organizations, locations, specific concepts) from the query.
    *   These entities provide context and specify the subject matter of the user's request.
*   **Request Routing:**
    *   Based on the recognized intent and extracted entities, determines the appropriate downstream service:
        *   `SEARCH` intents are typically routed to the **Search Service**.
        *   `QA`, `TRANSFORM`, and `LINK` intents are typically routed to the **AI Services Gateway** for advanced processing by AI models.
    *   Constructs a structured query object containing the processed information for the target service.

## 3. API Definition

### Input

The QUE receives a raw user query as input.

*   **Endpoint:** (Internal, e.g., a function call or message queue) `processQuery`
*   **Method:** (Internal, e.g., `POST` if it were a web service)
*   **Payload:**
    ```json
    {
      "rawQuery": "string" // e.g., "Find documents about AI in healthcare" or "What are the benefits of knowledge graphs?"
    }
    ```

### Output

The QUE produces a structured query object.

*   **Payload:**
    ```json
    {
      "originalQuery": "string", // The raw query received
      "parsedQuery": {
        "tokens": ["string"], // Optional: list of processed tokens
        "keywords": ["string"], // Key terms extracted from the query
        "entities": [ // List of identified entities
          {
            "text": "string", // The text of the entity
            "type": "string", // e.g., "CONCEPT", "PERSON", "TOPIC"
            "relevance": "float" // Optional: relevance score
          }
        ]
      },
      "intent": {
        "type": "string", // "SEARCH", "QA", "TRANSFORM", "LINK", "UNKNOWN"
        "confidence": "float" // Confidence score for the recognized intent (0.0 to 1.0)
      },
      "targetService": "string", // "SearchService", "AIServicesGateway", "None"
      "routingParameters": { // Parameters specific to the target service
        // For SearchService:
        "searchQuery": "string", // The refined query string for the search service
        "searchType": "string", // e.g., "keyword", "semantic" (can be inferred or specified)
        // For AIServicesGateway:
        "prompt": "string", // The formulated prompt for the AI model
        "taskType": "string" // e.g., "question_answering", "concept_explanation", "relationship_identification"
      }
    }
    ```

## 4. Data Structures

*   **Raw Query:**
    *   A simple string representing the user's input.
    *   Example: `"Tell me about the applications of machine learning in finance."`

*   **Parsed Query Components:**
    *   **Tokens:** An array of strings representing individual words or sub-words after initial processing.
        *   Example: `["tell", "me", "about", "the", "applications", "of", "machine", "learning", "in", "finance"]`
    *   **Keywords:** An array of strings representing the most significant terms.
        *   Example: `["machine learning", "finance", "applications"]`
    *   **Entities:** An array of objects, each representing an identified entity.
        *   Example:
            ```json
            [
              {"text": "machine learning", "type": "CONCEPT"},
              {"text": "finance", "type": "DOMAIN"}
            ]
            ```

*   **Intent Object:**
    *   An object representing the recognized user intent.
    *   Example:
        ```json
        {
          "type": "QA",
          "confidence": 0.85
        }
        ```

*   **Structured Query Object (Output):**
    *   As defined in the API Output section. This is the primary data structure produced by the QUE.

## 5. Interaction with other components

*   **UI Layer:**
    *   Receives: Raw natural language query from the user via the UI Layer.
    *   Sends: (Indirectly) The results from downstream services are ultimately presented back to the user via the UI Layer, but the QUE itself does not directly send data back to the UI Layer. Its output is consumed by other backend components.

*   **Search Service:**
    *   Sends: A structured query object with `targetService: "SearchService"`, containing refined search terms and potentially identified filters or keywords.
    *   Receives: (Indirectly) The Search Service processes the query and returns search results, which are then passed on (potentially through other components) to the UI Layer.

*   **AI Services Gateway:**
    *   Sends: A structured query object with `targetService: "AIServicesGateway"`, containing a formulated prompt or task description, along with extracted entities and context for AI model processing.
    *   Receives: (Indirectly) The AI Services Gateway interacts with AI models and returns their responses, which are then passed on to the UI Layer.

## 6. Query Processing Logic

The query processing logic within the QUE follows these general steps:

1.  **Input Reception:** Receive the raw query string from the UI Layer.
2.  **Preprocessing (Optional but Recommended):**
    *   Normalize text (e.g., lowercasing).
    *   Tokenize the query into individual words or sub-words.
    *   Perform stemming or lemmatization to reduce words to their root form.
    *   Remove stop words (common words like "the", "is", "in") if they are not critical for intent/entity recognition in the chosen NLP approach.
3.  **Keyword and Entity Extraction:**
    *   Employ NLP techniques (e.g., part-of-speech tagging, named entity recognition (NER), keyword extraction algorithms like TF-IDF or RAKE if operating on a corpus, or leveraging an AI model via AI Services Gateway for more advanced extraction).
    *   Identify key terms, concepts, and named entities.
4.  **Intent Recognition:**
    *   Analyze the query structure, keywords, entities, and potentially verb patterns to determine the user's intent.
    *   This can be achieved through:
        *   Rule-based systems (e.g., matching query patterns: "What is X?" -> QA).
        *   Machine learning classifiers trained on example queries for each intent type (could leverage AI Services Gateway).
        *   A hybrid approach.
    *   Assign a confidence score to the recognized intent.
5.  **Routing Decision:**
    *   Based on the primary intent:
        *   If intent is `SEARCH` (e.g., "find documents on X", "search for Y"), route to `SearchService`. Formulate `routingParameters.searchQuery`.
        *   If intent is `QA` (e.g., "What is X?", "How does Y work?"), route to `AIServicesGateway`. Formulate `routingParameters.prompt` and `routingParameters.taskType` as "question_answering".
        *   If intent is `TRANSFORM` (e.g., "explain X simply", "compare A and B"), route to `AIServicesGateway`. Formulate `routingParameters.prompt` and `routingParameters.taskType` accordingly (e.g., "explanation", "comparison").
        *   If intent is `LINK` (e.g., "how is X related to Y?", "show concepts linked to Z"), route to `AIServicesGateway` (or potentially a specialized graph traversal service if available). Formulate `routingParameters.prompt` and `routingParameters.taskType` as "relationship_identification" or "concept_linking".
        *   If intent is `UNKNOWN` or confidence is low, proceed to error handling.
6.  **Output Formulation:**
    *   Construct the structured query object (as defined in API Output) including the original query, parsed elements, recognized intent, target service, and routing parameters.
7.  **Dispatch:** Send the structured query object to the determined `targetService`.

## 7. Error Handling

The QUE must gracefully handle situations where queries are ambiguous or intent cannot be reliably determined:

*   **Ambiguous Queries:**
    *   If a query can be interpreted in multiple ways with similar confidence scores for different intents, the system might:
        *   Default to a safe option (e.g., `SEARCH`).
        *   (Future Enhancement) Prompt the user for clarification via the UI Layer.
*   **Unrecognized Intent:**
    *   If the intent confidence score is below a predefined threshold, or if no intent can be matched (classified as `UNKNOWN`):
        *   The `targetService` might be set to `None` or default to `SearchService` with the raw query.
        *   Log the query for later analysis and model/rule improvement.
        *   The UI Layer should be informed to display a message like "Sorry, I didn't understand that. Could you please rephrase your query or try a general search?"
*   **NLP Processing Failures:**
    *   Handle exceptions during NLP tasks (e.g., issues with an external NLP library or service).
    *   Log the error and potentially fall back to a simpler processing strategy (e.g., basic keyword extraction).
*   **Malicious Input:**
    *   Basic sanitization should be performed on the input string to prevent injection attacks if parts of the query are used directly in constructing queries for other systems (though structured parameters are preferred). The primary defense against complex malicious inputs targeting NLP models would be at the AI Services Gateway or the NLP library level.

## 8. AI Verifiable Outcomes

The design of the QUE supports AI verifiable outcomes, particularly for intent recognition and routing, in the following ways:

1.  **Structured Output:** The `structuredQueryObject` provides a clear, machine-readable output. Test assertions can be made against:
    *   `intent.type`: Verifying that the correct intent (e.g., `SEARCH`, `QA`) is identified for a given test query.
    *   `intent.confidence`: Ensuring confidence scores are within expected ranges for well-defined queries.
    *   `targetService`: Confirming that the query is routed to the correct downstream component (`SearchService` or `AIServicesGateway`).
    *   `parsedQuery.keywords` and `parsedQuery.entities`: Checking if relevant terms and entities are correctly extracted.
    *   `routingParameters`: Validating that the parameters passed to the downstream service are correctly formulated based on the query and intent.

2.  **Test Case Alignment:**
    *   **Test Case 4 (Search):** Queries like "find information on X" should result in `intent.type: "SEARCH"` and `targetService: "SearchService"`, with `routingParameters.searchQuery` containing "X" or a relevant derivative.
    *   **Test Case 5 (AI Q&A):** Queries like "What is X?" or "Explain Y" should result in `intent.type: "QA"` (or `TRANSFORM` for "Explain Y") and `targetService: "AIServicesGateway"`, with an appropriate `routingParameters.prompt`.

3.  **Modularity:** The distinct steps in query processing (parsing, intent recognition, entity extraction, routing) allow for focused testing of each sub-function. Mocking dependencies (like an external NLP service) facilitates unit testing.

4.  **Loggable Intermediate States:** Logging the intermediate results of parsing, entity extraction, and intent scoring can help in debugging test failures and understanding the decision-making process, which is crucial for AI-driven components.

## 9. Self-Reflection

*   **Quality:**
    *   The design emphasizes a structured approach to query understanding, which is crucial for consistency and accuracy.
    *   The separation of concerns (parsing, intent, entities, routing) promotes modularity.
    *   The accuracy of intent recognition and entity extraction will heavily depend on the chosen NLP techniques/models. Continuous evaluation and refinement will be necessary.
*   **Security:**
    *   **Input Sanitization:** Basic input sanitization is mentioned for raw query strings if they are ever used in a way that could lead to injection vulnerabilities (e.g., constructing database queries directly, though this is bad practice). The primary reliance is on structured parameterization for downstream services.
    *   **NLP Model Vulnerabilities:** If using complex AI models (especially via the AI Services Gateway), these models themselves could be targets for adversarial attacks (e.g., prompts designed to elicit unintended behavior or bypass safety filters). This risk is managed at the AI Services Gateway and the model provider level. The QUE should ensure it doesn't introduce new vulnerabilities by how it passes data.
    *   **Data Privacy:** User queries might contain sensitive information. The QUE must handle this data according to the project's overall data privacy and security policies. Logging should be carefully considered to avoid storing sensitive PII unnecessarily.
*   **Performance:**
    *   **NLP Processing Time:** NLP tasks, especially those involving complex models or multiple stages (tokenization, POS tagging, NER, intent classification), can be computationally intensive and introduce latency.
        *   Consideration should be given to the complexity of the chosen NLP pipeline. Simpler rule-based systems might be faster but less accurate/flexible.
        *   If using external AI services, network latency to the AI Services Gateway will be a factor.
        *   Caching strategies (e.g., for common queries or parsed components) could be explored if performance becomes a bottleneck, but might be complex to implement correctly for natural language.
    *   **Scalability:** The QUE needs to be able to handle the expected load of user queries. If stateful NLP models are used, managing their resources will be important.
*   **Maintainability:**
    *   **Modularity:** The component's breakdown into distinct responsibilities (parsing, intent, entities, routing) should make it easier to maintain and update individual parts. For example, the intent recognition model could be updated without affecting the core routing logic, as long as the API contract is maintained.
    *   **Configuration:** Intent definitions, keywords, and rules (if used) should be configurable to allow for easy updates and tuning without code changes.
    *   **Testability:** The clear API and structured output facilitate automated testing, which is crucial for maintainability.
    *   **Logging and Monitoring:** Comprehensive logging of query processing steps, decisions, and errors will be vital for troubleshooting and improving the system over time.
*   **Alignment with Acceptance Tests:**
    *   The design directly supports the natural language understanding aspects required for **Test Case 4 (Search)** by ensuring queries indicative of search are routed to the `SearchService`.
    *   It also supports **Test Case 5 (AI Q&A)** by identifying question-like queries and routing them to the `AIServicesGateway` for processing by AI models.
    *   The defined `intent` types (`SEARCH`, `QA`, `TRANSFORM`, `LINK`) and the `targetService` routing mechanism are fundamental to fulfilling these test cases. The AI verifiable outcomes section explicitly details how these can be tested.
    *   The exclusion of Content Summarization query understanding aligns with the task requirements and ensures this component remains focused.