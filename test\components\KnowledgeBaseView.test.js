import React from 'react';
import { render, screen, fireEvent, act, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import KnowledgeBaseView from '../../src/components/KnowledgeBaseView.js';
import useKnowledgeBaseStore from '../../src/state/store.js';
import DOMPurify from 'dompurify';

jest.mock('../../src/state/store.js');
jest.mock('dompurify', () => ({
  sanitize: jest.fn((html) => html),
}));

const mockNotes = [
  { id: '1', title: 'Alpha Note', content: 'Content Alpha' },
  { id: '2', title: 'Beta Test Note', content: 'Content Beta' },
  { id: '3', title: 'Gamma Another World', content: 'Content Gamma' },
  { id: '4', title: 'Beta Second Entry', content: 'Content Beta 2'},
];

describe('KnowledgeBaseView', () => {
  beforeEach(() => {
    useKnowledgeBaseStore.mockReturnValue(mockNotes);
    DOMPurify.sanitize.mockClear();
  });

  it('should render the component with initial knowledge base items', () => {
    render(<KnowledgeBaseView />);
    expect(screen.getByText('Knowledge Base View')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    mockNotes.forEach(note => {
      expect(screen.getByText(note.title)).toBeInTheDocument();
    });
    expect(DOMPurify.sanitize).toHaveBeenCalledTimes(mockNotes.length); 
  });

  it('should filter knowledge base items based on search term (case-insensitive)', async () => {
    render(<KnowledgeBaseView />);
    const searchInput = screen.getByPlaceholderText('Search...');
    DOMPurify.sanitize.mockClear();

    // Search for "Beta"
    fireEvent.change(searchInput, { target: { value: 'Beta' } });
    await waitFor(() => {
      expect(screen.getByText('Beta Test Note')).toBeInTheDocument();
      expect(screen.getByText('Beta Second Entry')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.queryByText('Alpha Note')).not.toBeInTheDocument();
      expect(screen.queryByText('Gamma Another World')).not.toBeInTheDocument();
    });
    // After debounce, 2 items should be rendered
    await waitFor(() => expect(DOMPurify.sanitize).toHaveBeenCalledTimes(2));

    DOMPurify.sanitize.mockClear();
    // Search for "alpha" (lowercase)
    fireEvent.change(searchInput, { target: { value: 'alpha' } });
    await waitFor(() => {
      expect(screen.getByText('Alpha Note')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.queryByText('Beta Test Note')).not.toBeInTheDocument();
    });
    // After debounce, 1 item should be rendered
    await waitFor(() => expect(DOMPurify.sanitize).toHaveBeenCalledTimes(1));
  });

  it('should display all items if search term is cleared', async () => {
    render(<KnowledgeBaseView />);
    const searchInput = screen.getByPlaceholderText('Search...');
    DOMPurify.sanitize.mockClear();

    fireEvent.change(searchInput, { target: { value: 'Beta' } });
    // Wait for "Beta" search to complete (2 items)
    await waitFor(() => expect(DOMPurify.sanitize).toHaveBeenCalledTimes(2));
    DOMPurify.sanitize.mockClear();

    fireEvent.change(searchInput, { target: { value: '' } });
    await waitFor(() => {
      mockNotes.forEach(note => {
        expect(screen.getByText(note.title)).toBeInTheDocument();
      });
    });
    // After debounce, all 4 items should be rendered
    await waitFor(() => expect(DOMPurify.sanitize).toHaveBeenCalledTimes(4));
  });

  it('should display all items if search term results in no matches (falls back to knowledgeBase)', async () => {
    render(<KnowledgeBaseView />);
    const searchInput = screen.getByPlaceholderText('Search...');
    DOMPurify.sanitize.mockClear();

    fireEvent.change(searchInput, { target: { value: 'NonExistentTerm' } });
    await waitFor(() => {
      mockNotes.forEach(note => {
        expect(screen.getByText(note.title)).toBeInTheDocument();
      });
    });
    // After debounce, if it falls back to knowledgeBase (4 items)
    await waitFor(() => expect(DOMPurify.sanitize).toHaveBeenCalledTimes(0));
  });
  
  it('should update displayed items when the knowledgeBase store changes', () => {
    const { rerender } = render(<KnowledgeBaseView />);
    DOMPurify.sanitize.mockClear(); // Clear after initial render
    
    const updatedNotes = [...mockNotes, { id: '5', title: 'Delta New Note', content: 'Content Delta' }];
    act(() => {
        useKnowledgeBaseStore.mockReturnValue(updatedNotes);
    });
    
    rerender(<KnowledgeBaseView />);
    updatedNotes.forEach(note => {
        expect(screen.getByText(note.title)).toBeInTheDocument();
    });
    // With useMemo, this should now be the length of updatedNotes
    expect(DOMPurify.sanitize).toHaveBeenCalledTimes(updatedNotes.length);
  });

  // Enhanced existing conceptual test
  it('should render a moderately large list of items (100) and measure time', () => {
    const numberOfItems = 100;
    const largeMockNotes = Array.from({ length: numberOfItems }, (_, i) => ({
      id: `perf-${i}`,
      title: `Performance Note ${i}`,
      content: `Content for ${i}`,
    }));
    useKnowledgeBaseStore.mockReturnValue(largeMockNotes);
    DOMPurify.sanitize.mockClear();

    const startTime = performance.now();
    render(<KnowledgeBaseView />);
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    console.log(`KnowledgeBaseView: Render time for ${numberOfItems} items: ${renderTime.toFixed(2)} ms`);

    expect(screen.getByText(`Performance Note 0`)).toBeInTheDocument();
    // For virtualized lists, the last item might not be rendered initially.
    const expectedVisibleItemsModerate = Math.min(numberOfItems, Math.ceil(400 / 50) + 2);
    if (numberOfItems > 1) {
        expect(screen.getByText(`Performance Note ${Math.min(numberOfItems - 1, 7)}`)).toBeInTheDocument();
    }
    expect(DOMPurify.sanitize).toHaveBeenCalledTimes(expectedVisibleItemsModerate);
    // Add a general assertion for performance, this threshold might need adjustment
    // For now, this is more about establishing a measurement process
    expect(renderTime).toBeLessThan(2000); // Example: Expect render time to be less than 2000ms
  });
});

describe('KnowledgeBaseView - Performance Benchmarks', () => {
  beforeEach(() => {
    useKnowledgeBaseStore.mockReturnValue([]); // Default to empty, specific tests will override
    DOMPurify.sanitize.mockClear();
  });

  const runPerformanceTest = (itemCount) => {
    const mockPerformanceNotes = Array.from({ length: itemCount }, (_, i) => ({
      id: `perf-large-${i}`,
      title: `Large Perf Note ${i}`,
      content: `Content for large performance test item ${i}`,
    }));
    useKnowledgeBaseStore.mockReturnValue(mockPerformanceNotes);
    DOMPurify.sanitize.mockClear();

    const startTime = performance.now();
    render(<KnowledgeBaseView />);
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    console.log(`KnowledgeBaseView Benchmark: Render time for ${itemCount} items (pre-virtualization): ${renderTime.toFixed(2)} ms`);

    // Verify some items are rendered
    expect(screen.getByText(`Large Perf Note 0`)).toBeInTheDocument();
    // For virtualized lists, the last item might not be rendered initially.
    // We can check for an item that is likely to be rendered.
    // The number of items rendered will depend on the list height and itemSize.
    // For height 400 and itemSize 50, around 8-9 items are visible.
    const expectedVisibleItems = Math.min(itemCount, Math.ceil(400 / 50) + 2); // +2 for buffer
    if (itemCount > 1) {
        // Check for an item within the initial visible range
        expect(screen.getByText(`Large Perf Note ${Math.min(itemCount - 1, 7)}`)).toBeInTheDocument();
    }
    // DOMPurify.sanitize will only be called for rendered items.
    expect(DOMPurify.sanitize).toHaveBeenCalledTimes(expectedVisibleItems);
    
    return renderTime;
  };

  it('should establish baseline rendering time for 1000 items (pre-virtualization)', () => {
    const itemCount = 1000;
    const renderTime = runPerformanceTest(itemCount);
    // This is a baseline, so we're not strictly asserting a maximum yet,
    // but logging it and ensuring it completes.
    // A very high threshold to ensure the test passes while capturing the metric.
    expect(renderTime).toBeLessThan(15000); // e.g., less than 15 seconds, adjust as needed
    // In a real scenario, this threshold would be refined based on acceptable performance.
  });

  it('should establish baseline rendering time for a very large dataset (e.g., 2000 items, pre-virtualization) - can be slow', () => {
    const itemCount = 2000; // This might be slow and could be adjusted or conditionally run
    const renderTime = runPerformanceTest(itemCount);
    expect(renderTime).toBeLessThan(30000); // e.g., less than 30 seconds
    // This test helps understand the scaling without virtualization.
  });
});