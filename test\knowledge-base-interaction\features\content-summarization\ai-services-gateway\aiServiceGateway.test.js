// test/knowledge-base-interaction/features/content-summarization/ai-services-gateway/aiServiceGateway.test.js

// Note: We will dynamically import summarizeContentWithAIService in tests that modify process.env
// to ensure the module picks up the changes to GEMINI_API_KEY.
// Note: We will dynamically import summarizeContentWithAIService in tests that modify process.env
// to ensure the module picks up the changes to GEMINI_API_KEY.
// We also need to dynamically require the mocked logger for assertions.

jest.mock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');

const originalApiKey = process.env.GEMINI_API_KEY;

describe('summarizeContentWithAIService', () => {
  const mockPayload = {
    content: 'This is test content.',
    contentType: 'text/plain', // This is what QLUE sends
    options: { summaryLength: 'medium' },
  };

  const expectedSimulatedSuccessData = {
    summary: `This is a simulated summary for: "${mockPayload.content.substring(0, 50)}..."`,
    contentType: 'text/plain',
    model: 'gemini-simulated',
  };

  beforeEach(() => {
    // Set default API key for most tests
    process.env.GEMINI_API_KEY = 'test-api-key';
    jest.resetModules(); // Clears the module cache
    
    // Get the mock functions from the mocked module to clear them
    const { logError, logInfo } = jest.requireMock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');
    logError.mockClear();
    logInfo.mockClear();
  });

  afterEach(() => { // Changed to afterEach for better isolation
    if (originalApiKey === undefined) {
      delete process.env.GEMINI_API_KEY;
    } else {
      process.env.GEMINI_API_KEY = originalApiKey;
    }
    jest.resetModules(); // Clean up for the next test file if any
  });

  test('should return summary on successful AI service call (Test Case 5.4.1 & 5.3.2)', async () => {
    const { summarizeContentWithAIService } = await import('../../../../../src/knowledge-base-interaction/features/content-summarization/ai-services-gateway/aiServiceGateway');
    const { logInfo } = jest.requireMock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');
    const result = await summarizeContentWithAIService(mockPayload);

    expect(logInfo).toHaveBeenCalledWith('AI Services Gateway: Received request to summarize content.', mockPayload);
    expect(result).toEqual(expectedSimulatedSuccessData);
  });

  test('should return error if GEMINI_API_KEY is not configured', async () => {
    delete process.env.GEMINI_API_KEY;
    jest.resetModules(); // Ensure module reloads with new env state
    jest.resetModules(); // Ensure module reloads with new env state
    const { summarizeContentWithAIService } = await import('../../../../../src/knowledge-base-interaction/features/content-summarization/ai-services-gateway/aiServiceGateway');
    const { logError } = jest.requireMock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');
    
    const expectedError = { error: 'AI service configuration error', message: 'API key not configured.' };
    const result = await summarizeContentWithAIService(mockPayload);

    expect(logError).toHaveBeenCalledWith('AI Services Gateway: Gemini API key is not configured.');
    expect(result).toEqual(expectedError);
  });

  test('should return error if GEMINI_API_KEY is the placeholder value', async () => {
    process.env.GEMINI_API_KEY = 'YOUR_GEMINI_API_KEY';
    jest.resetModules(); // Ensure module reloads with new env state
    jest.resetModules(); // Ensure module reloads with new env state
    const { summarizeContentWithAIService } = await import('../../../../../src/knowledge-base-interaction/features/content-summarization/ai-services-gateway/aiServiceGateway');
    const { logError } = jest.requireMock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');

    const expectedError = { error: 'AI service configuration error', message: 'API key not configured.' };
    const result = await summarizeContentWithAIService(mockPayload);
    
    expect(logError).toHaveBeenCalledWith('AI Services Gateway: Gemini API key is not configured.');
    expect(result).toEqual(expectedError);
  });

  test('should handle simulated 500 error from AI service (Test Case 5.4.2)', async () => {
    const { summarizeContentWithAIService } = await import('../../../../../src/knowledge-base-interaction/features/content-summarization/ai-services-gateway/aiServiceGateway');
    const { logError } = jest.requireMock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');
    const errorPayload = { ...mockPayload, content: 'trigger an error_test_case please' };
    const expectedError = {
      error: 'AI service error',
      message: 'Failed to summarize content (simulated)',
    };

    const result = await summarizeContentWithAIService(errorPayload);

    expect(logError).toHaveBeenCalledWith('AI Services Gateway: Error calling AI service.',
      expect.objectContaining({
        status: 500,
        data: expect.objectContaining({ error: "AI service error" })
      })
    );
    expect(result).toEqual(expectedError);
  });

  test('should handle simulated 400 error (empty content) from AI service (Test Case 5.4.2)', async () => {
    const { summarizeContentWithAIService } = await import('../../../../../src/knowledge-base-interaction/features/content-summarization/ai-services-gateway/aiServiceGateway');
    const { logError } = jest.requireMock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');
    // The callAIService simulation rejects with 400 if content is empty
    const errorPayload = { ...mockPayload, content: '' };
     const expectedError = {
      error: 'Invalid request',
      message: 'Content is missing or invalid',
    };

    const result = await summarizeContentWithAIService(errorPayload);
    
    expect(logError).toHaveBeenCalledWith('AI Services Gateway: Error calling AI service.',
      expect.objectContaining({
        status: 400,
        data: expect.objectContaining({ error: "Invalid request" })
      })
    );
    expect(result).toEqual(expectedError);
  });
  
  test('should correctly pass options to the (simulated) AI service', async () => {
    const { summarizeContentWithAIService } = await import('../../../../../src/knowledge-base-interaction/features/content-summarization/ai-services-gateway/aiServiceGateway');
    const { logInfo } = jest.requireMock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');
    // This test primarily ensures that the options are part of the payload
    // The current simulation doesn't use options, but a real API would.
    const customOptions = { summaryLength: 'long', format: 'bulletpoints' };
    const payloadWithOptions = { ...mockPayload, options: customOptions };
    
    await summarizeContentWithAIService(payloadWithOptions);
    
    expect(logInfo).toHaveBeenCalledWith('AI Services Gateway: Received request to summarize content.', payloadWithOptions);
  });

});