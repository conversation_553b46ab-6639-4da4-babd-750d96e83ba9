import styles from '../styles/KnowledgeBaseView.module.css';

import React, { useState, useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
// Fix the import to use the default export correctly
import useKnowledgeBaseStore from '../state/store';
import DOMPurify from 'dompurify';

// Debounce utility function
function debounce(func, delay) {
  let timeoutId;
  return function(...args) {
    const context = this;
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(context, args), delay);
  };
}

function KnowledgeBaseView() {
  const knowledgeBase = useKnowledgeBaseStore((state) => state.knowledgeBase);
  const [displayedSearchTerm, setDisplayedSearchTerm] = useState(''); // For the input field's immediate value
  const [activeSearchTerm, setActiveSearchTerm] = useState(''); // For filtering, updated debounced
  // Debounced function to update the active search term that triggers filtering
  const debouncedSetActiveSearchTerm = useCallback(
    debounce((term) => {
      setActiveSearchTerm(term);
    }, 300), // 300ms delay
    [] // setActiveSearchTerm is stable, so empty dependency array is fine.
  );

  const handleSearchInputChange = (event) => {
    const term = event.target.value;
    setDisplayedSearchTerm(term); // Update input field immediately
    debouncedSetActiveSearchTerm(term); // Debounce the update for actual filtering
  };

  const itemsToDisplay = useMemo(() => {
    if (activeSearchTerm === '') {
      return knowledgeBase;
    }
    const lowerCaseTerm = activeSearchTerm.toLowerCase();
    const filtered = knowledgeBase.filter((note) =>
      note.title.toLowerCase().includes(lowerCaseTerm)
    );
    // Fallback logic from original component:
    // If search is active and yields no results, show original list.
    if (activeSearchTerm !== '' && filtered.length === 0) {
      return knowledgeBase;
    }
    return filtered;
  }, [knowledgeBase, activeSearchTerm]);

  return (
    <div className={styles.knowledgeBaseView}>
      <h2>Knowledge Base View</h2>
      <div className={styles.searchBar}>
        <input
          type="text"
          placeholder="Search..."
          value={displayedSearchTerm}
          onChange={handleSearchInputChange}
        />
      </div>
      <MemoizedKnowledgeList items={itemsToDisplay} />
    </div>
  );
}

const MemoizedKnowledgeList = React.memo(function KnowledgeList({ items }) {
  const Row = ({ index, style }) => {
    const note = items[index];
    return (
      // Changed from div to li, added aria-label
      <li style={style} className={styles.knowledgeBaseListItem} aria-label={`View details for ${note.title}`}>
        {/* Added class "content-list-item-title" for the XSS test query */}
        <div className="content-list-item-title" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(note.title) }}></div>
        <button>Edit</button>
        <button>Delete</button>
      </li>
    );
  };

  if (!items || items.length === 0) {
    return <p>No items to display.</p>;
  }

  return (
    <List
      outerElementType="ul" // Added to make this a <ul>
      className={styles.knowledgeBaseList}
      height={400} // Example height, adjust as needed
      itemCount={items.length}
      itemSize={50} // Example item size, adjust as needed
      width="100%" // Example width, adjust as needed
      aria-label="Knowledge base items" // Added for the list itself
    >
      {Row}
    </List>
  );
});

export default KnowledgeBaseView;