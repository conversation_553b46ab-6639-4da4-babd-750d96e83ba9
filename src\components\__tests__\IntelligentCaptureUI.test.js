import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import IntelligentCaptureUI from '../IntelligentCaptureUI'; // Assuming the component will be in ../IntelligentCaptureUI.js

describe('IntelligentCaptureUI', () => {
  const mockSuggestedTags = ['Tech', 'AI', 'React'];
  const mockSuggestedCategories = ['Software Development', 'Productivity'];
  const mockOnTagAccept = jest.fn();
  const mockOnTagReject = jest.fn();
  const mockOnCategoryAccept = jest.fn();
  const mockOnCategoryReject = jest.fn();
  const mockOnTagAdd = jest.fn();
  const mockOnCategoryAdd = jest.fn();
  const mockOnFeedback = jest.fn();
  const mockAiSummary = "This is a concise AI-generated summary of the content.";
  const mockExistingDestinations = ['Inbox', 'Project Alpha', 'Research'];
  const mockSuggestedDestination = 'Project Alpha';
  const mockOnDestinationSelect = jest.fn();
  const mockOnDestinationCreate = jest.fn();
  const mockOnNotesChange = jest.fn();
  const mockOnHighlight = jest.fn();
  const mockContentPreview = "This is the content that can be previewed and highlighted. It contains several sentences.";


  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders suggested tags', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={mockSuggestedTags}
        suggestedCategories={[]}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    mockSuggestedTags.forEach(tag => {
      expect(screen.getByText(tag)).toBeInTheDocument();
    });
  });

  test('renders suggested categories', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={[]}
        suggestedCategories={mockSuggestedCategories}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    mockSuggestedCategories.forEach(category => {
      expect(screen.getByText(category)).toBeInTheDocument();
    });
  });

  test('calls onTagAccept when a suggested tag is accepted', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={mockSuggestedTags}
        suggestedCategories={[]}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    // Assuming each tag has an accept button, e.g., "Accept Tech"
    const acceptButton = screen.getByRole('button', { name: `Accept ${mockSuggestedTags[0]}` });
    fireEvent.click(acceptButton);
    expect(mockOnTagAccept).toHaveBeenCalledWith(mockSuggestedTags[0]);
  });

  test('calls onTagReject when a suggested tag is rejected', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={mockSuggestedTags}
        suggestedCategories={[]}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    // Assuming each tag has a reject button
    const rejectButton = screen.getByRole('button', { name: `Reject ${mockSuggestedTags[0]}` });
    fireEvent.click(rejectButton);
    expect(mockOnTagReject).toHaveBeenCalledWith(mockSuggestedTags[0]);
  });

  test('calls onCategoryAccept when a suggested category is accepted', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={[]}
        suggestedCategories={mockSuggestedCategories}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    const acceptButton = screen.getByRole('button', { name: `Accept ${mockSuggestedCategories[0]}` });
    fireEvent.click(acceptButton);
    expect(mockOnCategoryAccept).toHaveBeenCalledWith(mockSuggestedCategories[0]);
  });

  test('calls onCategoryReject when a suggested category is rejected', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={[]}
        suggestedCategories={mockSuggestedCategories}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    const rejectButton = screen.getByRole('button', { name: `Reject ${mockSuggestedCategories[0]}` });
    fireEvent.click(rejectButton);
    expect(mockOnCategoryReject).toHaveBeenCalledWith(mockSuggestedCategories[0]);
  });

  test('allows adding a new tag manually', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={[]}
        suggestedCategories={[]}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    const input = screen.getByLabelText('Add new tag:');
    const addButton = screen.getByRole('button', { name: 'Add Tag' });
    
    fireEvent.change(input, { target: { value: 'ManualTag' } });
    fireEvent.click(addButton);
    
    expect(mockOnTagAdd).toHaveBeenCalledWith('ManualTag');
    expect(input).toHaveValue(''); // Input should clear after adding
  });

  test('allows adding a new category manually', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={[]}
        suggestedCategories={[]}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    const input = screen.getByLabelText('Add new category:');
    const addButton = screen.getByRole('button', { name: 'Add Category' });

    fireEvent.change(input, { target: { value: 'ManualCategory' } });
    fireEvent.click(addButton);

    expect(mockOnCategoryAdd).toHaveBeenCalledWith('ManualCategory');
    expect(input).toHaveValue(''); // Input should clear after adding
  });

  test('calls onFeedback when feedback is provided for a tag', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={mockSuggestedTags}
        suggestedCategories={[]}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback} // Ensure this prop is passed
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    // Assuming feedback buttons like "👍 Tech" and "👎 Tech"
    const thumbsUpButton = screen.getByRole('button', { name: `👍 ${mockSuggestedTags[0]}` });
    fireEvent.click(thumbsUpButton);
    expect(mockOnFeedback).toHaveBeenCalledWith({ type: 'tag', item: mockSuggestedTags[0], feedback: 'positive' });

    const thumbsDownButton = screen.getByRole('button', { name: `👎 ${mockSuggestedTags[0]}` });
    fireEvent.click(thumbsDownButton);
    expect(mockOnFeedback).toHaveBeenCalledWith({ type: 'tag', item: mockSuggestedTags[0], feedback: 'negative' });
  });

   test('calls onFeedback when feedback is provided for a category', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={[]}
        suggestedCategories={mockSuggestedCategories}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback} // Ensure this prop is passed
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    const thumbsUpButton = screen.getByRole('button', { name: `👍 ${mockSuggestedCategories[0]}` });
    fireEvent.click(thumbsUpButton);
    expect(mockOnFeedback).toHaveBeenCalledWith({ type: 'category', item: mockSuggestedCategories[0], feedback: 'positive' });

    const thumbsDownButton = screen.getByRole('button', { name: `👎 ${mockSuggestedCategories[0]}` });
    fireEvent.click(thumbsDownButton);
    expect(mockOnFeedback).toHaveBeenCalledWith({ type: 'category', item: mockSuggestedCategories[0], feedback: 'negative' });
  });

  test('renders AI-generated summary', () => {
    render(
      <IntelligentCaptureUI
        suggestedTags={[]}
        suggestedCategories={[]}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary={mockAiSummary}
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    expect(screen.getByText('AI Generated Summary')).toBeInTheDocument();
    expect(screen.getByText(mockAiSummary)).toBeInTheDocument();
  });

  test('renders "No summary available." when aiSummary is empty or not provided', () => {
    const { rerender } = render(
      <IntelligentCaptureUI
        suggestedTags={[]}
        suggestedCategories={[]}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        aiSummary=""
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
    );
    expect(screen.getByText('No summary available.')).toBeInTheDocument();

    rerender(
      <IntelligentCaptureUI
        suggestedTags={[]}
        suggestedCategories={[]}
        onTagAccept={mockOnTagAccept}
        onTagReject={mockOnTagReject}
        onCategoryAccept={mockOnCategoryAccept}
        onCategoryReject={mockOnCategoryReject}
        onTagAdd={mockOnTagAdd}
        onCategoryAdd={mockOnCategoryAdd}
        onFeedback={mockOnFeedback}
        // aiSummary prop is omitted
        existingDestinations={[]}
        suggestedDestination=""
        onDestinationSelect={mockOnDestinationSelect}
        onDestinationCreate={mockOnDestinationCreate}
        onNotesChange={mockOnNotesChange}
        personalNotes=""
        contentPreview=""
        onHighlight={mockOnHighlight}
      />
   );
   expect(screen.getByText('No summary available.')).toBeInTheDocument();
 });

 test('renders organizational destination section with suggested and existing destinations', () => {
   render(
     <IntelligentCaptureUI
       suggestedTags={[]}
       suggestedCategories={[]}
       onTagAccept={mockOnTagAccept}
       onTagReject={mockOnTagReject}
       onCategoryAccept={mockOnCategoryAccept}
       onCategoryReject={mockOnCategoryReject}
       onTagAdd={mockOnTagAdd}
       onCategoryAdd={mockOnCategoryAdd}
       onFeedback={mockOnFeedback}
       aiSummary=""
       existingDestinations={mockExistingDestinations}
       suggestedDestination={mockSuggestedDestination}
       onDestinationSelect={mockOnDestinationSelect}
       onDestinationCreate={mockOnDestinationCreate}
       onNotesChange={mockOnNotesChange}
       personalNotes=""
       contentPreview=""
       onHighlight={mockOnHighlight}
     />
   );
   expect(screen.getByText('Organizational Destination')).toBeInTheDocument();
   expect(screen.getByDisplayValue(mockSuggestedDestination)).toBeInTheDocument(); // Select should show suggested
   mockExistingDestinations.forEach(dest => {
     expect(screen.getByText(dest)).toBeInTheDocument(); // Options in select
   });
 });

 test('calls onDestinationSelect when an existing destination is selected', () => {
   render(
     <IntelligentCaptureUI
       suggestedTags={[]}
       suggestedCategories={[]}
       onTagAccept={mockOnTagAccept}
       onTagReject={mockOnTagReject}
       onCategoryAccept={mockOnCategoryAccept}
       onCategoryReject={mockOnCategoryReject}
       onTagAdd={mockOnTagAdd}
       onCategoryAdd={mockOnCategoryAdd}
       onFeedback={mockOnFeedback}
       aiSummary=""
       existingDestinations={mockExistingDestinations}
       suggestedDestination={mockSuggestedDestination}
       onDestinationSelect={mockOnDestinationSelect}
       onDestinationCreate={mockOnDestinationCreate}
       onNotesChange={mockOnNotesChange}
       personalNotes=""
       contentPreview=""
       onHighlight={mockOnHighlight}
     />
   );
   const selectElement = screen.getByLabelText('Select destination:');
   fireEvent.change(selectElement, { target: { value: mockExistingDestinations[1] } });
   expect(mockOnDestinationSelect).toHaveBeenCalledWith(mockExistingDestinations[1]);
 });

 test('calls onDestinationCreate when a new destination is created', () => {
   render(
     <IntelligentCaptureUI
       suggestedTags={[]}
       suggestedCategories={[]}
       onTagAccept={mockOnTagAccept}
       onTagReject={mockOnTagReject}
       onCategoryAccept={mockOnCategoryAccept}
       onCategoryReject={mockOnCategoryReject}
       onTagAdd={mockOnTagAdd}
       onCategoryAdd={mockOnCategoryAdd}
       onFeedback={mockOnFeedback}
       aiSummary=""
       existingDestinations={mockExistingDestinations}
       suggestedDestination={mockSuggestedDestination}
       onDestinationSelect={mockOnDestinationSelect}
       onDestinationCreate={mockOnDestinationCreate}
       onNotesChange={mockOnNotesChange}
       personalNotes=""
       contentPreview=""
       onHighlight={mockOnHighlight}
     />
   );
   const input = screen.getByLabelText('Or create new destination:');
   const addButton = screen.getByRole('button', { name: 'Create Destination' });
   const newDestName = 'New Project Zeta';

   fireEvent.change(input, { target: { value: newDestName } });
   fireEvent.click(addButton);

   expect(mockOnDestinationCreate).toHaveBeenCalledWith(newDestName);
   expect(input).toHaveValue(''); // Input should clear
 });

 test('renders personal notes section and calls onNotesChange', () => {
   const initialNotes = "Initial personal notes.";
   render(
     <IntelligentCaptureUI
       suggestedTags={[]}
       suggestedCategories={[]}
       onTagAccept={mockOnTagAccept}
       onTagReject={mockOnTagReject}
       onCategoryAccept={mockOnCategoryAccept}
       onCategoryReject={mockOnCategoryReject}
       onTagAdd={mockOnTagAdd}
       onCategoryAdd={mockOnCategoryAdd}
       onFeedback={mockOnFeedback}
       aiSummary=""
       existingDestinations={[]}
       suggestedDestination=""
       onDestinationSelect={mockOnDestinationSelect}
       onDestinationCreate={mockOnDestinationCreate}
       personalNotes={initialNotes}
       onNotesChange={mockOnNotesChange}
       contentPreview=""
       onHighlight={mockOnHighlight}
     />
   );
   expect(screen.getByText('Personal Notes')).toBeInTheDocument();
   const notesTextarea = screen.getByLabelText('Add your notes:');
   expect(notesTextarea).toHaveValue(initialNotes);

   const newNotes = "Updated personal notes.";
   fireEvent.change(notesTextarea, { target: { value: newNotes } });
   expect(mockOnNotesChange).toHaveBeenCalledWith(newNotes);
 });

 test('renders content preview and allows highlighting text', () => {
   render(
     <IntelligentCaptureUI
       suggestedTags={[]}
       suggestedCategories={[]}
       onTagAccept={mockOnTagAccept}
       onTagReject={mockOnTagReject}
       onCategoryAccept={mockOnCategoryAccept}
       onCategoryReject={mockOnCategoryReject}
       onTagAdd={mockOnTagAdd}
       onCategoryAdd={mockOnCategoryAdd}
       onFeedback={mockOnFeedback}
       aiSummary=""
       existingDestinations={[]}
       suggestedDestination=""
       onDestinationSelect={mockOnDestinationSelect}
       onDestinationCreate={mockOnDestinationCreate}
       personalNotes=""
       onNotesChange={mockOnNotesChange}
       contentPreview={mockContentPreview}
       onHighlight={mockOnHighlight}
     />
   );

   expect(screen.getByText('Content Preview')).toBeInTheDocument();
   const previewArea = screen.getByTestId('content-preview-area');
   expect(previewArea).toHaveValue(mockContentPreview);

   // Simulate text selection - this is tricky with JSDOM
   // For this test, we'll simulate the outcome of a selection event
   // by directly calling a prop or checking a state if that's how it's implemented.
   // Actual browser selection events are hard to mock accurately.
   
   // Let's assume there's a button to trigger highlighting of selected text
   // or the component handles onMouseUp/onSelect on the preview area.
   // For simplicity, we'll add a "Highlight Selection" button for the test.
   
   // Mock window.getSelection for this test
   const mockGetSelection = jest.fn(() => ({
     toString: () => "content that can be previewed",
     anchorOffset: 12,
     focusOffset: 39,
   }));
   window.getSelection = mockGetSelection;

   const highlightButton = screen.getByRole('button', { name: 'Highlight Selected Text' });
   fireEvent.click(highlightButton);
   
   expect(mockGetSelection).toHaveBeenCalled();
   expect(mockOnHighlight).toHaveBeenCalledWith({
     text: "content that can be previewed",
     startOffset: 12,
     endOffset: 39,
   });

   // Clean up mock
   delete window.getSelection;
 });
});