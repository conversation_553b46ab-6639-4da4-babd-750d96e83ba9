// src/knowledge-base-interaction/search-service/tests/KeywordSearch.test.js

const KeywordSearch = require('../algorithms/KeywordSearch');

// Mock for KBALService
const mockKbalService = {
    getContent: jest.fn(async (params) => {
        // AI-Verifiable: Mock K<PERSON>L simulates data retrieval for keyword search
        if (params && params.type === 'keyword' && params.keywords && params.keywords.includes('test')) {
            return [
                { id: 'doc1', text: 'This document contains the test keyword.', title: 'Test Doc 1' },
                { id: 'doc2', text: 'Another test document here.', title: 'Test Doc 2' },
            ];
        }
        if (params && params.type === 'keyword' && params.keywords && params.keywords.includes('unique')) {
            return [{ id: 'doc3', text: 'A unique keyword document.', title: 'Unique Doc' }];
        }
        return [];
    }),
    // getAllContent: jest.fn() // If used by the actual implementation
};

describe('KeywordSearch', () => {
    let keywordSearch;

    beforeEach(() => {
        // AI-Verifiable: Test setup initializes KeywordSearch
        jest.clearAllMocks();
        keywordSearch = new KeywordSearch();
    });

    // AI-Verifiable: Test case for constructor
    test('should be instantiated correctly', () => {
        expect(keywordSearch).toBeInstanceOf(KeywordSearch);
    });

    // AI-Verifiable: Test case for successful keyword search
    describe('performSearch method', () => {
        test('should return relevant documents for a given keyword query', async () => {
            const query = 'test';
            // AI-Verifiable: Call performSearch
            const results = await keywordSearch.performSearch(query, mockKbalService);

            // AI-Verifiable: Check KBAL interaction
            expect(mockKbalService.getContent).toHaveBeenCalledWith({ type: 'keyword', keywords: ['test'] });
            
            // AI-Verifiable: Check results structure and content
            expect(results).toBeInstanceOf(Array);
            expect(results.length).toBe(2);
            expect(results).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    // id: expect.stringMatching(/^keyword-doc[1-2]$|^keyword-[a-z0-9]+$/),
                    id: 'doc1', // Align with mockKbalService output
                    title: expect.stringContaining('Test Doc'),
                    snippet: expect.stringContaining('test keyword'),
                    source: 'kbal-keyword',
                    score: expect.any(Number),
                    type: 'keyword'
                }),
                expect.objectContaining({
                    // id: expect.stringMatching(/^keyword-doc[1-2]$|^keyword-[a-z0-9]+$/),
                    id: 'doc2', // Align with mockKbalService output
                    title: 'Test Doc 2',
                    snippet: expect.stringContaining('Another test document'),
                    source: 'kbal-keyword',
                    score: expect.any(Number),
                    type: 'keyword'
                })
            ]));
            results.forEach(result => {
                expect(result.score).toBeGreaterThanOrEqual(0.2);
                expect(result.score).toBeLessThanOrEqual(0.7);
            });
        });

        test('should return an empty array if no documents match the query', async () => {
            const query = 'nonexistentkeyword';
            // AI-Verifiable: Call performSearch with non-matching query
            const results = await keywordSearch.performSearch(query, mockKbalService);
            
            expect(mockKbalService.getContent).toHaveBeenCalledWith({ type: 'keyword', keywords: ['nonexistentkeyword'] });
            expect(results).toEqual([]);
        });

        // AI-Verifiable: Test case for input validation (query)
        test('should throw an error if the query is invalid', async () => {
            await expect(keywordSearch.performSearch(null, mockKbalService))
                .rejects.toThrow('Keyword search query must be a non-empty string.');
            await expect(keywordSearch.performSearch('', mockKbalService))
                .rejects.toThrow('Keyword search query must be a non-empty string.');
             await expect(keywordSearch.performSearch(123, mockKbalService))
                .rejects.toThrow('Keyword search query must be a non-empty string.');
        });

        // AI-Verifiable: Test case for input validation (kbalService)
        test('should throw an error if kbalService is invalid or missing getContent', async () => {
            await expect(keywordSearch.performSearch('test', null))
                .rejects.toThrow('Valid KBAL service with getContent method is required.');
            await expect(keywordSearch.performSearch('test', {}))
                .rejects.toThrow('Valid KBAL service with getContent method is required.');
        });

        // AI-Verifiable: Test case for error propagation from KBAL
        test('should propagate errors from kbalService.getContent', async () => {
            mockKbalService.getContent.mockRejectedValueOnce(new Error('KBAL service error'));
            await expect(keywordSearch.performSearch('test', mockKbalService))
                .rejects.toThrow('Keyword search failed.');
        });

        // AI-Verifiable: Test case for result structure
        test('should ensure all results have id, title, snippet, source, score, and type', async () => {
            const query = 'unique';
            const results = await keywordSearch.performSearch(query, mockKbalService);
            expect(results.length).toBe(1);
            const result = results[0];
            expect(result).toHaveProperty('id');
            expect(typeof result.id).toBe('string');
            expect(result).toHaveProperty('title', 'Unique Doc');
            expect(result).toHaveProperty('snippet');
            expect(typeof result.snippet).toBe('string');
            expect(result).toHaveProperty('source', 'kbal-keyword');
            expect(result).toHaveProperty('score');
            expect(typeof result.score).toBe('number');
            expect(result).toHaveProperty('type', 'keyword');
        });
    });
});