// AI-VERIFIABLE: Placeholder for EntityExtractor logic.
// This class will extract entities (keywords, named entities) from the query.

class EntityExtractor {
    constructor() {
        // Initialization logic, e.g., loading NER models, gazetteers.
        console.log('[EntityExtractor] Initialized.');
    }

    /**
     * Extracts entities from a parsed query, potentially guided by the recognized intent.
     * @param {object} parsedQuery - The structured output from QueryParser.
     * @param {object} intent - The recognized intent object from IntentRecognizer.
     * @returns {Promise<object>} An object containing extracted entities,
     *                                 e.g., { keywords: ['AI', 'TDD'], namedEntities: [{text: 'London', type: 'LOC'}] }.
     */
    async extractEntities(parsedQuery, intent) {
        // AI-VERIFIABLE: extractEntities method placeholder
        if (!parsedQuery || typeof parsedQuery !== 'object' || !parsedQuery.keywords || !parsedQuery.tokens || !parsedQuery.original) {
            throw new Error('Parsed query object with original, tokens, and keywords is required for entity extraction.');
        }
        if (!intent || typeof intent !== 'object' || !intent.type) {
            throw new Error('Intent object with type is required for entity extraction.');
        }

        console.log('[EntityExtractor] Extracting entities for:', parsedQuery, 'with intent:', intent.type);

        const { tokens, keywords: queryKeywords, original: originalQuery } = parsedQuery;

        // Use keywords directly from QueryParser as they are already processed (e.g., stopword filtered)
        const finalKeywords = [...queryKeywords];

        const namedEntities = [];
        const dates = [];
        const authors = [];
        const relationships = [];

        // Predefined lists for known entities (case-insensitive matching on tokens)
        const knownLocations = ['london', 'paris', 'berlin'];
        const knownOrganizations = ['google', 'microsoft', 'openai'];

        tokens.forEach((token, index) => {
            const lowerToken = token.toLowerCase(); // Use this for all matching logic

            // Date check should be independent and can happen first
            if (/^\d{4}$/.test(token) || /^\d{4}-\d{2}-\d{2}$/.test(token) || /^\d{2}\/\d{2}\/\d{4}$/.test(token)) {
                // Check if this date has already been added to avoid duplicate NEs from this rule
                if (!namedEntities.some(ne => ne.text === token && ne.type === 'DATE_NE')) {
                    dates.push({ text: token, type: 'DATE', confidence: 0.85, source: 'regex_date' });
                    namedEntities.push({ text: token, type: 'DATE_NE', confidence: 0.85, source: 'regex_date' });
                }
                if (index > 0 && tokens[index - 1].toLowerCase() === 'after') {
                    relationships.push({ type: 'publishedAfter', date: token });
                }
            }
            // Entity checks: Known Locations, Known Orgs, then "by author"
            // Ensure a token isn't classified multiple ways by these NE rules if possible,
            // though uniqueNamedEntities at the end handles duplicates.
            else if (knownLocations.includes(lowerToken)) {
                namedEntities.push({ text: token, type: 'LOCATION', confidence: 0.8, source: 'known_list_location' });
            }
            else if (knownOrganizations.includes(lowerToken)) {
                namedEntities.push({ text: token, type: 'ORGANIZATION', confidence: 0.8, source: 'known_list_organization' });
            }
            // "by author" pattern: ensure the current token is not "by" itself.
            else if (lowerToken !== 'by' && index > 0 && tokens[index - 1].toLowerCase() === 'by') {
                namedEntities.push({ text: token, type: 'PERSON', confidence: 0.65, source: 'pattern_author_by' });
                authors.push(token); // Store original case author name
                if (index > 1) {
                    const entity1Candidate = tokens[index - 2];
                    if (queryKeywords.includes(entity1Candidate.toLowerCase())) { // Match against lowercase keywords
                        relationships.push({ type: 'authoredBy', entity1: entity1Candidate, entity2: token });
                    }
                }
            }
            // No generic capitalization-based NE for now to simplify and avoid conflicts with QueryParser's lowercasing
        });

        // Relationship hints based on prepositions/keywords (e.g., "X on Y")
        // This loop is separate to allow all NEs to be identified first.
        tokens.forEach((token, index) => {
            const lowerToken = token.toLowerCase();
            if (lowerToken === 'on' && index > 0 && index < tokens.length - 1) {
                const entity1Candidate = tokens[index - 1];
                const topicCandidate = tokens[index + 1];
                if (queryKeywords.includes(entity1Candidate.toLowerCase()) && queryKeywords.includes(topicCandidate.toLowerCase())) {
                    relationships.push({ type: 'topicOf', entity1: entity1Candidate, topic: topicCandidate });
                }
            }
        });


        // Remove duplicates from namedEntities based on text and type (simple dedup)
        const uniqueNamedEntities = Array.from(new Map(namedEntities.map(ne => [`${ne.text}-${ne.type}`, ne])).values());

        const extractedResult = {
            keywords: finalKeywords,
            namedEntities: uniqueNamedEntities,
            dates: Array.from(new Set(dates.map(d => d.text))), // Store unique date strings
            authors: Array.from(new Set(authors)), // Store unique author strings
            relationships: relationships,
            // queryStructure can be enhanced based on identified relationships
            queryStructure: `Intent: '${intent.type}'. Entities processed. Found ${uniqueNamedEntities.length} NEs, ${dates.length} dates, ${authors.length} authors.`,
        };

        console.log('[EntityExtractor] Extracted entities:', extractedResult);
        return extractedResult;
    }
}

export default EntityExtractor;
// AI-VERIFIABLE: End of EntityExtractor.js