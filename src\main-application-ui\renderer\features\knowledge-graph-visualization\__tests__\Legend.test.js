import React from 'react';
import { render, screen } from '@testing-library/react';
import Legend from '../components/Legend';

const mockVisualEncodings = {
  nodeTypes: {
    typeA: { label: 'Type A Label', color: 'blue', shape: 'circle' },
    typeB: { label: 'Type B Label', color: 'green', shape: 'square' },
    typeC: { label: 'Type C Without Shape', color: 'red' }, // Test case for missing shape
  },
  edgeTypes: {
    relX: { label: 'Relation X Label', color: 'gray', style: 'solid' },
    relY: { label: 'Relation Y Label', color: 'purple', style: 'dashed' },
    relZ: { label: 'Relation Z Without Style', color: 'orange'}, // Test case for missing style
  },
};

const emptyVisualEncodings = {
  nodeTypes: {},
  edgeTypes: {},
};

describe('Legend Component', () => {
  test('TC_KGV_LEG_001: should render without crashing with valid encodings', () => {
    render(<Legend visualEncodings={mockVisualEncodings} />);
    expect(screen.getByText(/Legend/i)).toBeInTheDocument();
    expect(screen.getByText(/Node Types/i)).toBeInTheDocument();
    expect(screen.getByText(/Edge Types/i)).toBeInTheDocument();
  });

  test('TC_KGV_LEG_002: should display legend items for node types correctly (corresponds to TC_KGV_GR_007)', () => {
    render(<Legend visualEncodings={mockVisualEncodings} />);
    
    const typeA = mockVisualEncodings.nodeTypes.typeA;
    expect(screen.getByText(typeA.label)).toBeInTheDocument();
    const typeAElement = screen.getByText(typeA.label).closest('li'); // Assuming list items for legend entries
    expect(typeAElement).toHaveStyle(`--legend-color: ${typeA.color}`); // Check for CSS variable or direct style
    // Add check for shape if rendered (e.g., a specific class or SVG)

    const typeB = mockVisualEncodings.nodeTypes.typeB;
    expect(screen.getByText(typeB.label)).toBeInTheDocument();
    const typeBElement = screen.getByText(typeB.label).closest('li');
    expect(typeBElement).toHaveStyle(`--legend-color: ${typeB.color}`);

    const typeC = mockVisualEncodings.nodeTypes.typeC;
    expect(screen.getByText(typeC.label)).toBeInTheDocument();
    const typeCElement = screen.getByText(typeC.label).closest('li');
    expect(typeCElement).toHaveStyle(`--legend-color: ${typeC.color}`);

  });

  test('TC_KGV_LEG_003: should display legend items for edge types correctly (corresponds to TC_KGV_GR_008)', () => {
    render(<Legend visualEncodings={mockVisualEncodings} />);

    const relX = mockVisualEncodings.edgeTypes.relX;
    expect(screen.getByText(relX.label)).toBeInTheDocument();
    const relXElement = screen.getByText(relX.label).closest('li');
    expect(relXElement).toHaveStyle(`--legend-color: ${relX.color}`);
    // Add check for line style if rendered (e.g., a specific class or SVG line attribute)

    const relY = mockVisualEncodings.edgeTypes.relY;
    expect(screen.getByText(relY.label)).toBeInTheDocument();
    const relYElement = screen.getByText(relY.label).closest('li');
    expect(relYElement).toHaveStyle(`--legend-color: ${relY.color}`);

    const relZ = mockVisualEncodings.edgeTypes.relZ;
    expect(screen.getByText(relZ.label)).toBeInTheDocument();
    const relZElement = screen.getByText(relZ.label).closest('li');
    expect(relZElement).toHaveStyle(`--legend-color: ${relZ.color}`);
  });

  test('TC_KGV_LEG_004: should render gracefully with empty visual encodings', () => {
    render(<Legend visualEncodings={emptyVisualEncodings} />);
    expect(screen.getByText(/Legend/i)).toBeInTheDocument();
    expect(screen.getByText(/Node Types/i)).toBeInTheDocument();
    expect(screen.getByText(/Edge Types/i)).toBeInTheDocument();
    // Check that no specific legend items are rendered beyond the titles
    expect(screen.queryByText(mockVisualEncodings.nodeTypes.typeA.label)).not.toBeInTheDocument();
    expect(screen.queryByText(mockVisualEncodings.edgeTypes.relX.label)).not.toBeInTheDocument();
  });

  test('TC_KGV_LEG_005: should handle visual encodings with missing optional properties (e.g., shape, style)', () => {
    render(<Legend visualEncodings={mockVisualEncodings} />);
    // Check for Type C (missing shape)
    const typeC = mockVisualEncodings.nodeTypes.typeC;
    expect(screen.getByText(typeC.label)).toBeInTheDocument();
    const typeCElement = screen.getByText(typeC.label).closest('li');
    expect(typeCElement).toHaveStyle(`--legend-color: ${typeC.color}`); // Color should still apply

    // Check for Relation Z (missing style)
    const relZ = mockVisualEncodings.edgeTypes.relZ;
    expect(screen.getByText(relZ.label)).toBeInTheDocument();
    const relZElement = screen.getByText(relZ.label).closest('li');
    expect(relZElement).toHaveStyle(`--legend-color: ${relZ.color}`); // Color should still apply
  });

  // Add more tests for:
  // - Interactivity (e.g., clicking a legend item to highlight/filter in the graph, if applicable)
  // - Handling a large number of legend items (e.g., scrollability)
  // - Accessibility of the legend items (e.g., ARIA attributes for color/shape indicators)
});