/**
 * @class ContentPreprocessor
 * @description Preprocesses raw text content for embedding generation.
 */
class ContentPreprocessor {
    /**
     * Preprocesses the given text.
     * @param {string} rawText - The raw text content to preprocess.
     * @returns {string} The preprocessed text.
     */
    preprocess(rawText) {
        if (typeof rawText !== 'string') {
            throw new TypeError('Input must be a string.');
        }
        // Remove excessive whitespace (multiple spaces, tabs, newlines) and trim
        let cleanedText = rawText.replace(/\s+/g, ' ').trim();
        // Potential future enhancements:
        // - HTML stripping (if not handled by KBAL or if raw HTML is expected)
        // - Lowercasing (though some embedding models might be case-sensitive)
        // - Punctuation handling (depends on the embedding model's tokenizer)
        // - Stop word removal (generally not recommended for sentence transformers)
        return cleanedText;
    }
}

export default ContentPreprocessor;