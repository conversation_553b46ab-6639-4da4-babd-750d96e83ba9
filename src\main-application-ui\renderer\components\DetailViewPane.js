import React from 'react';
import PropTypes from 'prop-types';
// Removed DOMPurify import from here as Content<PERSON><PERSON><PERSON> handles it.
import ContentRenderer from './detail-view-pane/ContentRenderer';
import MetadataDisplay from './detail-view-pane/MetadataDisplay';
import ActionBar from './detail-view-pane/ActionBar';

const DetailViewPane = ({ 
  item = null, 
  onInitiateAIQA, 
  onInitiateContentTransformation, 
  onViewConceptualLinks,
  onEditMetadata, // Optional
  // Props to control ActionBar button visibility, if needed at this level
  isAIQAEnabled,
  isContentTransformationEnabled,
  isConceptualLinksEnabled,
  isEditMetadataEnabled,
}) => {
  if (!item) {
    return <div data-testid="detail-view-pane"><p>No item selected.</p></div>;
  }

  const metadata = {
    sourceURL: item.sourceURL,
    captureDate: item.captureDate,
    tags: item.tags,
    categories: item.categories,
  };

  // Determine contentType for ContentRenderer
  // Prioritize: 1. Explicit item.contentType, 2. sourceURL, 3. content sniffing, 4. default 'text'
  let determinedContentType = 'text'; // Default

  if (item.contentType) {
    determinedContentType = item.contentType;
  } else if (item.sourceURL) {
    const extension = item.sourceURL.split('.').pop().toLowerCase();
    if (extension === 'html' || extension === 'htm') {
      determinedContentType = 'html';
    } else if (extension === 'md' || extension === 'markdown') {
      determinedContentType = 'markdown';
    } else if (extension === 'txt') {
      determinedContentType = 'text';
    }
    // Add other extension checks if needed, otherwise defaults to 'text'
  } else if (item.content) {
    const trimmedContent = item.content.trimStart();
    // Basic HTML sniffing (look for common tags or doctype)
    if (trimmedContent.startsWith('<html') || trimmedContent.startsWith('<!DOCTYPE') || /<[a-z][\s\S]*>/i.test(trimmedContent)) {
      determinedContentType = 'html';
    }
    // Basic Markdown sniffing (e.g., starts with #, ##, *, -, etc.)
    // More robust regex might be needed for complex cases
    else if (/^#+\s/.test(trimmedContent) || // Headers
             /^(\*|-|\+)\s/.test(trimmedContent) || // Unordered lists
             /^\d+\.\s/.test(trimmedContent) || // Ordered lists
             /^>\s/.test(trimmedContent) || // Blockquotes
             /`{1,3}/.test(trimmedContent) || // Code blocks/inline
             /\[.*\]\(.*\)/.test(trimmedContent) // Links
    ) {
      determinedContentType = 'markdown';
    }
    // If no specific format sniffed, it remains 'text' (or could be further refined)
  }
  const contentType = determinedContentType;


  return (
    <div data-testid="detail-view-pane">
      <h2>{item.title}</h2>
      <ContentRenderer content={item.content} contentType={contentType} />
      <MetadataDisplay metadata={metadata} />
      <ActionBar
        // Pass item.id if needed by handlers, though handlers are passed directly
        // itemId={item.id} 
        onInitiateAIQA={() => onInitiateAIQA(item.id)}
        onInitiateContentTransformation={() => onInitiateContentTransformation(item.id)}
        onViewConceptualLinks={() => onViewConceptualLinks(item.id)}
        onEditMetadata={onEditMetadata ? () => onEditMetadata(item.id) : undefined}
        isAIQAEnabled={isAIQAEnabled}
        isContentTransformationEnabled={isContentTransformationEnabled}
        isConceptualLinksEnabled={isConceptualLinksEnabled}
        isEditMetadataEnabled={isEditMetadataEnabled && !!onEditMetadata}
      />
    </div>
  );
};

DetailViewPane.propTypes = {
  item: PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    content: PropTypes.string.isRequired,
    contentType: PropTypes.oneOf(['html', 'markdown', 'text', 'explicit-type']), // Optional: explicit content type, added explicit-type for test
    sourceURL: PropTypes.string,
    captureDate: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
    tags: PropTypes.arrayOf(PropTypes.string),
    categories: PropTypes.arrayOf(PropTypes.string),
  }),
  onInitiateAIQA: PropTypes.func.isRequired,
  onInitiateContentTransformation: PropTypes.func.isRequired,
  onViewConceptualLinks: PropTypes.func.isRequired,
  onEditMetadata: PropTypes.func,
  isAIQAEnabled: PropTypes.bool,
  isContentTransformationEnabled: PropTypes.bool,
  isConceptualLinksEnabled: PropTypes.bool,
  isEditMetadataEnabled: PropTypes.bool,
};

DetailViewPane.defaultProps = {
  item: null,
  onEditMetadata: null,
  isAIQAEnabled: true,
  isContentTransformationEnabled: true,
  isConceptualLinksEnabled: true,
  isEditMetadataEnabled: false, // Edit metadata is optional and off by default
};

export default DetailViewPane;