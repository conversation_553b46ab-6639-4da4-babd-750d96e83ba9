{"fileNames": ["../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2021.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2022.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2023.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2024.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.esnext.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.date.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2021.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2022.array.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2022.error.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2022.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2022.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2022.regexp.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2023.array.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2023.collection.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2023.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2024.arraybuffer.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2024.collection.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2024.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2024.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2024.regexp.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2024.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2024.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.esnext.array.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.esnext.collection.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.esnext.disposable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.esnext.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.esnext.decorators.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.esnext.iterator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.esnext.float16.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./src/chrome.d.ts", "../../node_modules/.pnpm/@types+react@19.1.4/node_modules/@types/react/global.d.ts", "../../node_modules/.pnpm/csstype@3.1.3/node_modules/csstype/index.d.ts", "../../node_modules/.pnpm/@types+react@19.1.4/node_modules/@types/react/index.d.ts", "../../node_modules/.pnpm/@types+react@19.1.4/node_modules/@types/react/jsx-runtime.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/compatibility/disposable.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/compatibility/indexable.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/compatibility/iterators.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/compatibility/index.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/globals.typedarray.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/buffer.buffer.d.ts", "../../node_modules/.pnpm/buffer@5.7.1/node_modules/buffer/index.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/header.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/readable.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/file.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/fetch.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/formdata.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/connector.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/client.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/errors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/dispatcher.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/global-dispatcher.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/global-origin.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/pool-stats.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/handlers.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/balanced-pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-interceptor.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-client.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-errors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/proxy-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/env-http-proxy-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/retry-handler.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/retry-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/api.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/interceptors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/util.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/cookies.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/patch.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/websocket.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/eventsource.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/filereader.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/diagnostics-channel.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/content-type.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/cache.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/index.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/globals.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/assert.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/assert/strict.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/async_hooks.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/buffer.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/child_process.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/cluster.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/console.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/constants.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/crypto.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/dgram.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/dns.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/dns/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/domain.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/dom-events.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/events.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/fs.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/fs/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/http.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/http2.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/https.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/inspector.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/module.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/net.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/os.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/path.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/process.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/punycode.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/querystring.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/readline.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/readline/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/repl.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/sea.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/sqlite.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/stream.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/stream/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/stream/web.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/string_decoder.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/test.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/timers.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/timers/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/tls.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/trace_events.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/tty.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/url.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/util.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/v8.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/vm.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/wasi.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/worker_threads.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/zlib.d.ts", "../../node_modules/.pnpm/@types+node@22.15.19/node_modules/@types/node/index.d.ts", "../../node_modules/.pnpm/@types+yargs-parser@21.0.3/node_modules/@types/yargs-parser/index.d.ts", "../../node_modules/.pnpm/@types+yargs@17.0.33/node_modules/@types/yargs/index.d.ts", "../../node_modules/.pnpm/@types+istanbul-lib-coverage@2.0.6/node_modules/@types/istanbul-lib-coverage/index.d.ts", "../../node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/index.d.ts", "../../node_modules/.pnpm/@types+istanbul-lib-report@3.0.3/node_modules/@types/istanbul-lib-report/index.d.ts", "../../node_modules/.pnpm/@types+istanbul-reports@3.0.4/node_modules/@types/istanbul-reports/index.d.ts", "../../node_modules/.pnpm/@sinclair+typebox@0.27.8/node_modules/@sinclair/typebox/typebox.d.ts", "../../node_modules/.pnpm/@jest+schemas@29.6.3/node_modules/@jest/schemas/build/index.d.ts", "../../node_modules/.pnpm/@jest+types@29.6.3/node_modules/@jest/types/build/index.d.ts", "../../node_modules/.pnpm/jest-mock@29.7.0/node_modules/jest-mock/build/index.d.ts", "../../node_modules/.pnpm/@types+stack-utils@2.0.3/node_modules/@types/stack-utils/index.d.ts", "../../node_modules/.pnpm/jest-message-util@29.7.0/node_modules/jest-message-util/build/index.d.ts", "../../node_modules/.pnpm/@jest+fake-timers@29.7.0/node_modules/@jest/fake-timers/build/index.d.ts", "../../node_modules/.pnpm/@jest+environment@29.7.0/node_modules/@jest/environment/build/index.d.ts", "../../node_modules/.pnpm/@jest+expect-utils@29.7.0/node_modules/@jest/expect-utils/build/index.d.ts", "../../node_modules/.pnpm/pretty-format@29.7.0/node_modules/pretty-format/build/index.d.ts", "../../node_modules/.pnpm/jest-diff@29.7.0/node_modules/jest-diff/build/index.d.ts", "../../node_modules/.pnpm/jest-matcher-utils@29.7.0/node_modules/jest-matcher-utils/build/index.d.ts", "../../node_modules/.pnpm/expect@29.7.0/node_modules/expect/build/index.d.ts", "../../node_modules/.pnpm/jest-snapshot@29.7.0/node_modules/jest-snapshot/build/index.d.ts", "../../node_modules/.pnpm/@jest+expect@29.7.0/node_modules/@jest/expect/build/index.d.ts", "../../node_modules/.pnpm/@jest+globals@29.7.0/node_modules/@jest/globals/build/index.d.ts", "../../packages/knowledge-base-service/dist/types.d.ts", "./src/__mocks__/@pkm-ai/knowledge-base-service-old.ts", "../../node_modules/.pnpm/@types+har-format@1.2.16/node_modules/@types/har-format/index.d.ts", "../../node_modules/.pnpm/@types+chrome@0.0.323/node_modules/@types/chrome/har-format/index.d.ts", "../../node_modules/.pnpm/@types+chrome@0.0.323/node_modules/@types/chrome/chrome-cast/index.d.ts", "../../node_modules/@types/filewriter/index.d.ts", "../../node_modules/@types/filesystem/index.d.ts", "../../node_modules/.pnpm/@types+chrome@0.0.323/node_modules/@types/chrome/index.d.ts", "../../packages/knowledge-base-service/dist/knowledgebaseservice.d.ts", "../../packages/knowledge-base-service/dist/index.d.ts", "./src/background/index.ts", "../../node_modules/.pnpm/@types+jest@29.5.14/node_modules/@types/jest/index.d.ts", "./src/background/__tests__/index.test.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/events.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/experiments.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/manifest.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/extensiontypes.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/runtime.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/windows.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/tabs.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/action.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/activitylog.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/alarms.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/bookmarks.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/browseraction.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/types.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/browsersettings_colormanagement.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/browsersettings.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/browsingdata.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/captiveportal.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/clipboard.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/commands.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/contentscripts.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/extension.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/menus.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/contextmenus.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/contextualidentities.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/cookies.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/declarativecontent.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/declarativenetrequest.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/devtools_inspectedwindow.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/devtools_network.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/devtools_panels.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/devtools.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/dns.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/downloads.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/find.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/geckoprofiler.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/history.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/i18n.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/identity.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/idle.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/management.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/networkstatus.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/normandyaddonstudy.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/notifications.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/omnibox.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/pageaction.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/permissions.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/pkcs11.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/privacy_network.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/privacy_services.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/privacy_websites.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/privacy.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/webrequest.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/proxy.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/scripting.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/search.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/sessions.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/sidebaraction.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/storage.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/tabgroups.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/theme.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/topsites.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/trial_ml.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/trial.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/userscripts.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/webnavigation.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/index.d.ts", "./src/types/messaging.ts", "./src/capture/captureservice.ts", "./src/capture/index.ts", "./src/config-ui/index.ts", "./src/content/index.ts", "./src/knowledge-base-ui/index.ts", "./src/mocks/knowledgebaseservice.ts", "./src/organization/categoryinput.tsx", "./src/organization/notesinput.tsx", "./src/organization/suggestiondisplay.tsx", "./src/organization/summarydisplay.tsx", "./src/organization/taginput.tsx", "./src/organization/index.ts", "./src/organization/mockaiservice.ts", "./src/organization/suggestionservice.ts", "./src/organization/__mocks__/suggestionservice.ts", "../../node_modules/.pnpm/@types+react-window@1.8.8/node_modules/@types/react-window/index.d.ts", "./src/ui/options/contentlist.tsx", "./src/ui/options/detailviewpane.tsx", "./src/ui/options/knowledgebaseview.tsx", "./src/ui/options/settingspage.tsx", "../../node_modules/.pnpm/@types+react-dom@19.1.5_@types+react@19.1.4/node_modules/@types/react-dom/client.d.ts", "./src/ui/options/index.tsx", "../../node_modules/.pnpm/@types+aria-query@5.0.4/node_modules/@types/aria-query/index.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/matches.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/wait-for.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/query-helpers.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/queries.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/get-queries-for-element.d.ts", "../../node_modules/.pnpm/pretty-format@27.5.1/node_modules/pretty-format/build/types.d.ts", "../../node_modules/.pnpm/pretty-format@27.5.1/node_modules/pretty-format/build/index.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/screen.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/wait-for-element-to-be-removed.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/get-node-text.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/events.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/pretty-dom.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/role-helpers.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/config.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/suggestions.d.ts", "../../node_modules/.pnpm/@testing-library+dom@10.4.0/node_modules/@testing-library/dom/types/index.d.ts", "../../node_modules/.pnpm/@types+react-dom@19.1.5_@types+react@19.1.4/node_modules/@types/react-dom/test-utils/index.d.ts", "../../node_modules/.pnpm/@testing-library+react@16.3_caf59d6a4dbb299184675507e0e61dda/node_modules/@testing-library/react/types/index.d.ts", "../../node_modules/.pnpm/@testing-library+jest-dom@6.6.3/node_modules/@testing-library/jest-dom/types/matchers.d.ts", "../../node_modules/.pnpm/@testing-library+jest-dom@6.6.3/node_modules/@testing-library/jest-dom/types/jest.d.ts", "../../node_modules/.pnpm/@testing-library+jest-dom@6.6.3/node_modules/@testing-library/jest-dom/types/index.d.ts", "./src/ui/options/__tests__/contentlist.test.tsx", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/eventmap.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/types.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/dispatchevent.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/focus.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/input.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/click/isclickableinput.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/datatransfer/blob.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/datatransfer/datatransfer.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/datatransfer/filelist.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/datatransfer/clipboard.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/edit/timevalue.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/edit/iscontenteditable.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/edit/iseditable.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/edit/maxlength.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/edit/setfiles.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/focus/cursor.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/focus/getactiveelement.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/focus/gettabdestination.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/focus/isfocusable.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/focus/selection.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/focus/selector.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/keydef/readnextdescriptor.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/cloneevent.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/findclosest.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/getdocumentfromnode.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/gettreediff.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/getwindow.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/isdescendantorself.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/iselementtype.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/isvisible.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/isdisabled.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/level.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/misc/wait.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/pointer/csspointerevents.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utils/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/document/ui.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/document/getvalueortextcontent.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/document/copyselection.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/document/trackvalue.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/document/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/getinputrange.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/modifyselection.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/moveselection.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/setselectionpermouse.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/modifyselectionpermouse.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/selectall.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/setselectionrange.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/setselection.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/updateselectiononfocus.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/selection/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/event/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/system/pointer/buttons.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/system/pointer/shared.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/system/pointer/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/system/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/system/keyboard.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/options.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/convenience/click.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/convenience/hover.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/convenience/tab.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/convenience/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/keyboard/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/clipboard/copy.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/clipboard/cut.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/clipboard/paste.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/clipboard/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/pointer/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utility/clear.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utility/selectoptions.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utility/type.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utility/upload.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/utility/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/setup/api.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/setup/directapi.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/setup/setup.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/setup/index.d.ts", "../../node_modules/.pnpm/@testing-library+user-event_cb78dbca79d2daaf2e0b841406a0b2aa/node_modules/@testing-library/user-event/dist/types/index.d.ts", "./src/ui/options/__tests__/detailviewpane.test.tsx", "./src/ui/options/__tests__/knowledgebaseview.test.tsx", "./src/ui/options/__tests__/settingspage.test.tsx", "./src/ui/popup/index.tsx", "./src/ui/popup/__tests__/popup.test.tsx", "./__mocks__/@pkm-ai/knowledge-base-service.ts", "../../node_modules/.pnpm/vite@6.3.5_@types+node@22.15.19/node_modules/vite/types/hmrpayload.d.ts", "../../node_modules/.pnpm/vite@6.3.5_@types+node@22.15.19/node_modules/vite/types/customevent.d.ts", "../../node_modules/.pnpm/vite@6.3.5_@types+node@22.15.19/node_modules/vite/types/hot.d.ts", "../../node_modules/.pnpm/vite@6.3.5_@types+node@22.15.19/node_modules/vite/types/importglob.d.ts", "../../node_modules/.pnpm/vite@6.3.5_@types+node@22.15.19/node_modules/vite/types/importmeta.d.ts", "../../node_modules/.pnpm/vite@6.3.5_@types+node@22.15.19/node_modules/vite/client.d.ts", "../../node_modules/.pnpm/@types+react-dom@19.1.5_@types+react@19.1.4/node_modules/@types/react-dom/index.d.ts"], "fileIdsList": [[85, 91, 134, 206, 207], [85, 91, 134, 216, 218], [85, 91, 134, 214, 216], [85, 91, 134, 285, 286], [85, 91, 134], [91, 134], [85, 91, 134, 206], [84, 85, 91, 134], [84, 85, 91, 134, 206, 207, 303, 327], [84, 85, 91, 134, 216, 286, 304, 327, 408], [84, 85, 91, 134, 206, 216, 286, 305, 327, 330, 408], [84, 85, 91, 134, 306, 327], [84, 85, 91, 134, 207, 302], [84, 85, 91, 134, 216, 286], [84, 85, 91, 134, 305, 306, 307, 420], [84, 85, 91, 134, 216, 286, 303, 304], [84, 85, 91, 134, 206, 299, 327, 412], [84, 85, 91, 134, 214, 293, 294, 295, 296, 297, 299, 307, 420], [91, 134, 180, 184, 193, 194, 197], [91, 134, 203, 204], [91, 134, 193, 194, 196], [91, 134, 193, 194, 198, 205], [91, 134, 191], [91, 134, 184, 186, 187, 188, 190, 192], [91, 134, 313], [91, 134, 310, 311, 312, 313, 314, 317, 318, 319, 320, 321, 322, 323, 324], [91, 134, 309], [91, 134, 316], [91, 134, 310, 311, 312], [91, 134, 310, 311], [91, 134, 313, 314, 316], [91, 134, 311], [91, 134, 329], [91, 134, 218, 328], [84, 91, 134, 307, 325, 326], [91, 134, 407], [91, 134, 394, 395, 396], [91, 134, 389, 390, 391], [91, 134, 367, 368, 369, 370], [91, 134, 333, 407], [91, 134, 333], [91, 134, 333, 334, 335, 336, 381], [91, 134, 371], [91, 134, 366, 372, 373, 374, 375, 376, 377, 378, 379, 380], [91, 134, 381], [91, 134, 332], [91, 134, 385, 387, 388, 406, 407], [91, 134, 385, 387], [91, 134, 382, 385, 407], [91, 134, 392, 393, 397, 398, 403], [91, 134, 386, 388, 398, 406], [91, 134, 405, 406], [91, 134, 382, 386, 388, 404, 405], [91, 134, 386, 407], [91, 134, 384], [91, 134, 384, 386, 407], [91, 134, 382, 383], [91, 134, 399, 400, 401, 402], [91, 134, 388, 407], [91, 134, 343], [91, 134, 337, 344], [91, 134, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365], [91, 134, 363, 407], [91, 134, 209], [91, 134, 210, 211, 213], [91, 134, 187], [91, 134, 189], [91, 134, 200, 203], [91, 131, 134], [91, 133, 134], [134], [91, 134, 139, 169], [91, 134, 135, 140, 146, 147, 154, 166, 177], [91, 134, 135, 136, 146, 154], [86, 87, 88, 91, 134], [91, 134, 137, 178], [91, 134, 138, 139, 147, 155], [91, 134, 139, 166, 174], [91, 134, 140, 142, 146, 154], [91, 133, 134, 141], [91, 134, 142, 143], [91, 134, 146], [91, 134, 144, 146], [91, 133, 134, 146], [91, 134, 146, 147, 148, 166, 177], [91, 134, 146, 147, 148, 161, 166, 169], [91, 129, 134, 182], [91, 129, 134, 142, 146, 149, 154, 166, 177], [91, 134, 146, 147, 149, 150, 154, 166, 174, 177], [91, 134, 149, 151, 166, 174, 177], [89, 90, 91, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183], [91, 134, 146, 152], [91, 134, 153, 177], [91, 134, 142, 146, 154, 166], [91, 134, 155], [91, 134, 156], [91, 133, 134, 157], [91, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183], [91, 134, 159], [91, 134, 160], [91, 134, 146, 161, 162], [91, 134, 161, 163, 178, 180], [91, 134, 146, 166, 167, 169], [91, 134, 168, 169], [91, 134, 166, 167], [91, 134, 169], [91, 134, 170], [91, 131, 134, 166], [91, 134, 146, 172, 173], [91, 134, 172, 173], [91, 134, 139, 154, 166, 174], [91, 134, 175], [91, 134, 154, 176], [91, 134, 149, 160, 177], [91, 134, 139, 178], [91, 134, 166, 179], [91, 134, 153, 180], [91, 134, 181], [91, 134, 139, 146, 148, 157, 166, 177, 180, 182], [91, 134, 166, 183], [84, 91, 134], [82, 83, 91, 134], [91, 134, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 282, 283, 284], [91, 134, 220, 226], [91, 134, 220, 223], [91, 134, 220], [91, 134, 227], [91, 134, 232, 233], [91, 134, 232], [91, 134, 223], [91, 134, 220, 232], [91, 134, 222, 223], [91, 134, 241], [91, 134, 247, 248, 249], [91, 134, 220, 222], [91, 134, 222], [91, 134, 221, 223], [91, 134, 220, 226, 240], [91, 134, 267, 268, 269], [91, 134, 220, 232, 271], [91, 134, 220, 222, 226], [91, 134, 220, 225, 226], [91, 134, 220, 223, 224, 225], [91, 134, 281], [91, 134, 185], [91, 134, 199, 202], [91, 134, 200], [91, 134, 188, 201], [91, 134, 193, 195], [91, 134, 193, 200, 203], [91, 134, 315], [91, 134, 192], [91, 101, 105, 134, 177], [91, 101, 134, 166, 177], [91, 96, 134], [91, 98, 101, 134, 174, 177], [91, 134, 154, 174], [91, 134, 184], [91, 96, 134, 184], [91, 98, 101, 134, 154, 177], [91, 93, 94, 97, 100, 134, 146, 166, 177], [91, 101, 108, 134], [91, 93, 99, 134], [91, 101, 122, 123, 134], [91, 97, 101, 134, 169, 177, 184], [91, 122, 134, 184], [91, 95, 96, 134, 184], [91, 101, 134], [91, 95, 96, 97, 98, 99, 100, 101, 102, 103, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 123, 124, 125, 126, 127, 128, 134], [91, 101, 116, 134], [91, 101, 108, 109, 134], [91, 99, 101, 109, 110, 134], [91, 100, 134], [91, 93, 96, 101, 134], [91, 101, 105, 109, 110, 134], [91, 105, 134], [91, 99, 101, 104, 134, 177], [91, 93, 98, 101, 108, 134], [91, 134, 166], [91, 96, 101, 122, 134, 182, 184], [91, 134, 419], [91, 134, 415], [91, 134, 416], [91, 134, 417, 418], [91, 134, 212], [91, 134, 207, 215], [91, 134, 207]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "impliedFormat": 1}, {"version": "ee7bad0c15b58988daa84371e0b89d313b762ab83cb5b31b8a2d1162e8eb41c2", "impliedFormat": 1}, {"version": "27bdc30a0e32783366a5abeda841bc22757c1797de8681bbe81fbc735eeb1c10", "impliedFormat": 1}, {"version": "8fd575e12870e9944c7e1d62e1f5a73fcf23dd8d3a321f2a2c74c20d022283fe", "impliedFormat": 1}, {"version": "8bf8b5e44e3c9c36f98e1007e8b7018c0f38d8adc07aecef42f5200114547c70", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "959d36cddf5e7d572a65045b876f2956c973a586da58e5d26cde519184fd9b8a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "965f36eae237dd74e6cca203a43e9ca801ce38824ead814728a2807b1910117d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3925a6c820dcb1a06506c90b1577db1fdbf7705d65b62b99dce4be75c637e26b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a3d63ef2b853447ec4f749d3f368ce642264246e02911fcb1590d8c161b8005", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b5ce7a470bc3628408429040c4e3a53a27755022a32fd05e2cb694e7015386c7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b4b67b1a91182421f5df999988c690f14d813b9850b40acd06ed44691f6727ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "df83c2a6c73228b625b0beb6669c7ee2a09c914637e2d35170723ad49c0f5cd4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "436aaf437562f276ec2ddbee2f2cdedac7664c1e4c1d2c36839ddd582eeb3d0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e3c06ea092138bf9fa5e874a1fdbc9d54805d074bee1de31b99a11e2fec239d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "87dc0f382502f5bbce5129bdc0aea21e19a3abbc19259e0b43ae038a9fc4e326", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b1cb28af0c891c8c96b2d6b7be76bd394fddcfdb4709a20ba05a7c1605eea0f9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2fef54945a13095fdb9b84f705f2b5994597640c46afeb2ce78352fab4cb3279", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac77cb3e8c6d3565793eb90a8373ee8033146315a3dbead3bde8db5eaf5e5ec6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "56e4ed5aab5f5920980066a9409bfaf53e6d21d3f8d020c17e4de584d29600ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ece9f17b3866cc077099c73f4983bddbcb1dc7ddb943227f1ec070f529dedd1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a6282c8827e4b9a95f4bf4f5c205673ada31b982f50572d27103df8ceb8013c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1c9319a09485199c1f7b0498f2988d6d2249793ef67edda49d1e584746be9032", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e3a2a0cee0f03ffdde24d89660eba2685bfbdeae955a6c67e8c4c9fd28928eeb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "51ad4c928303041605b4d7ae32e0c1ee387d43a24cd6f1ebf4a2699e1076d4fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "60037901da1a425516449b9a20073aa03386cce92f7a1fd902d7602be3a7c2e9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d4b1d2c51d058fc21ec2629fff7a76249dec2e36e12960ea056e3ef89174080f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "22adec94ef7047a6c9d1af3cb96be87a335908bf9ef386ae9fd50eeb37f44c47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4245fee526a7d1754529d19227ecbf3be066ff79ebb6a380d78e41648f2f224d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f4bd6f16dfd90a0ced98b061a25b26d228e6a1891ef6766c726ed9bd1bd916d8", "affectsGlobalScope": true}, {"version": "742d4b7b02ffc3ba3c4258a3d196457da2b3fec0125872fd0776c50302a11b9d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "03566a51ebc848dec449a4ed69518e9f20caa6ac123fa32676aaaabe64adae8e", "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "impliedFormat": 1}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "030e350db2525514580ed054f712ffb22d273e6bc7eddc1bb7eda1e0ba5d395e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d802f0e6b5188646d307f070d83512e8eb94651858de8a82d1e47f60fb6da4e2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e9c23ba78aabc2e0a27033f18737a6df754067731e69dc5f52823957d60a4b6", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "88d9a77d2abc23a7d26625dd6dae5b57199a8693b85c9819355651c9d9bab90f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a38efe83ff77c34e0f418a806a01ca3910c02ee7d64212a59d59bca6c2c38fa1", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "3fe4022ba1e738034e38ad9afacbf0f1f16b458ed516326f5bf9e4a31e9be1dc", "impliedFormat": 1}, {"version": "a957197054b074bcdf5555d26286e8461680c7c878040d0f4e2d5509a7524944", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4314c7a11517e221f7296b46547dbc4df047115b182f544d072bdccffa57fc72", "impliedFormat": 1}, {"version": "e9b97d69510658d2f4199b7d384326b7c4053b9e6645f5c19e1c2a54ede427fc", "impliedFormat": 1}, {"version": "c2510f124c0293ab80b1777c44d80f812b75612f297b9857406468c0f4dafe29", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "f478f6f5902dc144c0d6d7bdc919c5177cac4d17a8ca8653c2daf6d7dc94317f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "19d5f8d3930e9f99aa2c36258bf95abbe5adf7e889e6181872d1cdba7c9a7dd5", "impliedFormat": 1}, {"version": "b200675fd112ffef97c166d0341fb33f6e29e9f27660adde7868e95c5bc98beb", "impliedFormat": 1}, {"version": "a6bf63d17324010ca1fbf0389cab83f93389bb0b9a01dc8a346d092f65b3605f", "impliedFormat": 1}, {"version": "e009777bef4b023a999b2e5b9a136ff2cde37dc3f77c744a02840f05b18be8ff", "impliedFormat": 1}, {"version": "1e0d1f8b0adfa0b0330e028c7941b5a98c08b600efe7f14d2d2a00854fb2f393", "impliedFormat": 1}, {"version": "ee1ee365d88c4c6c0c0a5a5701d66ebc27ccd0bcfcfaa482c6e2e7fe7b98edf7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "88bc59b32d0d5b4e5d9632ac38edea23454057e643684c3c0b94511296f2998c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a0a1dda070290b92da5a50113b73ecc4dd6bcbffad66e3c86503d483eafbadcf", "impliedFormat": 1}, {"version": "59dcad36c4549175a25998f6a8b33c1df8e18df9c12ebad1dfb25af13fd4b1ce", "impliedFormat": 1}, {"version": "9ba5b6a30cb7961b68ad4fb18dca148db151c2c23b8d0a260fc18b83399d19d3", "impliedFormat": 1}, {"version": "3f3edb8e44e3b9df3b7ca3219ab539710b6a7f4fe16bd884d441af207e03cd57", "impliedFormat": 1}, {"version": "528b62e4272e3ddfb50e8eed9e359dedea0a4d171c3eb8f337f4892aac37b24b", "impliedFormat": 1}, {"version": "d71535813e39c23baa113bc4a29a0e187b87d1105ccc8c5a6ebaca38d9a9bff2", "impliedFormat": 1}, {"version": "8cf7e92bdb2862c2d28ba4535c43dc599cfbc0025db5ed9973d9b708dcbe3d98", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "278e70975bd456bba5874eaee17692355432e8d379b809a97f6af0eee2b702d8", "impliedFormat": 1}, {"version": "b1b6ee0d012aeebe11d776a155d8979730440082797695fc8e2a5c326285678f", "impliedFormat": 1}, {"version": "45875bcae57270aeb3ebc73a5e3fb4c7b9d91d6b045f107c1d8513c28ece71c0", "impliedFormat": 1}, {"version": "1dc73f8854e5c4506131c4d95b3a6c24d0c80336d3758e95110f4c7b5cb16397", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "636302a00dfd1f9fe6e8e91e4e9350c6518dcc8d51a474e4fc3a9ba07135100b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3f16a7e4deafa527ed9995a772bb380eb7d3c2c0fd4ae178c5263ed18394db2c", "impliedFormat": 1}, {"version": "933921f0bb0ec12ef45d1062a1fc0f27635318f4d294e4d99de9a5493e618ca2", "impliedFormat": 1}, {"version": "71a0f3ad612c123b57239a7749770017ecfe6b66411488000aba83e4546fde25", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "e1120271ebbc9952fdc7b2dd3e145560e52e06956345e6fdf91d70ca4886464f", "impliedFormat": 1}, {"version": "814118df420c4e38fe5ae1b9a3bafb6e9c2aa40838e528cde908381867be6466", "impliedFormat": 1}, {"version": "e1ce1d622f1e561f6cdf246372ead3bbc07ce0342024d0e9c7caf3136f712698", "impliedFormat": 1}, {"version": "c878f74b6d10b267f6075c51ac1d8becd15b4aa6a58f79c0cfe3b24908357f60", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "27e4532aaaa1665d0dd19023321e4dc12a35a741d6b8e1ca3517fcc2544e0efe", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2754d8221d77c7b382096651925eb476f1066b3348da4b73fe71ced7801edada", "impliedFormat": 1}, {"version": "8c2ad42d5d1a2e8e6112625767f8794d9537f1247907378543106f7ba6c7df90", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f0be1b8078cd549d91f37c30c222c2a187ac1cf981d994fb476a1adc61387b14", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0aaed1d72199b01234152f7a60046bc947f1f37d78d182e9ae09c4289e06a592", "impliedFormat": 1}, {"version": "98ffdf93dfdd206516971d28e3e473f417a5cfd41172e46b4ce45008f640588e", "impliedFormat": 1}, {"version": "66ba1b2c3e3a3644a1011cd530fb444a96b1b2dfe2f5e837a002d41a1a799e60", "impliedFormat": 1}, {"version": "7e514f5b852fdbc166b539fdd1f4e9114f29911592a5eb10a94bb3a13ccac3c4", "impliedFormat": 1}, {"version": "7d6ff413e198d25639f9f01f16673e7df4e4bd2875a42455afd4ecc02ef156da", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "12e8ce658dd17662d82fb0509d2057afc5e6ee30369a2e9e0957eff725b1f11d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74736930d108365d7bbe740c7154706ccfb1b2a3855a897963ab3e5c07ecbf19", "impliedFormat": 1}, {"version": "858f999b3e4a45a4e74766d43030941466460bf8768361d254234d5870480a53", "impliedFormat": 1}, {"version": "ac5ed35e649cdd8143131964336ab9076937fa91802ec760b3ea63b59175c10a", "impliedFormat": 1}, {"version": "63b05afa6121657f25e99e1519596b0826cda026f09372c9100dfe21417f4bd6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3797dd6f4ea3dc15f356f8cdd3128bfa18122213b38a80d6c1f05d8e13cbdad8", "impliedFormat": 1}, {"version": "ad90122e1cb599b3bc06a11710eb5489101be678f2920f2322b0ac3e195af78d", "impliedFormat": 1}, {"version": "bae8d023ef6b23df7da26f51cea44321f95817c190342a36882e93b80d07a960", "impliedFormat": 1}, {"version": "26a770cec4bd2e7dbba95c6e536390fffe83c6268b78974a93727903b515c4e7", "impliedFormat": 1}, {"version": "035a5df183489c2e22f3cf59fc1ed2b043d27f357eecc0eb8d8e840059d44245", "impliedFormat": 1}, {"version": "0d14fa22c41fdc7277e6f71473b20ebc07f40f00e38875142335d5b63cdfc9d2", "impliedFormat": 1}, {"version": "a4809f4d92317535e6b22b01019437030077a76fec1d93b9881c9ed4738fcc54", "impliedFormat": 1}, {"version": "5f53fa0bd22096d2a78533f94e02c899143b8f0f9891a46965294ee8b91a9434", "impliedFormat": 1}, {"version": "c085e9aa62d1ae1375794c1fb927a445fa105fed891a7e24edbb1c3300f7384a", "impliedFormat": 1}, {"version": "f315e1e65a1f80992f0509e84e4ae2df15ecd9ef73df975f7c98813b71e4c8da", "impliedFormat": 1}, {"version": "e00243d23c495ca2170c9b9e20b5c92331239100b51efdc2b4401cdad859bbef", "impliedFormat": 1}, {"version": "41ea7fd137518560e0d2af581edadadd236b685b5e2f80f083127a28e01cf0ac", "impliedFormat": 1}, {"version": "ab82804a14454734010dcdcd43f564ff7b0389bee4c5692eec76ff5b30d4cf66", "impliedFormat": 1}, {"version": "6fa5d56af71f07dc276aae3f6f30807a9cccf758517fb39742af72e963553d80", "impliedFormat": 1}, {"version": "819dddfec57391f8458929ca8e4377f030d42107ff6ec431e620b70b0695d530", "impliedFormat": 1}, {"version": "701bdef1f4a13932f64c4ce89537f2c66301eb46daf30a16a436c991df568686", "impliedFormat": 1}, {"version": "cdcc132f207d097d7d3aa75615ab9a2e71d6a478162dde8b67f88ea19f3e54de", "impliedFormat": 1}, {"version": "5b9586e9b0b6322e5bfbd2c29bd3b8e21ab9d871f82346cb71020e3d84bae73e", "impliedFormat": 1}, {"version": "3e70a7e67c2cb16f8cd49097360c0309fe9d1e3210ff9222e9dac1f8df9d4fb6", "impliedFormat": 1}, {"version": "ab68d2a3e3e8767c3fba8f80de099a1cfc18c0de79e42cb02ae66e22dfe14a66", "impliedFormat": 1}, {"version": "d96cc6598148bf1a98fb2e8dcf01c63a4b3558bdaec6ef35e087fd0562eb40ec", "impliedFormat": 1}, {"version": "ac5f598a09eed39b957ae3d909b88126f3faf605bd4589c19e9ae85d23ef71e3", "impliedFormat": 1}, {"version": "92abba98a71c0244a6bcdd3ad4d2e04f1d0a8bcae57d2bb865bf53d1ac86e3d0", "impliedFormat": 1}, {"version": "d2afa0d86bc6f2e72c1cf2ecb2372bf1b0f002493706a81f2b9a3ee4f944e219", "impliedFormat": 1}, "d422c4eef8c26f9a65400f20ebf25214b8717bc16b316ee12f22550194386028", "d20dbb6ae7817e28590b0b20756af807a15aa2be53aebef9e990d5c08b1bff26", {"version": "5574d520dabc450de6be799f1791d86d71da4fb236f16e6ca21b953788bb5154", "impliedFormat": 1}, {"version": "5f877dfc985d1fd3ac8bf4a75cd77b06c42ca608809b324c44b4151758de7189", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4f585cea32567574de0301ed79131a4c3d0bb36bbfea7f66e2f29b5dce1c4293", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "14c2fd6220654a41c53836a62ba96d4b515ae1413b0ccb31c2445fb1ae1de5de", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4f29c38739500cd35a2ce41d15a35e34445ca755ebb991915b5f170985a49d21", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "865f27c635741c45905459c1b45a7f410421cda3f24362f703b111fa27799142", "affectsGlobalScope": true, "impliedFormat": 1}, "d0cf8ff973299488d1886377fc85eff3f7a23b7b790cafbe187e1357c990dbc5", "2f4a90c99616921d834aa63a96f4edf4b18d43783e90803badb3df816d3f724f", "69dda85bc6a343109bef0f6517e0b790ff57104dafb489faa9ef3adf316fee20", {"version": "f8db4fea512ab759b2223b90ecbbe7dae919c02f8ce95ec03f7fb1cf757cfbeb", "affectsGlobalScope": true, "impliedFormat": 1}, "ab4d18c3107620e7dc375d9cda4051891135b6107e43dad9e94e42407b463c75", {"version": "6291bcfb741d49ef57db7f30ca0342f20252a011cd5020e7d242e7c6d8846030", "impliedFormat": 1}, {"version": "467973ab10cebf60fcaf6d8e3305240f50257185679093ea5745ca8a43b3282b", "impliedFormat": 1}, {"version": "2b70a3d54893ecf7422c3ef8fd12f5ccc6f94e566a094c7d8cd190c94be5e9f8", "impliedFormat": 1}, {"version": "b009e972db39bf7d44b7deefd69e47a819982d8380a209c5a4647d0894555aa9", "impliedFormat": 1}, {"version": "ee3ba0ff639e5036b1478225e984f30f0ea8860cf990b06b8bd7d33f00b68fa2", "impliedFormat": 1}, {"version": "610e734fb0e3017ef56b87c0727444e25c1a451b7cb1a1c306c299f67195d410", "impliedFormat": 1}, {"version": "ae3d7105f310192cf8a52cb92cad5c291ff889fcc2f33de118aa839a641420b0", "impliedFormat": 1}, {"version": "91912fc86688d5775aa292067b20fefe1ec499a804c8aea70b2eed763f45f3af", "impliedFormat": 1}, {"version": "b53ba2451a78354e7c225ba59cb53907d1a5378326b4d10cdee54a79c34bd7ff", "impliedFormat": 1}, {"version": "8808dfbc083776589359c1a5dc38ee2ab281fa7db0a9d8301a527bfa95027844", "impliedFormat": 1}, {"version": "e2e8f7ef9ba99c50e86967eecc9deee7f27daada80d13fd25ec7fa0c7eab055e", "impliedFormat": 1}, {"version": "a28d0f3496ec030df69cd5c8e76745af30213449f9eed4858f400ac8e5faf732", "impliedFormat": 1}, {"version": "230adc9909c36b8e0e16747d7ee231d5f1838717f085f74675c1b2aad28cb5bb", "impliedFormat": 1}, {"version": "0142517c012e6d9e878125d084bed7d8bc57d21f215c312f076c8c5df6d23be8", "impliedFormat": 1}, {"version": "799e64658ba9bf6a50316f5a47c703120a8913da721543bbd606500946432bfe", "impliedFormat": 1}, {"version": "f99a652fa0f324d1de15db1807ceda90316b0dc755223a250343dd5e9bd05810", "impliedFormat": 1}, {"version": "4e8bc96fe6012f0ddd3a12796c6aff0bdbe7b8cce907b05028ff84cc9260a97a", "impliedFormat": 1}, {"version": "1a56093c8425500f94935e6438e424a7f2d037fe401ea8e91b9343629af19d5a", "impliedFormat": 1}, {"version": "a15afedb5972da56d5e2ce818f7b3f98b73d81d738d07deda0f6ac5e895d66cb", "impliedFormat": 1}, {"version": "c70e4f3c8386a7a1d20cc6e5a6712378a4da84f137c4670ee88349714ceca33f", "impliedFormat": 1}, {"version": "dc28600a49340ac895322fff6ce8d22126b7e141aeb96d2146ce0a5ed7601658", "impliedFormat": 1}, {"version": "ae36256e28625cd4ec5415670fecf5bd82d76cf1e6c26e36490747c6c1e3aeb5", "impliedFormat": 1}, {"version": "d0d33027f9e7f599a166f6c41ee55ac8c62491a03ce8ef7e4c2bef0d2f9fc3c6", "impliedFormat": 1}, {"version": "5dabe302693e2adf0bab3ab050347a06b3bac1e616f69a2c9b279e9e7fd15b2b", "impliedFormat": 1}, {"version": "9883eb753f740cb4697c245c0a23a8f56bfd19dfa26cf21b5c333e32681010a4", "impliedFormat": 1}, {"version": "ad3ee2fcd875af6ec1c80da2cd4a77c0c630a5d29dda566365f72f7626575a19", "impliedFormat": 1}, {"version": "da06b7416ca3beb6b0eb3e6c790bdfa8f0f2ac48b49b6520a8272f7c48c453b4", "impliedFormat": 1}, {"version": "95fe501b64dde048ee6b0452991cb2f41f8c4dfc36d0800246ee7f8a0c3e01e1", "impliedFormat": 1}, {"version": "71dc5749fb4d997be52118c2235348de143d7c586b2e7b90170f667f50846249", "impliedFormat": 1}, {"version": "221c2b9f2560ba52cf2e72490dc2bbe03fadb4b559e5b6cedddf49b96c0f161c", "impliedFormat": 1}, {"version": "ab482807a9a7e822770d72874475e04c2ae47e2bc3668da1a25a2d74f473fb40", "impliedFormat": 1}, {"version": "cd500e2be6f67ab2698c4560fbcc14ede38e84032940c7a39dfd4fcb14234d01", "impliedFormat": 1}, {"version": "6441cce5ef12cde40ada24dca3d2b801bdef29e56386ecdf0b65c705cdab7539", "impliedFormat": 1}, {"version": "caf2e17da84228ea9148167096e26206b30dd51a3336291e2bdd1f8261a250f1", "impliedFormat": 1}, {"version": "e48e765bd1dbdf29d89111276309399fe76cc8784aaf0b730b0f182fb08fa02e", "impliedFormat": 1}, {"version": "ebf6ef4477b7e56cb126c0297b87e01ab316236a87f2ba6e333a4988920fdd7b", "impliedFormat": 1}, {"version": "78683f5abd657ebd50d4824999abfa1e04eaa9f628f0c37f3e801dad7f4e6288", "impliedFormat": 1}, {"version": "1ee3972069e4d95bad7cd3bc2af0f6bdb2299a42bf9c9b4db994938a81261e13", "impliedFormat": 1}, {"version": "3a12d7aae929c4b36a06f1f1ce2389c1d49a42d449985562c076461a4e119658", "impliedFormat": 1}, {"version": "ad589a70ad4302d9853ddb14520104ba93ebca9b3f8e3010f0dfe0e0eb15d41e", "impliedFormat": 1}, {"version": "e37cf3a920817edcecf2c525ccb3c9777538c18561f8d129fa369e1b4ff45296", "impliedFormat": 1}, {"version": "7f0f5646625369f0479bf9b34cfa0e7adcbe96ff4fcbc5d823cfc1e8b987dab4", "impliedFormat": 1}, {"version": "022502ed2d8cdd756c29e6a3226a700dcd77d60e38be1403ed0f6b9f83b69c34", "impliedFormat": 1}, {"version": "f7e18d335f61d5decef172f61946985ce68d8d7cf989b8a9783f24c08fee5e37", "impliedFormat": 1}, {"version": "134d21ae2f63dded24437d4adc6e7b3ace3f9bb1158cb6affdba1499f013e344", "impliedFormat": 1}, {"version": "6dcebfbf5d4a5c862442457b571bd448c387683090cf84ff4c6af8ac923bf8b9", "impliedFormat": 1}, {"version": "877d970b4f092c37bf2e93fcda13f1cdef87d5a0b0f7d861ceee5f3425ffcd9b", "impliedFormat": 1}, {"version": "4a5f560c9d3a2ae15b1b4b91b4737490ac2257e025ddcfd67f1f3f0b4fceeb74", "impliedFormat": 1}, {"version": "a4309c325e9fba429721c9ce7b3528a998c11c4b1d01ed23d38187c651ce8677", "impliedFormat": 1}, {"version": "d26c0f7416fbb4f5521f93d5709bf8cebf45a303cc44cb27b521fae769dfb05b", "impliedFormat": 1}, {"version": "44fdea337219625ebf8086f7da275d1ace9f691a42096fe40a029b3d666c3d37", "impliedFormat": 1}, {"version": "484d91625363e1f136adcefe32345c26ca0e3f4dd48ad7aec0dc0e39578d51e2", "impliedFormat": 1}, {"version": "92c88c69c7df7e6540849e48e63536655aa483c33a5b88199176223a2dd65782", "impliedFormat": 1}, {"version": "bc5b2762892a43c4beac3b597b0bcd87484af66a38714ba90bb27689873947ba", "impliedFormat": 1}, {"version": "bfb8aa01341f564648653c4bbd015e944c7e4c6cb814bc53fc0eb2763c698a45", "impliedFormat": 1}, {"version": "39aa4bcf639907ddf14e26f88e917ce27cada52a0db8ae15708323fdb1d877c6", "impliedFormat": 1}, {"version": "ec95844f22f008c2503c2bb02e1ace3c73c3fd1e3ebc3e883bd6c3548da7c634", "impliedFormat": 1}, {"version": "bdb40ace5c69322eeb1c98b70aab94c930195b043189a6793506a34a095c7e03", "impliedFormat": 1}, {"version": "048ea7a82b78552ccaaf552e13c8bd067ca2b774a10834b1b718e738ffa9a5ad", "impliedFormat": 1}, {"version": "673a798ca4193d31aa4fd98f6359673a356904506b5390f6ee071b61b6889c95", "impliedFormat": 1}, {"version": "e6619829422070bc70eff2e8867b98f6e8bba20672ffa4a461749193049f55c2", "impliedFormat": 1}, {"version": "9797ea8ccffacd16ab6fce35cff2c35392d7e81f42cc84e1b3e3664039abf31e", "impliedFormat": 1}, {"version": "bf364c41c5bbd6557613e0549a547483ebe99f2647e265e06c3a399d8d5a9c9f", "impliedFormat": 1}, {"version": "21ad37f86d9cced1c2ae37955d4408c87fdcc920d12c242d832e124f1d404fba", "impliedFormat": 1}, {"version": "907917d1120c65ced96b3ed1f7c25fbc3ea1b1ba33f94bd8f934392cb3ae505f", "impliedFormat": 1}, {"version": "3a697f137e43d91a85154c9775aff98f2d4f038ee8bdd127509a3e21dd730021", "impliedFormat": 1}, {"version": "29efd4a5234694d4126a9e88175f2b5e5fa2bc52c40099ce1b4dbc0499c1ff63", "signature": "513ce3872c00a7d42ca54c008e1b5f8b32230caa056e720712448818401fad8e"}, {"version": "9a9092de0dba32116c9daf2f7237027a1b4f1873371877d841ecfa93a207a255", "signature": "88f5b135a635861eb8b763d40c969769f898512df8f249d91229d70d8c245ac2"}, "1c3bde9fb2d7319edda1eaa2252c5188d135ce50019e7b504d47d8815b7e31d2", "ce306c2a4d42c0aa20e2c43fdde1bd95c4a055823851056415c61ac11dd87a58", "2b12ad6cda0d6d2c32889657443ceb44234caef31fdac5cbd39a89946a4e18eb", "639413b37452cf403b83bda763fa89d8415719c41c03202306d9c4100908441d", "62d5695fc3f7ffce60fb9926ed0bfbcbdade29f847909e70c053861746ad5819", {"version": "816f8415bbfdbe26bc44e472798ee69a9d5e866b72de727160d3d8707dc706fd", "signature": "83f6221416c563349caf80b7a49ba3c6190abf81f2693fb7efe3c10db2cb6c94"}, {"version": "4366a92b9b8d3a2238dcc19a3af1d0a8a761eb445d9f74dc9eb04b3bf0857367", "signature": "0cc3272c89890e3f8da80fd1e3ffa31e7ae355d0ed6c054afe8ed3bb0acf07fc"}, {"version": "b8f007807889a65093cd1ab2edd3a7090298cf22f6e55d9409f15cc6c0054a25", "signature": "cc73b38c22ba6155198cb4f44a8fb4235bb2c7cd670ab9bf19efa98f0d8584d5"}, {"version": "3fbe172f477e247f81bc8e94f295d6fba75826597e5b611adc245a995a0f0c77", "signature": "a0b495c1ac4180cdff5f4be8e0a8c367988fc4710d2bb5ca376439a3ccc58f76"}, {"version": "1a903570e4e797c5358942a417f9e7b9b9226f37bf564408267b8398fe20dd67", "signature": "18a27cf29e5796a0ff284c0e15e25ab1704990148d51a6891107120d71350d3a"}, "b422c5feb7b89caaeed0d787613f1c1bea65343afe31d8b003de7eb9336a26d3", {"version": "068c0f0fffbf402ac19f6942358f8141073fb57e24b137f815a146dc0141343e", "signature": "2efd364513523fcc8137747b4a7164c5ee33064847ff579fa52a3178713e749c"}, "6f0ebcdf1ab586e91b0c316d6a3d23ecf49d34d1ff208f92c86765a79853e372", "3ba445babcb4449addfecafc0d4212037d71b7a4ffaddc643cb035a240a0ab9f", {"version": "59859bcb84574c0f1bd8a04251054fb54f2e2d2718f1668a148e7e2f48c4980d", "impliedFormat": 1}, {"version": "7c3b6ae96909166caf7b9e7ac32b090446a53ae5881b35b9319128307741a8cc", "signature": "de04e58e66e6f7233daeb102e32a000b1406eb492a4685d093e086e605728144"}, "4bb4c4c46164e05692d2af7bb6e3310bae9fb11f56db09d416b138acc81918f1", "9b871f8c8a8437e78154aabc333369fc234cf12019b7a5a9fef707bfb974dcc6", "5ef2a7aa8fc4b68f5ea0a645b24b3571592f9ea6bb17d5cb1ad88d7ebf3551a2", {"version": "88e9caa9c5d2ba629240b5913842e7c57c5c0315383b8dc9d436ef2b60f1c391", "impliedFormat": 1}, "87669bf90cde59f4d38f386ca4e5858c3000113d41a8b14144ada5b89cc69bb8", {"version": "ae77d81a5541a8abb938a0efedf9ac4bea36fb3a24cc28cfa11c598863aba571", "impliedFormat": 1}, {"version": "3cfb7c0c642b19fb75132154040bb7cd840f0002f9955b14154e69611b9b3f81", "impliedFormat": 1}, {"version": "8387ec1601cf6b8948672537cf8d430431ba0d87b1f9537b4597c1ab8d3ade5b", "impliedFormat": 1}, {"version": "d16f1c460b1ca9158e030fdf3641e1de11135e0c7169d3e8cf17cc4cc35d5e64", "impliedFormat": 1}, {"version": "a934063af84f8117b8ce51851c1af2b76efe960aa4c7b48d0343a1b15c01aedf", "impliedFormat": 1}, {"version": "e3c5ad476eb2fca8505aee5bdfdf9bf11760df5d0f9545db23f12a5c4d72a718", "impliedFormat": 1}, {"version": "462bccdf75fcafc1ae8c30400c9425e1a4681db5d605d1a0edb4f990a54d8094", "impliedFormat": 1}, {"version": "5923d8facbac6ecf7c84739a5c701a57af94a6f6648d6229a6c768cf28f0f8cb", "impliedFormat": 1}, {"version": "d0570ce419fb38287e7b39c910b468becb5b2278cf33b1000a3d3e82a46ecae2", "impliedFormat": 1}, {"version": "3aca7f4260dad9dcc0a0333654cb3cde6664d34a553ec06c953bce11151764d7", "impliedFormat": 1}, {"version": "a0a6f0095f25f08a7129bc4d7cb8438039ec422dc341218d274e1e5131115988", "impliedFormat": 1}, {"version": "b58f396fe4cfe5a0e4d594996bc8c1bfe25496fbc66cf169d41ac3c139418c77", "impliedFormat": 1}, {"version": "45785e608b3d380c79e21957a6d1467e1206ac0281644e43e8ed6498808ace72", "impliedFormat": 1}, {"version": "bece27602416508ba946868ad34d09997911016dbd6893fb884633017f74e2c5", "impliedFormat": 1}, {"version": "2a90177ebaef25de89351de964c2c601ab54d6e3a157cba60d9cd3eaf5a5ee1a", "impliedFormat": 1}, {"version": "82200e963d3c767976a5a9f41ecf8c65eca14a6b33dcbe00214fcbe959698c46", "impliedFormat": 1}, {"version": "b4966c503c08bbd9e834037a8ab60e5f53c5fd1092e8873c4a1c344806acdab2", "impliedFormat": 1}, {"version": "3d3208d0f061e4836dd5f144425781c172987c430f7eaee483fadaa3c5780f9f", "impliedFormat": 1}, {"version": "480c20eddc2ee5f57954609b2f7a3368f6e0dda4037aa09ccf0d37e0b20d4e5c", "impliedFormat": 1}, {"version": "72e9425f1ba1eb7fd8122d08f48848a0d56de1cd4c7b51f26dc2612bd26c7241", "impliedFormat": 1}, {"version": "841784cfa9046a2b3e453d638ea5c3e53680eb8225a45db1c13813f6ea4095e5", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "646ef1cff0ec3cf8e96adb1848357788f244b217345944c2be2942a62764b771", "impliedFormat": 1}, "a94d124bb6e9f2fc507ed4f9d9e9183632f6549036f693f2ba45d1d1d87ca8ca", {"version": "da0f84fcd93700b4a5fbf9c6f166a6cc19fc798231bff56dd1e3875bfc6966eb", "impliedFormat": 1}, {"version": "634ff08e0143bec98401c737de7bfc6883bfec09200bd3806d2a4cfc79c62aaa", "impliedFormat": 1}, {"version": "90a86863e3a57143c50fec5129d844ec12cef8fe44d120e56650ed51a6ce9867", "impliedFormat": 1}, {"version": "472c0a98c5de98b8f5206132c941b052f5cc1ae78860cb8712ac4f1ebf4550ca", "impliedFormat": 1}, {"version": "538c4903ef9f8df7d84c6cf2e065d589a2532d152fa44105c7093a606393b814", "impliedFormat": 1}, {"version": "cfcb6acbb793a78b20899e6537c010bfbbf939c77471abcdc2a41faf9682ca1a", "impliedFormat": 1}, {"version": "a7798e86de8e76844f774f8e0e338149893789cdc08970381f0ae78c86e8667f", "impliedFormat": 1}, {"version": "eebc21bb922816f92302a1f9dcefc938e74d4af8c0a111b2a52519d7e25d4868", "impliedFormat": 1}, {"version": "6b359d3c3138a9f4d3a9c9a8fda24be6fd15bd789e692252b53e68ce99db8edc", "impliedFormat": 1}, {"version": "9488b648a6a4146b26c0fd4e85984f617056293092a89861f5259a69be16ca5c", "impliedFormat": 1}, {"version": "e156513655462b5811a8f980e32ccd204c19042f8c9756430fe4e8d6f7c1326e", "impliedFormat": 1}, {"version": "5679b694d138b8c4b3d56c9b1210f903c6b0ca2b5e7f1682a2dd41a6c955f094", "impliedFormat": 1}, {"version": "ca8da035b76fb0136d2c1390dda650b7979202dbe0f5dc7eaefcde1c76dee4f4", "impliedFormat": 1}, {"version": "4b1022a607444684abeee6537e4cace97263d1ef047c31b012c41fdc15838a79", "impliedFormat": 1}, {"version": "dd0271250f1e4314e52d7e0da9f3b25a708827f8a43ceff847a2a5e3fd3283e8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "47971d8a8639a2a2dd684091c6e7660ec5909fed540c4479ca24e22ac237194e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e1075312b07671ef1cbf46409a0fa2eb2b90bb59c6215c94f0e530113013eeda", "impliedFormat": 1}, {"version": "1bfd63c3f3749c5dc925bb0c05f229f9a376b8d3f8173d0e01901c08202caf6f", "impliedFormat": 1}, {"version": "da850b4fdbabdd528f8b9c2784c5ba3b3bedc4e2e1e34dcd08b6407f9ec61a25", "impliedFormat": 1}, {"version": "e61c918bb5f4a39b795a06e22bc4d44befcefd22f6a5c8a732c9ed0b565a6128", "impliedFormat": 1}, {"version": "ee56351989b0e6f31fd35c9048e222146ced0aac68c64ce2e034f7c881327d6d", "impliedFormat": 1}, {"version": "f58b2f1c8f4bcf519377d39f9555631b6507977ad2f4d8b73ac04622716dc925", "impliedFormat": 1}, {"version": "4c805d3d1228c73877e7550afd8b881d89d9bc0c6b73c88940cffcdd2931b1f6", "impliedFormat": 1}, {"version": "4aa74b4bc57c535815ae004550c59a953c8f8c3c61418ac47a7dcfefba76d1ba", "impliedFormat": 1}, {"version": "78b17ceb133d95df989a1e073891259b54c968f71f416cd76185308af4f9a185", "impliedFormat": 1}, {"version": "d76e5d04d111581b97e0aa35de3063022d20d572f22f388d3846a73f6ce0b788", "impliedFormat": 1}, {"version": "0a53bb48eba6e9f5a56e3b85529fbbe786d96e84871579d10593d4f3ae0f9dba", "impliedFormat": 1}, {"version": "d34fb8b0a66f0a406c7ce63a36f16dda7ff4500b11b0bd30a491aa0d59336d1f", "impliedFormat": 1}, {"version": "282b31893b18a06114e5173f775dd085597ca220d183b8bd474d21846c048334", "impliedFormat": 1}, {"version": "ed27d5ce258f069acf0036471d1fbb56b4cb3c16d7401b52a51297eca651db62", "impliedFormat": 1}, {"version": "ec203a515afd88589bf1d384535024f5b90ebe6b5c416fb3dcca0abd428a8ba4", "impliedFormat": 1}, {"version": "32a2a1374b57f0744d284ca93b477bd97825922513a24dfe262cbf3497377d96", "impliedFormat": 1}, {"version": "a8b60d24dc1eb26c0e987f9461c893744339a7f48e4496f8077f258a644cffab", "impliedFormat": 1}, {"version": "3f9df27a77a23d69088e369b42af5f95bcb3e605e6b5c2395f0bfcd82045e051", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9fd080a9458c6d6f3eb6d4e2b12a3ec498d7d219863e9dca0646bdee9acce875", "impliedFormat": 1}, {"version": "e5d31928bee2ba0e72aeb858881891f8948326e4f91823028d0aea5c6f9e7564", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9a9ba9f6fd097bb2f57d68da8a39403bbe4dc818b8ccd155a780e4e23fa556f2", "impliedFormat": 1}, {"version": "e50c4cd1f5cbce3e74c19a5bbf503c460e6ae86597e6d648a98c7f6c90b596dd", "impliedFormat": 1}, {"version": "fa140f881e20591ce163039a7968b54c5e51c11228708b4f9147473d06471cf5", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "295eca0c47be1191690fd2fe588195fff9d4dc43852aceb8b4cab2aa634579f0", "impliedFormat": 1}, {"version": "59ee7346e19b0050508a592702871dc943083c6dcb69a47d52e888115d840781", "impliedFormat": 1}, {"version": "067712491fb2094c212c733dd8e2d56e74c309a9ce9dac9e919286b7245a1eb4", "impliedFormat": 1}, {"version": "a5eae58ac55bd30c42359e4b01fb2be5eddac336869d3f04ffb4daa54b58f009", "impliedFormat": 1}, {"version": "d12d691ef8933e8db39f2ca81d6973940ff5e37bb421752f5b6e7bc15dea3abf", "impliedFormat": 1}, {"version": "4c5f8bd9b3a1aae4e4fddfee41667e495a045f73ed603993038fa6a8ba92fa14", "impliedFormat": 1}, {"version": "dfb274ab0f319cf18ce7152067c25f984c7fd1924fc72b3f66734588444c934a", "impliedFormat": 1}, {"version": "108c8c05cbc3fbbbd4ff4fc0779c9bef55655c28528eb0f77829795dc9f0b484", "impliedFormat": 1}, {"version": "a7e5444d24cdec45f113f4fb8a687e1c83a5d30c55d2da19a04be71108ad77bd", "impliedFormat": 1}, {"version": "41ec17e218b7358fcff25c719bc419fec8ec98f13e561b9a33b07392d4fec24c", "impliedFormat": 1}, {"version": "23c204326746e981e02d7f0a15ab6f8015f9035998cb3766c9ddbf8ea247aea2", "impliedFormat": 1}, {"version": "25f994b5d76ce6a3186a3319555bbba79706dac2174019915c39ac6080e98c7e", "impliedFormat": 1}, {"version": "dfa4e2c6a612d43851ccbc499598cb006a3a78bc8c7f972c52078f862fa84e47", "impliedFormat": 1}, {"version": "02c1705fa902f172be6e9020d74bcd92ce5db8d2ef3e1b03aabc2ac8eb46c3db", "impliedFormat": 1}, {"version": "99d2d8a0c7bb3dd77459552269a7b5865fa912cedab69db686d40d2586b551f7", "impliedFormat": 1}, {"version": "b47abe58626d76d258472b1d5f76752dd29efe681545f32698db84e7f83517df", "impliedFormat": 1}, {"version": "3a99bbbbbf42e45c3d203e7c74f1319b79f9821c5e5f3cdd03249184d3e003ce", "impliedFormat": 1}, {"version": "aaacc0e12ab4de27bdf131f666e315d8e60abec26c7f87501e0a7806fc824ae6", "impliedFormat": 1}, {"version": "3b4195afd41a9215afc7be0820f8083f6bd2e85e5e0b45bb0061fb041944711e", "impliedFormat": 1}, {"version": "108df8095f5e25d7189dd0d1433ac2df75ec40c779d8faf7d2670f1485beb643", "impliedFormat": 1}, {"version": "ddd3c1d3c9ff67140191a3cf49b09875e20f28f2fc5535ae5ea16e14293a989b", "impliedFormat": 1}, {"version": "7b496e53d5f7e1737adcb5610516476ee055bf547918797348f245c68e7418fe", "impliedFormat": 1}, {"version": "577f44389d7faedd7fc9c0330caf73140e5d0d5f6c968210bff78be569f398a7", "impliedFormat": 1}, {"version": "3046c57724587a59bceefadd30040d418e9df81b9f3cfd680618a3511302ed7a", "impliedFormat": 1}, {"version": "15ccc911ed15397e838471bfe6d476c28deffe976c05cb057e6b1ea7491242c2", "impliedFormat": 1}, {"version": "64b5a5ebdaead77a9a564aa938f4fb7a45e27cda7441d3bee8c9de8a4df5a04f", "impliedFormat": 1}, {"version": "a48037f7af5f80df8973db5e562e17566407541de284b8dadf1879ea3aed8a2f", "impliedFormat": 1}, {"version": "dab97d96ce986857150db03f0d435b44c060d126b4a387c7807f4e9f6c92e531", "impliedFormat": 1}, {"version": "85f39366ea7bc5e34b596fc97de18a7e377856755e789d8e931054f2191d9b8b", "impliedFormat": 1}, {"version": "daf3ea3d49f6e8a2fa70b7ca1f21bd97f1b65021b31fbfccb73dd55f86abb792", "impliedFormat": 1}, {"version": "b15bd260805f9dd06cd4b2b741057209994823942c5696fd835e8a04fb4aab6b", "impliedFormat": 1}, {"version": "6635a824edf99ed52dbd3502d5bce35990c3ed5e2ec5cef88229df8ac0c52b06", "impliedFormat": 1}, {"version": "d6577effa37aae713c34363b7cc4c84851cbabe399882c60e2b70bcbb02bfa01", "impliedFormat": 1}, {"version": "8eaf80ad438890fe5880c39a7bbf2c998ce7d29d4c14dd56d82db63bd871eefb", "impliedFormat": 1}, {"version": "9b3e7f776f312c76ac67e1060e5398d7ac2c69d6a3a928a9daaae2eb05b15f56", "impliedFormat": 1}, {"version": "202042eccb4789b7dee51ba9ecab0b854834ea5c1d6a3946504bfc733d4468c3", "impliedFormat": 1}, {"version": "2b2ef76a9f36094b07ee6f76a5ac6903f2f65c0a20283201814a8d1e752cb592", "impliedFormat": 1}, {"version": "8882e4e087d0bc8cc713cb3d8090c45d33e373e6f5c83e0f8d00fe6a950ef875", "impliedFormat": 1}, "a763346ab671d9dea6da4819614ae2f7caeac863da22a9c8131104ad87312749", "092be8f64144e5625bb0422d504e2291a18f5407817d28a0180462c1adfef206", "bfaaf66756629dfc6c54605e44891aaaf5823cf65b48b38da3e812c59f840e5f", {"version": "109334b1738f6428fc0d9fd1ba6067db689845ee0116da493364fceceb6b9569", "signature": "4becb8d75f3b6068c6dcb341157ae997794ce191b61993aa3a6d6cfe907e0085"}, {"version": "61dff2df705418101a498c3b90377e26762cd745f245df9cdfe1566063c292d2", "signature": "a46d66851af2c056e805fdd574bf5ec3adb1181c43c5e41f0a1c592e338afe64"}, "d20dbb6ae7817e28590b0b20756af807a15aa2be53aebef9e990d5c08b1bff26", {"version": "a7ca8df4f2931bef2aa4118078584d84a0b16539598eaadf7dce9104dfaa381c", "impliedFormat": 1}, {"version": "11443a1dcfaaa404c68d53368b5b818712b95dd19f188cab1669c39bee8b84b3", "impliedFormat": 1}, {"version": "36977c14a7f7bfc8c0426ae4343875689949fb699f3f84ecbe5b300ebf9a2c55", "impliedFormat": 1}, {"version": "7f698624bbbb060ece7c0e51b7236520ebada74b747d7523c7df376453ed6fea", "impliedFormat": 1}, {"version": "19efad8495a7a6b064483fccd1d2b427403dd84e67819f86d1c6ee3d7abf749c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1eef826bc4a19de22155487984e345a34c9cd511dd1170edc7a447cb8231dd4a", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "a0acca63c9e39580f32a10945df231815f0fe554c074da96ba6564010ffbd2d8", "impliedFormat": 1}], "root": [81, 208, 217, 219, [286, 301], [303, 306], 308, 331, [409, 414]], "options": {"allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "jsx": 4, "module": 99, "skipLibCheck": true, "strict": true, "target": 99}, "referencedMap": [[414, 1], [208, 1], [219, 2], [217, 3], [287, 4], [288, 5], [81, 6], [289, 5], [290, 5], [291, 5], [292, 5], [301, 7], [293, 8], [298, 5], [299, 5], [294, 8], [295, 8], [300, 5], [296, 8], [297, 8], [286, 5], [331, 9], [409, 10], [410, 11], [411, 12], [303, 13], [304, 14], [308, 15], [305, 16], [306, 8], [413, 17], [412, 18], [198, 19], [199, 6], [205, 20], [197, 21], [206, 22], [192, 23], [193, 24], [191, 6], [323, 6], [320, 6], [319, 6], [314, 25], [325, 26], [310, 27], [321, 28], [313, 29], [312, 30], [322, 6], [317, 31], [324, 6], [318, 32], [311, 6], [330, 33], [329, 34], [328, 27], [327, 35], [394, 36], [395, 36], [397, 37], [396, 36], [389, 36], [390, 36], [392, 38], [391, 36], [369, 6], [368, 6], [371, 39], [370, 6], [367, 6], [334, 40], [332, 41], [335, 6], [382, 42], [336, 36], [372, 43], [381, 44], [373, 6], [376, 45], [374, 6], [377, 6], [379, 6], [375, 45], [378, 6], [380, 6], [333, 46], [408, 47], [393, 36], [388, 48], [398, 49], [404, 50], [405, 51], [407, 52], [406, 53], [386, 48], [387, 54], [383, 55], [385, 56], [384, 57], [399, 36], [403, 58], [400, 36], [401, 59], [402, 36], [337, 6], [338, 6], [341, 6], [339, 6], [340, 6], [343, 6], [344, 60], [345, 6], [346, 6], [342, 6], [347, 6], [348, 6], [349, 6], [350, 6], [351, 61], [352, 6], [366, 62], [353, 6], [354, 6], [355, 6], [356, 6], [357, 6], [358, 6], [359, 6], [362, 6], [360, 6], [361, 6], [363, 36], [364, 36], [365, 63], [309, 6], [211, 6], [210, 64], [214, 65], [209, 6], [187, 6], [189, 66], [190, 67], [218, 68], [131, 69], [132, 69], [133, 70], [91, 71], [134, 72], [135, 73], [136, 74], [86, 6], [89, 75], [87, 6], [88, 6], [137, 76], [138, 77], [139, 78], [140, 79], [141, 80], [142, 81], [143, 81], [145, 82], [144, 83], [146, 84], [147, 85], [148, 86], [130, 87], [90, 6], [149, 88], [150, 89], [151, 90], [184, 91], [152, 92], [153, 93], [154, 94], [155, 95], [156, 96], [157, 97], [158, 98], [159, 99], [160, 100], [161, 101], [162, 101], [163, 102], [164, 6], [165, 6], [166, 103], [168, 104], [167, 105], [169, 106], [170, 107], [171, 108], [172, 109], [173, 110], [174, 111], [175, 112], [176, 113], [177, 114], [178, 115], [179, 116], [180, 117], [181, 118], [182, 119], [183, 120], [307, 121], [421, 121], [326, 121], [302, 121], [82, 6], [84, 122], [85, 121], [195, 6], [285, 123], [227, 124], [228, 125], [229, 126], [230, 126], [231, 127], [234, 128], [233, 129], [235, 130], [236, 131], [237, 6], [238, 124], [239, 132], [242, 133], [243, 126], [244, 126], [245, 126], [246, 6], [250, 134], [247, 6], [248, 126], [249, 135], [251, 6], [252, 125], [220, 6], [221, 6], [240, 6], [223, 136], [253, 6], [254, 126], [255, 125], [256, 6], [257, 136], [258, 126], [259, 135], [222, 137], [241, 138], [260, 126], [261, 125], [262, 126], [263, 126], [264, 124], [265, 135], [266, 6], [270, 139], [267, 129], [268, 129], [269, 129], [272, 140], [224, 141], [273, 132], [274, 6], [275, 142], [276, 6], [277, 126], [278, 126], [226, 143], [279, 135], [280, 6], [282, 144], [281, 126], [232, 126], [283, 132], [284, 126], [271, 126], [225, 124], [185, 6], [186, 145], [92, 6], [188, 6], [83, 6], [203, 146], [201, 147], [202, 148], [196, 149], [194, 6], [204, 150], [316, 151], [315, 6], [200, 152], [79, 6], [80, 6], [13, 6], [14, 6], [16, 6], [15, 6], [2, 6], [17, 6], [18, 6], [19, 6], [20, 6], [21, 6], [22, 6], [23, 6], [24, 6], [3, 6], [25, 6], [26, 6], [4, 6], [27, 6], [31, 6], [28, 6], [29, 6], [30, 6], [32, 6], [33, 6], [34, 6], [5, 6], [35, 6], [36, 6], [37, 6], [38, 6], [6, 6], [42, 6], [39, 6], [40, 6], [41, 6], [43, 6], [7, 6], [44, 6], [49, 6], [50, 6], [45, 6], [46, 6], [47, 6], [48, 6], [8, 6], [54, 6], [51, 6], [52, 6], [53, 6], [55, 6], [9, 6], [56, 6], [57, 6], [58, 6], [60, 6], [59, 6], [61, 6], [62, 6], [10, 6], [63, 6], [64, 6], [65, 6], [11, 6], [66, 6], [67, 6], [68, 6], [69, 6], [70, 6], [1, 6], [71, 6], [72, 6], [12, 6], [76, 6], [74, 6], [78, 6], [73, 6], [77, 6], [75, 6], [108, 153], [118, 154], [107, 153], [128, 155], [99, 156], [98, 157], [127, 158], [121, 159], [126, 160], [101, 161], [115, 162], [100, 163], [124, 164], [96, 165], [95, 158], [125, 166], [97, 167], [102, 168], [103, 6], [106, 168], [93, 6], [129, 169], [119, 170], [110, 171], [111, 172], [113, 173], [109, 174], [112, 175], [122, 158], [104, 176], [105, 177], [114, 178], [94, 179], [117, 170], [116, 168], [120, 6], [123, 180], [420, 181], [416, 182], [415, 6], [417, 183], [418, 6], [419, 184], [213, 185], [212, 6], [216, 186], [215, 187], [207, 6]], "semanticDiagnosticsPerFile": [[287, [{"start": 15, "length": 3, "messageText": "'\"webextension-polyfill\"' has no exported member named 'tab'. Did you mean 'tabs'?", "category": 1, "code": 2724, "relatedInformation": [{"file": "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/index.d.ts", "start": 14016, "length": 4, "messageText": "'tabs' is declared here.", "category": 3, "code": 2728}]}]], [304, [{"start": 166, "length": 15, "messageText": "Module '\"../../types/messaging\"' has no exported member 'UpdateEntryData'.", "category": 1, "code": 2305}, {"start": 1200, "length": 4, "messageText": "Parameter 'prev' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1375, "length": 4, "messageText": "Parameter 'prev' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [305, [{"start": 276, "length": 15, "messageText": "Module '\"../../types/messaging\"' has no exported member 'CreateEntryData'.", "category": 1, "code": 2305}, {"start": 293, "length": 15, "messageText": "Module '\"../../types/messaging\"' has no exported member 'UpdateEntryData'.", "category": 1, "code": 2305}]], [409, [{"start": 361, "length": 15, "messageText": "Module '\"../../../types/messaging\"' has no exported member 'UpdateEntryData'.", "category": 1, "code": 2305}]], [410, [{"start": 486, "length": 15, "messageText": "Module '\"../../../types/messaging\"' has no exported member 'CreateEntryData'.", "category": 1, "code": 2305}, {"start": 503, "length": 15, "messageText": "Module '\"../../../types/messaging\"' has no exported member 'UpdateEntryData'.", "category": 1, "code": 2305}, {"start": 520, "length": 17, "messageText": "Module '\"../../../types/messaging\"' has no exported member 'BackgroundMessage'.", "category": 1, "code": 2305}, {"start": 539, "length": 18, "messageText": "Module '\"../../../types/messaging\"' has no exported member 'BackgroundResponse'.", "category": 1, "code": 2305}]], [413, [{"start": 7464, "length": 19, "messageText": "Cannot redeclare block-scoped variable 'categorySuggestions'.", "category": 1, "code": 2451}, {"start": 7849, "length": 19, "messageText": "Cannot redeclare block-scoped variable 'categorySuggestions'.", "category": 1, "code": 2451}, {"start": 8294, "length": 19, "messageText": "Cannot assign to 'categorySuggestions' because it is a constant.", "category": 1, "code": 2588}, {"start": 8633, "length": 19, "messageText": "Cannot assign to 'categorySuggestions' because it is a constant.", "category": 1, "code": 2588}]]], "affectedFilesPendingEmit": [[414, 49], [208, 49], [219, 49], [217, 49], 287, [288, 49], [289, 49], [290, 49], [291, 49], [292, 49], [301, 49], 293, [298, 49], 299, 294, 295, [300, 49], 296, 297, 286, [331, 49], 409, 410, [411, 49], [303, 49], 304, 308, 305, [306, 49], 413, 412], "emitSignatures": [208, 217, 219, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 303, 304, 305, 306, 308, 331, 409, 410, 411, 412, 413, 414], "version": "5.8.3"}