// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MetadataDisplay matches snapshot with sanitized content and validated URL 1`] = `
<div
  class="metadata-display"
>
  <div
    class="metadata-item metadata-source"
  >
    <strong>
      Source:
    </strong>
     
    <a
      href="https://example.com/article"
      rel="noopener noreferrer"
      target="_blank"
    >
      https://example.com/article
       
    </a>
  </div>
  <div
    class="metadata-item metadata-capture-date"
  >
    <strong>
      Captured:
    </strong>
     
    2023-03-15 11:30:00
  </div>
  <div
    class="metadata-item metadata-tags"
  >
    <strong>
      Tags:
    </strong>
     
    react, testing, javascript
  </div>
  <div
    class="metadata-item metadata-categories"
  >
    <strong>
      Categories:
    </strong>
     
    Technology, Web Development
  </div>
</div>
`;
