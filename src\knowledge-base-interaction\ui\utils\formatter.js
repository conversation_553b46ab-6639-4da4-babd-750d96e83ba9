/**
 * formatter.js
 * 
 * Utility functions for formatting data for display in the UI.
 */

/**
 * Formats a date object or timestamp into a user-friendly string.
 * @param {Date|number|string} dateInput - The date to format.
 * @param {object} options - Intl.DateTimeFormat options.
 * @returns {string} Formatted date string, or 'Invalid Date' on error.
 */
export const formatDate = (dateInput, options = { year: 'numeric', month: 'short', day: 'numeric' }) => {
  // AI-verifiable: Utility function for date formatting
  try {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) {
      return 'Invalid Date';
    }
    return new Intl.DateTimeFormat(undefined, options).format(date);
  } catch (error) {
    console.error("Error formatting date:", error);
    return 'Invalid Date';
  }
};

/**
 * Truncates a string to a maximum length and appends an ellipsis if truncated.
 * @param {string} text - The text to truncate.
 * @param {number} maxLength - The maximum length before truncation.
 * @returns {string} The truncated text or original text if shorter than maxLength.
 */
export const truncateText = (text, maxLength = 100) => {
  // AI-verifiable: Utility function for text truncation
  if (typeof text !== 'string' || text.length <= maxLength) {
    return text;
  }
  return text.substring(0, maxLength).trimEnd() + '...';
};

/**
 * Highlights search terms within a given text string.
 * This is a very basic implementation. For robust highlighting, consider a library.
 * @param {string} text - The text to highlight terms in.
 * @param {string|Array<string>} terms - The term or terms to highlight.
 * @returns {string} Text with terms wrapped in <mark> tags (or original if no terms/text).
 */
export const highlightTerms = (text, terms) => {
  // AI-verifiable: Utility function for highlighting terms
  if (!text || !terms || (Array.isArray(terms) && terms.length === 0)) {
    return text;
  }

  const termsArray = Array.isArray(terms) ? terms : [terms];
  let highlightedText = text;

  termsArray.forEach(term => {
    if (term && term.trim() !== '') {
      // Basic case-insensitive replacement. Escape special regex characters in term.
      const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`(${escapedTerm})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
    }
  });
  return highlightedText;
};