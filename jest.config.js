module.exports = {
  preset: 'ts-jest',
  testEnvironment: "jsdom",
  transform: {
    "^.+\\.(ts|tsx|js|jsx)$": ["ts-jest", {
      babelConfig: false,
      tsconfig: '<rootDir>/apps/chrome-extension/tsconfig.json'
    }]
  },
  testTimeout: 30000, // 30 seconds
  // Automatically clear mock calls and instances between every test
  clearMocks: true,
  // Indicates whether the coverage information should be collected while executing the test
  collectCoverage: false, // Can be enabled if coverage reports are needed
  // An array of glob patterns indicating a set of files for which coverage information should be collected
  // collectCoverageFrom: ['src/**/*.{js,jsx,ts,tsx}', '!**/node_modules/**', '!**/vendor/**'],
  // The directory where Jest should output its coverage files
  // coverageDirectory: 'coverage',
  // A list of reporter names that <PERSON><PERSON> uses when writing coverage reports
  // coverageReporters: ['json', 'text', 'lcov', 'clover'],
  // An object that configures minimum threshold enforcement for coverage results
  // coverageThreshold: {
  //   global: {
  //     branches: 80,
  //     functions: 80,
  //     lines: 80,
  //     statements: -10,
  //   },
  // },
  // A path to a module which exports an async function that is triggered once before all test suites
  // globalSetup: undefined,
  // A path to a module which exports an async function that is triggered once after all test suites
  // globalTeardown: undefined,
  // A set of global variables that need to be available in all test environments
  // globals: {}, // Removed 'ts-jest' from here as it's moved to transform
  // An array of directory names to be searched recursively up from the requiring module's location
  moduleDirectories: ['node_modules'],
  // An array of file extensions your modules use
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json', 'node'],
  // A map from regular expressions to module names or to arrays of module names that allow to stub out resources with a single module
  moduleNameMapper: {
    // Handle CSS imports (if you're using them in your components)
"\\.(css|less|scss|sass)$": "identity-obj-proxy",
    // Handle image imports
    "\\.(jpg|jpeg|png|gif|webp|svg)$": "<rootDir>/src/main-application-ui/__mocks__/fileMock.js",
    // Map @/ imports to the chrome extension src directory
    "^@/(.*)$": "<rootDir>/apps/chrome-extension/src/$1"
  },
  // The glob patterns Jest uses to detect test files
  testMatch: [
    '**/__tests__/**/*.[jt]s?(x)',
    '**/?(*.)+(spec|test).[tj]s?(x)',
    '<rootDir>/src/test/**/*.test.[jt]s?(x)',
    '<rootDir>/test/**/*.test.[jt]s?(x)'
  ],
  // An array of regexp pattern strings that are matched against all test paths, matched tests are skipped
  testPathIgnorePatterns: ['/node_modules/', '/dist/'],
  // This option allows use of a custom results processor
  // testResultsProcessor: undefined,
  // This option allows use of a custom test runner
  // testRunner: 'jest-circus/runner',
  // A map from regular expressions to paths to transformers
  transformIgnorePatterns: ['/node_modules/(?!lowdb)/', '\\.pnp\\.[^\\/]+$'],
  // Indicates whether each individual test should be reported during the run
  // verbose: undefined,
  // Use this configuration option to add custom reporters to Jest
  // reporters: undefined,
  // Setup files after env
  setupFilesAfterEnv: ['<rootDir>/src/main-application-ui/jest.setup.js'] // if you have a setup file
};