{"fileNames": ["../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.webworker.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.date.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./src/chrome.d.ts", "../../node_modules/.pnpm/@types+react@19.1.5/node_modules/@types/react/global.d.ts", "../../node_modules/.pnpm/csstype@3.1.3/node_modules/csstype/index.d.ts", "../../node_modules/.pnpm/@types+react@19.1.5/node_modules/@types/react/index.d.ts", "../../node_modules/.pnpm/@types+react@19.1.5/node_modules/@types/react/jsx-runtime.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/compatibility/disposable.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/compatibility/indexable.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/compatibility/iterators.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/compatibility/index.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/globals.typedarray.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/buffer.buffer.d.ts", "../../node_modules/.pnpm/buffer@5.7.1/node_modules/buffer/index.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/header.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/readable.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/file.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/fetch.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/formdata.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/connector.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/client.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/errors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/dispatcher.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/global-dispatcher.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/global-origin.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/pool-stats.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/handlers.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/balanced-pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-interceptor.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-client.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-pool.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/mock-errors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/proxy-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/env-http-proxy-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/retry-handler.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/retry-agent.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/api.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/interceptors.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/util.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/cookies.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/patch.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/websocket.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/eventsource.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/filereader.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/diagnostics-channel.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/content-type.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/cache.d.ts", "../../node_modules/.pnpm/undici-types@6.21.0/node_modules/undici-types/index.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/globals.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/assert.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/assert/strict.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/async_hooks.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/buffer.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/child_process.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/cluster.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/console.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/constants.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/crypto.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/dgram.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/dns.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/dns/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/domain.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/dom-events.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/events.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/fs.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/fs/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/http.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/http2.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/https.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/inspector.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/module.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/net.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/os.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/path.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/process.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/punycode.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/querystring.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/readline.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/readline/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/repl.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/sea.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/sqlite.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/stream.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/stream/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/stream/web.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/string_decoder.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/test.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/timers.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/timers/promises.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/tls.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/trace_events.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/tty.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/url.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/util.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/v8.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/vm.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/wasi.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/worker_threads.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/zlib.d.ts", "../../node_modules/.pnpm/@types+node@22.15.21/node_modules/@types/node/index.d.ts", "../../node_modules/.pnpm/@types+yargs-parser@21.0.3/node_modules/@types/yargs-parser/index.d.ts", "../../node_modules/.pnpm/@types+yargs@17.0.33/node_modules/@types/yargs/index.d.ts", "../../node_modules/.pnpm/@types+istanbul-lib-coverage@2.0.6/node_modules/@types/istanbul-lib-coverage/index.d.ts", "../../node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/index.d.ts", "../../node_modules/.pnpm/@types+istanbul-lib-report@3.0.3/node_modules/@types/istanbul-lib-report/index.d.ts", "../../node_modules/.pnpm/@types+istanbul-reports@3.0.4/node_modules/@types/istanbul-reports/index.d.ts", "../../node_modules/.pnpm/@sinclair+typebox@0.27.8/node_modules/@sinclair/typebox/typebox.d.ts", "../../node_modules/.pnpm/@jest+schemas@29.6.3/node_modules/@jest/schemas/build/index.d.ts", "../../node_modules/.pnpm/@jest+types@29.6.3/node_modules/@jest/types/build/index.d.ts", "../../node_modules/.pnpm/jest-mock@29.7.0/node_modules/jest-mock/build/index.d.ts", "../../node_modules/.pnpm/@types+stack-utils@2.0.3/node_modules/@types/stack-utils/index.d.ts", "../../node_modules/.pnpm/jest-message-util@29.7.0/node_modules/jest-message-util/build/index.d.ts", "../../node_modules/.pnpm/@jest+fake-timers@29.7.0/node_modules/@jest/fake-timers/build/index.d.ts", "../../node_modules/.pnpm/@jest+environment@29.7.0/node_modules/@jest/environment/build/index.d.ts", "../../node_modules/.pnpm/@jest+expect-utils@29.7.0/node_modules/@jest/expect-utils/build/index.d.ts", "../../node_modules/.pnpm/pretty-format@29.7.0/node_modules/pretty-format/build/index.d.ts", "../../node_modules/.pnpm/jest-diff@29.7.0/node_modules/jest-diff/build/index.d.ts", "../../node_modules/.pnpm/jest-matcher-utils@29.7.0/node_modules/jest-matcher-utils/build/index.d.ts", "../../node_modules/.pnpm/expect@29.7.0/node_modules/expect/build/index.d.ts", "../../node_modules/.pnpm/jest-snapshot@29.7.0/node_modules/jest-snapshot/build/index.d.ts", "../../node_modules/.pnpm/@jest+expect@29.7.0/node_modules/@jest/expect/build/index.d.ts", "../../node_modules/.pnpm/@jest+globals@29.7.0/node_modules/@jest/globals/build/index.d.ts", "../../packages/knowledge-base-service/dist/types.d.ts", "./src/__mocks__/@pkm-ai/knowledge-base-service-old.ts", "../../node_modules/.pnpm/@types+har-format@1.2.16/node_modules/@types/har-format/index.d.ts", "../../node_modules/.pnpm/@types+chrome@0.0.323/node_modules/@types/chrome/har-format/index.d.ts", "../../node_modules/.pnpm/@types+chrome@0.0.323/node_modules/@types/chrome/chrome-cast/index.d.ts", "../../node_modules/@types/filewriter/index.d.ts", "../../node_modules/@types/filesystem/index.d.ts", "../../node_modules/.pnpm/@types+chrome@0.0.323/node_modules/@types/chrome/index.d.ts", "./src/services/electronapi.ts", "./src/background/index.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/events.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/experiments.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/manifest.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/extensiontypes.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/runtime.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/windows.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/tabs.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/action.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/activitylog.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/alarms.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/bookmarks.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/browseraction.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/types.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/browsersettings_colormanagement.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/browsersettings.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/browsingdata.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/captiveportal.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/clipboard.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/commands.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/contentscripts.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/extension.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/menus.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/contextmenus.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/contextualidentities.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/cookies.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/declarativecontent.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/declarativenetrequest.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/devtools_inspectedwindow.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/devtools_network.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/devtools_panels.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/devtools.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/dns.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/downloads.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/find.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/geckoprofiler.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/history.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/i18n.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/identity.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/idle.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/management.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/networkstatus.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/normandyaddonstudy.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/notifications.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/omnibox.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/pageaction.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/permissions.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/pkcs11.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/privacy_network.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/privacy_services.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/privacy_websites.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/privacy.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/webrequest.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/proxy.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/scripting.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/search.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/sessions.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/sidebaraction.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/storage.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/tabgroups.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/theme.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/topsites.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/trial_ml.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/trial.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/userscripts.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/namespaces/webnavigation.d.ts", "../../node_modules/.pnpm/@types+webextension-polyfill@0.12.3/node_modules/@types/webextension-polyfill/index.d.ts", "./src/types/messaging.ts", "../../packages/knowledge-base-service/dist/knowledgebaseservice.d.ts", "../../packages/knowledge-base-service/dist/index.d.ts", "./src/capture/captureservice.ts", "./src/capture/index.ts", "./src/config-ui/index.ts", "./src/content/index.ts", "./src/knowledge-base-ui/index.ts", "./src/mocks/knowledgebaseservice.ts", "./src/organization/categoryinput.tsx", "./src/organization/notesinput.tsx", "./src/organization/suggestiondisplay.tsx", "./src/organization/summarydisplay.tsx", "./src/organization/taginput.tsx", "./src/organization/index.ts", "./src/organization/mockaiservice.ts", "./src/organization/suggestionservice.ts", "./src/organization/__mocks__/suggestionservice.ts", "../../node_modules/.pnpm/@types+react-window@1.8.8/node_modules/@types/react-window/index.d.ts", "./src/ui/options/contentlist.tsx", "./src/ui/options/detailviewpane.tsx", "./src/ui/options/knowledgebaseview.tsx", "./src/ui/options/settingspage.tsx", "../../node_modules/.pnpm/@types+react-dom@19.1.5_@types+react@19.1.5/node_modules/@types/react-dom/client.d.ts", "./src/ui/options/index.tsx", "./src/ui/popup/index.tsx", "../../node_modules/.pnpm/@types+react-dom@19.1.5_@types+react@19.1.5/node_modules/@types/react-dom/index.d.ts"], "fileIdsList": [[53, 59, 102, 174, 175], [53, 59, 102, 182, 183], [53, 59, 102, 175, 250, 251, 253], [53, 59, 102], [59, 102], [53, 59, 102, 174], [52, 53, 59, 102], [52, 53, 59, 102, 175, 269], [52, 53, 59, 102, 251, 253], [52, 53, 59, 102, 272, 273, 274], [52, 53, 59, 102, 251, 253, 270, 271], [52, 53, 59, 102, 272], [52, 53, 59, 102, 182, 260, 261, 262, 263, 264, 272, 274], [59, 102, 148, 152, 161, 162, 165], [59, 102, 171, 172], [59, 102, 161, 162, 164], [59, 102, 161, 162, 166, 173], [59, 102, 159], [59, 102, 152, 154, 155, 156, 158, 160], [59, 102, 177], [59, 102, 178, 179, 181], [59, 102, 155], [59, 102, 157], [59, 99, 102], [59, 101, 102], [102], [59, 102, 107, 137], [59, 102, 103, 108, 114, 115, 122, 134, 145], [59, 102, 103, 104, 114, 122], [54, 55, 56, 59, 102], [59, 102, 105, 146], [59, 102, 106, 107, 115, 123], [59, 102, 107, 134, 142], [59, 102, 108, 110, 114, 122], [59, 101, 102, 109], [59, 102, 110, 111], [59, 102, 114], [59, 102, 112, 114], [59, 101, 102, 114], [59, 102, 114, 115, 116, 134, 145], [59, 102, 114, 115, 116, 129, 134, 137], [59, 97, 102, 150], [59, 97, 102, 110, 114, 117, 122, 134, 145], [59, 102, 114, 115, 117, 118, 122, 134, 142, 145], [59, 102, 117, 119, 134, 142, 145], [57, 58, 59, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151], [59, 102, 114, 120], [59, 102, 121, 145], [59, 102, 110, 114, 122, 134], [59, 102, 123], [59, 102, 124], [59, 101, 102, 125], [59, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151], [59, 102, 127], [59, 102, 128], [59, 102, 114, 129, 130], [59, 102, 129, 131, 146, 148], [59, 102, 114, 134, 135, 137], [59, 102, 136, 137], [59, 102, 134, 135], [59, 102, 137], [59, 102, 138], [59, 99, 102, 134], [59, 102, 114, 140, 141], [59, 102, 140, 141], [59, 102, 107, 122, 134, 142], [59, 102, 143], [59, 102, 122, 144], [59, 102, 117, 128, 145], [59, 102, 107, 146], [59, 102, 134, 147], [59, 102, 121, 148], [59, 102, 149], [59, 102, 107, 114, 116, 125, 134, 145, 148, 150], [59, 102, 134, 151], [52, 59, 102], [50, 51, 59, 102], [59, 102, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 247, 248, 249], [59, 102, 185, 191], [59, 102, 185, 188], [59, 102, 185], [59, 102, 192], [59, 102, 197, 198], [59, 102, 197], [59, 102, 188], [59, 102, 185, 197], [59, 102, 187, 188], [59, 102, 206], [59, 102, 212, 213, 214], [59, 102, 185, 187], [59, 102, 187], [59, 102, 186, 188], [59, 102, 185, 191, 205], [59, 102, 232, 233, 234], [59, 102, 185, 197, 236], [59, 102, 185, 187, 191], [59, 102, 185, 190, 191], [59, 102, 185, 188, 189, 190], [59, 102, 246], [59, 102, 153], [59, 102, 167, 170], [59, 102, 168], [59, 102, 156, 169], [59, 102, 161, 163], [59, 102, 161, 168, 171], [59, 102, 160], [59, 69, 73, 102, 145], [59, 69, 102, 134, 145], [59, 64, 102], [59, 66, 69, 102, 142, 145], [59, 102, 122, 142], [59, 102, 152], [59, 64, 102, 152], [59, 66, 69, 102, 122, 145], [59, 61, 62, 65, 68, 102, 114, 134, 145], [59, 69, 76, 102], [59, 61, 67, 102], [59, 69, 90, 91, 102], [59, 65, 69, 102, 137, 145, 152], [59, 90, 102, 152], [59, 63, 64, 102, 152], [59, 69, 102], [59, 63, 64, 65, 66, 67, 68, 69, 70, 71, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 102], [59, 69, 84, 102], [59, 69, 76, 77, 102], [59, 67, 69, 77, 78, 102], [59, 68, 102], [59, 61, 64, 69, 102], [59, 69, 73, 77, 78, 102], [59, 73, 102], [59, 67, 69, 72, 102, 145], [59, 61, 66, 69, 76, 102], [59, 102, 134], [59, 64, 69, 90, 102, 150, 152], [59, 102, 180], [59, 102, 175, 252], [59, 102, 175]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8ea22b76493fc658fe9b76a84e64c9618823608076ecee5ed3f93f7f0dde904e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f4bd6f16dfd90a0ced98b061a25b26d228e6a1891ef6766c726ed9bd1bd916d8", "affectsGlobalScope": true}, {"version": "170d4db14678c68178ee8a3d5a990d5afb759ecb6ec44dbd885c50f6da6204f6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "63a3a080e64f95754b32cfbf6d1d06b2703ee53e3a46962739e88fdd98703261", "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "impliedFormat": 1}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "030e350db2525514580ed054f712ffb22d273e6bc7eddc1bb7eda1e0ba5d395e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d802f0e6b5188646d307f070d83512e8eb94651858de8a82d1e47f60fb6da4e2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e9c23ba78aabc2e0a27033f18737a6df754067731e69dc5f52823957d60a4b6", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "88d9a77d2abc23a7d26625dd6dae5b57199a8693b85c9819355651c9d9bab90f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a38efe83ff77c34e0f418a806a01ca3910c02ee7d64212a59d59bca6c2c38fa1", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "3fe4022ba1e738034e38ad9afacbf0f1f16b458ed516326f5bf9e4a31e9be1dc", "impliedFormat": 1}, {"version": "a957197054b074bcdf5555d26286e8461680c7c878040d0f4e2d5509a7524944", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4314c7a11517e221f7296b46547dbc4df047115b182f544d072bdccffa57fc72", "impliedFormat": 1}, {"version": "e9b97d69510658d2f4199b7d384326b7c4053b9e6645f5c19e1c2a54ede427fc", "impliedFormat": 1}, {"version": "c2510f124c0293ab80b1777c44d80f812b75612f297b9857406468c0f4dafe29", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "f478f6f5902dc144c0d6d7bdc919c5177cac4d17a8ca8653c2daf6d7dc94317f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "19d5f8d3930e9f99aa2c36258bf95abbe5adf7e889e6181872d1cdba7c9a7dd5", "impliedFormat": 1}, {"version": "b200675fd112ffef97c166d0341fb33f6e29e9f27660adde7868e95c5bc98beb", "impliedFormat": 1}, {"version": "a6bf63d17324010ca1fbf0389cab83f93389bb0b9a01dc8a346d092f65b3605f", "impliedFormat": 1}, {"version": "e009777bef4b023a999b2e5b9a136ff2cde37dc3f77c744a02840f05b18be8ff", "impliedFormat": 1}, {"version": "1e0d1f8b0adfa0b0330e028c7941b5a98c08b600efe7f14d2d2a00854fb2f393", "impliedFormat": 1}, {"version": "ee1ee365d88c4c6c0c0a5a5701d66ebc27ccd0bcfcfaa482c6e2e7fe7b98edf7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "88bc59b32d0d5b4e5d9632ac38edea23454057e643684c3c0b94511296f2998c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a0a1dda070290b92da5a50113b73ecc4dd6bcbffad66e3c86503d483eafbadcf", "impliedFormat": 1}, {"version": "59dcad36c4549175a25998f6a8b33c1df8e18df9c12ebad1dfb25af13fd4b1ce", "impliedFormat": 1}, {"version": "206a70e72af3e24688397b81304358526ce70d020e4c2606c4acfd1fa1e81fb2", "impliedFormat": 1}, {"version": "3f3edb8e44e3b9df3b7ca3219ab539710b6a7f4fe16bd884d441af207e03cd57", "impliedFormat": 1}, {"version": "528b62e4272e3ddfb50e8eed9e359dedea0a4d171c3eb8f337f4892aac37b24b", "impliedFormat": 1}, {"version": "d71535813e39c23baa113bc4a29a0e187b87d1105ccc8c5a6ebaca38d9a9bff2", "impliedFormat": 1}, {"version": "8cf7e92bdb2862c2d28ba4535c43dc599cfbc0025db5ed9973d9b708dcbe3d98", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f72bc8fe16da67e4e3268599295797b202b95e54bd215a03f97e925dd1502a36", "impliedFormat": 1}, {"version": "b1b6ee0d012aeebe11d776a155d8979730440082797695fc8e2a5c326285678f", "impliedFormat": 1}, {"version": "45875bcae57270aeb3ebc73a5e3fb4c7b9d91d6b045f107c1d8513c28ece71c0", "impliedFormat": 1}, {"version": "1dc73f8854e5c4506131c4d95b3a6c24d0c80336d3758e95110f4c7b5cb16397", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "636302a00dfd1f9fe6e8e91e4e9350c6518dcc8d51a474e4fc3a9ba07135100b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3f16a7e4deafa527ed9995a772bb380eb7d3c2c0fd4ae178c5263ed18394db2c", "impliedFormat": 1}, {"version": "933921f0bb0ec12ef45d1062a1fc0f27635318f4d294e4d99de9a5493e618ca2", "impliedFormat": 1}, {"version": "71a0f3ad612c123b57239a7749770017ecfe6b66411488000aba83e4546fde25", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "e1120271ebbc9952fdc7b2dd3e145560e52e06956345e6fdf91d70ca4886464f", "impliedFormat": 1}, {"version": "814118df420c4e38fe5ae1b9a3bafb6e9c2aa40838e528cde908381867be6466", "impliedFormat": 1}, {"version": "e1ce1d622f1e561f6cdf246372ead3bbc07ce0342024d0e9c7caf3136f712698", "impliedFormat": 1}, {"version": "c878f74b6d10b267f6075c51ac1d8becd15b4aa6a58f79c0cfe3b24908357f60", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "27e4532aaaa1665d0dd19023321e4dc12a35a741d6b8e1ca3517fcc2544e0efe", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2754d8221d77c7b382096651925eb476f1066b3348da4b73fe71ced7801edada", "impliedFormat": 1}, {"version": "8c2ad42d5d1a2e8e6112625767f8794d9537f1247907378543106f7ba6c7df90", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f0be1b8078cd549d91f37c30c222c2a187ac1cf981d994fb476a1adc61387b14", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0aaed1d72199b01234152f7a60046bc947f1f37d78d182e9ae09c4289e06a592", "impliedFormat": 1}, {"version": "98ffdf93dfdd206516971d28e3e473f417a5cfd41172e46b4ce45008f640588e", "impliedFormat": 1}, {"version": "66ba1b2c3e3a3644a1011cd530fb444a96b1b2dfe2f5e837a002d41a1a799e60", "impliedFormat": 1}, {"version": "7e514f5b852fdbc166b539fdd1f4e9114f29911592a5eb10a94bb3a13ccac3c4", "impliedFormat": 1}, {"version": "7d6ff413e198d25639f9f01f16673e7df4e4bd2875a42455afd4ecc02ef156da", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "12e8ce658dd17662d82fb0509d2057afc5e6ee30369a2e9e0957eff725b1f11d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74736930d108365d7bbe740c7154706ccfb1b2a3855a897963ab3e5c07ecbf19", "impliedFormat": 1}, {"version": "858f999b3e4a45a4e74766d43030941466460bf8768361d254234d5870480a53", "impliedFormat": 1}, {"version": "ac5ed35e649cdd8143131964336ab9076937fa91802ec760b3ea63b59175c10a", "impliedFormat": 1}, {"version": "63b05afa6121657f25e99e1519596b0826cda026f09372c9100dfe21417f4bd6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3797dd6f4ea3dc15f356f8cdd3128bfa18122213b38a80d6c1f05d8e13cbdad8", "impliedFormat": 1}, {"version": "ad90122e1cb599b3bc06a11710eb5489101be678f2920f2322b0ac3e195af78d", "impliedFormat": 1}, {"version": "bae8d023ef6b23df7da26f51cea44321f95817c190342a36882e93b80d07a960", "impliedFormat": 1}, {"version": "26a770cec4bd2e7dbba95c6e536390fffe83c6268b78974a93727903b515c4e7", "impliedFormat": 1}, {"version": "035a5df183489c2e22f3cf59fc1ed2b043d27f357eecc0eb8d8e840059d44245", "impliedFormat": 1}, {"version": "0d14fa22c41fdc7277e6f71473b20ebc07f40f00e38875142335d5b63cdfc9d2", "impliedFormat": 1}, {"version": "a4809f4d92317535e6b22b01019437030077a76fec1d93b9881c9ed4738fcc54", "impliedFormat": 1}, {"version": "5f53fa0bd22096d2a78533f94e02c899143b8f0f9891a46965294ee8b91a9434", "impliedFormat": 1}, {"version": "c085e9aa62d1ae1375794c1fb927a445fa105fed891a7e24edbb1c3300f7384a", "impliedFormat": 1}, {"version": "f315e1e65a1f80992f0509e84e4ae2df15ecd9ef73df975f7c98813b71e4c8da", "impliedFormat": 1}, {"version": "e00243d23c495ca2170c9b9e20b5c92331239100b51efdc2b4401cdad859bbef", "impliedFormat": 1}, {"version": "41ea7fd137518560e0d2af581edadadd236b685b5e2f80f083127a28e01cf0ac", "impliedFormat": 1}, {"version": "ab82804a14454734010dcdcd43f564ff7b0389bee4c5692eec76ff5b30d4cf66", "impliedFormat": 1}, {"version": "6fa5d56af71f07dc276aae3f6f30807a9cccf758517fb39742af72e963553d80", "impliedFormat": 1}, {"version": "819dddfec57391f8458929ca8e4377f030d42107ff6ec431e620b70b0695d530", "impliedFormat": 1}, {"version": "701bdef1f4a13932f64c4ce89537f2c66301eb46daf30a16a436c991df568686", "impliedFormat": 1}, {"version": "cdcc132f207d097d7d3aa75615ab9a2e71d6a478162dde8b67f88ea19f3e54de", "impliedFormat": 1}, {"version": "5b9586e9b0b6322e5bfbd2c29bd3b8e21ab9d871f82346cb71020e3d84bae73e", "impliedFormat": 1}, {"version": "3e70a7e67c2cb16f8cd49097360c0309fe9d1e3210ff9222e9dac1f8df9d4fb6", "impliedFormat": 1}, {"version": "ab68d2a3e3e8767c3fba8f80de099a1cfc18c0de79e42cb02ae66e22dfe14a66", "impliedFormat": 1}, {"version": "d96cc6598148bf1a98fb2e8dcf01c63a4b3558bdaec6ef35e087fd0562eb40ec", "impliedFormat": 1}, {"version": "ac5f598a09eed39b957ae3d909b88126f3faf605bd4589c19e9ae85d23ef71e3", "impliedFormat": 1}, {"version": "92abba98a71c0244a6bcdd3ad4d2e04f1d0a8bcae57d2bb865bf53d1ac86e3d0", "impliedFormat": 1}, {"version": "d2afa0d86bc6f2e72c1cf2ecb2372bf1b0f002493706a81f2b9a3ee4f944e219", "impliedFormat": 1}, "d422c4eef8c26f9a65400f20ebf25214b8717bc16b316ee12f22550194386028", {"version": "d20dbb6ae7817e28590b0b20756af807a15aa2be53aebef9e990d5c08b1bff26", "signature": "88bac21d5416c48fb0b56cfa2733a3e75b20bee898b9b36e52bedb0ad78f578c"}, {"version": "5574d520dabc450de6be799f1791d86d71da4fb236f16e6ca21b953788bb5154", "impliedFormat": 1}, {"version": "5f877dfc985d1fd3ac8bf4a75cd77b06c42ca608809b324c44b4151758de7189", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4f585cea32567574de0301ed79131a4c3d0bb36bbfea7f66e2f29b5dce1c4293", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "14c2fd6220654a41c53836a62ba96d4b515ae1413b0ccb31c2445fb1ae1de5de", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4f29c38739500cd35a2ce41d15a35e34445ca755ebb991915b5f170985a49d21", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "865f27c635741c45905459c1b45a7f410421cda3f24362f703b111fa27799142", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ee122b801dc8267652491b2dbfadfca083c478b650e22e5eb3086368e4ef614c", "signature": "0b052441855e1c883e38d7661908ec0553a5286b97a846cac1d5a7d4f05e3b0c"}, {"version": "773551bd1c8e1b637ba89c9a5e7249081ab14b5ea6347fd3d04bdb2c17cb4f8b", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "6291bcfb741d49ef57db7f30ca0342f20252a011cd5020e7d242e7c6d8846030", "impliedFormat": 1}, {"version": "467973ab10cebf60fcaf6d8e3305240f50257185679093ea5745ca8a43b3282b", "impliedFormat": 1}, {"version": "2b70a3d54893ecf7422c3ef8fd12f5ccc6f94e566a094c7d8cd190c94be5e9f8", "impliedFormat": 1}, {"version": "b009e972db39bf7d44b7deefd69e47a819982d8380a209c5a4647d0894555aa9", "impliedFormat": 1}, {"version": "ee3ba0ff639e5036b1478225e984f30f0ea8860cf990b06b8bd7d33f00b68fa2", "impliedFormat": 1}, {"version": "610e734fb0e3017ef56b87c0727444e25c1a451b7cb1a1c306c299f67195d410", "impliedFormat": 1}, {"version": "ae3d7105f310192cf8a52cb92cad5c291ff889fcc2f33de118aa839a641420b0", "impliedFormat": 1}, {"version": "91912fc86688d5775aa292067b20fefe1ec499a804c8aea70b2eed763f45f3af", "impliedFormat": 1}, {"version": "b53ba2451a78354e7c225ba59cb53907d1a5378326b4d10cdee54a79c34bd7ff", "impliedFormat": 1}, {"version": "8808dfbc083776589359c1a5dc38ee2ab281fa7db0a9d8301a527bfa95027844", "impliedFormat": 1}, {"version": "e2e8f7ef9ba99c50e86967eecc9deee7f27daada80d13fd25ec7fa0c7eab055e", "impliedFormat": 1}, {"version": "a28d0f3496ec030df69cd5c8e76745af30213449f9eed4858f400ac8e5faf732", "impliedFormat": 1}, {"version": "230adc9909c36b8e0e16747d7ee231d5f1838717f085f74675c1b2aad28cb5bb", "impliedFormat": 1}, {"version": "0142517c012e6d9e878125d084bed7d8bc57d21f215c312f076c8c5df6d23be8", "impliedFormat": 1}, {"version": "799e64658ba9bf6a50316f5a47c703120a8913da721543bbd606500946432bfe", "impliedFormat": 1}, {"version": "f99a652fa0f324d1de15db1807ceda90316b0dc755223a250343dd5e9bd05810", "impliedFormat": 1}, {"version": "4e8bc96fe6012f0ddd3a12796c6aff0bdbe7b8cce907b05028ff84cc9260a97a", "impliedFormat": 1}, {"version": "1a56093c8425500f94935e6438e424a7f2d037fe401ea8e91b9343629af19d5a", "impliedFormat": 1}, {"version": "a15afedb5972da56d5e2ce818f7b3f98b73d81d738d07deda0f6ac5e895d66cb", "impliedFormat": 1}, {"version": "c70e4f3c8386a7a1d20cc6e5a6712378a4da84f137c4670ee88349714ceca33f", "impliedFormat": 1}, {"version": "dc28600a49340ac895322fff6ce8d22126b7e141aeb96d2146ce0a5ed7601658", "impliedFormat": 1}, {"version": "ae36256e28625cd4ec5415670fecf5bd82d76cf1e6c26e36490747c6c1e3aeb5", "impliedFormat": 1}, {"version": "d0d33027f9e7f599a166f6c41ee55ac8c62491a03ce8ef7e4c2bef0d2f9fc3c6", "impliedFormat": 1}, {"version": "5dabe302693e2adf0bab3ab050347a06b3bac1e616f69a2c9b279e9e7fd15b2b", "impliedFormat": 1}, {"version": "9883eb753f740cb4697c245c0a23a8f56bfd19dfa26cf21b5c333e32681010a4", "impliedFormat": 1}, {"version": "ad3ee2fcd875af6ec1c80da2cd4a77c0c630a5d29dda566365f72f7626575a19", "impliedFormat": 1}, {"version": "da06b7416ca3beb6b0eb3e6c790bdfa8f0f2ac48b49b6520a8272f7c48c453b4", "impliedFormat": 1}, {"version": "95fe501b64dde048ee6b0452991cb2f41f8c4dfc36d0800246ee7f8a0c3e01e1", "impliedFormat": 1}, {"version": "71dc5749fb4d997be52118c2235348de143d7c586b2e7b90170f667f50846249", "impliedFormat": 1}, {"version": "221c2b9f2560ba52cf2e72490dc2bbe03fadb4b559e5b6cedddf49b96c0f161c", "impliedFormat": 1}, {"version": "ab482807a9a7e822770d72874475e04c2ae47e2bc3668da1a25a2d74f473fb40", "impliedFormat": 1}, {"version": "cd500e2be6f67ab2698c4560fbcc14ede38e84032940c7a39dfd4fcb14234d01", "impliedFormat": 1}, {"version": "6441cce5ef12cde40ada24dca3d2b801bdef29e56386ecdf0b65c705cdab7539", "impliedFormat": 1}, {"version": "caf2e17da84228ea9148167096e26206b30dd51a3336291e2bdd1f8261a250f1", "impliedFormat": 1}, {"version": "e48e765bd1dbdf29d89111276309399fe76cc8784aaf0b730b0f182fb08fa02e", "impliedFormat": 1}, {"version": "ebf6ef4477b7e56cb126c0297b87e01ab316236a87f2ba6e333a4988920fdd7b", "impliedFormat": 1}, {"version": "78683f5abd657ebd50d4824999abfa1e04eaa9f628f0c37f3e801dad7f4e6288", "impliedFormat": 1}, {"version": "1ee3972069e4d95bad7cd3bc2af0f6bdb2299a42bf9c9b4db994938a81261e13", "impliedFormat": 1}, {"version": "3a12d7aae929c4b36a06f1f1ce2389c1d49a42d449985562c076461a4e119658", "impliedFormat": 1}, {"version": "ad589a70ad4302d9853ddb14520104ba93ebca9b3f8e3010f0dfe0e0eb15d41e", "impliedFormat": 1}, {"version": "e37cf3a920817edcecf2c525ccb3c9777538c18561f8d129fa369e1b4ff45296", "impliedFormat": 1}, {"version": "7f0f5646625369f0479bf9b34cfa0e7adcbe96ff4fcbc5d823cfc1e8b987dab4", "impliedFormat": 1}, {"version": "022502ed2d8cdd756c29e6a3226a700dcd77d60e38be1403ed0f6b9f83b69c34", "impliedFormat": 1}, {"version": "f7e18d335f61d5decef172f61946985ce68d8d7cf989b8a9783f24c08fee5e37", "impliedFormat": 1}, {"version": "134d21ae2f63dded24437d4adc6e7b3ace3f9bb1158cb6affdba1499f013e344", "impliedFormat": 1}, {"version": "6dcebfbf5d4a5c862442457b571bd448c387683090cf84ff4c6af8ac923bf8b9", "impliedFormat": 1}, {"version": "877d970b4f092c37bf2e93fcda13f1cdef87d5a0b0f7d861ceee5f3425ffcd9b", "impliedFormat": 1}, {"version": "4a5f560c9d3a2ae15b1b4b91b4737490ac2257e025ddcfd67f1f3f0b4fceeb74", "impliedFormat": 1}, {"version": "a4309c325e9fba429721c9ce7b3528a998c11c4b1d01ed23d38187c651ce8677", "impliedFormat": 1}, {"version": "d26c0f7416fbb4f5521f93d5709bf8cebf45a303cc44cb27b521fae769dfb05b", "impliedFormat": 1}, {"version": "44fdea337219625ebf8086f7da275d1ace9f691a42096fe40a029b3d666c3d37", "impliedFormat": 1}, {"version": "484d91625363e1f136adcefe32345c26ca0e3f4dd48ad7aec0dc0e39578d51e2", "impliedFormat": 1}, {"version": "92c88c69c7df7e6540849e48e63536655aa483c33a5b88199176223a2dd65782", "impliedFormat": 1}, {"version": "bc5b2762892a43c4beac3b597b0bcd87484af66a38714ba90bb27689873947ba", "impliedFormat": 1}, {"version": "bfb8aa01341f564648653c4bbd015e944c7e4c6cb814bc53fc0eb2763c698a45", "impliedFormat": 1}, {"version": "39aa4bcf639907ddf14e26f88e917ce27cada52a0db8ae15708323fdb1d877c6", "impliedFormat": 1}, {"version": "ec95844f22f008c2503c2bb02e1ace3c73c3fd1e3ebc3e883bd6c3548da7c634", "impliedFormat": 1}, {"version": "bdb40ace5c69322eeb1c98b70aab94c930195b043189a6793506a34a095c7e03", "impliedFormat": 1}, {"version": "048ea7a82b78552ccaaf552e13c8bd067ca2b774a10834b1b718e738ffa9a5ad", "impliedFormat": 1}, {"version": "673a798ca4193d31aa4fd98f6359673a356904506b5390f6ee071b61b6889c95", "impliedFormat": 1}, {"version": "e6619829422070bc70eff2e8867b98f6e8bba20672ffa4a461749193049f55c2", "impliedFormat": 1}, {"version": "9797ea8ccffacd16ab6fce35cff2c35392d7e81f42cc84e1b3e3664039abf31e", "impliedFormat": 1}, {"version": "bf364c41c5bbd6557613e0549a547483ebe99f2647e265e06c3a399d8d5a9c9f", "impliedFormat": 1}, {"version": "21ad37f86d9cced1c2ae37955d4408c87fdcc920d12c242d832e124f1d404fba", "impliedFormat": 1}, {"version": "907917d1120c65ced96b3ed1f7c25fbc3ea1b1ba33f94bd8f934392cb3ae505f", "impliedFormat": 1}, {"version": "3a697f137e43d91a85154c9775aff98f2d4f038ee8bdd127509a3e21dd730021", "impliedFormat": 1}, {"version": "6318856ab21e3e2abf2668ab337213a95bba6d5546a3b80565bc931a8ec3d932", "signature": "0a2ef85200dc29ff4d31a46a78e9e14718c5f6bb3d902f85066fac480839f1e0"}, "d0cf8ff973299488d1886377fc85eff3f7a23b7b790cafbe187e1357c990dbc5", "2f4a90c99616921d834aa63a96f4edf4b18d43783e90803badb3df816d3f724f", {"version": "546529f8bb9f82af0dbde9272228db81c7f94f74d51ff9ab95aee69595a47424", "signature": "5b1a844d2794bbfc9d9189b0ea0e9f0f87070e4d5955ff8bf851f2c24743ffa6"}, {"version": "1c3bde9fb2d7319edda1eaa2252c5188d135ce50019e7b504d47d8815b7e31d2", "signature": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"version": "ce306c2a4d42c0aa20e2c43fdde1bd95c4a055823851056415c61ac11dd87a58", "signature": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"version": "2b12ad6cda0d6d2c32889657443ceb44234caef31fdac5cbd39a89946a4e18eb", "signature": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"version": "639413b37452cf403b83bda763fa89d8415719c41c03202306d9c4100908441d", "signature": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"version": "62d5695fc3f7ffce60fb9926ed0bfbcbdade29f847909e70c053861746ad5819", "signature": "7fbca3042fc49185c230f883ea6e8df18a7bb18c7e0a142f59ddcdf4ba3ccfca"}, {"version": "816f8415bbfdbe26bc44e472798ee69a9d5e866b72de727160d3d8707dc706fd", "signature": "83f6221416c563349caf80b7a49ba3c6190abf81f2693fb7efe3c10db2cb6c94"}, {"version": "4366a92b9b8d3a2238dcc19a3af1d0a8a761eb445d9f74dc9eb04b3bf0857367", "signature": "0cc3272c89890e3f8da80fd1e3ffa31e7ae355d0ed6c054afe8ed3bb0acf07fc"}, {"version": "b8f007807889a65093cd1ab2edd3a7090298cf22f6e55d9409f15cc6c0054a25", "signature": "cc73b38c22ba6155198cb4f44a8fb4235bb2c7cd670ab9bf19efa98f0d8584d5"}, {"version": "3fbe172f477e247f81bc8e94f295d6fba75826597e5b611adc245a995a0f0c77", "signature": "a0b495c1ac4180cdff5f4be8e0a8c367988fc4710d2bb5ca376439a3ccc58f76"}, {"version": "1a903570e4e797c5358942a417f9e7b9b9226f37bf564408267b8398fe20dd67", "signature": "18a27cf29e5796a0ff284c0e15e25ab1704990148d51a6891107120d71350d3a"}, {"version": "b422c5feb7b89caaeed0d787613f1c1bea65343afe31d8b003de7eb9336a26d3", "signature": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"}, {"version": "068c0f0fffbf402ac19f6942358f8141073fb57e24b137f815a146dc0141343e", "signature": "2efd364513523fcc8137747b4a7164c5ee33064847ff579fa52a3178713e749c"}, {"version": "6f0ebcdf1ab586e91b0c316d6a3d23ecf49d34d1ff208f92c86765a79853e372", "signature": "15c40b5b3dbb5d601b439acad9118788d5c04607f7f80c23d2933060fd1e7b89"}, {"version": "3ba445babcb4449addfecafc0d4212037d71b7a4ffaddc643cb035a240a0ab9f", "signature": "913b775d133a3337d4ad2963e43604fc7690f26d4ba6c2f322f3553b7f2a10ee"}, {"version": "59859bcb84574c0f1bd8a04251054fb54f2e2d2718f1668a148e7e2f48c4980d", "impliedFormat": 1}, {"version": "7c3b6ae96909166caf7b9e7ac32b090446a53ae5881b35b9319128307741a8cc", "signature": "de04e58e66e6f7233daeb102e32a000b1406eb492a4685d093e086e605728144"}, {"version": "be831751c2977ca751ca27f6483cf835262182a6f3f2e2c124d7ffd5a0780ab7", "signature": "ba9cadc0fa0b9c6fa18c5c4e061aa83380ab3e3f49ecafa09f4ece7190327fb6"}, {"version": "3608f3cfd47127c9fdb5f6ce53314637752e6844a21d563bb68aa05302e70cac", "signature": "c6998333fd60e710c064e3d7093f7892227c4eb56bef8a625d4866e680003f04"}, {"version": "ebf9d480337563d5c534c5bd19a4f6df4df9042026231a8f2a76fb06389b992e", "signature": "e4cfc98b04a541855a842d2d96e106d9ebebeae7cd558e7dba00b3719e9ccb21"}, {"version": "88e9caa9c5d2ba629240b5913842e7c57c5c0315383b8dc9d436ef2b60f1c391", "impliedFormat": 1}, {"version": "87669bf90cde59f4d38f386ca4e5858c3000113d41a8b14144ada5b89cc69bb8", "signature": "9dd495057f32aee309552e82e091df0724a2daa75a2b70b337bccc798bfbdea9"}, {"version": "7e34730d79a0c5b35db7a00c88327bfdf6ced2b019969b591e5ba80599a68ee3", "signature": "4becb8d75f3b6068c6dcb341157ae997794ce191b61993aa3a6d6cfe907e0085"}, {"version": "a0acca63c9e39580f32a10945df231815f0fe554c074da96ba6564010ffbd2d8", "impliedFormat": 1}], "root": [49, 176, 183, 184, 251, [254, 268], [270, 273], 275, 276], "options": {"allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "experimentalDecorators": true, "jsx": 4, "module": 99, "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "strict": true, "target": 99}, "referencedMap": [[176, 1], [184, 2], [254, 3], [255, 4], [49, 5], [256, 4], [257, 4], [258, 4], [259, 4], [268, 6], [260, 7], [265, 4], [266, 4], [261, 7], [262, 7], [267, 4], [263, 7], [264, 7], [183, 4], [251, 4], [270, 8], [271, 9], [275, 10], [272, 11], [273, 12], [276, 13], [166, 14], [167, 5], [173, 15], [165, 16], [174, 17], [160, 18], [161, 19], [159, 5], [179, 5], [178, 20], [182, 21], [177, 5], [155, 5], [157, 22], [158, 23], [99, 24], [100, 24], [101, 25], [59, 26], [102, 27], [103, 28], [104, 29], [54, 5], [57, 30], [55, 5], [56, 5], [105, 31], [106, 32], [107, 33], [108, 34], [109, 35], [110, 36], [111, 36], [113, 37], [112, 38], [114, 39], [115, 40], [116, 41], [98, 42], [58, 5], [117, 43], [118, 44], [119, 45], [152, 46], [120, 47], [121, 48], [122, 49], [123, 50], [124, 51], [125, 52], [126, 53], [127, 54], [128, 55], [129, 56], [130, 56], [131, 57], [132, 5], [133, 5], [134, 58], [136, 59], [135, 60], [137, 61], [138, 62], [139, 63], [140, 64], [141, 65], [142, 66], [143, 67], [144, 68], [145, 69], [146, 70], [147, 71], [148, 72], [149, 73], [150, 74], [151, 75], [274, 76], [277, 76], [269, 76], [50, 5], [52, 77], [53, 76], [163, 5], [250, 78], [192, 79], [193, 80], [194, 81], [195, 81], [196, 82], [199, 83], [198, 84], [200, 85], [201, 86], [202, 5], [203, 79], [204, 87], [207, 88], [208, 81], [209, 81], [210, 81], [211, 5], [215, 89], [212, 5], [213, 81], [214, 90], [216, 5], [217, 80], [185, 5], [186, 5], [205, 5], [188, 91], [218, 5], [219, 81], [220, 80], [221, 5], [222, 91], [223, 81], [224, 90], [187, 92], [206, 93], [225, 81], [226, 80], [227, 81], [228, 81], [229, 79], [230, 90], [231, 5], [235, 94], [232, 84], [233, 84], [234, 84], [237, 95], [189, 96], [238, 87], [239, 5], [240, 97], [241, 5], [242, 81], [243, 81], [191, 98], [244, 90], [245, 5], [247, 99], [246, 81], [197, 81], [248, 87], [249, 81], [236, 81], [190, 79], [153, 5], [154, 100], [60, 5], [156, 5], [51, 5], [171, 101], [169, 102], [170, 103], [164, 104], [162, 5], [172, 105], [168, 106], [47, 5], [48, 5], [8, 5], [9, 5], [12, 5], [11, 5], [2, 5], [13, 5], [14, 5], [15, 5], [16, 5], [17, 5], [18, 5], [19, 5], [20, 5], [3, 5], [21, 5], [22, 5], [4, 5], [23, 5], [27, 5], [24, 5], [25, 5], [26, 5], [28, 5], [29, 5], [30, 5], [5, 5], [31, 5], [32, 5], [33, 5], [34, 5], [6, 5], [38, 5], [35, 5], [36, 5], [37, 5], [39, 5], [7, 5], [40, 5], [45, 5], [46, 5], [41, 5], [42, 5], [43, 5], [44, 5], [1, 5], [10, 5], [76, 107], [86, 108], [75, 107], [96, 109], [67, 110], [66, 111], [95, 112], [89, 113], [94, 114], [69, 115], [83, 116], [68, 117], [92, 118], [64, 119], [63, 112], [93, 120], [65, 121], [70, 122], [71, 5], [74, 122], [61, 5], [97, 123], [87, 124], [78, 125], [79, 126], [81, 127], [77, 128], [80, 129], [90, 112], [72, 130], [73, 131], [82, 132], [62, 133], [85, 124], [84, 122], [88, 5], [91, 134], [181, 135], [180, 5], [253, 136], [252, 137], [175, 5]], "latestChangedDtsFile": "./dist/ui/options/KnowledgeBaseView.d.ts", "version": "5.8.3"}