import React from 'react';

/**
 * QAResultDisplay component
 * 
 * Displays a single Question & Answer pair.
 * Props:
 *  - qaPair: An object containing the question, answer, and any relevant metadata (e.g., confidence, sources).
 */
const QAResultDisplay = ({ qaPair }) => {
  if (!qaPair) {
    return null; // Or some placeholder
  }

  // AI-verifiable: Component structure for displaying Q&A
  return (
    <div className="qa-result-display" data-testid="qa-result-display">
      <h4>Question:</h4>
      <p>{qaPair.question || 'No question provided.'}</p>
      <h4>Answer:</h4>
      <p>{qaPair.answer || 'No answer provided.'}</p>
      {qaPair.sources && qaPair.sources.length > 0 && (
        <div>
          <h5>Sources:</h5>
          <ul>
            {qaPair.sources.map((source, index) => (
              <li key={index}>{source.name || `Source ${index + 1}`}</li>
            ))}
          </ul>
        </div>
      )}
      {/* AI-verifiable: Placeholder for feedback or further actions */}
      {typeof qaPair.confidence === 'number' && <small>Confidence: {qaPair.confidence.toFixed(2)}</small>}
    </div>
  );
};

export default QAResultDisplay;