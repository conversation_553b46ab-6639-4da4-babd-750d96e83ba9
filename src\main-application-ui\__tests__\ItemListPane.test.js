// __tests__/ItemListPane.test.js
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
// waitFor is removed as direct async operations in component are gone
import ItemListPane from '../renderer/components/ItemListPane';
import useStore from '../renderer/store/useStore';

// No longer mocking getItems from api/client
// jest.mock('../renderer/api/client');

// Mock the Zustand store
let mockStoreState; // This will be set in beforeEach or per test

jest.mock('../renderer/store/useStore', () => {
  return {
    __esModule: true,
    // The mock function will now use the dynamically set mockStoreState
    default: jest.fn((selector) => selector(mockStoreState)),
  };
});

// Helper to set mock store state for a specific test
const setMockStore = (newState) => {
  mockStoreState = { ...mockStoreState, ...newState };
  // Ensure the mock implementation uses the updated state
  useStore.mockImplementation((selector) => selector(mockStoreState));
};

const mockRawItemsFromStore = [
  { id: 1, title: 'Store Item 1', type: 'Article', content: 'This is the full content for store item 1, which is longer than one hundred and fifty characters to ensure that the snippet generation logic is properly tested.', date: '2024-01-01T10:00:00.000Z' },
  { id: 2, title: 'Store Item 2', type: 'Note', content: 'Short content for item 2.', date: new Date('2024-01-02T12:30:00.000Z') },
  { id: 3, name: 'Store Item 3 No Title', type: 'PDF', content: 'PDF content here.', date: '2023-12-25' },
  { id: 4, type: 'Bookmark', content: 'A bookmark item, no title or name.', date: '2024-02-01T10:00:00.000Z' },
];

const generateExpectedSnippet = (content) => {
  if (!content) return '';
  return content.length > 150 ? `${content.substring(0, 150)}...` : content;
};

const formatDateForDisplay = (dateInput) => {
  if (!dateInput) return dateInput; // Return as is if null or undefined
  try {
    const date = new Date(dateInput);
    if (!isNaN(date.getTime())) {
      return date.toLocaleDateString();
    }
  } catch (e) { /* ignore */ }
  return String(dateInput); // Fallback to original string if parsing fails
};

const formatItemsForDisplay = (items) => items.map(item => ({
  ...item,
  title: item.title || item.name || 'Untitled', // Ensure title logic matches component
  snippet: generateExpectedSnippet(item.content),
  displayDate: formatDateForDisplay(item.date),
}));

const mockFormattedItemsFromStore = formatItemsForDisplay(mockRawItemsFromStore);

const defaultProps = {
  className: "test-pane",
  onSelectItem: jest.fn(),
  // activeTagFilters and activeCategoryFilters are removed
};

describe('ItemListPane Component', () => {
  beforeEach(() => {
    // Reset onSelectItem mock
    defaultProps.onSelectItem.mockClear();

    // Reset store state for each test
    mockStoreState = {
      searchTerm: '',
      searchResults: [],
      searchLoading: false,
      searchError: null,
    };
    // Ensure the mock uses the fresh state for each test run
    useStore.mockImplementation((selector) => selector ? selector(mockStoreState) : mockStoreState);
  });

  test('renders "Knowledge Items" heading when no search term is active and items exist', () => {
    setMockStore({ searchResults: mockRawItemsFromStore });
    render(<ItemListPane {...defaultProps} />);
    expect(screen.getByRole('heading', { name: /knowledge items/i })).toBeInTheDocument();
  });

  test('renders "Results for [searchTerm]" heading when search term is active', () => {
    const currentSearchTerm = 'test query';
    setMockStore({ searchTerm: currentSearchTerm, searchResults: mockRawItemsFromStore });
    render(<ItemListPane {...defaultProps} />);
    expect(screen.getByRole('heading', { name: `Results for "${currentSearchTerm}"` })).toBeInTheDocument();
  });

  test('displays items from searchResults correctly (title, snippet, date, type)', () => {
    setMockStore({ searchResults: mockRawItemsFromStore });
    render(<ItemListPane {...defaultProps} />);

    mockFormattedItemsFromStore.forEach(expectedItem => {
      expect(screen.getByText(expectedItem.title)).toBeInTheDocument();
      expect(screen.getByText(expectedItem.snippet)).toBeInTheDocument();
      if (expectedItem.displayDate) {
        // Check if any element contains the date string. Date formatting can be tricky.
        expect(screen.getAllByText(new RegExp(expectedItem.displayDate.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))).length).toBeGreaterThan(0);
      }
      if (expectedItem.type) {
        expect(screen.getByText(`Type: ${expectedItem.type}`)).toBeInTheDocument();
      }
    });
  });

  test('displays "No items found." when searchResults are empty', () => {
    setMockStore({ searchResults: [] }); // searchResults is empty
    render(<ItemListPane {...defaultProps} />);
    expect(screen.getByText(/no items found./i)).toBeInTheDocument();
  });

  test('displays "Loading items..." when searchLoading is true', () => {
    setMockStore({ searchLoading: true });
    render(<ItemListPane {...defaultProps} />);
    expect(screen.getByText(/loading items.../i)).toBeInTheDocument();
  });

  test('displays error message when searchError is present', () => {
    const errorMessage = "Network Error during search";
    setMockStore({ searchError: errorMessage });
    render(<ItemListPane {...defaultProps} />);
    expect(screen.getByText(`Error: ${errorMessage}`)).toBeInTheDocument();
  });

  test('calls onSelectItem with the correct formatted item when an item is clicked', () => {
    setMockStore({ searchResults: mockRawItemsFromStore });
    render(<ItemListPane {...defaultProps} />);

    // Use the title from the formatted item which includes the "Untitled" fallback
    const firstItemTitle = mockFormattedItemsFromStore[0].title;
    const firstItemElement = screen.getByText(firstItemTitle).closest('li');
    fireEvent.click(firstItemElement);

    expect(defaultProps.onSelectItem).toHaveBeenCalledTimes(1);
    // The component formats items for display; ensure we assert against that formatted version.
    expect(defaultProps.onSelectItem).toHaveBeenCalledWith(mockFormattedItemsFromStore[0]);
  });

  test('uses item.name if item.title is not available, then "Untitled"', () => {
    // Item 3 has name, no title. Item 4 has neither.
    setMockStore({ searchResults: [mockRawItemsFromStore[2], mockRawItemsFromStore[3]] });
    render(<ItemListPane {...defaultProps} />);
    expect(screen.getByText(mockFormattedItemsFromStore[2].name)).toBeInTheDocument(); // Should be "Store Item 3 No Title"
    expect(screen.getByText('Untitled')).toBeInTheDocument(); // For item 4
  });

  // Obsolete tests for getItems, initial loading via getItems, and client-side filtering are removed.
});
