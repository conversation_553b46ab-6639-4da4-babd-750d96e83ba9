// content_script.js
// This script runs in the context of the web page

chrome.runtime.onMessage.addListener(
  function(request, sender, sendResponse) {
    // Helper function to get content from meta tags
    function getMetaTagContent(doc, names, propertyNames = []) {
      for (const name of names) {
        const element = doc.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
        if (element && element.content) {
          return element.content.trim();
        }
      }
      for (const prop of propertyNames) {
         const element = doc.querySelector(`meta[property="${prop}"]`);
         if (element && element.content) {
          return element.content.trim();
        }
      }
      return null;
    }

    // Helper function to get multiple content from meta tags (for keywords)
    function getMultipleMetaTagContent(doc, names, propertyNames = []) {
        let results = [];
        const allSelectors = names.map(name => `meta[name="${name}"], meta[property="${name}"]`)
                             .concat(propertyNames.map(prop => `meta[property="${prop}"]`));
        
        doc.querySelectorAll(allSelectors.join(', ')).forEach(element => {
            if (element && element.content) {
                results = results.concat(element.content.split(',').map(k => k.trim()).filter(k => k));
            }
        });
        return results;
    }


    // Helper function to parse JSON-LD data
    function getJsonLdData(doc) {
      const scripts = doc.querySelectorAll('script[type="application/ld+json"]');
      const jsonData = {
        title: null,
        author: null,
        publicationDate: null,
        keywords: [],
        description: null,
      };

      scripts.forEach(script => {
        try {
          const data = JSON.parse(script.textContent);
          const items = Array.isArray(data) ? data : [data]; // Handle array of JSON-LD objects

          items.forEach(item => {
            if (!item) return;

            if (!jsonData.title && (item.headline || item.name)) {
              jsonData.title = item.headline || item.name;
            }
            if (!jsonData.description && item.description) {
                jsonData.description = typeof item.description === 'string' ? item.description : null;
            }

            if (!jsonData.author && item.author) {
              if (Array.isArray(item.author)) {
                const personAuthor = item.author.find(a => a && (a['@type'] === 'Person' || !a['@type']) && a.name);
                if (personAuthor) jsonData.author = personAuthor.name;
                else { // Fallback to first author name if no Person type found
                    const firstAuthor = item.author.find(a => a && a.name);
                    if (firstAuthor) jsonData.author = firstAuthor.name;
                }
              } else if (item.author.name && (item.author['@type'] === 'Person' || !item.author['@type'])) {
                jsonData.author = item.author.name;
              }
            }

            if (!jsonData.publicationDate && (item.datePublished || item.uploadDate)) {
              jsonData.publicationDate = item.datePublished || item.uploadDate;
            }
            
            if (item.keywords) {
              if (typeof item.keywords === 'string') {
                jsonData.keywords = jsonData.keywords.concat(item.keywords.split(',').map(k => k.trim()).filter(k => k));
              } else if (Array.isArray(item.keywords)) {
                jsonData.keywords = jsonData.keywords.concat(item.keywords.map(k => String(k).trim()).filter(k => k));
              }
            }
          });
        } catch (e) {
          console.warn("Error parsing JSON-LD:", e);
        }
      });
      return jsonData;
    }

    if (request.action === "captureArticle") {
      try {
        // Use a clone of the document to avoid modifying the live page
        const documentClone = document.cloneNode(true);
        const reader = new Readability(documentClone);
        const article = reader.parse();

        if (article && article.content) {
          const jsonLd = getJsonLdData(documentClone);

          // Title extraction priority: JSON-LD -> Readability -> document.title
          const title = jsonLd.title || article.title || document.title;
          
          // Author extraction
          let author = jsonLd.author;
          if (!author) {
            author = getMetaTagContent(documentClone,
              ['author', 'creator', 'article:author', 'og:article:author', 'twitter:creator'],
              ['article:author', 'og:article:author'] // Explicitly check property attributes too
            );
          }
          if (!author && article.byline) { // Fallback to Readability's byline if still no author
            author = article.byline;
          }

          // Publication Date extraction
          let publicationDate = jsonLd.publicationDate;
          if (!publicationDate) {
            publicationDate = getMetaTagContent(documentClone,
              ['date', 'publishdate', 'DC.date.issued', 'dcterms.created', 'dcterms.dateCopyrighted', 'dcterms.modified'],
              ['article:published_time', 'og:publish_date', 'article:modified_time', 'og:updated_time']
            ) || documentClone.querySelector('time[itemprop="datePublished"], time[itemprop="dateModified"]')?.getAttribute('datetime');
          }
          
          // Keywords extraction
          let keywords = jsonLd.keywords || [];
          keywords = keywords.concat(getMultipleMetaTagContent(documentClone,
              ['keywords', 'news_keywords', 'tags'],
              ['article:tag']
          ));
          // Deduplicate and sort keywords
          keywords = [...new Set(keywords.map(k => k.toLowerCase()))].sort();


          // Description extraction
          let description = jsonLd.description;
          if (!description) {
            description = getMetaTagContent(documentClone,
              ['description', 'og:description', 'twitter:description', 'dc.description', 'dcterms.abstract', 'dcterms.description']
            );
          }
          if (!description && article.excerpt) { // Fallback to Readability's excerpt
            description = article.excerpt;
          }


          sendResponse({
            success: true,
            data: article.content, // Cleaned article HTML
            metadata: {
              title: title,
              url: window.location.href,
              author: author || null, // Ensure null if not found
              publicationDate: publicationDate || null,
              keywords: keywords.length > 0 ? keywords : null,
              description: description || null,
              // Keep Readability specific fields if they weren't overridden or for additional context
              excerpt: article.excerpt, // Readability's raw excerpt
              byline: article.byline, // Readability's raw byline
              length: article.length,
              siteName: article.siteName
            }
          });
        } else {
          sendResponse({ success: false, error: "Could not extract article content using Readability." });
        }
      } catch (e) {
        console.error("Error capturing article:", e);
        sendResponse({ success: false, error: e.message || "An unknown error occurred during article capture." });
      }
      return true; // Indicates that the response will be sent asynchronously
    } else if (request.action === "captureSelectedContent") {
      try {
        const selection = window.getSelection();
        const selectedText = selection.toString().trim();

        if (selectedText) {
          // Attempt to get the HTML of the selection
          let selectedHTML = "";
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            const div = document.createElement("div");
            div.appendChild(range.cloneContents());
            selectedHTML = div.innerHTML;
          }

          sendResponse({
            success: true,
            data: selectedHTML || selectedText, // Prefer HTML, fallback to text
            isHTML: !!selectedHTML, // Indicate if the data is HTML
            metadata: {
              title: document.title, // Or generate a title from selection
              url: window.location.href,
              selectionText: selectedText, // Keep plain text for potential use
            }
          });
        } else {
          sendResponse({ success: false, error: "No content selected." });
        }
      } catch (e) {
        console.error("Error capturing selected content:", e);
        sendResponse({ success: false, error: e.message || "An unknown error occurred during selection capture." });
      }
      return true; // Indicates that the response will be sent asynchronously
    } else if (request.action === "capturePdfContent") {
      // Implement PDF parsing using an immediately-invoked async function
      // to allow use of await, while the main listener function remains synchronous
      // and returns true to indicate an asynchronous response.
      (async () => {
        try {
          const isPdfPage = document.contentType === 'application/pdf' ||
                            document.querySelector('embed[type="application/pdf"], object[type="application/pdf"]') !== null;

          if (isPdfPage) {
            let pdfDataSourceUrl = window.location.href; // Default for direct PDF load
            const pdfEmbed = document.querySelector('embed[type="application/pdf"]');
            const pdfObject = document.querySelector('object[type="application/pdf"]');

            let potentialSrc = null;
            if (pdfEmbed && pdfEmbed.src) {
              potentialSrc = pdfEmbed.src;
            } else if (pdfObject && pdfObject.data) {
              potentialSrc = pdfObject.data;
            }

            if (potentialSrc) {
                // Resolve potential relative URL from embed/object against the page's base URL
                pdfDataSourceUrl = new URL(potentialSrc, window.location.href).href;
            }

            // Check for PDF.js library
            if (typeof pdfjsLib === 'undefined') {
              if (typeof jest !== 'undefined') { // In Jest and PDF.js is not mocked/available by test setup
                console.warn("pdfjsLib not found in Jest environment, using mock response for PDF capture.");
                const mockPdfTextContent = "This is the extracted text from the PDF document. (Jest mock: pdfjsLib undefined)";
                sendResponse({
                    success: true,
                    data: mockPdfTextContent,
                    metadata: { title: document.title || "Sample PDF Document", url: window.location.href, contentType: "application/pdf" }
                });
                return; // Exit async IIFE
              }
              throw new Error("PDF.js library is not available. Please ensure it is loaded and web_accessible.");
            }
            
            // Configure PDF.js worker if not already configured or if set to a blob URL.
            // This path assumes 'pdf.worker.js' from 'pdfjs-dist' is placed in 'lib/pdfjs-dist/build/'
            // and this path is registered in manifest.json's web_accessible_resources.
            if (!pdfjsLib.GlobalWorkerOptions.workerSrc || pdfjsLib.GlobalWorkerOptions.workerSrc.startsWith('blob:')) {
                try {
                    pdfjsLib.GlobalWorkerOptions.workerSrc = chrome.runtime.getURL('lib/pdfjs-dist/build/pdf.worker.js');
                } catch (e) {
                    // chrome.runtime.getURL might not be available in all test contexts (e.g. pure Node Jest)
                    if (typeof jest === 'undefined') { // Only log error if not in Jest
                        console.error("Failed to set PDF.js worker path. PDF processing may fail or be slow:", e);
                    }
                }
            }

            const pdfDocument = await pdfjsLib.getDocument(pdfDataSourceUrl).promise;
            let fullText = "";
            // Attempt to get metadata, with a fallback if it fails or Title is missing
            const metadata = await pdfDocument.getMetadata().catch(() => ({ info: { Title: undefined } }));
            const docTitle = (metadata.info && metadata.info.Title) || document.title || "PDF Document";

            for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
              const page = await pdfDocument.getPage(pageNum);
              const textContent = await page.getTextContent();
              // Join text items with a space, and add a space after each page's content if it's not empty
              const pageText = textContent.items.map(item => item.str).join(" ");
              fullText += pageText + (textContent.items.length > 0 ? " " : "");
            }
            // Normalize whitespace (replace multiple spaces/newlines with single space) and trim
            fullText = fullText.trim().replace(/\s+/g, ' ');

            sendResponse({
              success: true,
              data: fullText,
              metadata: {
                title: docTitle,
                url: window.location.href, // Use the main page URL for consistency
                contentType: "application/pdf",
                numPages: pdfDocument.numPages
              }
            });
          } else if (typeof jest !== 'undefined') {
            // This case handles when it's not a PDF page, but we are in a Jest test environment.
            // Tests might rely on this path for specific mock scenarios.
            console.warn("Not a PDF page, but in Jest environment. Sending mock PDF response for capturePdfContent.");
            const mockPdfTextContent = "This is the extracted text from the PDF document. (Jest mock: not a PDF page)";
            sendResponse({
                success: true,
                data: mockPdfTextContent,
                metadata: { title: document.title || "Sample PDF Document", url: window.location.href, contentType: "application/pdf" }
            });
          } else {
            sendResponse({ success: false, error: "Not a PDF page or PDF content not found." });
          }
        } catch (e) {
          console.error("Error capturing PDF content:", e);
          sendResponse({ success: false, error: e.message || "An unknown error occurred during PDF capture." });
        }
      })();
      return true; // Indicates that the response will be sent asynchronously
    }
  }
);