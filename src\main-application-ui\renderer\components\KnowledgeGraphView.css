.knowledge-graph-container {
  width: 100%;
  height: 100%; /* Or a fixed height like 500px */
  background-color: #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.knowledge-graph-container-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #777;
  font-style: italic;
}

/* React Flow specific overrides if needed */
.react-flow__node {
  font-size: 12px;
  padding: 10px;
  border-radius: 5px;
}

.react-flow__node-input {
  /* Styles for the central/input node */
  background: #eef2ff !important; /* Light blue background */
  border-color: #6366f1 !important; /* Indigo border */
  color: #312e81 !important; /* Dark indigo text */
  font-weight: bold;
}

.react-flow__node-default {
  /* Styles for other nodes */
  background: #ffffff !important;
  border-color: #cbd5e1 !important; /* Cool gray border */
  color: #334155 !important; /* Slate text */
}

.react-flow__edge-path {
  stroke: #6366f1; /* Indigo edge color */
  stroke-width: 1.5;
}

.react-flow__controls {
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.react-flow__minimap {
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}