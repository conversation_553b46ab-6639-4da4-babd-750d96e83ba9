import React, { useState, useEffect, useCallback } from 'react';
import { sendMessageToBackground } from './KnowledgeBaseView'; // Re-use the helper

interface CaptureSettings {
  captureMode: string;
  contentFormat: string;
}

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<CaptureSettings>({ captureMode: '', contentFormat: '' });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  const fetchSettings = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await sendMessageToBackground<{ success: boolean, settings?: CaptureSettings, error?: string }>({ action: 'getCaptureSettings' });
      if (response.success && response.settings) {
        setSettings(response.settings);
      } else {
        throw new Error(response.error || 'Failed to fetch settings.');
      }
    } catch (err: any) {
      console.error('Failed to fetch settings:', err);
      setError(err.message || 'Failed to load settings.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  const handleInputChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setSaveMessage(null);
    try {
      const response = await sendMessageToBackground<{ success: boolean, error?: string }>({ action: 'saveCaptureSettings', data: settings });
      if (response.success) {
        setSaveMessage('Settings saved successfully!');
      } else {
        throw new Error(response.error || 'Failed to save settings.');
      }
    } catch (err: any) {
      console.error('Failed to save settings:', err);
      setSaveMessage(`Failed to save settings: ${err.message}`);
      setError(err.message || 'Failed to save settings.');
    } finally {
      setIsSaving(false);
      setTimeout(() => setSaveMessage(null), 3000); // Clear message after 3 seconds
    }
  };

  if (isLoading) {
    return <div className="p-4 text-gray-500">Loading settings...</div>;
  }

  if (error) {
    return <div className="p-4 text-red-500">Error: {error}</div>;
  }

  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">Capture Settings</h2>
      <form onSubmit={handleSaveSettings} className="space-y-6">
        <div>
          <label htmlFor="captureMode" className="block text-sm font-medium text-gray-700">Default Capture Mode</label>
          <select
            id="captureMode"
            name="captureMode"
            value={settings.captureMode}
            onChange={handleInputChange}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="fullPage">Full Page</option>
            <option value="selection">Selection</option>
            <option value="visible">Visible Content</option>
          </select>
        </div>
        <div>
          <label htmlFor="contentFormat" className="block text-sm font-medium text-gray-700">Preferred Content Format</label>
          <select
            id="contentFormat"
            name="contentFormat"
            value={settings.contentFormat}
            onChange={handleInputChange}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="markdown">Markdown</option>
            <option value="html">HTML</option>
            <option value="text">Plain Text</option>
          </select>
        </div>
        <button
          type="submit"
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Settings'}
        </button>
        {saveMessage && (
          <p className={`mt-2 text-sm ${saveMessage.includes('successfully') ? 'text-green-600' : 'text-red-600'}`}>
            {saveMessage}
          </p>
        )}
      </form>
    </div>
  );
};

export default SettingsPage;