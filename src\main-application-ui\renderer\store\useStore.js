import { createStore } from 'zustand/vanilla'; // Changed: aliased create<PERSON><PERSON><PERSON> to create, now createStore
import { useStore as useZustandReactHook } from 'zustand'; // Changed: Try importing useStore from the main 'zustand' package
import {
  searchItems as apiSearchItems,
  askQuestion as apiAskQuestion,
  summarizeItem as apiSummarizeItem,
  getConceptualLinks as apiGetConceptualLinks,
  transformContent as apiTransformContent, // Added for content transformation
  createItem as apiCreateItem, // Added for saving new item
  getManualLinks as apiGetManualLinks,
  addManualLink as apiAddManualLink,
  removeManualLink as apiRemoveManualLink,
  // Placeholders for Tag and Category API functions - will be added to client.js later
  fetchTags as apiFetchTags,
  createTag as apiCreateTag,
  updateTag as apiUpdateTag,
  deleteTag as apiDeleteTag,
  fetchCategories as apiFetchCategories,
  createCategory as apiCreateCategory,
  updateCategory as apiUpdate<PERSON>ategory,
  deleteCategory as apiDeleteCategory,
  getCaptureSettings as apiGetCaptureSettings,
  putCaptureSettings as apiPutCaptureSettings,
  fetchClippingTemplates as apiFetchClippingTemplates,
  createClippingTemplate as apiCreateClippingTemplate,
  updateClippingTemplate as apiUpdateClippingTemplate,
  deleteClippingTemplate as apiDeleteClippingTemplate,
  setDefaultClippingTemplate as apiSetDefaultClippingTemplate,
  // Data Management API functions (to be added to client.js)
  triggerExport as apiTriggerExport,
  triggerImport as apiTriggerImport,
} from '../api/client';

// Define a basic store
const store = createStore((set, get) => ({ // Changed: use createStore (originally createVanilla, then create)
  // Example state: a list of items
  items: [
    { id: 1, name: 'Sample Item 1', description: 'This is the first sample item.' },
    { id: 2, name: 'Sample Item 2', description: 'Another item for demonstration.' },
    { id: 3, name: 'Sample Item 3', description: 'Yet another placeholder.' },
  ],
  selectedItemId: null,

  // Example actions
  addItem: (item) => set((state) => ({ items: [...state.items, item] })),
  removeItem: (itemId) => set((state) => ({ items: state.items.filter(item => item.id !== itemId) })),
  selectItem: (itemId) => {
    set({ selectedItemId: itemId });
    // When a new item is selected, fetch its conceptual links
    // Also, clear any existing links from a previously selected item
    get().clearConceptualLinks(); // Clear previous conceptual links first
    get().clearManualLinks(); // Clear previous manual links first
    if (itemId) {
      get().fetchConceptualLinks(itemId);
      get().fetchManualLinks(itemId);
    }
  },
  clearSelectedItem: () => {
    set({ selectedItemId: null });
    get().clearConceptualLinks(); // Clear conceptual links when item is cleared
    get().clearManualLinks(); // Clear manual links when item is cleared
  },

  // Conceptual Links State
  conceptualLinks: [],
  conceptualLinksLoading: false,
  conceptualLinksError: null,

  // Manual Links State
  manualLinks: [],
  manualLinksLoading: false,
  manualLinksError: null,
  manualLinkCreating: false,
  manualLinkCreateError: null,
  manualLinkDeleting: false,
  manualLinkDeleteError: null,

  // Search state
  searchTerm: '',
  searchResults: [],
  searchLoading: false,
  searchError: null,

  // Advanced Filter States
  // Note: allTags and allCategories below are for *filtering*.
  // We will add separate state for *managing* all available tags/categories.
  filterTags: ['tech', 'productivity', 'react', 'javascript', 'ai'], // Renamed from allTags to avoid conflict
  selectedTags: [],
  dateRangeFilter: { startDate: null, endDate: null, selectedDateOption: '' },
  allContentTypes: ['article', 'bookmark', 'pdf', 'selection', 'note'], // Example types
  selectedContentTypes: [],
  filterCategories: ['work', 'personal', 'learning', 'research'], // Renamed from allCategories
  selectedCategories: '', // Changed to single string for selected category
  sourceFilter: '',
  tagFilterLogic: 'AND', // Default to AND logic
  sortCriteria: { by: 'date', order: 'desc' }, // New state for sort criteria

  // Tag Management State
  allTags: [], // For managing the complete list of tags
  tagOperationLoading: false,
  tagOperationError: null,

  // Category Management State
  allCategories: [], // For managing the complete list of categories
  categoryOperationLoading: false,
  categoryOperationError: null,

  // Capture Settings State
  captureSettings: {
    defaultFormat: 'markdown', // e.g., 'markdown', 'html', 'text'
    extractTitle: true,
    extractUrl: true,
    extractPublicationDate: true,
    enableAiAutoTagging: false,
  },
  captureSettingsLoading: false,
  captureSettingsError: null,
  captureSettingsSaving: false,
  captureSettingsSaveError: null,

  // Clipping Templates State
  clippingTemplates: [],
  clippingTemplatesLoading: false,
  clippingTemplatesError: null,
  clippingTemplateCreating: false,
  clippingTemplateCreateError: null,
  clippingTemplateUpdating: false,
  clippingTemplateUpdateError: null,
  clippingTemplateDeleting: false,
  clippingTemplateDeleteError: null,
  clippingTemplateSettingDefault: false,
  clippingTemplateSetDefaultError: null,

  // Data Management State
  exportInProgress: false,
  exportError: null,
  importInProgress: false,
  importError: null,

  setSearchTerm: (term) => set({ searchTerm: term, searchError: null }), // Clear error when term changes

  // Offline Status State
  isOnline: navigator.onLine, // Initial status

  // Action to check and update online status
  checkOnlineStatus: () => {
    set({ isOnline: navigator.onLine });
  },

  // Actions for Advanced Filters
  setFilterTags: (tags) => set({ filterTags: tags }), // Renamed from setAllTags
  setSelectedTags: (tags) => set({ selectedTags: tags }),
  setSelectedDateFilter: (option) => {
    let startDate = null;
    let endDate = null;
    const now = new Date();
    if (option === 'today') {
      startDate = new Date(now.setHours(0, 0, 0, 0)).toISOString();
      endDate = new Date(now.setHours(23, 59, 59, 999)).toISOString();
    } else if (option === 'last7days') {
      startDate = new Date(now.setDate(now.getDate() - 7)).toISOString();
      endDate = new Date().toISOString();
    } else if (option === 'last30days') {
      startDate = new Date(now.setDate(now.getDate() - 30)).toISOString();
      endDate = new Date().toISOString();
    }
    set({ dateRangeFilter: { startDate, endDate, selectedDateOption: option } });
  },
  setAllContentTypes: (types) => set({ allContentTypes: types }),
  setSelectedContentTypes: (types) => set({ selectedContentTypes: types }),
  setFilterCategories: (categories) => set({ filterCategories: categories }), // Renamed from setAllCategories
  setSelectedCategories: (category) => set({ selectedCategories: category }), // Changed to single string
  setSelectedSourceFilter: (source) => set({ sourceFilter: source }),
  setTagFilterLogic: (logic) => set({ tagFilterLogic: logic }),
  setSortCriteria: (criteria) => set({ sortCriteria: criteria }), // New action for sort criteria

  clearAdvancedFilters: () => set({
    selectedTags: [],
    dateRangeFilter: { startDate: null, endDate: null, selectedDateOption: '' },
    selectedContentTypes: [],
    selectedCategories: '',
    sourceFilter: '',
    tagFilterLogic: 'AND',
    sortCriteria: { by: 'date', order: 'desc' }, // Reset sort as well
  }),

  performSearch: async (query) => {
    const { selectedTags, dateRangeFilter, selectedContentTypes, selectedCategories, sourceFilter, tagFilterLogic, sortCriteria } = get();
    // If query is empty and no filters are active, clear results.
    // Otherwise, even with an empty query, if filters are active, we should fetch.
    const noActiveFilters =
      selectedTags.length === 0 &&
      !dateRangeFilter.startDate &&
      !dateRangeFilter.endDate &&
      selectedContentTypes.length === 0 &&
      (!selectedCategories || selectedCategories.trim() === '') && // Check for empty string
      (!sourceFilter || sourceFilter.trim() === '');

    if ((!query || query.trim() === '') && noActiveFilters) {
      set({ searchResults: [], searchTerm: query || '', searchLoading: false, searchError: null });
      return;
    }

    set({ searchLoading: true, searchError: null, searchTerm: query || '' });
    try {
      const filterParams = {};
      if (selectedTags.length > 0) {
        filterParams.tags = selectedTags.join(','); // Assuming backend expects comma-separated string
        filterParams.tagLogic = tagFilterLogic; // Add tag logic
      }
      if (dateRangeFilter.startDate) {
        filterParams.createdAfter = dateRangeFilter.startDate;
      }
      if (dateRangeFilter.endDate) {
        filterParams.createdBefore = dateRangeFilter.endDate;
      }
      if (selectedContentTypes.length > 0) {
        filterParams.contentTypes = selectedContentTypes.join(','); // Assuming backend expects comma-separated string
      }
      if (selectedCategories && selectedCategories.trim() !== '') { // Check for empty string
        filterParams.category = selectedCategories; // Assuming backend expects single category string
      }
      if (sourceFilter && sourceFilter.trim() !== '') {
        filterParams.source = sourceFilter.trim();
      }
      // Add sort parameters
      filterParams.sortBy = sortCriteria.by;
      filterParams.sortOrder = sortCriteria.order;

      // Convert date range filter to ISO strings if they are Date objects
      if (dateRangeFilter.startDate instanceof Date) {
        filterParams.createdAfter = dateRangeFilter.startDate.toISOString();
      }
      if (dateRangeFilter.endDate instanceof Date) {
        filterParams.createdBefore = dateRangeFilter.endDate.toISOString();
      }

      // Pass both query and filterParams to apiSearchItems
      // This assumes apiSearchItems will be updated to handle an object with query and other filter keys
      const results = await apiSearchItems({ query: query || '', ...filterParams });
      set({ searchResults: results || [], searchLoading: false });
    } catch (error) {
      console.error('Search failed:', error);
      set({ searchResults: [], searchLoading: false, searchError: error.message || 'Failed to perform search.' });
    }
  },

  clearSearch: () => set((state) => {
    // Also clear advanced filters when clearing search, or decide if they should persist
    // For now, let's keep advanced filters and only clear the search term and results.
    // If a full reset is needed, clearAdvancedFilters can be called separately or integrated here.
    return { searchTerm: '', searchResults: [], searchLoading: false, searchError: null };
  }),

  // Q&A State
  qaQuestion: '',
  qaAnswer: null,
  qaLoading: false,
  qaError: null,

  setQaQuestion: (question) => set({ qaQuestion: question, qaError: null }),

  submitQaQuestion: async (itemId, question) => {
    if (!itemId || !question || question.trim() === '') {
      set({ qaAnswer: null, qaLoading: false, qaError: 'Item ID and question are required.' });
      return;
    }
    set({ qaLoading: true, qaError: null, qaAnswer: null });
    try {
      const answer = await apiAskQuestion(itemId, question);
      set({ qaAnswer: answer, qaLoading: false });
    } catch (error) {
      console.error('Q&A failed:', error);
      set({ qaAnswer: null, qaLoading: false, qaError: error.message || 'Failed to get an answer.' });
    }
  },

  clearQa: () => set({ qaQuestion: '', qaAnswer: null, qaLoading: false, qaError: null }),

  // Summarization State
  summary: null,
  summaryLoading: false,
  summaryError: null,
  summaryGenerated: false,

  fetchSummary: async (itemId) => {
    if (!itemId) {
      set({ summary: null, summaryLoading: false, summaryError: 'Item ID is required for summarization.', summaryGenerated: false });
      return;
    }
    set({ summaryLoading: true, summaryError: null, summary: null, summaryGenerated: false });
    try {
      const summaryText = await apiSummarizeItem(itemId);
      set({ summary: summaryText, summaryLoading: false, summaryGenerated: true });
    } catch (error) {
      console.error('Summarization failed:', error);
      set({ summary: null, summaryLoading: false, summaryError: error.message || 'Failed to generate summary.', summaryGenerated: false });
    }
  },

  clearSummary: () => set({ summary: null, summaryLoading: false, summaryError: null, summaryGenerated: false }),

  // Conceptual Links Actions
  fetchConceptualLinks: async (itemId) => {
    if (!itemId) {
      set({ conceptualLinks: [], conceptualLinksLoading: false, conceptualLinksError: 'Item ID is required to fetch conceptual links.' });
      return;
    }
    set({ conceptualLinksLoading: true, conceptualLinksError: null });
    try {
      const links = await apiGetConceptualLinks(itemId);
      set({ conceptualLinks: links || [], conceptualLinksLoading: false });
    } catch (error) {
      console.error('Fetching conceptual links failed:', error);
      set({ conceptualLinks: [], conceptualLinksLoading: false, conceptualLinksError: error.message || 'Failed to fetch conceptual links.' });
    }
  },

  clearConceptualLinks: () => set({
    conceptualLinks: [],
    conceptualLinksLoading: false,
    conceptualLinksError: null,
  }),

  // Manual Links Actions
  fetchManualLinks: async (itemId) => {
    if (!itemId) {
      set({ manualLinks: [], manualLinksLoading: false, manualLinksError: 'Item ID is required to fetch manual links.' });
      return;
    }
    set({ manualLinksLoading: true, manualLinksError: null });
    try {
      const links = await apiGetManualLinks(itemId);
      set({ manualLinks: links || [], manualLinksLoading: false });
    } catch (error) {
      console.error('Fetching manual links failed:', error);
      set({ manualLinks: [], manualLinksLoading: false, manualLinksError: error.message || 'Failed to fetch manual links.' });
    }
  },

  createManualLink: async (sourceItemId, targetItemId, linkType, description) => {
    if (!sourceItemId || !targetItemId) {
      set({ manualLinkCreating: false, manualLinkCreateError: 'Source and Target Item IDs are required.' });
      return;
    }
    set({ manualLinkCreating: true, manualLinkCreateError: null });
    try {
      const newLink = await apiAddManualLink(sourceItemId, { targetItemId, linkType, description });
      set((state) => ({
        manualLinks: [...state.manualLinks, newLink], // Assuming the API returns the created link
        manualLinkCreating: false,
      }));
      // Optionally, re-fetch all manual links to ensure consistency if the API response is minimal
      // get().fetchManualLinks(sourceItemId);
    } catch (error) {
      console.error('Creating manual link failed:', error);
      set({ manualLinkCreating: false, manualLinkCreateError: error.message || 'Failed to create manual link.' });
      throw error;
    }
  },

  deleteManualLink: async (sourceItemId, linkId) => {
    if (!sourceItemId || !linkId) {
      set({ manualLinkDeleting: false, manualLinkDeleteError: 'Source Item ID and Link ID are required.' });
      return;
    }
    set({ manualLinkDeleting: true, manualLinkDeleteError: null });
    try {
      await apiRemoveManualLink(sourceItemId, linkId);
      set((state) => ({
        manualLinks: state.manualLinks.filter(link => link.linkId !== linkId), // Assuming link object has linkId
        manualLinkDeleting: false,
      }));
    } catch (error) {
      console.error('Deleting manual link failed:', error);
      set({ manualLinkDeleting: false, manualLinkDeleteError: error.message || 'Failed to delete manual link.' });
      throw error;
    }
  },

  clearManualLinks: () => set({
    manualLinks: [],
    manualLinksLoading: false,
    manualLinksError: null,
  }),

  clearManualLinkStatus: () => set({ // Action to clear create/delete status/errors
    manualLinkCreating: false,
    manualLinkCreateError: null,
    manualLinkDeleting: false,
    manualLinkDeleteError: null,
  }),

  // Content Transformation State
  selectedTransformationType: null,
  transformedContent: null,
  transformationLoading: false,
  transformationError: null,

// Content Transformation Actions
  setSelectedTransformationType: (type) => set({ selectedTransformationType: type, transformationError: null, transformedContent: null }),

  transformItemContent: async (itemId, transformationType, selectedText = null) => {
    if (!itemId || !transformationType) {
      set({
        transformedContent: null,
        transformationLoading: false,
        transformationError: 'Item ID and transformation type are required.',
      });
      return;
    }
    set({ transformationLoading: true, transformationError: null, transformedContent: null });
    try {
      // Ensure the transformationType from the component (e.g., "Rephrase")
      // matches what the API expects (e.g., "rephrase").
      // This might involve a mapping or ensuring consistency.
      // For now, we assume they are consistent or the API handles various casings.
      // Pass selectedText to the API if available, otherwise the API defaults to full content.
      const result = await apiTransformContent(itemId, transformationType, selectedText);
      set({ transformedContent: result.transformedContent, transformationLoading: false }); // Assuming API returns { transformedContent: "..." }
    } catch (error) {
      console.error('Content transformation failed:', error);
      set({
        transformedContent: null,
        transformationLoading: false,
        transformationError: error.message || 'Failed to transform content.',
      });
    }
  },

  clearTransformationState: () => set({
    selectedTransformationType: null,
    transformedContent: null,
    transformationLoading: false,
    transformationError: null,
  }),

  // State for saving transformed content as new item
  savingNewItemLoading: false,
  savingNewItemError: null,

  // Action to save transformed content as a new item
  saveTransformedContentAsNewItem: async () => {
    const { transformedContent, selectedItemId, items, clearTransformationState } = get();

    if (!transformedContent) {
      set({ savingNewItemError: 'No transformed content to save.', savingNewItemLoading: false });
      return;
    }

    const originalItem = items.find(item => item.id === selectedItemId);
    let title = 'Transformed Content';
    let tags = [];

    if (originalItem) {
      title = `Transformed: ${originalItem.name || originalItem.title || 'Untitled'}`;
      tags = originalItem.tags || []; // Assuming items might have tags
    }

    const newItemData = {
      title,
      content: transformedContent, // Assuming backend expects 'content'
      tags,
      // Potentially copy other relevant metadata from originalItem if needed
      // e.g., category, sourceURL if applicable
      source: originalItem?.source || 'Transformed', // Add a source
      itemType: originalItem?.itemType || 'note', // Default or copy item type
    };

    set({ savingNewItemLoading: true, savingNewItemError: null });

    try {
      const createdItem = await apiCreateItem(newItemData);
      set((state) => ({
        savingNewItemLoading: false,
        // Optionally, add to items list and select it
        // items: [...state.items, createdItem],
        // selectedItemId: createdItem.id,
        // For now, just clear transformation state on success
      }));
      clearTransformationState(); // Clear transformed content after saving
      // Potentially call get().performSearch('') to refresh the item list if it's displayed via searchResults
      // Or a dedicated fetchItems action if that's how the main list is populated.
      // For this task, success/error state handling is sufficient.
      console.log('New item created successfully:', createdItem);
      // Consider adding a success message/toast for the user
    } catch (error) {
      console.error('Failed to save new item:', error);
      set({
        savingNewItemLoading: false,
        savingNewItemError: error.message || 'Failed to save new item.',
      });
    }
  },

  clearSaveNewItemStatus: () => set({ // Action to clear save status if user dismisses an error message
    savingNewItemLoading: false,
    savingNewItemError: null,
  }),

  // --- Tag Management Actions ---
  fetchTags: async () => {
    set({ tagOperationLoading: true, tagOperationError: null });
    try {
      const tags = await apiFetchTags();
      set({ allTags: tags || [], tagOperationLoading: false });
    } catch (error) {
      console.error('Fetching tags failed:', error);
      set({ allTags: [], tagOperationLoading: false, tagOperationError: error.message || 'Failed to fetch tags.' });
    }
  },

  createTag: async (name) => {
    set({ tagOperationLoading: true, tagOperationError: null });
    try {
      const newTag = await apiCreateTag({ name });
      set((state) => ({
        allTags: [...state.allTags, newTag],
        tagOperationLoading: false,
      }));
      return newTag;
    } catch (error) {
      console.error('Creating tag failed:', error);
      set({ tagOperationLoading: false, tagOperationError: error.message || 'Failed to create tag.' });
      throw error;
    }
  },

  updateTag: async (tagId, newName) => {
    set({ tagOperationLoading: true, tagOperationError: null });
    try {
      const updatedTag = await apiUpdateTag(tagId, { name: newName });
      set((state) => ({
        allTags: state.allTags.map(tag => tag.id === tagId ? updatedTag : tag),
        tagOperationLoading: false,
      }));
      return updatedTag;
    } catch (error) {
      console.error('Updating tag failed:', error);
      set({ tagOperationLoading: false, tagOperationError: error.message || 'Failed to update tag.' });
      throw error;
    }
  },

  deleteTag: async (tagId) => {
    set({ tagOperationLoading: true, tagOperationError: null });
    try {
      await apiDeleteTag(tagId);
      set((state) => ({
        allTags: state.allTags.filter(tag => tag.id !== tagId),
        tagOperationLoading: false,
      }));
    } catch (error) {
      console.error('Deleting tag failed:', error);
      set({ tagOperationLoading: false, tagOperationError: error.message || 'Failed to delete tag.' });
      throw error;
    }
  },

  // --- Category Management Actions ---
  fetchCategories: async () => {
    set({ categoryOperationLoading: true, categoryOperationError: null });
    try {
      const categories = await apiFetchCategories();
      set({ allCategories: categories || [], categoryOperationLoading: false });
    } catch (error) {
      console.error('Fetching categories failed:', error);
      set({ allCategories: [], categoryOperationLoading: false, categoryOperationError: error.message || 'Failed to fetch categories.' });
    }
  },

  createCategory: async (name) => {
    set({ categoryOperationLoading: true, categoryOperationError: null });
    try {
      const newCategory = await apiCreateCategory({ name });
      set((state) => ({
        allCategories: [...state.allCategories, newCategory],
        categoryOperationLoading: false,
      }));
      return newCategory;
    } catch (error) {
      console.error('Creating category failed:', error);
      set({ categoryOperationLoading: false, categoryOperationError: error.message || 'Failed to create category.' });
      throw error;
    }
  },

  updateCategory: async (categoryId, newName) => {
    set({ categoryOperationLoading: true, categoryOperationError: null });
    try {
      const updatedCategory = await apiUpdateCategory(categoryId, { name: newName });
      set((state) => ({
        allCategories: state.allCategories.map(cat => cat.id === categoryId ? updatedCategory : cat),
        categoryOperationLoading: false,
      }));
      return updatedCategory;
    } catch (error) {
      console.error('Updating category failed:', error);
      set({ categoryOperationLoading: false, categoryOperationError: error.message || 'Failed to update category.' });
      throw error;
    }
  },

  deleteCategory: async (categoryId) => {
    set({ categoryOperationLoading: true, categoryOperationError: null });
    try {
      await apiDeleteCategory(categoryId);
      set((state) => ({
        allCategories: state.allCategories.filter(cat => cat.id !== categoryId),
        categoryOperationLoading: false,
      }));
    } catch (error) {
      console.error('Deleting category failed:', error);
      set({ categoryOperationLoading: false, categoryOperationError: error.message || 'Failed to delete category.' });
      throw error;
    }
  },

  // --- Capture Settings Actions ---
  fetchCaptureSettings: async () => {
    set({ captureSettingsLoading: true, captureSettingsError: null });
    try {
      const settings = await apiGetCaptureSettings();
      set({
        captureSettings: settings || get().captureSettings, // Fallback to current/default if API returns null
        captureSettingsLoading: false,
      });
    } catch (error) {
      console.error('Fetching capture settings failed:', error);
      set({
        captureSettingsLoading: false,
        captureSettingsError: error.message || 'Failed to fetch capture settings.',
      });
    }
  },

  updateCaptureSetting: (settingKey, value) => {
    set((state) => ({
      captureSettings: {
        ...state.captureSettings,
        [settingKey]: value,
      },
    }));
  },

  saveCaptureSettings: async (settingsObject) => {
    // If settingsObject is not provided, use the current state.
    // This allows calling it directly from a button click without passing the whole object from the component.
    const settingsToSave = settingsObject || get().captureSettings;
    set({ captureSettingsSaving: true, captureSettingsSaveError: null });
    try {
      const updatedSettings = await apiPutCaptureSettings(settingsToSave);
      set({
        captureSettings: updatedSettings || settingsToSave, // Use returned settings or fallback
        captureSettingsSaving: false,
      });
      // Optionally, show a success message to the user
    } catch (error) {
      console.error('Saving capture settings failed:', error);
      set({
        captureSettingsSaving: false,
        captureSettingsSaveError: error.message || 'Failed to save capture settings.',
      });
      // Optionally, re-throw error if the component needs to react to it
      // throw error;
    }
  },

  clearCaptureSettingsStatus: () => set({ // Action to clear save/load status/errors
    captureSettingsLoading: false,
    captureSettingsError: null,
    captureSettingsSaving: false,
    captureSettingsSaveError: null,
  }),

  // --- Clipping Template Actions ---
  fetchClippingTemplates: async () => {
    set({ clippingTemplatesLoading: true, clippingTemplatesError: null });
    try {
      const templates = await apiFetchClippingTemplates();
      set({ clippingTemplates: templates || [], clippingTemplatesLoading: false });
    } catch (error) {
      console.error('Fetching clipping templates failed:', error);
      set({ clippingTemplates: [], clippingTemplatesLoading: false, clippingTemplatesError: error.message || 'Failed to fetch clipping templates.' });
    }
  },

  createClippingTemplate: async (templateData) => {
    set({ clippingTemplateCreating: true, clippingTemplateCreateError: null });
    try {
      const newTemplate = await apiCreateClippingTemplate(templateData);
      set((state) => ({
        clippingTemplates: [...state.clippingTemplates, newTemplate],
        clippingTemplateCreating: false,
      }));
      // If the new template is set as default, update other templates
      if (newTemplate.isDefault) {
        set((state) => ({
          clippingTemplates: state.clippingTemplates.map(t => t.id === newTemplate.id ? newTemplate : {...t, isDefault: false })
        }));
      }
      return newTemplate;
    } catch (error) {
      console.error('Creating clipping template failed:', error);
      set({ clippingTemplateCreating: false, clippingTemplateCreateError: error.message || 'Failed to create clipping template.' });
      throw error;
    }
  },

  updateClippingTemplate: async (templateId, templateData) => {
    set({ clippingTemplateUpdating: true, clippingTemplateUpdateError: null });
    try {
      const updatedTemplate = await apiUpdateClippingTemplate(templateId, templateData);
      set((state) => ({
        clippingTemplates: state.clippingTemplates.map(t => t.id === templateId ? updatedTemplate : t),
        clippingTemplateUpdating: false,
      }));
       // If this template is set as default, ensure others are not
      if (updatedTemplate.isDefault) {
        set((state) => ({
          clippingTemplates: state.clippingTemplates.map(t => t.id === updatedTemplate.id ? updatedTemplate : {...t, isDefault: false })
        }));
      }
      return updatedTemplate;
    } catch (error) {
      console.error('Updating clipping template failed:', error);
      set({ clippingTemplateUpdating: false, clippingTemplateUpdateError: error.message || 'Failed to update clipping template.' });
      throw error;
    }
  },

  deleteClippingTemplate: async (templateId) => {
    set({ clippingTemplateDeleting: true, clippingTemplateDeleteError: null });
    try {
      await apiDeleteClippingTemplate(templateId);
      set((state) => ({
        clippingTemplates: state.clippingTemplates.filter(t => t.id !== templateId),
        clippingTemplateDeleting: false,
      }));
    } catch (error) {
      console.error('Deleting clipping template failed:', error);
      set({ clippingTemplateDeleting: false, clippingTemplateDeleteError: error.message || 'Failed to delete clipping template.' });
      throw error;
    }
  },

  setDefaultClippingTemplate: async (templateId) => {
    set({ clippingTemplateSettingDefault: true, clippingTemplateSetDefaultError: null });
    try {
      // The API might return the updated template or just a success status.
      // We'll assume it handles setting other templates' isDefault to false.
      // For robustness, we'll update the local state to reflect the change.
      await apiSetDefaultClippingTemplate(templateId);
      set((state) => ({
        clippingTemplates: state.clippingTemplates.map(t => ({
          ...t,
          isDefault: t.id === templateId,
        })),
        clippingTemplateSettingDefault: false,
      }));
    } catch (error) {
      console.error('Setting default clipping template failed:', error);
      set({ clippingTemplateSettingDefault: false, clippingTemplateSetDefaultError: error.message || 'Failed to set default template.' });
      throw error;
    }
  },

  clearClippingTemplateStatus: () => set({
    clippingTemplatesLoading: false,
    clippingTemplatesError: null,
    clippingTemplateCreating: false,
    clippingTemplateCreateError: null,
    clippingTemplateUpdating: false,
    clippingTemplateUpdateError: null,
    clippingTemplateDeleting: false,
    clippingTemplateDeleteError: null,
    clippingTemplateSettingDefault: false,
    clippingTemplateSetDefaultError: null,
    }),
  
    // --- Data Management Actions ---
    exportData: async () => {
      set({ exportInProgress: true, exportError: null });
      try {
        // Assuming 'window.electron.ipcRenderer.invoke' is set up in preload.js
        const filePath = await window.electron.ipcRenderer.invoke('show-save-dialog', {
          title: 'Export Knowledge Base',
          defaultPath: 'knowledge-base-export.zip', // Or whatever format is used
          filters: [
            { name: 'ZIP Archives', extensions: ['zip'] }, // Adjust as per actual export format
            { name: 'JSON Files', extensions: ['json'] },
            { name: 'SQLite Databases', extensions: ['sqlite', 'db'] },
            { name: 'All Files', extensions: ['*'] },
          ],
        });
  
        if (filePath) {
          // The API call might need the filePath if the main process doesn't handle saving directly
          // For now, assume triggerExport initiates a process that results in a download or uses the path.
          // If the backend streams the file, the main process might handle writing it to filePath.
          // This part depends heavily on the backend/main process export implementation.
          await apiTriggerExport(filePath); // Pass filePath if API needs it
          set({ exportInProgress: false });
          // User will be notified by the component or a global notification system
        } else {
          // User cancelled the save dialog
          set({ exportInProgress: false });
        }
      } catch (error) {
        console.error('Export data failed:', error);
        set({ exportInProgress: false, exportError: error.message || 'Failed to export data.' });
        throw error; // Re-throw for the component to catch
      }
    },
  
     importData: async () => {
       set({ importInProgress: true, importError: null });
       try {
         const filePaths = await window.electron.ipcRenderer.invoke('show-open-dialog', {
           title: 'Import Knowledge Base',
           properties: ['openFile'],
           filters: [
             { name: 'Supported Formats', extensions: ['zip', 'json', 'sqlite', 'db'] }, // Adjust as per import capabilities
             { name: 'All Files', extensions: ['*'] },
           ],
         });
   
         if (filePaths && filePaths.length > 0) {
           const filePath = filePaths[0];
           // Simple confirmation for overwrite. A more sophisticated UI could be used.
           const confirmed = window.confirm('Importing will replace existing data. Are you sure you want to proceed?');
           if (confirmed) {
             await apiTriggerImport(filePath);
             set({ importInProgress: false });
             // User will be notified by the component or a global notification system
             // Potentially trigger a data refresh or app reload if necessary
           } else {
             set({ importInProgress: false }); // User cancelled confirmation
           }
         } else {
           // User cancelled the open dialog
           set({ importInProgress: false });
         }
       } catch (error) {
         console.error('Import data failed:', error);
         set({ importInProgress: false, importError: error.message || 'Failed to import data.' });
         throw error; // Re-throw for the component to catch
       }
     },
   
     clearDataManagementStatus: () => set({
       exportInProgress: false,
       exportError: null,
       importInProgress: false,
       importError: null,
     }),
   
   }));
 
 // Export the hook to be used in components
 const useStoreHook = (selector) => useZustandReactHook(store, selector); // Changed: use useStore (originally useZustandStore) and renamed to useStoreHook to avoid conflict with import
 
 export default useStoreHook; // Changed: export useStoreHook
 // Export the store instance itself if needed for non-React usage or direct manipulation
 export { store };