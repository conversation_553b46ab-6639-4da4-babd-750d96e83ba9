/**
 * UI Layer Entry Point for Knowledge Base Interaction & Insights Module
 * 
 * This file exports the main components and views that make up the UI 
 * for this module, excluding Content Summarization UI components.
 * 
 * AI-verifiable: This index file serves as a central export point for the module's UI.
 */

// Components
export { default as SearchResultItem } from './components/SearchResultItem';
export { default as QAResultDisplay } from './components/QAResultDisplay';
export { default as ConceptualLinkNode } from './components/ConceptualLinkNode';
export { default as ContentBrowserItem } from './components/ContentBrowserItem';
export { default as TransformedContentView } from './components/TransformedContentView';

// Views
export { default as KnowledgeBaseExplorerView } from './views/KnowledgeBaseExplorerView';
export { default as SearchResultsView } from './views/SearchResultsView';
export { default as QASessionView } from './views/QASessionView';
export { default as ConceptualLinksGraphView } from './views/ConceptualLinksGraphView';
export { default as ContentViewerView } from './views/ContentViewerView';

// Hooks (example, if they are meant to be part of the public API of this UI module)
// export { default as useSearch } from './hooks/useSearch';

// Services and Utils are typically not exported from the UI module's main index
// as they are often used internally or imported directly where needed.
// If specific services are intended for broader use, they could be exported here.

// AI-verifiable: Existence of exports for key UI elements.