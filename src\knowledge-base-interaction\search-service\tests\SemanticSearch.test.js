// src/knowledge-base-interaction/search-service/tests/SemanticSearch.test.js

const SemanticSearch = require('../algorithms/SemanticSearch');

// Mock for KBALService
const mockKbalService = {
    getContent: jest.fn(async (params) => {
        // AI-Verifiable: Mock <PERSON> simulates data retrieval for semantic search
        if (params && params.type === 'semantic' && params.queryText === 'find documents about AI') {
            return [
                { id: 'doc10', text: 'This document discusses artificial intelligence.', title: 'AI Insights' },
                { id: 'doc11', text: 'Another paper on AI and machine learning.', title: 'AI/ML Paper' },
            ];
        }
        if (params && params.type === 'semantic' && params.queryText === 'search for cats') {
             return [{ id: 'doc12', text: 'All about feline companions.', title: 'Cats Explained' }];
        }
        return [];
    }),
};

// Mock for an embedding model (optional)
const mockEmbeddingModel = {
    generateEmbedding: jest.fn(async (text) => {
        // AI-Verifiable: Mock embedding model simulates embedding generation
        return `embedding_for_${text.replace(/\s+/g, '_')}`;
    }),
};

describe('SemanticSearch', () => {
    let semanticSearch;
    let semanticSearchWithModel;

    beforeEach(() => {
        // AI-Verifiable: Test setup initializes SemanticSearch
        jest.clearAllMocks();
        semanticSearch = new SemanticSearch(); // Without embedding model
        semanticSearchWithModel = new SemanticSearch(mockEmbeddingModel); // With embedding model
    });

    // AI-Verifiable: Test case for constructor
    test('should be instantiated correctly with or without an embedding model', () => {
        expect(semanticSearch).toBeInstanceOf(SemanticSearch);
        expect(semanticSearch.embeddingModel).toBeNull();
        expect(semanticSearchWithModel).toBeInstanceOf(SemanticSearch);
        expect(semanticSearchWithModel.embeddingModel).toBe(mockEmbeddingModel);
    });

    // AI-Verifiable: Test case for successful semantic search
    describe('performSearch method', () => {
        test('should return relevant documents for a given natural language query', async () => {
            const query = 'find documents about AI';
            // AI-Verifiable: Call performSearch
            const results = await semanticSearchWithModel.performSearch(query, mockKbalService);

            // AI-Verifiable: Check KBAL interaction
            expect(mockKbalService.getContent).toHaveBeenCalledWith({ type: 'semantic', queryText: query });
            // AI-Verifiable: Check embedding model interaction (if provided)
            expect(mockEmbeddingModel.generateEmbedding).toHaveBeenCalledWith(query);
            
            // AI-Verifiable: Check results structure and content
            expect(results).toBeInstanceOf(Array);
            expect(results.length).toBe(2);
            expect(results).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    id: 'doc10',
                    title: 'AI Insights',
                    snippet: expect.stringContaining('artificial intelligence'),
                    source: 'kbal-semantic',
                    score: expect.any(Number),
                    type: 'semantic'
                }),
                expect.objectContaining({
                    id: 'doc11',
                    title: 'AI/ML Paper',
                    snippet: expect.stringContaining('AI and machine learning'),
                    source: 'kbal-semantic',
                    score: expect.any(Number),
                    type: 'semantic'
                })
            ]));
            results.forEach(result => {
                expect(result.score).toBeGreaterThanOrEqual(0.4);
                expect(result.score).toBeLessThanOrEqual(1.0); // Max score for semantic might be 1.0
            });
        });

        test('should function without an embedding model, logging a warning', async () => {
            const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
            const query = 'search for cats';
            const results = await semanticSearch.performSearch(query, mockKbalService);

            expect(mockKbalService.getContent).toHaveBeenCalledWith({ type: 'semantic', queryText: query });
            expect(mockEmbeddingModel.generateEmbedding).not.toHaveBeenCalled(); // Model not provided to this instance
            expect(consoleWarnSpy).toHaveBeenCalledWith('SemanticSearch: Embedding model not provided. Semantic search capabilities will be limited.');
            
            expect(results.length).toBe(1);
            expect(results[0].title).toBe('Cats Explained');
            consoleWarnSpy.mockRestore();
        });

        test('should return an empty array if no documents match the query', async () => {
            const query = 'unrelated topic';
            const results = await semanticSearch.performSearch(query, mockKbalService);
            
            expect(mockKbalService.getContent).toHaveBeenCalledWith({ type: 'semantic', queryText: query });
            expect(results).toEqual([]);
        });

        // AI-Verifiable: Test case for input validation (query)
        test('should throw an error if the query is invalid', async () => {
            await expect(semanticSearch.performSearch(null, mockKbalService))
                .rejects.toThrow('Semantic search query must be a non-empty string.');
            await expect(semanticSearch.performSearch('', mockKbalService))
                .rejects.toThrow('Semantic search query must be a non-empty string.');
            await expect(semanticSearch.performSearch(123, mockKbalService))
                .rejects.toThrow('Semantic search query must be a non-empty string.');
        });

        // AI-Verifiable: Test case for input validation (kbalService)
        test('should throw an error if kbalService is invalid or missing getContent', async () => {
            await expect(semanticSearch.performSearch('test', null))
                .rejects.toThrow('Valid KBAL service with getContent method is required.');
            await expect(semanticSearch.performSearch('test', {}))
                .rejects.toThrow('Valid KBAL service with getContent method is required.');
        });

        // AI-Verifiable: Test case for error propagation from KBAL
        test('should propagate errors from kbalService.getContent', async () => {
            mockKbalService.getContent.mockRejectedValueOnce(new Error('KBAL service error'));
            await expect(semanticSearch.performSearch('test', mockKbalService))
                .rejects.toThrow('Semantic search failed.');
        });

        // AI-Verifiable: Test case for error propagation from embedding model
        test('should propagate errors from embeddingModel.generateEmbedding', async () => {
            mockEmbeddingModel.generateEmbedding.mockRejectedValueOnce(new Error('Embedding model error'));
            // This test needs to be adapted if performSearch catches this specific error internally
            // For now, assuming it propagates up to the main catch block in performSearch
            await expect(semanticSearchWithModel.performSearch('test query', mockKbalService))
                .rejects.toThrow('Semantic search failed.');
        });

        // AI-Verifiable: Test case for result structure
        test('should ensure all results have id, title, snippet, source, score, and type', async () => {
            const query = 'search for cats';
            const results = await semanticSearch.performSearch(query, mockKbalService);
            expect(results.length).toBe(1);
            const result = results[0];
            expect(result).toHaveProperty('id');
            expect(typeof result.id).toBe('string');
            expect(result).toHaveProperty('title', 'Cats Explained');
            expect(result).toHaveProperty('snippet');
            expect(typeof result.snippet).toBe('string');
            expect(result).toHaveProperty('source', 'kbal-semantic');
            expect(result).toHaveProperty('score');
            expect(typeof result.score).toBe('number');
            expect(result).toHaveProperty('type', 'semantic');
        });
    });
});