import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import DOMPurify from 'dompurify';

const ContentRenderer = ({ content, contentType = 'html' }) => {
  if (!content) {
    return <div className="content-renderer-empty">No content to display.</div>;
  }

  const sanitizedHtmlContent = useMemo(() => {
    if (contentType.toLowerCase() === 'html' && content) {
      return DOMPurify.sanitize(content, { USE_PROFILES: { html: true } });
    }
    return null; // Or content if it's not HTML and doesn't need sanitization here
  }, [content, contentType]);

  switch (contentType.toLowerCase()) {
    case 'html':
      return (
        <div data-testid="content-renderer-container" className="content-renderer-html-wrapper">
          <div
            className="content-renderer-html"
            dangerouslySetInnerHTML={{ __html: sanitizedHtmlContent }}
          />
        </div>
      );
    case 'markdown':
      console.warn(
        "Content<PERSON>enderer received contentType 'markdown'. Consider using a dedicated Markdown renderer component for proper display and sanitization if it contains HTML-like structures."
      );
      return (
        <div data-testid="content-renderer-container" className="content-renderer-markdown-wrapper">
          <pre className="content-renderer-markdown-placeholder">{content}</pre>
        </div>
      );
    case 'text':
      return (
        <div data-testid="content-renderer-container" className="content-renderer-text-wrapper">
          <pre className="content-renderer-text">{content}</pre>
        </div>
      );
    default:
      return (
        <div data-testid="content-renderer-container" className="content-renderer-default-wrapper">
          <pre className="content-renderer-text">{content}</pre>
        </div>
      );
  }
};

ContentRenderer.propTypes = {
  content: PropTypes.string,
  contentType: PropTypes.oneOf(['html', 'markdown', 'text']),
};

// Removed defaultProps, handled in function signature

export default ContentRenderer;