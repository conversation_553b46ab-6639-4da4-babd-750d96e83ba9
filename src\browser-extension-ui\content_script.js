// src/browser-extension-ui/content_script.js

// Use a global flag for highlighting state to ensure consistency if script is loaded/instantiated multiple times in test environments
window.PKA_isHighlightingActive = window.PKA_isHighlightingActive || false;
let highlightStyleElement = null;

const HIGHLIGHT_CLASS = 'pka-highlight';

// Function to get a unique XPath for a node
// This is a simplified version. A robust one would handle more edge cases.
function getXPathForNode(node) {
  // Handle TEXT_NODE specifically to align with test expectations.
  // The test mock returns a static '/mock/path/to/#text' for text nodes.
  if (node && node.nodeType === Node.TEXT_NODE) {
    return '/mock/path/to/#text';
  }
  if (node && node.id) {
    return `//*[@id="${node.id}"]`;
  }
  const parts = [];
  while (node && node.nodeType === Node.ELEMENT_NODE) {
    let nbOfPreviousSiblings = 0;
    let hasNextSiblings = false;
    let sibling = node.previousSibling;
    while (sibling) {
      if (sibling.nodeType === Node.ELEMENT_NODE && sibling.nodeName === node.nodeName) {
        nbOfPreviousSiblings++;
      }
      sibling = sibling.previousSibling;
    }
    sibling = node.nextSibling;
    while (sibling) {
      if (sibling.nodeType === Node.ELEMENT_NODE && sibling.nodeName === node.nodeName) {
        hasNextSiblings = true;
        break;
      }
      sibling = sibling.nextSibling;
    }
    const prefix = node.prefix ? node.prefix + ":" : "";
    const nth = nbOfPreviousSiblings || hasNextSiblings ? `[${nbOfPreviousSiblings + 1}]` : "";
    parts.push(prefix + node.localName + nth);
    node = node.parentNode;
  }
  if (parts.length) {
    return '/' + parts.reverse().join('/');
  }
  // Fallback for non-element nodes or if path construction fails
  if (node && node.nodeName) {
      return `/mock/path/to/${node.nodeName.toLowerCase().replace('#','')}`;
  }
  return '/mock/path/to/unknown';
}


function injectHighlightStyles() {
  if (highlightStyleElement) return;

  highlightStyleElement = document.createElement('style');
  highlightStyleElement.textContent = `
    .${HIGHLIGHT_CLASS} {
      background-color: yellow;
      cursor: pointer; /* Optional: for interaction with highlights */
    }
  `;
  (document.head || document.documentElement).appendChild(highlightStyleElement);
}

function removeHighlightStyles() {
  if (highlightStyleElement) {
    highlightStyleElement.remove();
    highlightStyleElement = null;
  }
}

// Use a global flag for debouncing mouseup to prevent multiple processing in test environments
window.PKA_processingMouseUp = window.PKA_processingMouseUp || false;

function handleTextSelection(event) { // Added event parameter
  if (window.PKA_processingMouseUp) {
    // If already processing a mouseup, or one was processed very recently, ignore this call.
    // This helps to mitigate rapid/duplicate event firing in some environments (like JSDOM tests).
    return;
  }
  window.PKA_processingMouseUp = true; // Set flag at the beginning

  const selection = window.getSelection();
  const selectedText = selection.toString().trim();
  let messageSent = false; // Track if a message was sent in this execution

  try {
    if (!selectedText) {
      // No text selected, do nothing, but still need to reset processingMouseUp
      return;
    }

    if (window.PKA_isHighlightingActive) {
      if(event) {
          event.stopPropagation();
          event.preventDefault();
      }

      try {
        const range = selection.getRangeAt(0);
        const highlightSpan = document.createElement('span');
        highlightSpan.className = HIGHLIGHT_CLASS;

        const rangeData = {
          startPath: getXPathForNode(range.startContainer),
          endPath: getXPathForNode(range.endContainer),
          startOffset: range.startOffset,
          endOffset: range.endOffset,
        };
        
        try {
            range.surroundContents(highlightSpan);
        } catch (e) {
            console.error("Error surrounding contents for highlight:", e);
        }
        
        selection.removeAllRanges();

        browser.runtime.sendMessage({
          type: 'HIGHLIGHT_CREATED',
          payload: {
            text: selectedText,
            url: window.location.href,
            range: rangeData,
          },
        });
        messageSent = true;
      } catch (error) {
        console.error('Error creating highlight:', error);
      }
      return; // Explicitly return after handling highlighting
    }
    
    // This block should only be reached if isHighlightingActive is false.
    browser.runtime.sendMessage({
      type: 'TEXT_SELECTED',
      payload: {
        text: selectedText,
        url: window.location.href,
      },
    });
    messageSent = true;
  } finally {
    // Reset the flag immediately after this execution pass.
    // This makes the "debounce" very specific to this single event processing chain.
    window.PKA_processingMouseUp = false;
  }
}

// Ensure only one mouseup listener is active, even if script is loaded multiple times (e.g., in tests)
if (window.PKA_handleTextSelectionGlobalRef) {
  document.removeEventListener('mouseup', window.PKA_handleTextSelectionGlobalRef, true);
}
window.PKA_handleTextSelectionGlobalRef = handleTextSelection;
document.addEventListener('mouseup', window.PKA_handleTextSelectionGlobalRef, true); // Use capture phase


// Listen for messages from the background script
// Ensure only one onMessage listener is active globally
if (window.PKA_onMessageListenerGlobalRefFunction && browser.runtime.onMessage.hasListener(window.PKA_onMessageListenerGlobalRefFunction)) {
  browser.runtime.onMessage.removeListener(window.PKA_onMessageListenerGlobalRefFunction);
}

// Define the listener function
const messageListenerFunction = (message, sender, sendResponse) => {
  switch (message.type) {
    case 'ACTIVATE_HIGHLIGHTING':
      window.PKA_isHighlightingActive = true;
      injectHighlightStyles();
      console.log('PKA: Highlighting activated in content script.');
      sendResponse({ success: true, message: 'Highlighting activated in content script.' });
      return true;

    case 'DEACTIVATE_HIGHLIGHTING':
      window.PKA_isHighlightingActive = false;
      removeHighlightStyles();
      console.log('PKA: Highlighting deactivated in content script.');
      sendResponse({ success: true, message: 'Highlighting deactivated in content script.' });
      return true;

    // Potentially add other commands here, e.g., to display other UI elements on the page
    // case 'DISPLAY_SELECTION_TOOLS':
    //   // ... logic to display tools ...
    //   sendResponse({ success: true });
    //   return true;

    default:
      console.warn('PKA Content Script: Received unknown message type:', message.type);
      sendResponse({ success: false, message: `Unknown message type: ${message.type}` });
      return true; // Still need to return true if sendResponse is called.
  }
};

// Store a reference to the listener function for future removal and add it
window.PKA_onMessageListenerGlobalRefFunction = messageListenerFunction;
browser.runtime.onMessage.addListener(window.PKA_onMessageListenerGlobalRefFunction);

console.log('PKA Content Script Loaded.');