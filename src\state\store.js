import { create } from 'zustand';

const useStore = create((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
}));

const useKnowledgeBaseStore = create((set) => ({
  knowledgeBase: [
    { id: 1, title: 'Note 1', content: 'This is the first note.' },
    { id: 2, title: 'Note 2', content: 'This is the second note.' },
    { id: 3, title: 'Note 3', content: 'This is the third note.' },
  ],
  addNote: (note) => set((state) => ({ knowledgeBase: [...state.knowledgeBase, note] })),
  removeNote: (id) => set((state) => ({ knowledgeBase: state.knowledgeBase.filter((note) => note.id !== id) })),
}));

export default useKnowledgeBaseStore;