/// <reference types="@testing-library/jest-dom" />
/// <reference types="@testing-library/user-event" />
import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import KnowledgeBaseView from '../KnowledgeBaseView';
import { KnowledgeBaseEntry } from '@pkm-ai/knowledge-base-service';
import { jest } from '@jest/globals';
import { CreateEntryData, UpdateEntryData, BackgroundMessage, BackgroundResponse } from '../../../types/messaging';


// Mock chrome.runtime API
const mockSendMessage = jest.fn();
global.chrome = {
  runtime: {
    sendMessage: mockSendMessage,
    lastError: undefined,
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
      hasListener: jest.fn(),
      hasListeners: jest.fn(),
    },
    // Add other chrome.runtime properties if needed by the component or its children
  },
  // Add other chrome APIs if needed
} as any;


const mockItems: KnowledgeBaseEntry[] = [
  { id: '1', title: 'React Best Practices', content: 'Content about React.', url: 'http://react.dev', type: 'article', tags: ['react', 'javascript'], createdAt: new Date('2023-01-01T10:00:00Z'), updatedAt: new Date('2023-01-01T11:00:00Z') },
  { id: '2', title: 'TypeScript Guide', content: 'Learn TypeScript.', url: 'http://typescriptlang.org', type: 'bookmark', tags: ['typescript'], createdAt: new Date('2023-01-02T12:00:00Z'), updatedAt: new Date('2023-01-02T13:00:00Z') },
  { id: '3', title: 'Tailwind CSS Tips', content: 'Styling with Tailwind.', url: 'http://tailwindcss.com', type: 'note', tags: ['css', 'tailwind'], createdAt: new Date('2023-01-03T14:00:00Z'), updatedAt: new Date('2023-01-03T15:00:00Z') },
];

// Define a more specific type for the mock implementation function
type MockSendMessageImplementation = (
  message: BackgroundMessage<any>,
  callback: (response: BackgroundResponse<any>) => void
) => void;

const mockSendMessageSuccess = (action: string, data: Partial<BackgroundResponse>) => {
  const implementation: MockSendMessageImplementation = (message, callback) => {
    if (message.action === action) {
      let responseData = { ...data };
      if (action === 'getKnowledgeBaseEntries' && Array.isArray(data.entries)) {
        responseData.entries = data.entries.map((e: any) => ({
          ...e,
          // Ensure original Date objects are converted to ISO strings for the mock response
          createdAt: e.createdAt instanceof Date ? e.createdAt.toISOString() : e.createdAt,
          updatedAt: e.updatedAt instanceof Date ? e.updatedAt.toISOString() : e.updatedAt,
        })) as any; // Cast to any because BackgroundResponse expects Date, but component handles string
      } else if ((action === 'getKnowledgeBaseEntryById' || action === 'updateKnowledgeBaseEntry' || action === 'createKnowledgeBaseEntry') && data.entry) {
        const entry = data.entry as any;
        responseData.entry = {
          ...entry,
          createdAt: entry.createdAt instanceof Date ? entry.createdAt.toISOString() : entry.createdAt,
          updatedAt: entry.updatedAt instanceof Date ? entry.updatedAt.toISOString() : entry.updatedAt,
        } as any; // Cast to any for the same reason
      }
      callback({ success: true, ...responseData });
    }
  };
  mockSendMessage.mockImplementation(implementation as any);
};

const mockSendMessageFailure = (action: string, errorMsg: string) => {
  const implementation: MockSendMessageImplementation = (message, callback) => {
    if (message.action === action) {
      callback({ success: false, error: errorMsg });
    }
  };
  mockSendMessage.mockImplementation(implementation as any);
};


describe('KnowledgeBaseView', () => {
  beforeEach(() => {
    mockSendMessage.mockReset();
    // Default mock for getAllEntries to avoid console errors if not specifically set by a test
    mockSendMessageSuccess('getKnowledgeBaseEntries', { entries: [] });
  });

  test('renders loading state initially then items', async () => {
    const implementation: MockSendMessageImplementation = (message, callback) => {
      if (message.action === 'getKnowledgeBaseEntries') {
        setTimeout(() => callback({
          success: true,
          entries: mockItems.map(e => ({
            ...e,
            createdAt: e.createdAt.toISOString(), // Send as string
            updatedAt: e.updatedAt.toISOString()  // Send as string
          })) as any // Cast as any for mock, component will parse to Date
        }), 100);
      }
    };
    mockSendMessage.mockImplementation(implementation as any);
    render(<KnowledgeBaseView />);
    expect(screen.getByText(/loading items.../i)).toBeInTheDocument();
    expect(await screen.findByText('React Best Practices')).toBeInTheDocument();
    expect(await screen.findByText('TypeScript Guide')).toBeInTheDocument();
  });

  test('renders error message on fetch failure', async () => {
    mockSendMessageFailure('getKnowledgeBaseEntries', 'Network Error');
    render(<KnowledgeBaseView />);
    expect(await screen.findByText(/network error/i)).toBeInTheDocument();
  });

  test('filters items based on search term', async () => {
    mockSendMessageSuccess('getKnowledgeBaseEntries', { entries: mockItems });
    render(<KnowledgeBaseView />);
    expect(await screen.findByText('React Best Practices')).toBeInTheDocument();

    const searchInput = screen.getByPlaceholderText(/search by title, content, url, tags, or type.../i);
    await userEvent.type(searchInput, 'typescript');

    await waitFor(() => {
      expect(screen.queryByText('React Best Practices')).not.toBeInTheDocument();
    });
    expect(screen.getByText('TypeScript Guide')).toBeInTheDocument();
  });

  test('displays item details when an item is selected from the list', async () => {
    mockSendMessageSuccess('getKnowledgeBaseEntries', { entries: mockItems });
    render(<KnowledgeBaseView />);
    const listItemToClick = await screen.findByText('React Best Practices');
    fireEvent.click(listItemToClick);

    expect(await screen.findByRole('heading', { name: 'React Best Practices', level: 2 })).toBeInTheDocument();
    
    // Get the DetailViewPane by finding an element unique to it, e.g., the h2 title, then its parent container.
    // The DetailViewPane has a structure like: <div class="w-2/3 ..."><div class="p-6 ..."><h2>...</h2><pre>...</pre>...</div></div>
    // We can find the h2, then navigate to its parent that contains the <pre> tag for content.
    const detailViewHeading = await screen.findByRole('heading', { name: 'React Best Practices', level: 2 });
    const detailViewContainer = detailViewHeading.closest('.w-2\\/3'); // This selector might need adjustment based on actual DOM
    
    expect(detailViewContainer).toBeInTheDocument();
    if (detailViewContainer) {
      const detailViewHtmlElement = detailViewContainer as HTMLElement; // Cast to HTMLElement
      // The content is inside a <pre> tag within the DetailViewPane
      const contentElement = within(detailViewHtmlElement).getByText((content, element) => {
        return element?.tagName.toLowerCase() === 'pre' && content.includes('Content about React.');
      });
      expect(contentElement).toBeInTheDocument();
      expect(within(detailViewHtmlElement).getByText('http://react.dev')).toBeInTheDocument();
    } else {
      throw new Error("Could not find DetailViewPane container to assert content.");
    }
  });

  test('creates a new entry and refreshes the list', async () => {
    const newEntryData: CreateEntryData = { title: 'New Test Entry from Options', content: 'Some content', type: 'note', url: 'http://example.com/new', tags: ['new', 'test'] };
    const createdEntry: KnowledgeBaseEntry = { ...newEntryData, id: '4', createdAt: new Date(), updatedAt: new Date() };

    mockSendMessageSuccess('getKnowledgeBaseEntries', { entries: mockItems });

    render(<KnowledgeBaseView />);
    await screen.findByText('React Best Practices'); // Ensure initial load complete

    const createUpdateGetImplementation: MockSendMessageImplementation = (message, callback) => {
        if (message.action === 'createKnowledgeBaseEntry' && message.data.title === newEntryData.title) {
            callback({ success: true, entry: {...createdEntry, createdAt: createdEntry.createdAt.toISOString(), updatedAt: createdEntry.updatedAt.toISOString()} as any });
        } else if (message.action === 'getKnowledgeBaseEntries') {
            // Simulate list refresh including the new item
            callback({ success: true, entries: [...mockItems, createdEntry].map(e => ({...e, createdAt: e.createdAt.toISOString(), updatedAt: e.updatedAt.toISOString()})) as any });
        }
    };
    mockSendMessage.mockImplementation(createUpdateGetImplementation as any);

    const addButton = screen.getByRole('button', { name: /add new test entry/i });
    fireEvent.click(addButton);

    expect(await screen.findByText(newEntryData.title)).toBeInTheDocument(); // New item appears in the list
    expect(mockSendMessage).toHaveBeenCalledWith(expect.objectContaining({ action: 'createKnowledgeBaseEntry', data: newEntryData }), expect.any(Function));
    // It should also call getKnowledgeBaseEntries again to refresh
    expect(mockSendMessage).toHaveBeenCalledWith(expect.objectContaining({ action: 'getKnowledgeBaseEntries' }), expect.any(Function));
  });

  test('updates an entry and reflects changes in detail view', async () => {
    mockSendMessageSuccess('getKnowledgeBaseEntries', { entries: mockItems });
    const itemToUpdate = mockItems[0]; // React Best Practices
    const updatedTitle = 'React Best Practices (Updated)';
    const updatedEntryFull: KnowledgeBaseEntry = { ...itemToUpdate, title: updatedTitle, updatedAt: new Date() };

    render(<KnowledgeBaseView />);
    const listItem = await screen.findByText(itemToUpdate.title);
    fireEvent.click(listItem); // Select item

    // Wait for detail view to show
    await screen.findByRole('heading', { name: itemToUpdate.title, level: 2 });

    const updateAndGetImplementation: MockSendMessageImplementation = (message, callback) => {
        if (message.action === 'updateKnowledgeBaseEntry' && message.data.id === itemToUpdate.id) {
            callback({ success: true, entry: {...updatedEntryFull, createdAt: updatedEntryFull.createdAt.toISOString(), updatedAt: updatedEntryFull.updatedAt.toISOString()} as any });
        } else if (message.action === 'getKnowledgeBaseEntries') {
            callback({ success: true, entries: [updatedEntryFull, ...mockItems.slice(1)].map(e => ({...e, createdAt: e.createdAt.toISOString(), updatedAt: e.updatedAt.toISOString()})) as any });
        }
    };
    mockSendMessage.mockImplementation(updateAndGetImplementation as any);

    const editButton = await screen.findByRole('button', { name: /edit/i });
    fireEvent.click(editButton);

    const titleInput = await screen.findByLabelText(/title/i);
    await userEvent.clear(titleInput);
    await userEvent.type(titleInput, updatedTitle);

    const saveButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(saveButton);

    expect(await screen.findByRole('heading', { name: updatedTitle, level: 2 })).toBeInTheDocument(); // Detail view updates
    expect(mockSendMessage).toHaveBeenCalledWith(expect.objectContaining({ action: 'updateKnowledgeBaseEntry', data: { id: itemToUpdate.id, updateData: { title: updatedTitle, url: itemToUpdate.url, content: itemToUpdate.content, tags: itemToUpdate.tags, type: itemToUpdate.type } } }), expect.any(Function));
  });


  test('deletes an entry and removes it from the list', async () => {
    mockSendMessageSuccess('getKnowledgeBaseEntries', { entries: mockItems });
    const itemToDelete = mockItems[0]; // React Best Practices

    render(<KnowledgeBaseView />);
    await screen.findByText(itemToDelete.title); // Wait for list to load

    const deleteAndGetImplementation: MockSendMessageImplementation = (message, callback) => {
        if (message.action === 'deleteKnowledgeBaseEntry' && message.data.id === itemToDelete.id) {
            callback({ success: true, id: itemToDelete.id });
        } else if (message.action === 'getKnowledgeBaseEntries') {
            // Simulate list refresh without the deleted item
            callback({ success: true, entries: mockItems.slice(1).map(e => ({...e, createdAt: e.createdAt.toISOString(), updatedAt: e.updatedAt.toISOString()})) as any });
        }
    };
    mockSendMessage.mockImplementation(deleteAndGetImplementation as any);

    // Find the delete button for the specific item in the ContentList
    // This assumes ContentList renders delete buttons that can be identified.
    // The ContentList was updated to have a "Delete" button per item.
    const deleteButton = await screen.findAllByRole('button', { name: `Delete ${itemToDelete.title}` });
    fireEvent.click(deleteButton[0]); // Assuming first one found is correct if titles are unique

    await waitFor(() => {
      expect(screen.queryByText(itemToDelete.title)).not.toBeInTheDocument();
    });
    expect(mockSendMessage).toHaveBeenCalledWith(expect.objectContaining({ action: 'deleteKnowledgeBaseEntry', data: { id: itemToDelete.id } }), expect.any(Function));
  });


  test('shows "No items match your search" when filter yields no results', async () => {
    mockSendMessageSuccess('getKnowledgeBaseEntries', { entries: mockItems });
    render(<KnowledgeBaseView />);
    expect(await screen.findByText('React Best Practices')).toBeInTheDocument();

    const searchInput = screen.getByPlaceholderText(/search by title, content, url, tags, or type.../i);
    await userEvent.type(searchInput, 'nonexistentsearchterm123');

    expect(await screen.findByText(/no items match your search/i)).toBeInTheDocument();
  });

  test('shows "No items found..." when no items are fetched and search is empty', async () => {
    mockSendMessageSuccess('getKnowledgeBaseEntries', { entries: [] });
    render(<KnowledgeBaseView />);
    expect(await screen.findByText(/no items found in the knowledge base. try adding one!/i)).toBeInTheDocument();
  });
});