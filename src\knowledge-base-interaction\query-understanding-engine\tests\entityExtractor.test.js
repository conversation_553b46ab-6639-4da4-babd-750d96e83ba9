// AI-VERIFIABLE: Placeholder test file for EntityExtractor.
// These tests will verify the logic for extracting entities from queries.

import EntityExtractor from '../entity-extraction/entityExtractor';

describe('EntityExtractor - Unit Tests', () => {
    let extractor;

    beforeEach(() => {
        extractor = new EntityExtractor();
    });

    // Helper to create a mock parsedQuery object, similar to QueryParser's output
    const createParsedQuery = (original, tokensOverride = null, keywordsOverride = null) => {
        const cleaned = original.toLowerCase().replace(/[.,?!;:()[\]{}]/g, ' ').replace(/['"`]/g, '').replace(/\s+/g, ' ').trim();
        // Basic stopword list for mock parsing, should align with QueryParser for consistency in tests
        const mockStopwords = new Set(['a', 'an', 'the', 'is', 'are', 'of', 'for', 'on', 'in', 'to', 'by', 'with', 'and']);
        const defaultTokens = cleaned.split(' ').filter(t => t.length > 0);
        const defaultKeywords = defaultTokens.filter(t => !mockStopwords.has(t) && t.length > 0);

        return {
            original: original,
            cleanedQuery: cleaned,
            tokens: tokensOverride || defaultTokens,
            keywords: keywordsOverride || defaultKeywords,
        };
    };

    test('AI-VERIFIABLE: should use keywords directly from parsedQuery', async () => {
        const parsedQuery = createParsedQuery(
            "search for AI and TDD principles",
            ["search", "for", "ai", "and", "tdd", "principles"], // tokens
            ["search", "ai", "tdd", "principles"] // keywords from QueryParser
        );
        const intent = { type: 'search', confidence: 0.9 };
        const expectedKeywords = ["search", "ai", "tdd", "principles"]; // Should be exactly what QueryParser provided

        const result = await extractor.extractEntities(parsedQuery, intent);
        // result.keywords should be the same as parsedQuery.keywords
        expect(result.keywords).toEqual(expectedKeywords);
    });

    test('AI-VERIFIABLE: should extract named entities (rudimentary: capitalized, known orgs/locs)', async () => {
        const parsedQuery = createParsedQuery(
            "Find projects by Google in London from OpenAI.",
            ["find", "projects", "by", "Google", "in", "London", "from", "OpenAI"],
            ["find", "projects", "google", "london", "openai"] // keywords are lowercased by parser
        );
        const intent = { type: 'search', confidence: 0.9 };
        
        const result = await extractor.extractEntities(parsedQuery, intent);

        expect(result.namedEntities).toEqual(expect.arrayContaining([
            // Expect original casing as per tokensOverride, and confidence 0.8 from EntityExtractor
            expect.objectContaining({ text: 'Google', type: 'ORGANIZATION', confidence: 0.8 }),
            expect.objectContaining({ text: 'London', type: 'LOCATION', confidence: 0.8 }),
            expect.objectContaining({ text: 'OpenAI', type: 'ORGANIZATION', confidence: 0.8 })
        ]));
        // Check authors. "Google" is identified as an ORG, so it should not be in authors here.
        expect(result.authors).toEqual([]);
    });
    
    test('AI-VERIFIABLE: should extract dates with YYYY, YYYY-MM-DD, MM/DD/YYYY formats', async () => {
        const parsedQuery = createParsedQuery("Reports from 2023, 2024-01-15 and 03/20/2025");
        const intent = { type: 'search' };
        const result = await extractor.extractEntities(parsedQuery, intent);
        expect(result.dates).toEqual(expect.arrayContaining(["2023", "2024-01-15", "03/20/2025"]));
        expect(result.namedEntities).toEqual(expect.arrayContaining([
            expect.objectContaining({ text: '2023', type: 'DATE_NE' }),
            expect.objectContaining({ text: '2024-01-15', type: 'DATE_NE' }),
            expect.objectContaining({ text: '03/20/2025', type: 'DATE_NE' }),
        ]));
    });

    test('AI-VERIFIABLE: should extract authors when "by AuthorName" pattern is found', async () => {
        const parsedQuery = createParsedQuery("Articles by John Doe on AI");
        const intent = { type: 'search' };
        const result = await extractor.extractEntities(parsedQuery, intent);
        // QueryParser provides lowercase tokens, so "john" (from "John Doe") would be extracted.
        expect(result.authors).toEqual(expect.arrayContaining(["john"]));
        expect(result.namedEntities).toEqual(expect.arrayContaining([
            expect.objectContaining({ text: 'john', type: 'PERSON' }) // text is lowercase
        ]));
        expect(result.relationships).toEqual(expect.arrayContaining([
            // entity2 should be the lowercase author name
            expect.objectContaining({ type: 'authoredBy', entity1: 'articles', entity2: 'john' })
        ]));
    });
    
    test('AI-VERIFIABLE: should extract basic relationships (topicOf, publishedAfter)', async () => {
        const parsedQuery = createParsedQuery("Find articles on TDD published after 2022");
        const intent = { type: 'search' };
        const result = await extractor.extractEntities(parsedQuery, intent);
        expect(result.relationships).toEqual(expect.arrayContaining([
            expect.objectContaining({ type: 'topicOf', entity1: 'articles', topic: 'tdd' }),
            expect.objectContaining({ type: 'publishedAfter', date: '2022' })
        ]));
    });


    test('AI-VERIFIABLE: should return empty arrays if no specific entities are found', async () => {
        const parsedQuery = createParsedQuery(
            "the and if then", // all stopwords or very short
            ["the", "and", "if", "then"],
            ["then"] // "then" might be a keyword if not in mockStopwords
        );
        const intent = { type: 'unknown', confidence: 0.5 };
        const result = await extractor.extractEntities(parsedQuery, intent);
        expect(result.keywords).toEqual(["then"]); // "then" is a keyword from parser
        expect(result.namedEntities).toEqual([]);
        expect(result.dates).toEqual([]);
        expect(result.authors).toEqual([]);
        expect(result.relationships).toEqual([]);
    });

    test('AI-VERIFIABLE: should throw an error if parsedQuery is invalid or missing required fields', async () => {
        const intent = { type: 'search', confidence: 0.9 };
        const errorMsg = 'Parsed query object with original, tokens, and keywords is required for entity extraction.';
        await expect(extractor.extractEntities(null, intent)).rejects.toThrow(errorMsg);
        await expect(extractor.extractEntities({}, intent)).rejects.toThrow(errorMsg);
        await expect(extractor.extractEntities({ original: "test" }, intent)).rejects.toThrow(errorMsg);
        await expect(extractor.extractEntities({ original: "test", tokens: [] }, intent)).rejects.toThrow(errorMsg);
    });

    test('AI-VERIFIABLE: should throw an error if intent is invalid', async () => {
        const parsedQuery = createParsedQuery("test query");
        await expect(extractor.extractEntities(parsedQuery, null)).rejects.toThrow('Intent object with type is required for entity extraction.');
        await expect(extractor.extractEntities(parsedQuery, {})).rejects.toThrow('Intent object with type is required for entity extraction.');
    });
    
    test('AI-VERIFIABLE: should not identify first word of query as NE just due to capitalization', async () => {
        const parsedQuery = createParsedQuery("London is a city."); // "London" is first word
        const intent = { type: 'search' };
        const result = await extractor.extractEntities(parsedQuery, intent);
        // It should still be identified if it's a known location like "london" (lowercase)
        const londonEntity = result.namedEntities.find(ne => ne.text === 'london' && ne.type === 'LOCATION');
        expect(londonEntity).toBeDefined();

        const parsedQueryGeneric = createParsedQuery("Capitalized word at start."); // Tokens will be lowercase
        const resultGeneric = await extractor.extractEntities(parsedQueryGeneric, intent);
        // Since tokens are lowercase and generic capitalization check is removed/ineffective,
        // "capitalized" (lowercase) will not be identified as a POTENTIAL_NE.
        const capitalizedEntity = resultGeneric.namedEntities.find(ne => ne.text === 'capitalized');
        expect(capitalizedEntity).toBeUndefined();
    });

    // Add more tests for different entity types, complex queries,
    // context-dependent extraction, and confidence scores as the extractor evolves.
});

// AI-VERIFIABLE: End of entityExtractor.test.js