import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ConceptualLinksDisplay from '../renderer/components/ConceptualLinksDisplay';

describe('ConceptualLinksDisplay', () => {
  const mockLinks = [
    { id: 'link1', title: 'Related Item 1', snippet: 'Snippet for item 1' },
    { id: 'link2', title: 'Related Item 2', snippet: 'Snippet for item 2' },
  ];
  const mockOnLinkClick = jest.fn();
  const mockOnHighlight = jest.fn(); // Placeholder for future highlight functionality

  beforeEach(() => {
    mockOnLinkClick.mockClear();
    mockOnHighlight.mockClear();
  });

  test('renders a list of conceptual links', () => {
    render(<ConceptualLinksDisplay links={mockLinks} onLinkClick={mockOnLinkClick} onHighlight={mockOnHighlight} />);
    expect(screen.getByText('Related Item 1')).toBeInTheDocument();
    expect(screen.getByText('Snippet for item 1')).toBeInTheDocument();
    expect(screen.getByText('Related Item 2')).toBeInTheDocument();
    expect(screen.getByText('Snippet for item 2')).toBeInTheDocument();
  });

  test('calls onLinkClick when a link title is clicked', () => {
    render(<ConceptualLinksDisplay links={mockLinks} onLinkClick={mockOnLinkClick} onHighlight={mockOnHighlight} />);
    const linkElement = screen.getByText('Related Item 1');
    fireEvent.click(linkElement);
    expect(mockOnLinkClick).toHaveBeenCalledWith('link1');
  });

  test('displays a message when no links are available', () => {
    render(<ConceptualLinksDisplay links={[]} onLinkClick={mockOnLinkClick} onHighlight={mockOnHighlight} />);
    expect(screen.getByText(/No conceptual links to display/i)).toBeInTheDocument();
  });

  test('renders nothing if links prop is undefined or null', () => {
    const { container: containerUndefined } = render(<ConceptualLinksDisplay links={undefined} onLinkClick={mockOnLinkClick} onHighlight={mockOnHighlight} />);
    expect(containerUndefined.firstChild).toBeNull(); // Or check for a specific placeholder if preferred

    const { container: containerNull } = render(<ConceptualLinksDisplay links={null} onLinkClick={mockOnLinkClick} onHighlight={mockOnHighlight} />);
    expect(containerNull.firstChild).toBeNull(); // Or check for a specific placeholder if preferred
  });

  // Placeholder test for future highlight functionality
  test('calls onHighlight when a snippet is interacted with (placeholder)', () => {
    render(<ConceptualLinksDisplay links={mockLinks} onLinkClick={mockOnLinkClick} onHighlight={mockOnHighlight} />);
    // This test will need to be updated when highlighting is implemented
    // For now, we can just ensure the prop is passed.
    // Example: fireEvent.mouseEnter(screen.getByText('Snippet for item 1'));
    // expect(mockOnHighlight).toHaveBeenCalledWith('link1', 'supporting text segment');
    expect(mockOnHighlight).not.toHaveBeenCalled(); // As it's a placeholder
  });
});