// Define the types locally to avoid Node.js imports
export interface KnowledgeBaseEntry {
  id: string;
  title: string;
  content: string;
  url?: string;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface KnowledgeBaseData {
  entries: KnowledgeBaseEntry[];
}

// Mock implementation of KnowledgeBaseService for browser environment
export class KnowledgeBaseService {
  private entries: KnowledgeBaseEntry[] = [];
  private initialized = false;
  private initializationPromise: Promise<void> | null = null;

  constructor() {
    this.initializationPromise = this._initializeService();
  }

  private async _initializeService(): Promise<void> {
    // In a browser environment, we'll use localStorage instead of a file
    try {
      const storedData = localStorage.getItem('knowledgeBase');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        if (Array.isArray(parsedData.entries)) {
          this.entries = parsedData.entries.map((entry: any) => ({
            ...entry,
            createdAt: new Date(entry.createdAt),
            updatedAt: new Date(entry.updatedAt)
          }));
        }
      }
    } catch (error) {
      console.error('Failed to initialize KnowledgeBaseService from localStorage:', error);
      this.entries = [];
    }
    this.initialized = true;
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.initializationPromise) {
      this.initializationPromise = this._initializeService();
    }
    await this.initializationPromise;
  }

  private saveToStorage(): void {
    try {
      localStorage.setItem('knowledgeBase', JSON.stringify({ entries: this.entries }));
    } catch (error) {
      console.error('Failed to save KnowledgeBaseService data to localStorage:', error);
    }
  }

  async createEntry(data: Omit<KnowledgeBaseEntry, 'id' | 'createdAt' | 'updatedAt'>): Promise<KnowledgeBaseEntry> {
    await this.ensureInitialized();
    const now = new Date();
    const newEntry: KnowledgeBaseEntry = {
      id: self.crypto.randomUUID(),
      ...data,
      createdAt: now,
      updatedAt: now,
    };
    this.entries.push(newEntry);
    this.saveToStorage();
    return { ...newEntry };
  }

  async getEntryById(id: string): Promise<KnowledgeBaseEntry | undefined> {
    await this.ensureInitialized();
    return this.entries.find(e => e.id === id);
  }

  async updateEntry(id: string, data: Partial<Omit<KnowledgeBaseEntry, 'id' | 'createdAt' | 'updatedAt'>>): Promise<KnowledgeBaseEntry | undefined> {
    await this.ensureInitialized();
    const entryIndex = this.entries.findIndex(e => e.id === id);
    if (entryIndex === -1) {
      return undefined;
    }
    const updatedEntry = {
      ...this.entries[entryIndex],
      ...data,
      updatedAt: new Date(),
    };
    this.entries[entryIndex] = updatedEntry;
    this.saveToStorage();
    return { ...updatedEntry };
  }

  async deleteEntry(id: string): Promise<boolean> {
    await this.ensureInitialized();
    const initialLength = this.entries.length;
    this.entries = this.entries.filter(e => e.id !== id);
    if (this.entries.length < initialLength) {
      this.saveToStorage();
      return true;
    }
    return false;
  }

  async getAllEntries(): Promise<KnowledgeBaseEntry[]> {
    await this.ensureInitialized();
    return [...this.entries];
  }

  async clearDatabase(): Promise<void> {
    this.entries = [];
    this.saveToStorage();
  }
}
