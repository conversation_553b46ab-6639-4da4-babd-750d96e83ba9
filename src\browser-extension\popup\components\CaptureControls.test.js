import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import CaptureControls from './CaptureControls';

describe('CaptureControls Component', () => {
  const mockOnCapture = jest.fn();

  beforeEach(() => {
    mockOnCapture.mockClear();
  });

  test('renders "Capture Full Page" button by default (idle status, fullPage type)', () => {
    render(<CaptureControls status="idle" onCapture={mockOnCapture} captureType="fullPage" />);
    expect(screen.getByRole('button', { name: /Capture Full Page/i })).toBeInTheDocument();
  });

  test('renders "Capture Selection" button when captureType is "selection"', () => {
    render(<CaptureControls status="idle" onCapture={mockOnCapture} captureType="selection" />);
    expect(screen.getByRole('button', { name: /Capture Selection/i })).toBeInTheDocument();
  });

  test('renders "Capture Bookmark" button when captureType is "bookmark"', () => {
    render(<CaptureControls status="idle" onCapture={mockOnCapture} captureType="bookmark" />);
    expect(screen.getByRole('button', { name: /Capture Bookmark/i })).toBeInTheDocument();
  });

  test('renders "Capturing..." button and is disabled when status is "capturing"', () => {
    render(<CaptureControls status="capturing" onCapture={mockOnCapture} captureType="fullPage" />);
    const button = screen.getByRole('button', { name: /Capturing.../i });
    expect(button).toBeInTheDocument();
    expect(button).toBeDisabled();
  });

  test('renders "Capture More" button and success message when status is "success"', () => {
    render(<CaptureControls status="success" onCapture={mockOnCapture} captureType="fullPage" />);
    expect(screen.getByRole('button', { name: /Capture More/i })).toBeInTheDocument();
    expect(screen.getByText(/Capture Successful!/i)).toBeInTheDocument();
  });

  test('renders button with default text and error message when status is "error"', () => {
    render(<CaptureControls status="error" onCapture={mockOnCapture} captureType="fullPage" />);
    expect(screen.getByRole('button', { name: /Capture Full Page/i })).toBeInTheDocument();
    expect(screen.getByText(/Capture Failed. Please try again./i)).toBeInTheDocument();
  });

  test('calls onCapture when the button is clicked (and not disabled)', () => {
    render(<CaptureControls status="idle" onCapture={mockOnCapture} captureType="fullPage" />);
    const button = screen.getByRole('button', { name: /Capture Full Page/i });
    fireEvent.click(button);
    expect(mockOnCapture).toHaveBeenCalledTimes(1);
  });

  test('does not call onCapture when the button is clicked and status is "capturing"', () => {
    render(<CaptureControls status="capturing" onCapture={mockOnCapture} captureType="fullPage" />);
    const button = screen.getByRole('button', { name: /Capturing.../i });
    fireEvent.click(button); // Attempt to click disabled button
    expect(mockOnCapture).not.toHaveBeenCalled();
  });
});