import { defineConfig, devices } from '@playwright/test';
import path from 'path';
import fs from 'fs';

// Path to the Chrome extension build output
const extensionPath = path.join(__dirname, '../../apps/chrome-extension/dist');

// Verify that the extension build exists
if (!fs.existsSync(extensionPath)) {
  console.error(`Extension build not found at ${extensionPath}`);
  console.error('Please build the extension first with: cd apps/chrome-extension && pnpm build');
  process.exit(1);
}

// Create a unique user data directory for testing
const userDataDir = path.join(__dirname, '../../.playwright-user-data');
if (!fs.existsSync(userDataDir)) {
  fs.mkdirSync(userDataDir, { recursive: true });
}

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  testDir: './tests/e2e',
  /* Run tests in files in parallel */
  fullyParallel: false, // Set to false for extension testing to avoid conflicts
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: 1, // Use a single worker for extension testing
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: 'html',
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',

    // Launch options to use for all projects
    launchOptions: {
      // Slow down execution to make it easier to follow
      slowMo: 100,
    },
    // Increase timeouts for extension testing
    navigationTimeout: 30000,
    actionTimeout: 30000,
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium-extension',
      testMatch: /.*\.spec\.ts/,
      use: {
        ...devices['Desktop Chrome'],
        // Use a persistent context with the extension loaded
        contextOptions: {
          ignoreHTTPSErrors: true,
        },
      },
    },
  ],

  // We'll build the extension manually before running tests
  // webServer: {
  //   command: 'cd apps/chrome-extension && pnpm build',
  //   url: 'http://localhost:9999', // Dummy URL, not actually used
  //   reuseExistingServer: true,
  //   timeout: 120000, // 2 minutes
  // },
});
