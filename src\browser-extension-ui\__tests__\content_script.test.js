// src/browser-extension-ui/__tests__/content_script.test.js

// Mock browser APIs
global.browser = {
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
      hasListener: jest.fn(() => true), // Assume listener is always there after script load
    },
    getURL: jest.fn(path => `moz-extension://test-uuid/${path}`), // Mock getURL
  },
  // Mock any other browser APIs if needed by content_script.js
};

// Mock window.getSelection
const mockGetSelection = jest.fn();
global.window.getSelection = mockGetSelection;
global.window.location.href = 'http://testdomain.com/testpage';

// DOM setup for testing
function setupDOM(html) {
  document.body.innerHTML = html;
  // Add a head if needed for CSS or other elements
  if (!document.head) {
    const head = document.createElement('head');
    document.documentElement.appendChild(head);
  }
}

// Helper to simulate text selection
function simulateTextSelection(text, startNode, startOffset, endNode, endOffset) {
  const range = document.createRange();
  range.setStart(startNode, startOffset);
  range.setEnd(endNode, endOffset);

  mockGetSelection.mockReturnValue({
    toString: () => text,
    rangeCount: text ? 1 : 0, // rangeCount is 0 if no text selected
    getRangeAt: (index) => (index === 0 && text ? range : null),
    removeAllRanges: jest.fn(),
    addRange: jest.fn(),
    collapseToEnd: jest.fn(),
  });

  // Trigger mouseup to simulate selection finalization
  const mouseUpEvent = new MouseEvent('mouseup', { bubbles: true, cancelable: true });
  document.dispatchEvent(mouseUpEvent);
}

describe('Content Script', () => {
  let messageListenerCallback;

  beforeEach(() => {
    jest.resetModules(); // Resets module cache, critical for re-requiring script
    jest.clearAllMocks(); // Clears mock call history etc.
    setupDOM(''); // Clear DOM

    // Mock getXPathForNode which is defined in content_script.js
    // This avoids needing to export it or copy it.
    // The mock should be available before the script is required.
    global.getXPathForNode = jest.fn((node) => {
        if (node && node.id) return `//*[@id="${node.id}"]`;
        // Simplify for text nodes, which is what getRangeAt(0).startContainer often is
        if (node && node.nodeType === Node.TEXT_NODE) return '/mock/path/to/#text';
        if (node && node.nodeName) return `/mock/path/to/${node.nodeName.toLowerCase().replace('#', '')}`;
        return '/mock/path/to/unknown';
    });


    // Require the content script. This will execute its top-level code,
    // including adding event listeners.
    require('../content_script.js');

    // Capture the onMessage listener callback
    if (browser.runtime.onMessage.addListener.mock.calls.length > 0) {
      messageListenerCallback = browser.runtime.onMessage.addListener.mock.calls[0][0];
    } else {
      // This should not happen if content_script.js correctly adds its listener.
      console.warn('browser.runtime.onMessage.addListener was not called by content_script.js');
      // Provide a dummy callback to prevent tests from failing due to undefined messageListenerCallback
      messageListenerCallback = () => {};
    }
  });

  afterEach(() => {
    // Clean up global mocks if they were set
    delete global.getXPathForNode;
    document.body.innerHTML = ''; // Clean up DOM
    if (document.head) {
        document.head.innerHTML = '';
    }
  });

  describe('Text Selection Capture', () => {
    test('should send selected text to background script on mouseup if highlighting is not active', () => {
      // Ensure highlighting is not active (default state after resetModules)
      setupDOM('<p id="test-para">This is some test text.</p>');
      const p = document.getElementById('test-para');
      const textNode = p.firstChild;

      simulateTextSelection('test text', textNode, 13, textNode, 23);

      expect(browser.runtime.sendMessage).toHaveBeenCalledTimes(1);
      expect(browser.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'TEXT_SELECTED',
        payload: {
          text: 'test text',
          url: window.location.href,
        },
      });
    });

    test('should not send empty selection', () => {
      setupDOM('<p id="test-para">This is some test text.</p>');
      const p = document.getElementById('test-para');
      const textNode = p.firstChild;

      simulateTextSelection('', textNode, 0, textNode, 0); // Empty selection

      expect(browser.runtime.sendMessage).not.toHaveBeenCalled();
    });
  });

  describe('Message Handling from Background Script', () => {
    test('should activate highlighting mode on ACTIVATE_HIGHLIGHTING message', (done) => {
      const sendResponse = jest.fn((response) => {
        expect(response.success).toBe(true);
        expect(response.message).toBe('Highlighting activated in content script.');
        done();
      });
      if (messageListenerCallback) {
        messageListenerCallback({ type: 'ACTIVATE_HIGHLIGHTING' }, {}, sendResponse);
      } else {
        done.fail('messageListenerCallback not defined');
      }
    });

    test('should deactivate highlighting mode on DEACTIVATE_HIGHLIGHTING message', (done) => {
      // First activate
      if (messageListenerCallback) {
        messageListenerCallback({ type: 'ACTIVATE_HIGHLIGHTING' }, {}, jest.fn());
      } else {
        return done.fail('messageListenerCallback not defined for activation');
      }


      const sendResponse = jest.fn((response) => {
        expect(response.success).toBe(true);
        expect(response.message).toBe('Highlighting deactivated in content script.');
        done();
      });
      if (messageListenerCallback) {
        messageListenerCallback({ type: 'DEACTIVATE_HIGHLIGHTING' }, {}, sendResponse);
      } else {
        done.fail('messageListenerCallback not defined for deactivation');
      }
    });

    test('should return error for unknown message type', (done) => {
        const sendResponse = jest.fn((response) => {
            expect(response.success).toBe(false);
            expect(response.message).toContain('Unknown message type');
            done();
        });
        if (messageListenerCallback) {
            messageListenerCallback({ type: 'UNKNOWN_MESSAGE_TYPE' }, {}, sendResponse);
        } else {
            done.fail('messageListenerCallback not defined');
        }
    });
  });

  describe('Highlighting Functionality', () => {
    beforeEach(() => {
        // Activate highlighting mode for these tests
        if (messageListenerCallback) {
            messageListenerCallback({ type: 'ACTIVATE_HIGHLIGHTING' }, {}, jest.fn());
            browser.runtime.sendMessage.mockClear(); // Clear calls from activation
        } else {
            throw new Error("messageListenerCallback not defined for highlighting setup");
        }
    });

    test('should apply highlight and send data when text selected in highlighting mode', () => {
      setupDOM('<p id="highlight-para">Highlight this important text.</p>');
      const p = document.getElementById('highlight-para');
      const textNode = p.firstChild;

      const mockSurroundContents = jest.fn(); // To track calls
      const originalCreateRange = document.createRange;
      document.createRange = jest.fn(() => {
          const range = originalCreateRange.call(document);
          // JSDOM's range.surroundContents is problematic.
          // We mock it to actually insert the span for this test's querySelector to work,
          // and also call our mockSurroundContents to verify the content script attempts it.
          range.surroundContents = (newNode) => {
            mockSurroundContents(newNode); // Track the call

            // Actual DOM manipulation for the test
            // This is a simplified version of surroundContents
            const commonAncestor = range.commonAncestorContainer;
            try {
                range.deleteContents(); // Remove the selected text
                // If commonAncestor is a text node, insert into its parent
                if (commonAncestor.nodeType === Node.TEXT_NODE) {
                    commonAncestor.parentNode.insertBefore(newNode, commonAncestor.nextSibling);
                } else {
                    // If it's an element, try to insert at the start of the range
                    // This might need refinement based on actual range content
                    const refNode = range.startContainer.childNodes[range.startOffset] || range.startContainer.nextSibling;
                    range.startContainer.insertBefore(newNode, refNode);
                }
            } catch (e) {
                console.error("Mock surroundContents error during test:", e);
                // Fallback: just append to parent of startContainer if specific insertion fails
                if (range.startContainer.parentNode) {
                    range.startContainer.parentNode.appendChild(newNode);
                } else {
                    document.body.appendChild(newNode); // Last resort
                }
            }
          };
          return range;
      });

      browser.runtime.sendMessage.mockClear(); // Ensure clean slate for this specific test action
      simulateTextSelection('important text', textNode, 14, textNode, 28); // This triggers the mouseup, then content_script calls range.surroundContents

      expect(browser.runtime.sendMessage).toHaveBeenCalledWith(expect.objectContaining({
        type: 'HIGHLIGHT_CREATED',
        payload: expect.objectContaining({
          text: 'important text',
          url: window.location.href,
          range: expect.objectContaining({
            startPath: '/mock/path/to/#text', // This comes from our getXPathForNode mock
            endPath: '/mock/path/to/#text',   // This comes from our getXPathForNode mock
            startOffset: 14,
            endOffset: 28,
          }),
        }),
      }));

      expect(mockSurroundContents).toHaveBeenCalledTimes(1); // Verify content_script attempted to surround
      // Now query the DOM, expecting the span to be there due to our modified mock
      const highlightedSpan = p.querySelector('span.pka-highlight'); // Query within the paragraph
      expect(highlightedSpan).not.toBeNull();

      document.createRange = originalCreateRange; // Restore
    });

    test('should not send TEXT_SELECTED message when highlighting mode is active and text is selected', () => {
        setupDOM('<p id="test-para">This is some test text.</p>');
        const p = document.getElementById('test-para');
        const textNode = p.firstChild;

        const mockSurroundContents = jest.fn();
        const originalCreateRange = document.createRange;
        document.createRange = jest.fn(() => {
            const range = originalCreateRange.call(document);
            range.surroundContents = mockSurroundContents;
            return range;
        });

        browser.runtime.sendMessage.mockClear(); // Ensure clean slate
        simulateTextSelection('test text', textNode, 13, textNode, 23);

        expect(browser.runtime.sendMessage).not.toHaveBeenCalledWith(expect.objectContaining({
            type: 'TEXT_SELECTED'
        }));
        // It should have been called with HIGHLIGHT_CREATED
        expect(browser.runtime.sendMessage).toHaveBeenCalledWith(expect.objectContaining({
            type: 'HIGHLIGHT_CREATED'
        }));
        document.createRange = originalCreateRange; // Restore
    });

    test('should handle error during range.surroundContents gracefully', () => {
        setupDOM('<p id="highlight-para">Highlight this important text.</p>');
        const p = document.getElementById('highlight-para');
        const textNode = p.firstChild;
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});


        const originalCreateRange = document.createRange;
        document.createRange = jest.fn(() => {
            const range = originalCreateRange.call(document);
            range.surroundContents = jest.fn().mockImplementation(() => {
                throw new Error("Simulated surroundContents error");
            });
            return range;
        });

        simulateTextSelection('important text', textNode, 14, textNode, 28);

        expect(browser.runtime.sendMessage).toHaveBeenCalledWith(expect.objectContaining({
            type: 'HIGHLIGHT_CREATED', // Message should still be sent
        }));
        expect(consoleErrorSpy).toHaveBeenCalledWith("Error surrounding contents for highlight:", expect.any(Error));

        consoleErrorSpy.mockRestore();
        document.createRange = originalCreateRange;
    });
  });
});