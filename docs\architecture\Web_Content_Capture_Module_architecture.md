# High-Level Architecture: Web Content Capture Module

**Version:** 1.0
**Date:** May 12, 2025
**Based on:** [`docs/PRD.md`](docs/PRD.md), [`docs/specs/Web_Content_Capture_Module_overview.md`](docs/specs/Web_Content_Capture_Module_overview.md)

## 1. Overview

The Web Content Capture Module enables users to capture web content through a browser extension. It focuses on various capture modes (full page, article, selection, bookmark, PDF), metadata extraction, content cleaning, and formatting (primarily to <PERSON><PERSON>), before handing off the data to a core storage service. The architecture prioritizes speed, reliability, and user privacy, aligning with a local-first approach.

## 2. Architectural Goals

*   **Modularity:** Clearly defined components with distinct responsibilities.
*   **Performance:** Fast and non-intrusive capture experience (NFR 6.3.1, NFR-WCC-001).
*   **Reliability:** Ensure captured data is consistently and accurately processed and prepared for storage (NFR 6.3.2, NFR-WCC-002).
*   **Flexibility:** Support for multiple capture modes and output formats.
*   **Maintainability:** Well-structured code for easier updates and bug fixes.
*   **Privacy:** Handle user data securely, especially during processing (NFR-WCC-004).

## 3. Key Components

The module consists of client-side components (within the browser extension) and potentially a local application service for heavier processing, interfacing with a core storage system.

### 3.1. Browser Extension (Client-Side)

*   **Purpose:** User interface for initiating capture, selecting modes, previewing content, and interacting with web pages.
*   **Sub-components:**
    *   **Popup UI / Sidebar UI:** Provides the user interface for selecting capture mode (Full Page, Article, Selection, Bookmark, PDF), viewing extracted metadata, previewing content, and initiating the save. Built with HTML, CSS, JavaScript.
    *   **Content Script(s):** JavaScript files injected into web pages. Responsible for DOM interaction, such as reading full page HTML, capturing user selections, and initial lightweight metadata scraping (e.g., page title, URL).
    *   **Background Script / Service Worker:** A persistent or event-driven script that manages the extension's state, handles communication between UI, content scripts, and the Content Processing Service. Orchestrates the capture workflow.
*   **Responsibilities:**
    *   Presenting capture options to the user.
    *   Capturing raw content from the web page based on the selected mode.
    *   Performing initial, quick metadata extraction (URL, visible title).
    *   Communicating with the Content Processing Service for advanced processing.
    *   Displaying previews and final metadata to the user.
    *   Sending confirmed capture data to the Storage Interface.
    *   Fetching and applying user configurations (default mode/format).

### 3.2. Content Processing Service (Local Application Service or Integrated Library)

*   **Purpose:** Handles the transformation of raw captured data into a clean, structured, and formatted representation ready for storage. This service is envisioned to run locally as part of the main PKM application or as a tightly integrated library to uphold privacy and offline capabilities.
*   **Sub-components:**
    *   **Content Parsing & Cleaning Engine:**
        *   Responsibility: Extracts the main article content from HTML (for "Article View"), removes clutter (ads, navigation), and sanitizes HTML. Processes user selections.
        *   Technology: Utilizes libraries like Mozilla's Readability.js or similar for article extraction, and DOMPurify or equivalent for sanitization.
    *   **Metadata Extraction Engine:**
        *   Responsibility: Extracts detailed metadata from the content or page (Author, Publication Date, etc.) using heuristics, and by parsing embedded metadata (JSON-LD, OpenGraph, Microdata).
        *   Technology: Custom parsing logic, potentially libraries for structured data extraction.
    *   **Format Conversion Engine:**
        *   Responsibility: Converts the cleaned content into the user-selected format, primarily Markdown.
        *   Technology: Libraries like Turndown for HTML-to-Markdown conversion.
*   **Responsibilities:**
    *   Receiving raw content (HTML, text, URL) from the Browser Extension.
    *   Implementing "article view" logic.
    *   Extracting comprehensive metadata.
    *   Converting content to specified formats (e.g., Markdown).
    *   Returning processed content and metadata for preview and final save.

### 3.3. Storage Interface (API to Core Application)

*   **Purpose:** A well-defined API provided by the Core PKM Application that the Web Content Capture Module uses to pass the finalized captured content and metadata for persistent storage.
*   **Responsibilities (of the interface, not the capture module itself):**
    *   Accepting captured data (content, metadata, format, source URL, capture time).
    *   Handling the actual storage mechanism (e.g., local database, file system).
    *   This module *calls* this interface; it does not implement it.

## 4. Component Interactions & Data Flow

The general flow involves the user initiating a capture via the extension UI, the extension gathering raw data, the Content Processing Service refining it, the user previewing/confirming, and finally, the extension sending it to the Storage Interface.

```mermaid
sequenceDiagram
    participant User
    participant Ext_UI as Browser Extension UI
    participant Ext_CS as Content Script
    participant Ext_BG as Background Script
    participant CPS as Content Processing Service (Local)
    participant SI as Storage Interface (Core App)

    User->>Ext_UI: Activates extension, selects mode (e.g., Article)
    Ext_UI->>Ext_BG: Initiate capture (mode, current URL)
    activate Ext_BG

    alt Full Page or Selection
        Ext_BG->>Ext_CS: Request content from page
        activate Ext_CS
        Ext_CS-->>Ext_BG: Raw HTML/selection
        deactivate Ext_CS
    else Article, Bookmark, PDF
        Ext_BG->>Ext_BG: Fetch current URL/page data
    end

    Ext_BG->>CPS: Send Raw Content/URL for processing
    activate CPS
    CPS->>CPS: Parse, Clean, Extract Metadata, Format (e.g., to MD)
    CPS-->>Ext_BG: Processed Content (for preview), Metadata
    deactivate CPS

    Ext_BG->>Ext_UI: Display Preview & Metadata
    User->>Ext_UI: Reviews, Confirms Save
    Ext_UI->>Ext_BG: Save confirmed
    Ext_BG->>SI: Send Final Content (MD), Metadata
    deactivate Ext_BG
    activate SI
    SI-->>Ext_BG: Confirmation (Success/Failure)
    deactivate SI
    Ext_BG->>Ext_UI: Notify user of save status
```

### 4.1. Data Flow Examples:

*   **Article Capture:**
    1.  User clicks "Capture Article" in Extension UI.
    2.  Extension UI sends message to Background Script with current tab URL.
    3.  Background Script sends URL (or full HTML of page) to Content Processing Service.
    4.  CPS:
        *   Fetches page if only URL provided.
        *   Uses Parsing & Cleaning Engine (e.g., Readability.js) to extract main article.
        *   Uses Metadata Extraction Engine for author, pub date.
        *   Uses Format Conversion Engine to convert article to Markdown.
    5.  CPS returns Markdown content and metadata to Background Script.
    6.  Background Script sends preview data to Extension UI.
    7.  User confirms. Background Script sends final Markdown and metadata to Storage Interface.

*   **Full Page Capture:**
    1.  User clicks "Capture Full Page".
    2.  Extension UI signals Background Script.
    3.  Background Script messages Content Script to get `document.documentElement.outerHTML`.
    4.  Content Script returns HTML to Background Script.
    5.  Background Script sends HTML and URL to Content Processing Service.
    6.  CPS:
        *   Performs basic HTML sanitization (optional).
        *   Extracts metadata.
        *   (May not convert to Markdown by default, might save as HTML archive or let user choose).
    7.  CPS returns (potentially sanitized) HTML and metadata for preview.
    8.  User confirms. Background Script sends final HTML/data to Storage Interface.

*   **PDF Capture:**
    1.  Extension UI detects current page is a direct PDF link or user clicks "Capture PDF" on a page linking to one.
    2.  Background Script receives PDF URL.
    3.  Background Script (or CPS via Background Script) initiates download of the PDF.
    4.  Metadata (URL, title from URL, capture date) is assembled.
    5.  User confirms. Background Script sends PDF file (or path to downloaded file) and metadata to Storage Interface.

## 5. Technology Choices & Architectural Patterns

*   **Browser Extension:** Standard WebExtensions API (JavaScript, HTML, CSS). Use of a modern JavaScript framework (e.g., React, Vue, Svelte) for the UI is possible but should be evaluated against performance (NFR 6.6.2) and complexity.
*   **Content Parsing/Cleaning:**
    *   **Article Extraction:** Mozilla Readability.js (JavaScript).
    *   **HTML Sanitization:** DOMPurify (JavaScript).
*   **Markdown Conversion:** Turndown (JavaScript) or similar libraries.
*   **Communication:**
    *   **Extension Internal:** `browser.runtime.sendMessage` and `browser.tabs.sendMessage`.
    *   **Extension to Local CPS:** If CPS is part of a local desktop application, use Native Messaging. If CPS is a local server started by the app, use `fetch` API over HTTP to `localhost`.
*   **Architectural Patterns:**
    *   **Component-Based Architecture:** Within the extension and the CPS.
    *   **Event-Driven:** Actions within the extension (e.g., user clicks, message received) trigger handlers.
    *   **Local-First Processing:** Prioritize processing within the browser extension or a local application component to ensure privacy and offline capability for core capture functions.

## 6. Non-Functional Requirements Compliance

*   **Performance (NFR 6.3.1, NFR-WCC-001):**
    *   Efficient DOM manipulation in Content Scripts.
    *   Asynchronous operations throughout to prevent UI blocking.
    *   Optimized parsing and conversion libraries.
    *   Minimal data transfer between components.
*   **Reliability (NFR 6.3.2, NFR-WCC-002):**
    *   Robust error handling and retry mechanisms (where appropriate, e.g., for network requests if CPS were remote, less critical for local).
    *   Clear feedback to the user on capture status.
    *   Validation of data before passing to Storage Interface.
*   **Privacy (NFR-WCC-004):**
    *   Content processing primarily occurs client-side or via a trusted local application service.
    *   No unnecessary data sent to external services for basic capture.
*   **Lightweight UI (NFR 6.6.2):**
    *   Simple HTML/CSS/JS for the extension UI. Avoid heavy frameworks if they impact startup or responsiveness.

## 7. Integration Points

*   **Primary: Storage Interface (Core Application):**
    *   The Capture Module sends an object containing:
        *   `content`: (String - Markdown, HTML, or path to PDF)
        *   `metadata`: { `title`: String, `originalUrl`: String, `captureDate`: ISOString, `author`: String (optional), `publicationDate`: ISOString (optional), ... }
        *   `format`: (String - e.g., "markdown", "html", "pdf")
        *   `sourceUrl`: String (Original URL of the captured page)
*   **Configuration Service (Core Application):**
    *   The Capture Module will fetch user preferences like:
        *   Default capture mode (e.g., "article", "fullpage").
        *   Default save format (e.g., "markdown").
    *   API: `getConfiguration(): Promise<UserConfig>`
*   **Intelligent Organization Module & Knowledge Base Module (Indirect):** These modules will consume the data saved by the Core Application, which originates from the Capture Module. The Capture Module ensures clean, well-structured data is available for them.

## 8. Future Considerations / Scalability

*   **Offloading to Web Workers:** For CPU-intensive tasks within the browser extension (like complex parsing if not done by CPS), Web Workers can be used to avoid blocking the main extension thread.
*   **More Sophisticated Metadata Extraction:** Could involve more advanced NLP techniques if requirements evolve, potentially as a separate, pluggable engine within CPS.
*   **Support for More Formats:** The Format Conversion Engine can be extended.
*   **Queue for Processing (if CPS becomes a bottleneck):** If the local CPS handles many concurrent requests or very large pages leading to delays, an in-process asynchronous queue could manage tasks. For the current scope, direct asynchronous processing is likely sufficient.

This high-level architecture provides a foundation for developing a robust and efficient Web Content Capture Module. Detailed design of each component and interface will follow.