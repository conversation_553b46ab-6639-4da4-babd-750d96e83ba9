import React, { useEffect } from 'react';
import './NavigationFiltersPane.css'; // Import the CSS file
// Removed getTags, getCategories as they are now managed in store or assumed available
import useStore from '../store/useStore'; // Import Zustand store

// The className prop is passed from App.js and includes 'navigation-pane'
// which defines width, padding, border, and height.
// We add 'navigation-filters-pane-component' for specific styles of this component.
function NavigationFiltersPane({ className }) {
  const searchTerm = useStore((state) => state.searchTerm);
  const setSearchTerm = useStore((state) => state.setSearchTerm);
  const performSearch = useStore((state) => state.performSearch);
  const searchLoading = useStore((state) => state.searchLoading);
  const searchError = useStore((state) => state.searchError);

  const allTags = useStore((state) => state.allTags);
  const selectedTags = useStore((state) => state.selectedTags);
  const setSelectedTags = useStore((state) => state.setSelectedTags);

  const dateRangeFilter = useStore((state) => state.dateRangeFilter);
  const setDateRangeFilter = useStore((state) => state.setDateRangeFilter);

  const allContentTypes = useStore((state) => state.allContentTypes);
  const selectedContentTypes = useStore((state) => state.selectedContentTypes);
  const setSelectedContentTypes = useStore((state) => state.setSelectedContentTypes);

  const allCategories = useStore((state) => state.allCategories);
  const selectedCategories = useStore((state) => state.selectedCategories);
  const setSelectedCategories = useStore((state) => state.setSelectedCategories);

  const sourceFilter = useStore((state) => state.sourceFilter);
  const setSourceFilter = useStore((state) => state.setSourceFilter);

  const tagFilterLogic = useStore((state) => state.tagFilterLogic);
  const setTagFilterLogic = useStore((state) => state.setTagFilterLogic);
  
  const clearAdvancedFilters = useStore((state) => state.clearAdvancedFilters);

 const sortCriteria = useStore((state) => state.sortCriteria);
 const setSortCriteria = useStore((state) => state.setSortCriteria);

 const sortOptions = [
   { value: 'date_desc', label: 'Date (Newest First)' },
   { value: 'date_asc', label: 'Date (Oldest First)' },
   { value: 'title_asc', label: 'Title (A-Z)' },
   { value: 'title_desc', label: 'Title (Z-A)' },
   { value: 'relevance_desc', label: 'Relevance' },
 ];

 // Effect to trigger search when filters or sort order change
 useEffect(() => {
   performSearch(searchTerm);
 }, [selectedTags, dateRangeFilter, selectedContentTypes, selectedCategories, sourceFilter, tagFilterLogic, searchTerm, sortCriteria, performSearch]);


  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    // Search is triggered by useEffect above when searchTerm changes
  };

  const handleSearchSubmit = (event) => {
    event.preventDefault();
    performSearch(searchTerm); // Explicitly call on submit as well
  };

  const handleTagChange = (tagName) => {
    const newSelectedTags = selectedTags.includes(tagName)
      ? selectedTags.filter(t => t !== tagName)
      : [...selectedTags, tagName];
    setSelectedTags(newSelectedTags);
    // Search is triggered by useEffect
  };

  const handleDateChange = (event) => {
    const { name, value } = event.target;
    setDateRangeFilter({ ...dateRangeFilter, [name]: value });
    // Search is triggered by useEffect
  };

  const handleContentTypeChange = (contentTypeName) => {
    const newSelectedContentTypes = selectedContentTypes.includes(contentTypeName)
      ? selectedContentTypes.filter(ct => ct !== contentTypeName)
      : [...selectedContentTypes, contentTypeName];
    setSelectedContentTypes(newSelectedContentTypes);
    // Search is triggered by useEffect
  };

  const handleCategoryChange = (categoryName) => {
    // Assuming selectedCategories is a single string in the store now
    setSelectedCategories(categoryName);
    // Search is triggered by useEffect
  };

  const handleSourceChange = (event) => {
    setSourceFilter(event.target.value);
    // Search is triggered by useEffect
  };

  const handleTagLogicChange = (event) => {
    setTagFilterLogic(event.target.value);
    // Search is triggered by useEffect
  };

  const handleClearFilters = () => {
    clearAdvancedFilters();
    // setSearchTerm(''); // Optionally clear search term as well
    // Search is triggered by useEffect due to filter state changes
  };

  const handleSortChange = (event) => {
    const [by, order] = event.target.value.split('_');
    setSortCriteria({ by, order });
    // Search is triggered by useEffect
  };

  return (
    <div className={`${className} navigation-filters-pane-component`}>
      <h3>Navigation & Filters</h3>

      <div className="search-section filter-section">
        <h4>Global Search</h4>
        <form onSubmit={handleSearchSubmit}>
          <input
            type="text"
            placeholder="Search knowledge..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="search-input"
          />
          <button type="submit" disabled={searchLoading} className="search-button">
            {searchLoading ? 'Searching...' : 'Search'}
          </button>
        </form>
        {searchError && <p className="error-message search-error-message">Search Error: {searchError}</p>}
      </div>

      <div className="filter-section">
        <h4>Filter by Tags ({tagFilterLogic})</h4>
        <div className="tag-logic-options">
          <label>
            <input
              type="radio"
              name="tagLogic"
              value="AND"
              checked={tagFilterLogic === 'AND'}
              onChange={handleTagLogicChange}
            />
            AND
          </label>
          <label>
            <input
              type="radio"
              name="tagLogic"
              value="OR"
              checked={tagFilterLogic === 'OR'}
              onChange={handleTagLogicChange}
            />
            OR
          </label>
        </div>
        {allTags.length === 0 && <p>No tags available.</p>}
        <div className="filter-options-group">
          {allTags.map(tag => (
            <div key={tag} className="filter-option">
              <input
                type="checkbox"
                id={`tag-${tag}`}
                value={tag}
                checked={selectedTags.includes(tag)}
                onChange={() => handleTagChange(tag)}
              />
              <label htmlFor={`tag-${tag}`}>{tag}</label>
            </div>
          ))}
        </div>
      </div>

      <div className="filter-section">
        <h4>Filter by Categories</h4>
        {allCategories.length === 0 && <p>No categories available.</p>}
        <div className="filter-options-group">
          {allCategories.map(category => (
            <div key={category} className="filter-option">
              <input
                type="radio" // Changed to radio for single selection
                id={`category-${category}`}
                value={category}
                checked={selectedCategories === category}
                onChange={() => handleCategoryChange(category)}
              />
              <label htmlFor={`category-${category}`}>{category}</label>
            </div>
          ))}
          {/* Option to clear category filter */}
          {selectedCategories && (
            <div className="filter-option">
              <input
                type="radio"
                id="category-none"
                value=""
                checked={selectedCategories === ''}
                onChange={() => handleCategoryChange('')}
              />
              <label htmlFor="category-none">None</label>
            </div>
          )}
        </div>
      </div>
      
      <div className="filter-section">
        <h4>Filter by Source</h4>
        <input
          type="text"
          placeholder="Enter source (e.g., example.com)"
          value={sourceFilter}
          onChange={handleSourceChange}
          className="source-input"
        />
      </div>

      <div className="filter-section">
        <h4>Filter by Creation Date</h4>
        <div className="date-range-filter">
          <div className="date-input-group">
            <label htmlFor="startDate">From:</label>
            <input
              type="date"
              id="startDate"
              name="startDate"
              value={dateRangeFilter.startDate || ''}
              onChange={handleDateChange}
              className="date-input"
            />
          </div>
          <div className="date-input-group">
            <label htmlFor="endDate">To:</label>
            <input
              type="date"
              id="endDate"
              name="endDate"
              value={dateRangeFilter.endDate || ''}
              onChange={handleDateChange}
              className="date-input"
            />
          </div>
        </div>
      </div>

      <div className="filter-section">
        <h4>Filter by Content Type</h4>
        {allContentTypes.length === 0 && <p>No content types available.</p>}
        <div className="filter-options-group">
          {allContentTypes.map(type => (
            <div key={type} className="filter-option">
              <input
                type="checkbox"
                id={`content-type-${type}`}
                value={type}
                checked={selectedContentTypes.includes(type)}
                onChange={() => handleContentTypeChange(type)}
              />
              <label htmlFor={`content-type-${type}`}>{type}</label>
            </div>
          ))}
        </div>
      </div>

      <div className="filter-section">
        <h4>Sort By</h4>
        <select value={`${sortCriteria.by}_${sortCriteria.order}`} onChange={handleSortChange} className="sort-select">
          {sortOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      <div className="filter-actions-section filter-section">
        <button onClick={handleClearFilters} className="clear-filters-button">
          Clear All Filters
        </button>
      </div>
      
      {/* Removed old Main Sections and Categories as they are not part of this task's scope for advanced filters */}
    </div>
  );
}

export default NavigationFiltersPane;