import React from 'react';
import './DataManagement.css';
import useStore from '../store/useStore';

const DataManagement = () => {
  const { exportData, importData, exportInProgress, importInProgress, exportError, importError } = useStore(state => ({
    exportData: state.exportData,
    importData: state.importData,
    exportInProgress: state.exportInProgress,
    importInProgress: state.importInProgress,
    exportError: state.exportError,
    importError: state.importError,
  }));

  const handleExport = async () => {
    try {
      await exportData();
      // Optionally, show a success message to the user
      alert('Export initiated successfully. Check your chosen location.');
    } catch (error) {
      console.error('Export failed:', error);
      // Optionally, show an error message to the user
      alert(`Export failed: ${error.message}`);
    }
  };

  const handleImport = async () => {
    try {
      // The importData action in the store will handle the file dialog
      await importData();
      // Optionally, show a success message to the user
      alert('Import initiated successfully. The application may reload or update.');
    } catch (error) {
      console.error('Import failed:', error);
      // Optionally, show an error message to the user
      alert(`Import failed: ${error.message}`);
    }
  };

  return (
    <div className="data-management-section">
      <h3>Data Management</h3>
      <div className="data-management-controls">
        <div className="control-group">
          <p>Export your entire knowledge base as an archive.</p>
          <button onClick={handleExport} disabled={exportInProgress}>
            {exportInProgress ? 'Exporting...' : 'Export Knowledge Base'}
          </button>
          {exportError && <p className="error-message">Export Error: {exportError}</p>}
        </div>
        <div className="control-group">
          <p>Import a previously exported knowledge base. This may overwrite existing data.</p>
          <button onClick={handleImport} disabled={importInProgress}>
            {importInProgress ? 'Importing...' : 'Import Knowledge Base'}
          </button>
          {importError && <p className="error-message">Import Error: {importError}</p>}
        </div>
      </div>
    </div>
  );
};

export default DataManagement;