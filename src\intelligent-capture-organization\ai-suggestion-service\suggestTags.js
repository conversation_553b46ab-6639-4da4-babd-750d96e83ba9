require('dotenv').config();
import { GoogleGenerativeAI } from '@google/generative-ai';
import { logInfo, logError } from '../../knowledge-base-interaction/features/content-summarization/utils/logger';

const DEFAULT_MAX_TAGS = 5;

// Initialize Gemini AI
const API_KEY = process.env.GEMINI_API_KEY; // Use a specific environment variable for Gemini API Key

if (!API_KEY) {
  logError("GEMINI_API_KEY environment variable is not set. Please set your Gemini API key.");
  // In a real application, you might want to throw an error or handle this more gracefully
  // For now, we'll proceed with a dummy model or return empty results if API_KEY is missing.
}

const genAI = API_KEY ? new GoogleGenerativeAI(API_KEY) : null;
const model = genAI ? genAI.getGenerativeModel({ model: "gemini-1.5-pro" }) : null;

/**
 * Cleans the input text for consistent processing.
 * @param {string} text
 * @returns {string} Cleaned text.
 */
function cleanText(text) {
  if (!text || typeof text !== 'string') return '';
  return text.toLowerCase().replace(/[^\w\s-]/g, "").replace(/\s+/g, " ").trim();
}

/**
 * Parses the AI's response to extract tag suggestions.
 * Assumes the AI might return tags in a list format, e.g., "1. Tag A, 2. Tag B" or "Tags: A, B, C".
 * @param {string} aiResponse - The raw response string from the AI.
 * @returns {string[]} An array of extracted tag strings.
 */
function parseAiTags(aiResponse) {
  const tags = new Set();
  if (!aiResponse) return [];

  // Attempt to find tags in common list formats
  const commaSeparated = aiResponse.split(/,\s*/);
  const numberedList = aiResponse.split(/\d+\.\s*/).filter(Boolean);
  const bulletList = aiResponse.split(/-\s*/).filter(Boolean);

  [commaSeparated, numberedList, bulletList].forEach(parts => {
    parts.forEach(part => {
      const cleanedPart = cleanText(part);
      if (cleanedPart.length > 0) {
        tags.add(cleanedPart.split('\n')[0].trim()); // Take only the first line if multi-line
      }
    });
  });

  return Array.from(tags).filter(tag => tag.length > 1); // Filter out very short or empty strings
}

/**
 * Suggests relevant tags for a given piece of content using Gemini AI.
 *
 * @param {string} content - The content to suggest tags for.
 * @param {string[]} [existingTags=[]] - An array of already existing tags for the content.
 * @param {number} [maxTags=DEFAULT_MAX_TAGS] - The maximum number of tags to suggest.
 * @returns {Promise<string[]>} A promise that resolves to an array of suggested tag strings.
 */
export async function suggestTags(content, existingTags = [], maxTags = DEFAULT_MAX_TAGS) {
  logInfo('AI Suggestion Service: Requesting tag suggestions from Gemini AI.', { contentSnippet: content.substring(0, 100) });

  if (!model) {
    logError('AI Suggestion Service: Gemini AI model not initialized. API_KEY might be missing or invalid.');
    return [];
  }

  if (!content || content.trim().length < 50) { // Increased minimum content length for AI
    logInfo('AI Suggestion Service: Content too short for meaningful AI tag suggestions.');
    return [];
  }

  try {
    const prompt = `Given the following content, suggest up to ${maxTags} relevant tags. Provide only the tag names, separated by commas. Avoid tags already present: ${existingTags.join(', ')}. Content: "${content}"`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const aiResponseText = response.text();

    if (aiResponseText) {
      const aiSuggestedTags = parseAiTags(aiResponseText);
      const lowerExistingTags = existingTags.map(tag => cleanText(tag));
      const finalSuggestions = new Set();

      for (const tag of aiSuggestedTags) {
        if (!lowerExistingTags.includes(cleanText(tag))) {
          finalSuggestions.add(tag);
        }
        if (finalSuggestions.size >= maxTags) {
          break;
        }
      }

      const result = Array.from(finalSuggestions);
      logInfo('AI Suggestion Service: Generated tag suggestions from Gemini AI.', { suggestions: result });
      return result;
    } else {
      logError('AI Suggestion Service: No valid response from Gemini AI for tag suggestion.');
      return [];
    }
  } catch (error) {
    logError('AI Suggestion Service: Error suggesting tags with Gemini AI.', error);
    // Log more details about the error if available
    if (error.status) {
      logError(`HTTP Status: ${error.status}`);
    }
    if (error.message) {
      logError(`Error Message: ${error.message}`);
    }
    return [];
  }
}