/// <reference types="chrome" />
import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom/client';
import '@/styles/tailwind.css'; // Global Tailwind CSS
import './index.css'; // Popup-specific styles (can override or add to Tailwind)
import SuggestionDisplay from '../../organization/SuggestionDisplay'; // Import SuggestionDisplay
import SummaryDisplay from '../../organization/SummaryDisplay'; // Import SummaryDisplay
import TagInput from '../../organization/TagInput'; // Import TagInput
import CategoryInput from '../../organization/CategoryInput'; // Import CategoryInput
import NotesInput from '../../organization/NotesInput'; // Import NotesInput
import { sendMessageToBackground } from '../options/KnowledgeBaseView'; // Re-use the helper

console.log('[Popup.tsx] Script start'); // Debug log

export const Popup = () => {
  console.log('[Popup.tsx] Popup component function start'); // Debug log
  const [currentTabInfo, setCurrentTabInfo] = useState<{ title: string; url: string }>({ title: '', url: '' });
  const [message, setMessage] = useState<string>('');
  const [suggestedTags, setSuggestedTags] = useState<string[]>([]);
  const [suggestedCategories, setSuggestedCategories] = useState<string[]>([]);
  const [suggestedSummary, setSuggestedSummary] = useState<string>('');
  const [userTags, setUserTags] = useState<string[]>([]);
  const [userCategory, setUserCategory] = useState<string>('');
  const [userNotes, setUserNotes] = useState<string>('');
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState<boolean>(true);


  useEffect(() => {
    console.log('[Popup.tsx] useEffect start');
    
    const fetchTabInfoAndSuggestions = async () => {
      setIsLoadingSuggestions(true);
      try {
        // 1. Get current tab info
        const tabInfoResponse = await sendMessageToBackground<{ success: boolean, tab?: { title: string, url: string }, error?: string }>({ action: 'getCurrentTabInfo' });
        if (tabInfoResponse.success && tabInfoResponse.tab) {
          setCurrentTabInfo(tabInfoResponse.tab);
        } else {
          throw new Error(tabInfoResponse.error || 'Failed to get current tab info.');
        }

        // 2. Fetch AI suggestions based on the tab's content (or a placeholder for now)
        // TODO: Implement actual content fetching from the active tab
        const contentToAnalyze = "This is some content from the current page to analyze for suggestions."; // Placeholder content
        const suggestionsResponse = await sendMessageToBackground<{ success: boolean, suggestions?: { tags: string[], categories: string[], summary: string }, error?: string }>({
          action: 'getAISuggestions',
          data: { content: contentToAnalyze }
        });

        if (suggestionsResponse.success && suggestionsResponse.suggestions) {
          setSuggestedTags(suggestionsResponse.suggestions.tags);
          setSuggestedCategories(suggestionsResponse.suggestions.categories);
          setSuggestedSummary(suggestionsResponse.suggestions.summary);
          setUserTags(suggestionsResponse.suggestions.tags); // Initialize user tags with suggested tags
          if (suggestionsResponse.suggestions.categories.length > 0) {
            setUserCategory(suggestionsResponse.suggestions.categories[0]); // Initialize user category with the first suggestion
          }
        } else {
          throw new Error(suggestionsResponse.error || 'Failed to fetch AI suggestions.');
        }

      } catch (err: any) {
        console.error('[Popup.tsx] Error fetching tab info or suggestions:', err);
        setMessage(`Error: ${err.message || 'Failed to load data.'}`);
        // Fallback to placeholder data if API fails
        setCurrentTabInfo({ title: 'Error Loading Page', url: 'about:blank' });
        setSuggestedTags(['error', 'fallback']);
        setSuggestedCategories(['Error']);
        setSuggestedSummary('Could not load suggestions due to an error.');
      } finally {
        setIsLoadingSuggestions(false);
      }
    };

    fetchTabInfoAndSuggestions();

  }, []); // Empty dependency array means this effect runs once on mount

  const handleCaptureBookmark = async () => {
    setMessage('Attempting to capture bookmark...');
    try {
      const response = await sendMessageToBackground<{ success: boolean, entry?: any, error?: string }>(
        {
          action: 'addBookmark',
          data: {
            title: currentTabInfo.title,
            url: currentTabInfo.url,
            tags: userTags,
            category: userCategory,
            notes: userNotes,
            summary: suggestedSummary,
          },
        }
      );
      if (response.success) {
        setMessage('Bookmark captured successfully!');
        console.log('Bookmark captured:', response);
      } else {
        throw new Error(response.error || 'Unknown error during bookmark capture.');
      }
    } catch (err: any) {
      console.error('Failed to capture bookmark:', err);
      setMessage(`Failed: ${err.message}`);
    }
  };

  // Handlers for user interaction with suggestions/inputs
  const handleSuggestedTagClick = (tag: string) => {
    if (!userTags.includes(tag)) {
      setUserTags([...userTags, tag]);
    }
  };

  const handleSuggestedCategoryClick = (category: string) => {
    setUserCategory(category);
  };


  console.log('[Popup.tsx] Popup component before return'); // Debug log
  return (
    <div style={{ width: '300px', padding: '10px' }} data-testid="popup-container">
      <h1>Web Content Capture</h1>
      <div>
        <h3>Current Tab:</h3>
        <p id="current-tab-title" data-testid="current-tab-title" style={{ wordBreak: 'break-all' }}>{currentTabInfo.title || 'Loading...'}</p>
        <p id="current-tab-url" data-testid="current-tab-url" style={{ wordBreak: 'break-all', fontSize: '0.9em', color: 'gray' }}>
          {currentTabInfo.url || 'Loading...'}
        </p>
      </div>

      {isLoadingSuggestions ? (
        <p data-testid="loading-suggestions">Loading suggestions...</p>
      ) : (
        <>
          <SummaryDisplay summary={suggestedSummary} />

          <SuggestionDisplay
            title="Suggested Tags"
            suggestions={suggestedTags}
            onSuggestionClick={handleSuggestedTagClick}
          />

          <TagInput initialTags={userTags} onTagsChange={setUserTags} />

          <SuggestionDisplay
            title="Suggested Categories"
            suggestions={suggestedCategories}
            onSuggestionClick={handleSuggestedCategoryClick}
          />

          <CategoryInput
            initialCategory={userCategory}
            onCategoryChange={setUserCategory}
            suggestedCategories={suggestedCategories} // Pass suggested categories to CategoryInput
          />

          <NotesInput initialNotes={userNotes} onNotesChange={setUserNotes} />

        </>
      )}


      <button id="capture-bookmark-button" data-testid="capture-bookmark-button" onClick={handleCaptureBookmark} style={{ marginTop: '20px' }}>
        Capture Bookmark
      </button>
      {message && <p style={{ marginTop: '10px', color: message.startsWith('Error') || message.startsWith('Failed') ? 'red' : 'green' }}>{message}</p>}
    </div>
  );
};

const rootElement = document.getElementById('root');
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <Popup />
    </React.StrictMode>
  );
} else {
  console.error('Failed to find the root element');
}