<!DOCTYPE html>
<html>
<head>
    <title>Web Content Capture</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <h1>Capture Web Content</h1>

        <div class="section">
            <h2>Capture Mode</h2>
            <div class="capture-modes">
                <button id="captureFullPage" data-mode="fullPage">Full Page</button>
                <button id="captureArticle" data-mode="article">Article View</button>
                <button id="captureSelection" data-mode="selection">Selection</button>
                <button id="captureBookmark" data-mode="bookmark">Bookmark</button>
                <button id="capturePdf" data-mode="pdf" style="display:none;">Capture PDF</button>
            </div>
        </div>

        <div class="section">
            <h2>Metadata</h2>
            <div id="metadataDisplay">
                <p><strong>Original URL:</strong> <span id="metaUrl">-</span></p>
                <p><strong>Original Title:</strong> <span id="metaTitle">-</span></p>
                <p><strong>Capture Date:</strong> <span id="metaCaptureDate">-</span></p>
                <p><strong>Author:</strong> <span id="metaAuthor">-</span></p>
                <p><strong>Publication Date:</strong> <span id="metaPubDate">-</span></p>
            </div>
        </div>

        <div class="section" id="contentPreviewSection" style="display:none;">
            <h2>Content Preview</h2>
            <div id="contentPreview">
                <p><em>Preview will appear here...</em></p>
            </div>
        </div>

        <div class="section">
            <h2>Save Options</h2>
            <div>
                <label for="saveFormat">Save Format:</label>
                <select id="saveFormat">
                    <option value="markdown" selected>Markdown</option>
                    <option value="html">HTML</option>
                    <!-- Add other formats if needed -->
                </select>
            </div>
        </div>

        <div class="section">
            <button id="saveButton">Save Capture</button>
            <div id="statusMessage" class="status-message"></div>
        </div>
    </div>
    <script src="popup.js"></script>
</body>
</html>