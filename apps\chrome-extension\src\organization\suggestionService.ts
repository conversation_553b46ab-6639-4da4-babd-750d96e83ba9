// Mock implementation for AI-driven suggestions

/**
 * Provides a mocked list of tag suggestions.
 * @returns An array of strings representing suggested tags.
 */
export const getMockTagSuggestions = (): string[] => {
  return ["mockTag1", "mockTag2", "mockTag3", "typescript", "react"];
};

/**
 * Provides a mocked list of category suggestions.
 * @returns An array of strings representing suggested categories.
 */
export const getMockCategorySuggestions = (): string[] => {
  return ["mockCategoryA", "mockCategoryB", "ProjectX", "KnowledgeBase"];
};