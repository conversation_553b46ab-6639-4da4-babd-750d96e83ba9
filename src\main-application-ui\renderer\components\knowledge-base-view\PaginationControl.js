import React from 'react';
import PropTypes from 'prop-types';

const PaginationControl = ({
  currentPage,
  totalPages,
  onPageChange,
  itemsPerPage,
  totalItems,
  maxPagesToShow = 5, // Maximum number of page buttons to show (excluding prev/next, first/last)
}) => {
  if (totalPages <= 1) {
    return null;
  }

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  const pageNumbers = [];
  const halfMaxPages = Math.floor(maxPagesToShow / 2);

  let startPage = Math.max(1, currentPage - halfMaxPages);
  let endPage = Math.min(totalPages, currentPage + halfMaxPages);

  if (currentPage - halfMaxPages < 1) {
    endPage = Math.min(totalPages, maxPagesToShow);
  }

  if (currentPage + halfMaxPages > totalPages) {
    startPage = Math.max(1, totalPages - maxPagesToShow + 1);
  }
  
  if (totalPages > maxPagesToShow) {
    if (startPage > 1) {
      pageNumbers.push({ type: 'page', number: 1, label: 'Go to page 1' });
      if (startPage > 2) {
        pageNumbers.push({ type: 'ellipsis', label: 'More pages before' });
      }
    }
  }

  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push({ type: 'page', number: i, label: `Go to page ${i}` });
  }

  if (totalPages > maxPagesToShow) {
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pageNumbers.push({ type: 'ellipsis', label: 'More pages after' });
      }
      pageNumbers.push({ type: 'page', number: totalPages, label: `Go to page ${totalPages}` });
    }
  }
  
  // Filter out duplicate page numbers that might arise from first/last page logic
  const uniquePageNumbers = pageNumbers.reduce((acc, current) => {
    if (current.type === 'ellipsis' || !acc.find(item => item.number === current.number)) {
      acc.push(current);
    }
    return acc;
  }, []);


  const firstItem = (currentPage - 1) * itemsPerPage + 1;
  const lastItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <nav className="pagination-control" aria-label="Pagination">
      <div className="pagination-info">
        Showing {firstItem}-{lastItem} of {totalItems} items
      </div>
      <ul className="pagination-list">
        <li>
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            aria-label="Go to previous page"
            className="pagination-button prev-button"
          >
            Previous
          </button>
        </li>
        {uniquePageNumbers.map((page, index) => (
          <li key={page.type === 'ellipsis' ? `ellipsis-${index}` : `page-${page.number}`}>
            {page.type === 'ellipsis' ? (
              <span className="pagination-ellipsis" aria-hidden="true">...</span>
            ) : (
              <button
                onClick={() => handlePageChange(page.number)}
                className={`pagination-button page-number-button ${currentPage === page.number ? 'active' : ''}`}
                aria-current={currentPage === page.number ? 'page' : undefined}
                aria-label={page.label}
                disabled={currentPage === page.number}
              >
                {page.number}
              </button>
            )}
          </li>
        ))}
        <li>
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            aria-label="Go to next page"
            className="pagination-button next-button"
          >
            Next
          </button>
        </li>
      </ul>
    </nav>
  );
};

PaginationControl.propTypes = {
  currentPage: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  itemsPerPage: PropTypes.number.isRequired,
  totalItems: PropTypes.number.isRequired,
  maxPagesToShow: PropTypes.number,
};

PaginationControl.defaultProps = {
    maxPagesToShow: 5,
};

export default PaginationControl;