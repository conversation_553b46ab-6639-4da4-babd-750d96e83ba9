.category-management-section {
  /* Inherits styles from .settings-section */
}

.category-form {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.category-form input[type="text"] {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
}

.category-form button {
  padding: 10px 15px;
  background-color: #28a745; /* Same as tag form for consistency */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.2s ease;
}

.category-form button:hover {
  background-color: #218838;
}

.category-form button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.category-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.category-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 10px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.category-list-item:last-child {
  border-bottom: none;
}

.category-name {
  font-size: 1.05em;
  color: #333;
}

.category-actions button {
  margin-left: 10px;
  padding: 6px 12px;
  font-size: 0.9em;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid transparent;
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.category-actions button:hover {
  opacity: 0.8;
}

.category-actions button.delete-button {
  background-color: #dc3545;
  color: white;
}

.category-actions button.delete-button:hover {
  background-color: #c82333;
}

.category-actions button:disabled {
  background-color: #e9ecef;
  color: #6c757d;
  border-color: #ced4da;
  cursor: not-allowed;
}

/* Edit Category Modal Styles (similar to Tag modal) */
.edit-category-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.edit-category-modal {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 400px;
}

.edit-category-modal h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5em;
  color: #333;
}

.edit-category-modal input[type="text"] {
  width: calc(100% - 22px);
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
}

.edit-category-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.edit-category-actions button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
}

.edit-category-actions button[type="submit"] {
  background-color: #007bff;
  color: white;
}
.edit-category-actions button[type="submit"]:hover {
  background-color: #0056b3;
}

.edit-category-actions button[type="button"] {
  background-color: #6c757d;
  color: white;
}
.edit-category-actions button[type="button"]:hover {
  background-color: #5a6268;
}

/* Shared error message style (if not already global) */
.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}