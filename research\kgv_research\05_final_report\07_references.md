# References: KnowledgeBaseView and Knowledge Graph Visualization Research Report

1.  Master Project Plan (docs/Master\_Project\_Plan.md)
2.  Knowledge Graph Visualization Feature Overview (docs/specs/Knowledge\_Graph\_Visualization\_Feature\_Overview.md)
3.  Perplexity AI - Chat responses regarding best practices for knowledge graph visualization usability, security vulnerabilities, and performance benchmarks.
4.  GraphVite Documentation - Benchmarks for knowledge graph embeddings.
5.  Stardog Blog - Querying knowledge graphs with billions of triples.