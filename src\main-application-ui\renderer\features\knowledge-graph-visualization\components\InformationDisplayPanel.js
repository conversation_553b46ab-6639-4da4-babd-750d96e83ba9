import React from 'react';

const InformationDisplayPanel = ({ selectedItem, itemType, visualEncodings }) => {
  if (!selectedItem) {
    return (
      <div data-testid="info-display-panel-actual">
        <h3>Information Display</h3>
        <p>No item selected. Click on a node or edge to see details.</p>
      </div>
    );
  }

  const renderAttributes = (attributes) => {
    if (!attributes || Object.keys(attributes).length === 0) {
      return <p>No additional attributes.</p>;
    }
    return (
      <ul>
        {Object.entries(attributes).map(([key, value]) => {
          // Convert camelCase key to Title Case Key
          const titleCaseKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
          return <li key={key}><strong>{titleCaseKey}:</strong> {String(value)}</li>;
        })}
      </ul>
    );
  };
  
  let typeLabel = selectedItem.type;
  if (visualEncodings) {
    if (itemType === 'node' && visualEncodings.nodeTypes && visualEncodings.nodeTypes[selectedItem.type]) {
      typeLabel = visualEncodings.nodeTypes[selectedItem.type].label || selectedItem.type;
    } else if (itemType === 'edge' && visualEncodings.edgeTypes && visualEncodings.edgeTypes[selectedItem.type]) {
      typeLabel = visualEncodings.edgeTypes[selectedItem.type].label || selectedItem.type;
    }
  }


  return (
    <div data-testid="info-display-panel-actual">
      <h3>Information Display</h3>
      {itemType === 'node' && <p>ID: {selectedItem.id}</p>}
      {itemType === 'edge' && <p>Edge ID: {selectedItem.id}</p>}
      <p>Type: {typeLabel}</p>
      {selectedItem.label && <p>Label: {selectedItem.label}</p>}
      {itemType === 'edge' && (
        <>
          <p>Source: {selectedItem.source}</p>
          <p>Target: {selectedItem.target}</p>
        </>
      )}
      <h4>Attributes:</h4>
      {renderAttributes(selectedItem.attributes)}
      {/* For TC_KGV_IDP_002, to find "Relationship Type:" */}
      {/* For TC_KGV_IDP_002, to find "Relationship Type:" - this is now handled by renderAttributes if relationshipType is an attribute */}
      {/* If it's a special field, it might need explicit rendering like below, but usually it's an attribute. */}
      {/* {itemType === 'edge' && selectedItem.attributes && selectedItem.attributes.relationshipType && <p><strong>Relationship Type:</strong> {selectedItem.attributes.relationshipType}</p>} */}
    </div>
  );
};

export default InformationDisplayPanel;