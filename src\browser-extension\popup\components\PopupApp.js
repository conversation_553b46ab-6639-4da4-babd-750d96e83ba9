import React, { useState } from 'react';
import './PopupApp.css';
import CaptureTypeSelector from './CaptureTypeSelector';
import CaptureControls from './CaptureControls';

function PopupApp() {
  const [captureType, setCaptureType] = useState('fullPage'); // Default capture type
  const [captureStatus, setCaptureStatus] = useState('idle'); // 'idle', 'capturing', 'success', 'error'

  const handleCapture = () => {
    setCaptureStatus('capturing');
    // Placeholder for actual capture logic
    console.log(`Initiating capture: ${captureType}`);
    setTimeout(() => {
      // Simulate capture success
      setCaptureStatus('success');
      console.log('Capture successful');
      setTimeout(() => setCaptureStatus('idle'), 2000); // Reset status
    }, 1500);
  };

  return (
    <div className="popup-app">
      <h1>Web Content Capture</h1>
      <CaptureTypeSelector selectedType={captureType} onTypeChange={setCaptureType} />
      <CaptureControls status={captureStatus} onCapture={handleCapture} captureType={captureType} />
      {/* Redundant button and status messages removed as CaptureControls handles them */}
    </div>
  );
}

export default PopupApp;