// main-application-ui/renderer/api/client.js
// API Client Layer
// This module will contain functions to interact with backend services.
// It now integrates KbalService via Electron IPC.

// Access the exposed electronAPI
const electronAPI = window.electronAPI;

const API_BASE_URL = '/api'; // Assume this will be proxied by dev server or Electron main

// Helper function for making API requests (for non-KBAL operations, if any remain)
// Currently, all operations are being routed through KBAL via IPC.
// This 'request' function might become obsolete if no direct HTTP API calls are made.
async function request(endpoint, options = {}) {
  // Check if we're in a test environment
  const isTestEnv = process.env.NODE_ENV === 'test';

  // Robust URL joining
  const baseUrl = options.absoluteUrl ? '' : API_BASE_URL; // Allow absolute URLs if needed

  let url;
  try {
    // In test environment, we'll use a simpler approach
    if (isTestEnv) {
      url = `${baseUrl}${endpoint}`;
    } else {
      url = new URL(endpoint, baseUrl.startsWith('http') ? baseUrl : window.location.origin + baseUrl).toString();
    }

    const response = await fetch(url, options);
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        // Ignore if error response is not JSON
      }
      const errorMessage = errorData?.message || `API Error: ${response.status} ${response.statusText} for ${url}`;
      console.error(`API Error for ${url}: ${errorMessage}`, errorData);
      throw new Error(errorMessage); // Include statusText for more context
    }
    if (response.status === 204) { // No Content
        return null;
    }
    return response.json();
  } catch (error) {
    // Log network errors or other fetch-related issues
    console.error(`Network or other error for ${url}: ${error.message}`, error);
    throw error;
  }
}

/**
 * Fetches a list of items.
 */
export const getItems = async (tags, categories) => {
  console.log(`KBAL Client (IPC): Fetching items with tags: ${tags}, categories: ${categories}`);
  const queryCriteria = {};
  if (tags && tags.length > 0) {
    queryCriteria.tags = tags;
  }
  // KbalService current queryContent doesn't explicitly support categories array like tags.
  // If categories are stored as item 'type', this could be:
  // if (categories && categories.length > 0) queryCriteria.type = categories[0]; // Assuming single category as type for now
  // For now, we'll only filter by tags using KBAL. Categories might be client-side or KBAL needs enhancement.
  try {
    const items = await electronAPI.kbalService.queryContent(queryCriteria);
    return items.map(item => ({
        ...item,
        name: item.title, // Map title to name if UI expects name
        description: item.content // Map content to description
    }));
  } catch (error) {
    console.error('KBAL Client (IPC): Error fetching items:', error);
    throw error;
  }
};

/**
 * Creates a new item.
 */
export const createItem = async (itemData) => {
  console.log(`KBAL Client (IPC): Creating item`, itemData);
  try {
    const kbalItemData = {
      title: itemData.title,
      content: itemData.content,
      type: itemData.itemType || 'note', // Default type if not provided
      sourceUrl: itemData.source,
      metadata: {
        tags: itemData.tags || [],
      },
    };
    const newItem = await electronAPI.kbalService.addContent(kbalItemData);
    if (!newItem || !newItem.id) {
        throw new Error('Failed to retrieve newly created item from KBAL via IPC.');
    }
    return { ...newItem, name: newItem.title, description: newItem.content }; // Map for UI
  } catch (error) {
    console.error('KBAL Client (IPC): Error creating item:', error);
    throw error;
  }
};

/**
 * Fetches a single item by its ID.
 */
export const getItemById = async (itemId) => {
  console.log(`KBAL Client (IPC): Fetching item by ID ${itemId}`);
  try {
    const item = await electronAPI.kbalService.getContentById(itemId);
    if (item) {
      return { ...item, name: item.title, description: item.content }; // Map for UI
    }
    return null;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error fetching item by ID ${itemId}:`, error);
    throw error;
  }
};

/**
 * Updates an existing item.
 */
export const updateItem = async (itemId, itemData) => {
  console.log(`KBAL Client (IPC): Updating item ${itemId}`, itemData);
  try {
    const updates = { ...itemData };
    if (updates.name) {
        updates.title = updates.name;
        delete updates.name;
    }
    if (updates.description) {
        updates.content = updates.description;
        delete updates.description;
    }
    const currentItem = await electronAPI.kbalService.getContentById(itemId);
    if (!currentItem) {
        throw new Error(`Item ${itemId} not found for update.`);
    }
    if (updates.tags) {
        updates.metadata = { ...(currentItem.metadata || {}), tags: updates.tags };
        delete updates.tags;
    }
    if (updates.itemType) {
        updates.type = updates.itemType;
        delete updates.itemType;
    }

    const success = await electronAPI.kbalService.updateContent(itemId, updates);
    if (success) {
      const updatedItem = await electronAPI.kbalService.getContentById(itemId);
      if (!updatedItem) {
        throw new Error('Failed to retrieve updated item from KBAL via IPC.');
      }
      return { ...updatedItem, name: updatedItem.title, description: updatedItem.content }; // Map for UI
    }
    throw new Error(`Failed to update item ${itemId} via IPC. Item may not exist.`);
  } catch (error) {
    console.error(`KBAL Client (IPC): Error updating item ${itemId}:`, error);
    throw error;
  }
};

/**
 * Deletes an item by its ID.
 */
export const deleteItem = async (itemId) => {
  console.log(`KBAL Client (IPC): Deleting item ${itemId}`);
  try {
    const success = await electronAPI.kbalService.deleteContent(itemId);
    if (success) {
      return { success: true, message: `Item ${itemId} deleted successfully from KBAL via IPC.` };
    }
    return { success: false, message: `Failed to delete item ${itemId} via IPC. Item may not exist.` };
  } catch (error) {
    console.error(`KBAL Client (IPC): Error deleting item ${itemId}:`, error);
    throw error;
  }
};

/**
 * Performs a search.
 */
export const searchItems = async (searchParams) => {
  let query = '';
  let filterCriteria = {};

  if (typeof searchParams === 'string') {
    query = searchParams;
  } else if (typeof searchParams === 'object' && searchParams !== null) {
    query = searchParams.query || '';
    if (searchParams.tags) {
        const tagsArray = typeof searchParams.tags === 'string' ? searchParams.tags.split(',') : searchParams.tags;
        if (Array.isArray(tagsArray) && tagsArray.length > 0) {
            filterCriteria.tags = tagsArray.filter(t => t.trim() !== '');
        }
    }
    if (searchParams.contentTypes) {
        const typesArray = typeof searchParams.contentTypes === 'string' ? searchParams.contentTypes.split(',') : searchParams.contentTypes;
        if (Array.isArray(typesArray) && typesArray.length > 0 && typesArray[0].trim() !== '') {
            filterCriteria.type = typesArray[0].trim();
        }
    }
  }

  console.log(`KBAL Client (IPC): Searching items with query: "${query}" and criteria:`, filterCriteria);

  if (query && query.trim() !== '') {
    filterCriteria.titleContains = query.trim();
  }

  try {
    const items = await electronAPI.kbalService.queryContent(filterCriteria);
    return items.map(item => ({
        ...item,
        name: item.title,
        description: item.content
    }));
  } catch (error) {
    console.error(`KBAL Client (IPC): Error searching items with query "${query}":`, error);
    throw error;
  }
};

/**
 * Fetches all tags.
 */
export const fetchTags = async () => {
  console.log('KBAL Client (IPC): Fetching tags.');
  try {
    const tags = await electronAPI.kbalService.fetchTags();
    return tags;
  } catch (error) {
    console.error('KBAL Client (IPC): Error fetching tags:', error);
    throw error;
  }
};
 
/**
 * Creates a new tag.
 */
export const createTag = async (tagData) => {
  console.log(`KBAL Client (IPC): Creating tag`, tagData);
  try {
    const newTag = await electronAPI.kbalService.createTag(tagData);
    return newTag;
  } catch (error) {
    console.error('KBAL Client (IPC): Error creating tag:', error);
    throw error;
  }
};
 
/**
 * Updates an existing tag.
 */
export const updateTag = async (tagId, tagData) => {
  console.log(`KBAL Client (IPC): Updating tag ${tagId}`, tagData);
  try {
    const updatedTag = await electronAPI.kbalService.updateTag(tagId, tagData);
    return updatedTag;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error updating tag ${tagId}:`, error);
    throw error;
  }
};
 
/**
 * Deletes a tag by its ID.
 */
export const deleteTag = async (tagId) => {
  console.log(`KBAL Client (IPC): Deleting tag ${tagId}`);
  try {
    const success = await electronAPI.kbalService.deleteTag(tagId);
    if (!success) {
      throw new Error(`Failed to delete tag ${tagId} via IPC. Tag may not exist.`);
    }
    return { success: true, message: `Tag ${tagId} deleted successfully.` };
  } catch (error) {
    console.error(`KBAL Client (IPC): Error deleting tag ${tagId}:`, error);
    throw error;
  }
};
 
/**
 * Fetches all categories.
 */
export const fetchCategories = async () => {
  console.log('KBAL Client (IPC): Fetching categories.');
  try {
    const categories = await electronAPI.kbalService.fetchCategories();
    return categories;
  } catch (error) {
    console.error('KBAL Client (IPC): Error fetching categories:', error);
    throw error;
  }
};
 
/**
 * Creates a new category.
 */
export const createCategory = async (categoryData) => {
  console.log(`KBAL Client (IPC): Creating category`, categoryData);
  try {
    const newCategory = await electronAPI.kbalService.createCategory(categoryData);
    return newCategory;
  } catch (error) {
    console.error('KBAL Client (IPC): Error creating category:', error);
    throw error;
  }
};
 
/**
 * Updates an existing category.
 */
export const updateCategory = async (categoryId, categoryData) => {
  console.log(`KBAL Client (IPC): Updating category ${categoryId}`, categoryData);
  try {
    const updatedCategory = await electronAPI.kbalService.updateCategory(categoryId, categoryData);
    return updatedCategory;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error updating category ${categoryId}:`, error);
    throw error;
  }
};
 
/**
 * Deletes a category by its ID.
 */
export const deleteCategory = async (categoryId) => {
  console.log(`KBAL Client (IPC): Deleting category ${categoryId}`);
  try {
    const success = await electronAPI.kbalService.deleteCategory(categoryId);
    if (!success) {
      throw new Error(`Failed to delete category ${categoryId} via IPC. Category may not exist.`);
    }
    return { success: true, message: `Category ${categoryId} deleted successfully.` };
  } catch (error) {
    console.error(`KBAL Client (IPC): Error deleting category ${categoryId}:`, error);
    throw error;
  }
};

/**
 * Asks a question about a specific item.
 */
export const askQuestion = async (itemId, question) => {
  console.log(`KBAL Client (IPC): Asking question for item ${itemId}`);
  try {
    const answer = await electronAPI.kbalService.askQuestion(itemId, question);
    return answer;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error asking question for item ${itemId}:`, error);
    throw error;
  }
};
 
/**
 * Requests a summary for a specific item.
 */
export const summarizeItem = async (itemId) => {
  console.log(`KBAL Client (IPC): Requesting summary for item ${itemId}`);
  try {
    const summaryText = await electronAPI.kbalService.summarizeItem(itemId);
    return summaryText;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error requesting summary for item ${itemId}:`, error);
    throw error;
  }
};
 
/**
 * Fetches conceptual links for a specific item.
 */
export const getConceptualLinks = async (itemId) => {
  console.log(`KBAL Client (IPC): Fetching conceptual links for item ${itemId}`);
  try {
    const links = await electronAPI.kbalService.getConceptualLinks(itemId);
    return links;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error fetching conceptual links for item ${itemId}:`, error);
    throw error;
  }
};
 
/**
 * Transforms the content of a specific item.
 */
export const transformContent = async (itemId, transformationType, selectedText = null) => {
  console.log(`KBAL Client (IPC): Transforming content for item ${itemId} with type ${transformationType}`);
  try {
    const result = await electronAPI.kbalService.transformContent(itemId, transformationType, selectedText);
    return result;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error transforming content for item ${itemId}:`, error);
    throw error;
  }
};
console.log('API Client module loaded.');
 
/**
 * Fetches manual links for a specific item.
 */
export const getManualLinks = async (itemId) => {
  console.log(`KBAL Client (IPC): Fetching manual links for item ${itemId}`);
  try {
    const links = await electronAPI.kbalService.getManualLinks(itemId);
    return links;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error fetching manual links for item ${itemId}:`, error);
    throw error;
  }
};
 
/**
 * Adds a manual link for a specific item.
 */
export const addManualLink = async (sourceItemId, linkData) => {
  console.log(`KBAL Client (IPC): Adding manual link for item ${sourceItemId}`);
  try {
    const newLink = await electronAPI.kbalService.addManualLink(sourceItemId, linkData);
    return newLink;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error adding manual link for item ${sourceItemId}:`, error);
    throw error;
  }
};
 
/**
 * Fetches capture settings.
 */
export const getCaptureSettings = async () => {
  console.log(`KBAL Client (IPC): Fetching capture settings.`);
  try {
    const settings = await electronAPI.kbalService.getCaptureSettings();
    return settings;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error fetching capture settings:`, error);
    throw error;
  }
};
 
/**
 * Updates capture settings.
 */
export const putCaptureSettings = async (settingsData) => {
  console.log(`KBAL Client (IPC): Updating capture settings.`, settingsData);
  try {
    const updatedSettings = await electronAPI.kbalService.putCaptureSettings(settingsData);
    return updatedSettings;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error updating capture settings:`, error);
    throw error;
  }
};
 
/**
 * Removes a manual link for a specific item.
 */
export const removeManualLink = async (sourceItemId, linkId) => {
  console.log(`KBAL Client (IPC): Removing manual link ${linkId} for item ${sourceItemId}`);
  try {
    const success = await electronAPI.kbalService.removeManualLink(sourceItemId, linkId);
    if (!success) {
      throw new Error(`Failed to remove manual link ${linkId} via IPC. Link may not exist.`);
    }
    return { success: true, message: `Manual link ${linkId} removed successfully.` };
  } catch (error) {
    console.error(`KBAL Client (IPC): Error removing manual link ${linkId} for item ${sourceItemId}:`, error);
    throw error;
  }
};
/**
 * Fetches all custom clipping templates.
 */
export const fetchClippingTemplates = async () => {
  console.log(`KBAL Client (IPC): Fetching clipping templates.`);
  try {
    const templates = await electronAPI.kbalService.fetchClippingTemplates();
    return templates;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error fetching clipping templates:`, error);
    throw error;
  }
};
 
/**
 * Creates a new clipping template.
 */
export const createClippingTemplate = async (templateData) => {
  console.log(`KBAL Client (IPC): Creating clipping template.`, templateData);
  try {
    const newTemplate = await electronAPI.kbalService.createClippingTemplate(templateData);
    return newTemplate;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error creating clipping template:`, error);
    throw error;
  }
};
 
/**
 * Updates an existing clipping template.
 */
export const updateClippingTemplate = async (templateId, templateData) => {
  console.log(`KBAL Client (IPC): Updating clipping template ${templateId}.`, templateData);
  try {
    const updatedTemplate = await electronAPI.kbalService.updateClippingTemplate(templateId, templateData);
    return updatedTemplate;
  } catch (error) {
    console.error(`KBAL Client (IPC): Error updating clipping template ${templateId}:`, error);
    throw error;
  }
};
 
/**
 * Deletes a clipping template by its ID.
 */
export const deleteClippingTemplate = async (templateId) => {
  console.log(`KBAL Client (IPC): Deleting clipping template ${templateId}.`);
  try {
    const success = await electronAPI.kbalService.deleteClippingTemplate(templateId);
    if (!success) {
      throw new Error(`Failed to delete clipping template ${templateId} via IPC. Template may not exist.`);
    }
    return { success: true, message: `Clipping template ${templateId} deleted successfully.` };
  } catch (error) {
    console.error(`KBAL Client (IPC): Error deleting clipping template ${templateId}:`, error);
    throw error;
  }
};
 
/**
 * Sets a clipping template as the default.
 */
export const setDefaultClippingTemplate = async (templateId) => {
  console.log(`KBAL Client (IPC): Setting default clipping template ${templateId}.`);
  try {
    const success = await electronAPI.kbalService.setDefaultClippingTemplate(templateId);
    if (!success) {
      throw new Error(`Failed to set default clipping template ${templateId} via IPC. Template may not exist.`);
    }
    return { success: true, message: `Clipping template ${templateId} set as default successfully.` };
  } catch (error) {
    console.error(`KBAL Client (IPC): Error setting default clipping template ${templateId}:`, error);
    throw error;
  }
};
/**
 * Triggers the knowledge base export process.
 */
export const triggerExport = async (filePath) => {
  console.log(`KBAL Client (IPC): Triggering export to ${filePath}.`);
  try {
    await electronAPI.kbalService.triggerExport(filePath);
    return { success: true, message: `Export process initiated to ${filePath}.` };
  } catch (error) {
    console.error(`KBAL Client (IPC): Error triggering export to ${filePath}:`, error);
    throw error;
  }
};
 
/**
 * Triggers the knowledge base import process.
 */
export const triggerImport = async (filePath) => {
  console.log(`KBAL Client (IPC): Triggering import from ${filePath}.`);
  try {
    await electronAPI.kbalService.triggerImport(filePath);
    return { success: true, message: `Import process initiated from ${filePath}.` };
  } catch (error) {
    console.error(`KBAL Client (IPC): Error triggering import from ${filePath}:`, error);
    throw error;
  }
};
