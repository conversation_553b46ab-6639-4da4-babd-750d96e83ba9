# UI Utilities

This directory contains utility functions specific to the UI layer of the Knowledge Base Interaction & Insights Module.

These utilities provide common, reusable logic that doesn't fit neatly into components, hooks, or services, but is helpful for UI-related tasks.

## Examples of Utilities:
- `formatter.js`: Functions for formatting data for display (e.g., dates, numbers, text snippets).
- `eventHandlers.js`: Common event handling utilities (though often custom hooks are preferred for complex event logic).
- `domHelpers.js`: Functions for interacting with the DOM, if necessary (use with caution in React).
- `constants.js`: UI-specific constants, like event names, display limits, or enum-like objects.

Utility functions should be pure where possible and well-tested.