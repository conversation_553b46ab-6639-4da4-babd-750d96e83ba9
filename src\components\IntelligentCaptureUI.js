import React, { useState } from 'react';
import PropTypes from 'prop-types';
import styles from '../styles/IntelligentCaptureUI.module.css';

const IntelligentCaptureUI = ({
  suggestedTags = [],
  suggestedCategories = [],
  onTagAccept,
  onTagReject,
  onCategoryAccept,
  onCategoryReject,
  onTagAdd,
  onCategoryAdd,
  onFeedback,
  aiSummary = '',
  existingDestinations = [],
  suggestedDestination = '',
  onDestinationSelect,
  onDestinationCreate,
  personalNotes = '',
  onNotesChange,
  contentPreview = '',
  onHighlight,
}) => {
  const [newTag, setNewTag] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [selectedDestination, setSelectedDestination] = useState(suggestedDestination || (existingDestinations.length > 0 ? existingDestinations[0] : ''));
  const [newDestinationName, setNewDestinationName] = useState('');

  const handleNotesChange = (event) => {
    onNotesChange(event.target.value);
  };

  const handleHighlight = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim() !== '') {
      onHighlight({
        text: selection.toString(),
        startOffset: Math.min(selection.anchorOffset, selection.focusOffset),
        endOffset: Math.max(selection.anchorOffset, selection.focusOffset),
      });
    }
  };

  const handleDestinationChange = (event) => {
    const value = event.target.value;
    setSelectedDestination(value);
    onDestinationSelect(value);
  };

  const handleCreateDestination = () => {
    if (newDestinationName.trim() !== '') {
      onDestinationCreate(newDestinationName.trim());
      setNewDestinationName('');
      // Optionally, auto-select the newly created destination
      // setSelectedDestination(newDestinationName.trim());
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() !== '') {
      onTagAdd(newTag.trim());
      setNewTag('');
    }
  };

  const handleAddCategory = () => {
    if (newCategory.trim() !== '') {
      onCategoryAdd(newCategory.trim());
      setNewCategory('');
    }
  };

  return (
    <div className={styles.intelligentCaptureUI}>
      <div className={styles.section}>
        <h3>AI Generated Summary</h3>
        {aiSummary ? (
          <p className={styles.summaryText}>{aiSummary}</p>
        ) : (
          <p>No summary available.</p>
        )}
      </div>

      <div className={styles.section}>
        <h3>Suggested Tags</h3>
        {suggestedTags.length > 0 ? (
          <ul className={styles.list}>
            {suggestedTags.map((tag) => (
              <li key={tag} className={styles.listItem}>
                <span>{tag}</span>
                <div className={styles.actions}>
                  <button onClick={() => onTagAccept(tag)} aria-label={`Accept ${tag}`}>Accept</button>
                  <button onClick={() => onTagReject(tag)} aria-label={`Reject ${tag}`}>Reject</button>
                  <button onClick={() => onFeedback({ type: 'tag', item: tag, feedback: 'positive' })} aria-label={`👍 ${tag}`} className={styles.feedbackButtonPositive}>👍</button>
                  <button onClick={() => onFeedback({ type: 'tag', item: tag, feedback: 'negative' })} aria-label={`👎 ${tag}`} className={styles.feedbackButtonNegative}>👎</button>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <p>No tag suggestions available.</p>
        )}
        <div className={styles.manualAdd}>
          <label htmlFor="newTagInput">Add new tag:</label>
          <input
            id="newTagInput"
            type="text"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            placeholder="Enter new tag"
          />
          <button onClick={handleAddTag}>Add Tag</button>
        </div>
      </div>

      <div className={styles.section}>
        <h3>Suggested Categories</h3>
        {suggestedCategories.length > 0 ? (
          <ul className={styles.list}>
            {suggestedCategories.map((category) => (
              <li key={category} className={styles.listItem}>
                <span>{category}</span>
                <div className={styles.actions}>
                  <button onClick={() => onCategoryAccept(category)} aria-label={`Accept ${category}`}>Accept</button>
                  <button onClick={() => onCategoryReject(category)} aria-label={`Reject ${category}`}>Reject</button>
                  <button onClick={() => onFeedback({ type: 'category', item: category, feedback: 'positive' })} aria-label={`👍 ${category}`} className={styles.feedbackButtonPositive}>👍</button>
                  <button onClick={() => onFeedback({ type: 'category', item: category, feedback: 'negative' })} aria-label={`👎 ${category}`} className={styles.feedbackButtonNegative}>👎</button>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <p>No category suggestions available.</p>
        )}
        <div className={styles.manualAdd}>
          <label htmlFor="newCategoryInput">Add new category:</label>
          <input
            id="newCategoryInput"
            type="text"
            value={newCategory}
            onChange={(e) => setNewCategory(e.target.value)}
            placeholder="Enter new category"
          />
          <button onClick={handleAddCategory}>Add Category</button>
        </div>
      </div>

      <div className={styles.section}>
        <h3>Organizational Destination</h3>
        <div className={styles.destinationSelect}>
          <label htmlFor="destinationSelector">Select destination:</label>
          <select
            id="destinationSelector"
            value={selectedDestination}
            onChange={handleDestinationChange}
            className={styles.selectDropdown}
          >
            {existingDestinations.map(dest => (
              <option key={dest} value={dest}>{dest}</option>
            ))}
          </select>
        </div>
        <div className={styles.manualAdd}>
          <label htmlFor="newDestinationInput">Or create new destination:</label>
          <input
            id="newDestinationInput"
            type="text"
            value={newDestinationName}
            onChange={(e) => setNewDestinationName(e.target.value)}
            placeholder="Enter new destination name"
          />
          <button onClick={handleCreateDestination}>Create Destination</button>
        </div>
      </div>

      <div className={styles.section}>
        <h3>Personal Notes</h3>
        <label htmlFor="personalNotesInput" className={styles.notesLabel}>Add your notes:</label>
        <textarea
          id="personalNotesInput"
          className={styles.notesTextarea}
          value={personalNotes}
          onChange={handleNotesChange}
          placeholder="Add any personal notes or comments here..."
          rows="4"
        />
      </div>

      <div className={styles.section}>
        <h3>Content Preview</h3>
        <textarea
          data-testid="content-preview-area"
          className={styles.contentTextarea}
          value={contentPreview}
          readOnly // Or controlled if edits are allowed, for now, readOnly for preview
          rows="10"
        />
        <button
          onClick={handleHighlight}
          className={styles.highlightButton}
          aria-label="Highlight Selected Text"
        >
          Highlight Selected Text
        </button>
      </div>
    </div>
  );
};

IntelligentCaptureUI.propTypes = {
  suggestedTags: PropTypes.arrayOf(PropTypes.string).isRequired,
  suggestedCategories: PropTypes.arrayOf(PropTypes.string).isRequired,
  onTagAccept: PropTypes.func.isRequired,
  onTagReject: PropTypes.func.isRequired,
  onCategoryAccept: PropTypes.func.isRequired,
  onCategoryReject: PropTypes.func.isRequired,
  onTagAdd: PropTypes.func.isRequired,
  onCategoryAdd: PropTypes.func.isRequired,
  onFeedback: PropTypes.func.isRequired,
  aiSummary: PropTypes.string,
  existingDestinations: PropTypes.arrayOf(PropTypes.string).isRequired,
  suggestedDestination: PropTypes.string,
  onDestinationSelect: PropTypes.func.isRequired,
  onDestinationCreate: PropTypes.func.isRequired,
  personalNotes: PropTypes.string,
  onNotesChange: PropTypes.func.isRequired,
  contentPreview: PropTypes.string,
  onHighlight: PropTypes.func.isRequired,
};

// Removed defaultProps as they are now handled by default parameters in the function signature

export default IntelligentCaptureUI;