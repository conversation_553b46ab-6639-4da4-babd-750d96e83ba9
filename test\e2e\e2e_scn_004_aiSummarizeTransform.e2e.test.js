// test/e2e/e2e_scn_004_aiSummarizeTransform.e2e.test.js

// Mock KBAL (storage service) - for fetching items for context
const mockGetItem = jest.fn();
const mockStorage = {
  getItem: mockGetItem,
};
jest.mock('../../src/knowledge-base-interaction/kbal/services/kbalService.js', () => mockStorage, { virtual: true });

// Mock AI Services Gateway (for Summarization and Transformation)
const mockPerformSummarization = jest.fn();
const mockPerformTransformation = jest.fn();
jest.mock('../../src/knowledge-base-interaction/ai-services-gateway/gateway.js', () => ({
  performSummarization: mockPerformSummarization,
  performTransformation: mockPerformTransformation,
}), { virtual: true });


// Helper function to simulate the workflow for E2E_SCN_004
async function simulateAiSummarizeTransformWorkflow({
  selectedItemIdsForAction, // Array of IDs user "selects"
  simulatedItemsForContext, // Array of full item objects { id, content, ... }
  actionType, // 'summarize' or 'transform'
  transformationDetails, // Optional: e.g., { type: 'extractKeyFacts' }
  simulatedServiceResponse, // The direct response from the AI service
}) {
  // 1. User navigates to Knowledge Base view and selects item(s) (simulated by selectedItemIdsForAction)
  // 2. User initiates AI summarization or transformation request
  
  // Simulate fetching the full content of selected items for context
  mockGetItem.mockImplementation(itemId => {
    const item = simulatedItemsForContext.find(it => it.id === itemId);
    return Promise.resolve(item);
  });
  
  const contextItems = await Promise.all(
    selectedItemIdsForAction.map(id => mockStorage.getItem(id))
  );

  let serviceResponse;
  if (actionType === 'summarize') {
    mockPerformSummarization.mockResolvedValue(simulatedServiceResponse);
    serviceResponse = await require('../../src/knowledge-base-interaction/ai-services-gateway/gateway.js')
      .performSummarization(contextItems);
  } else if (actionType === 'transform') {
    mockPerformTransformation.mockResolvedValue(simulatedServiceResponse);
    // Assuming the first item in contextItems is the primary target for transformation if multiple are passed
    // Or the gateway handles how multiple items are treated for transformation.
    // For simplicity, let's assume transformation is on a single item or the gateway aggregates.
    serviceResponse = await require('../../src/knowledge-base-interaction/ai-services-gateway/gateway.js')
      .performTransformation(contextItems[0], transformationDetails.type); // Pass transformation type
  }
  
  // 3. System processes request and displays result (serviceResponse is the result)
  return { serviceResponse };
}

describe('E2E_SCN_004: AI-Powered Content Summarization and Transformation', () => {
  const item1 = { id: 'item-s001', title: 'Long Article on Climate Change', content: 'Climate change is a significant global issue. It involves rising temperatures and sea levels. Many factors contribute to it.' };
  const item2 = { id: 'item-s002', title: 'Tech Innovations 2025', content: 'AI, Quantum Computing, and Biotech are leading innovations.' };

  beforeEach(() => {
    mockGetItem.mockClear();
    mockPerformSummarization.mockClear();
    mockPerformTransformation.mockClear();
  });

  test('should generate AI summary for selected content', async () => {
    const testParams = {
      selectedItemIdsForAction: [item1.id],
      simulatedItemsForContext: [item1, item2],
      actionType: 'summarize',
      simulatedServiceResponse: {
        summary: 'Climate change is a major global problem with rising temperatures and sea levels due to various factors.',
        sourceItemIds: [item1.id],
      },
    };

    const { serviceResponse } = await simulateAiSummarizeTransformWorkflow(testParams);

    expect(mockGetItem).toHaveBeenCalledWith(item1.id);
    expect(mockPerformSummarization).toHaveBeenCalledWith([item1]);
    expect(serviceResponse).toEqual(testParams.simulatedServiceResponse);
    expect(serviceResponse.summary).toContain('Climate change is a major global problem');
    expect(serviceResponse.sourceItemIds).toContain(item1.id);
  });

  test('should perform AI content transformation (extract key facts)', async () => {
    const testParams = {
      selectedItemIdsForAction: [item2.id], // Typically transformation acts on one item
      simulatedItemsForContext: [item1, item2],
      actionType: 'transform',
      transformationDetails: { type: 'extractKeyFacts' },
      simulatedServiceResponse: {
        transformedContent: [
          'AI is a leading innovation.',
          'Quantum Computing is a leading innovation.',
          'Biotech is a leading innovation.'
        ],
        sourceItemId: item2.id,
        transformationType: 'keyFacts',
      },
    };

    const { serviceResponse } = await simulateAiSummarizeTransformWorkflow(testParams);

    expect(mockGetItem).toHaveBeenCalledWith(item2.id);
    expect(mockPerformTransformation).toHaveBeenCalledWith(item2, testParams.transformationDetails.type);
    expect(serviceResponse).toEqual(testParams.simulatedServiceResponse);
    expect(serviceResponse.transformedContent).toContain('AI is a leading innovation.');
    expect(serviceResponse.sourceItemId).toBe(item2.id);
  });
  
  test('should handle AI summarization for multiple selected items', async () => {
    const testParams = {
      selectedItemIdsForAction: [item1.id, item2.id],
      simulatedItemsForContext: [item1, item2],
      actionType: 'summarize',
      simulatedServiceResponse: {
        summary: 'Combined summary of climate change and tech innovations.',
        sourceItemIds: [item1.id, item2.id],
      },
    };

    const { serviceResponse } = await simulateAiSummarizeTransformWorkflow(testParams);
    
    expect(mockGetItem).toHaveBeenCalledWith(item1.id);
    expect(mockGetItem).toHaveBeenCalledWith(item2.id);
    expect(mockPerformSummarization).toHaveBeenCalledWith([item1, item2]);
    expect(serviceResponse.summary).toBe('Combined summary of climate change and tech innovations.');
    expect(serviceResponse.sourceItemIds).toEqual([item1.id, item2.id]);
  });
});