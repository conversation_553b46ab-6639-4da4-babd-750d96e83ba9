/* src/browser-extension-ui/content_script.css */

/*
  This file is currently a placeholder.
  The actual highlight styles are injected dynamically by content_script.js
  to ensure they are applied correctly and can be removed.

  If other page-specific UI elements controlled by the content script
  were to be added (e.g., custom selection toolbars), their styles
  would go here or be similarly injected.
*/

.pka-highlight {
  background-color: yellow !important; /* !important to override page styles */
  color: black !important; /* Ensure text is readable */
  cursor: pointer; /* Optional: if highlights become interactive */
  /* Add any other desired default styling for highlights */
}

/* Example of a potential custom UI element style */
/*
.pka-selection-toolbar {
  position: fixed;
  top: 10px;
  right: 10px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  padding: 8px;
  z-index: 999999; // High z-index to appear above page content
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}
*/