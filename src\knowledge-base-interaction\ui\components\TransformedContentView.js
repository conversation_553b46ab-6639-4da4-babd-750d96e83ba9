import React from 'react';
import DOMPurify from 'dompurify';

/**
 * TransformedContentView component
 * 
 * Displays content that has been transformed (e.g., simplified, re-formatted).
 * Props:
 *  - originalContent: The original content (optional, for comparison).
 *  - transformedContent: The transformed content to display.
 *  - transformationType: A string describing the type of transformation (e.g., "simplified", "bullet-points").
 */
const TransformedContentView = ({ originalContent, transformedContent, transformationType }) => {
  if (!transformedContent) {
    return <p>No transformed content to display.</p>;
  }

  // AI-verifiable: Component structure for displaying transformed content
  return (
    <div className="transformed-content-view" data-testid="transformed-content-view">
      {transformationType && <h4>Content ({transformationType}):</h4>}
      {/* This is a very basic display. Real content might be HTML or require special rendering. */}
      <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(transformedContent) }} />

      {originalContent && (
        <details>
          <summary>Show Original Content</summary>
          <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(originalContent) }} />
        </details>
      )}
      {/* AI-verifiable: Placeholder for actions related to transformed content */}
    </div>
  );
};

export default TransformedContentView;