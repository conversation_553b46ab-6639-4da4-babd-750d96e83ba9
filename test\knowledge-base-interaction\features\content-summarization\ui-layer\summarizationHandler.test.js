// test/knowledge-base-interaction/features/content-summarization/ui-layer/summarizationHandler.test.js

import { handleSummarizationRequest } from '../../../../../src/knowledge-base-interaction/features/content-summarization/ui-layer/summarizationHandler';
import { sendToQueryUnderstandingEngine } from '../../../../../src/knowledge-base-interaction/features/content-summarization/query-understanding-engine/queryUnderstandingEngine';
import { logError, logInfo } from '../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger';

// Mock dependencies
jest.mock('../../../../../src/knowledge-base-interaction/features/content-summarization/query-understanding-engine/queryUnderstandingEngine');
jest.mock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');

describe('handleSummarizationRequest', () => {
  const mockQuery = 'Summarize this content';
  const mockContent = 'This is the content to be summarized.';
  const mockContentType = 'text/plain';
  const mockOptions = { summaryLength: 'short' };

  beforeEach(() => {
    // Clear all mock instances and calls to constructor and all methods:
    sendToQueryUnderstandingEngine.mockClear();
    logError.mockClear();
    logInfo.mockClear();
  });

  test('should call sendToQueryUnderstandingEngine with correct payload for valid input (Test Case 5.1.1)', async () => {
    const expectedPayload = {
      query: mockQuery,
      content: mockContent,
      contentType: mockContentType,
      options: mockOptions,
    };
    const mockResponseFromQLUE = { summary: 'This is a summary.', intent: 'summarization' };
    sendToQueryUnderstandingEngine.mockResolvedValue(mockResponseFromQLUE);

    const result = await handleSummarizationRequest(mockQuery, mockContent, mockContentType, mockOptions);

    expect(logInfo).toHaveBeenCalledWith('UI Layer: Received summarization request.', { query: mockQuery, contentType: mockContentType, options: mockOptions });
    expect(sendToQueryUnderstandingEngine).toHaveBeenCalledTimes(1);
    expect(sendToQueryUnderstandingEngine).toHaveBeenCalledWith(expectedPayload);
    expect(logInfo).toHaveBeenCalledWith('UI Layer: Sending request to Query Understanding Engine.', expectedPayload);
    expect(logInfo).toHaveBeenCalledWith('UI Layer: Received response from Query Understanding Engine.', mockResponseFromQLUE);
    expect(result).toEqual(mockResponseFromQLUE);
  });

  test('should return an error if contentToSummarize is empty and not call QLUE', async () => {
    const emptyContent = '';
    const expectedErrorResponse = { error: 'Content to summarize cannot be empty.', summary: null };

    const result = await handleSummarizationRequest(mockQuery, emptyContent, mockContentType, mockOptions);

    expect(logError).toHaveBeenCalledWith('UI Layer: Content to summarize is empty.');
    expect(sendToQueryUnderstandingEngine).not.toHaveBeenCalled();
    expect(result).toEqual(expectedErrorResponse);
  });

  test('should return an error if contentToSummarize is whitespace and not call QLUE', async () => {
    const whitespaceContent = '   ';
    const expectedErrorResponse = { error: 'Content to summarize cannot be empty.', summary: null };

    const result = await handleSummarizationRequest(mockQuery, whitespaceContent, mockContentType, mockOptions);
    
    expect(logError).toHaveBeenCalledWith('UI Layer: Content to summarize is empty.');
    expect(sendToQueryUnderstandingEngine).not.toHaveBeenCalled();
    expect(result).toEqual(expectedErrorResponse);
  });

  test('should handle errors from sendToQueryUnderstandingEngine', async () => {
    const errorMessage = 'QLUE Error';
    sendToQueryUnderstandingEngine.mockRejectedValue(new Error(errorMessage));
    const expectedErrorResponse = { error: 'Failed to process summarization request.', summary: null, details: errorMessage };

    const result = await handleSummarizationRequest(mockQuery, mockContent, mockContentType, mockOptions);

    expect(sendToQueryUnderstandingEngine).toHaveBeenCalledTimes(1);
    expect(logError).toHaveBeenCalledWith('UI Layer: Error communicating with Query Understanding Engine.', expect.any(Error));
    expect(result).toEqual(expectedErrorResponse);
  });
});