// src/knowledge-base-interaction/features/content-summarization/ui-layer/summarizationHandler.js

import { sendToQueryUnderstandingEngine } from '../query-understanding-engine/queryUnderstandingEngine';
import { logError, logInfo } from '../utils/logger';

/**
 * Handles the summarization request initiated from the UI.
 *
 * @param {string} query - The user's query, e.g., "Summarize this content".
 * @param {string} contentToSummarize - The actual content to be summarized.
 * @param {string} contentType - The type of the content (e.g., "text/plain", "text/markdown").
 * @param {object} options - Summarization options, e.g., { summaryLength: "short" }.
 * @returns {Promise<object>} A promise that resolves with the summarization result or an error object.
 */
export async function handleSummarizationRequest(query, contentToSummarize, contentType, options) {
  logInfo('UI Layer: Received summarization request.', { query, contentType, options });

  if (!contentToSummarize || contentToSummarize.trim() === '') {
    logError('UI Layer: Content to summarize is empty.');
    return { error: 'Content to summarize cannot be empty.', summary: null };
  }

  const requestPayload = {
    query,
    content: contentToSummarize,
    contentType,
    options,
  };

  try {
    logInfo('UI Layer: Sending request to Query Understanding Engine.', requestPayload);
    const result = await sendToQueryUnderstandingEngine(requestPayload);
    logInfo('UI Layer: Received response from Query Understanding Engine.', result);
    return result; // This will be the response from QLUE, ultimately containing the summary or an error
  } catch (error) {
    logError('UI Layer: Error communicating with Query Understanding Engine.', error);
    return { error: 'Failed to process summarization request.', summary: null, details: error.message };
  }
}

// Example Usage (for testing purposes, would be called by actual UI components)
/*
async function testSummarization() {
  const query = "Summarize this text for me.";
  const content = "This is a long piece of text that needs to be summarized. It talks about various topics and provides a lot of detail. The goal is to get a concise summary.";
  const contentType = "text/plain";
  const options = { summaryLength: "short" };

  const result = await handleSummarizationRequest(query, content, contentType, options);

  if (result.error) {
    console.error("Summarization failed:", result.error, result.details || '');
  } else {
    console.log("Summary:", result.summary);
  }
}

// testSummarization();
*/