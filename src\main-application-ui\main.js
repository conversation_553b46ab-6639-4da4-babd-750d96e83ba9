const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs'); // Import fs for directory creation
const { v4: uuidv4 } = require('uuid'); // Import uuid

// IMPORTANT: SECURE_APP_DATA_PATH should be defined based on your application's needs.
// For Electron, this might be obtained via app.getPath('userData').
const SECURE_APP_DATA_PATH = path.join(app.getPath('userData'), 'kbal_databases'); // Using app.getPath('userData')
const DEFAULT_DB_FILENAME = 'kbal_database.json'; // Default filename

let kbalServiceInstance = null; // Singleton instance for the main process

async function initializeKbalService() {
  // Dynamically import lowdb/node and KbalService
  const { JSONFilePreset } = await import('lowdb/node');
  const { default: KbalService } = await import('../knowledge-base-interaction/kbal/services/KbalService.mjs');

  const dbPath = path.join(SECURE_APP_DATA_PATH, DEFAULT_DB_FILENAME);

  try {
    if (!fs.existsSync(SECURE_APP_DATA_PATH)){
        fs.mkdirSync(SECURE_APP_DATA_PATH, { recursive: true });
        console.log(`Created database directory: ${SECURE_APP_DATA_PATH}`);
    } else {
        console.log(`Database directory already exists: ${SECURE_APP_DATA_PATH}`);
    }

    // Check if file exists and has valid content, or create with initial data
    let initialData = { items: [] };
    if (fs.existsSync(dbPath)) {
      try {
        const fileContent = fs.readFileSync(dbPath, 'utf8');
        if (fileContent.trim()) {
          initialData = JSON.parse(fileContent);
          console.log(`Loaded existing database from ${dbPath}`);
        } else {
           // File exists but is empty, write initial data
           fs.writeFileSync(dbPath, JSON.stringify(initialData, null, 2));
           console.log(`Database file was empty, wrote initial data to ${dbPath}`);
        }
      } catch (error) {
        console.error(`Error reading/parsing existing database file ${dbPath}:`, error);
        // If parsing fails, overwrite with initial data to prevent further errors
        fs.writeFileSync(dbPath, JSON.stringify(initialData, null, 2));
        console.log(`Overwrote corrupted database file with initial data at ${dbPath}`);
      }
    } else {
      // File doesn't exist, create it with initial data
      fs.writeFileSync(dbPath, JSON.stringify(initialData, null, 2));
      console.log(`Created new database file at ${dbPath}`);
    }

    const db = await JSONFilePreset(dbPath, initialData);
    kbalServiceInstance = new KbalService(db); // Inject the LowDB instance
    console.log('KbalService initialized in main process.');

  } catch (error) {
    console.error('Failed to initialize KbalService in main process:', error);
    // Depending on the severity, you might want to exit the app or show an error to the user.
  }
}


function createWindow () {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      enableRemoteModule: false, // It's good practice to disable remote module
      nodeIntegration: false // Disable nodeIntegration for security in renderer
    }
  });

  // Load the index.html of the app.
  // In a production app, you'd load a compiled React build.
  // For development, you might point to a development server.
  mainWindow.loadFile(path.join(__dirname, 'dist_renderer', 'index.html'));

  // Open the DevTools.
  // mainWindow.webContents.openDevTools();
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => { // Made async to await initializeKbalService
  await initializeKbalService(); // Initialize KbalService when the app is ready
  createWindow();

  // IPC handler for showing save dialog
  ipcMain.handle('show-save-dialog', async (event, options) => {
    const mainWindow = BrowserWindow.getFocusedWindow();
    if (!mainWindow) {
      console.error('No focused window to show save dialog.');
      return null; // Or throw an error
    }
    const { filePath, canceled } = await dialog.showSaveDialog(mainWindow, options);
    if (canceled) {
      return null;
    }
    return filePath;
  });

  // IPC handler for showing open dialog
  ipcMain.handle('show-open-dialog', async (event, options) => {
    const mainWindow = BrowserWindow.getFocusedWindow();
    if (!mainWindow) {
      console.error('No focused window to show open dialog.');
      return null; // Or throw an error
    }
    const { filePaths, canceled } = await dialog.showOpenDialog(mainWindow, options);
    if (canceled) {
      return null;
    }
    return filePaths; // Returns an array of paths, or null if canceled
  });

  // IPC handlers for KbalService methods
  // IPC handlers for KbalService methods
  // IPC handlers for KbalService methods
  ipcMain.handle('kbalService:getContentById', async (event, contentId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.getContentById.bind(kbalServiceInstance)(contentId);
  });

  ipcMain.handle('kbalService:queryContent', async (event, queryCriteria) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.queryContent.bind(kbalServiceInstance)(queryCriteria);
  });

  ipcMain.handle('kbalService:addContent', async (event, contentItemData) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    const newItemData = { ...contentItemData, id: uuidv4() };
    return kbalServiceInstance.addContent.bind(kbalServiceInstance)(newItemData);
  });

  ipcMain.handle('kbalService:updateContent', async (event, contentId, updates) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.updateContent.bind(kbalServiceInstance)(contentId, updates);
  });

  ipcMain.handle('kbalService:deleteContent', async (event, contentId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.deleteContent.bind(kbalServiceInstance)(contentId);
  });

  ipcMain.handle('kbalService:clearAllItems', async (event) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.clearAllItems.bind(kbalServiceInstance)();
  });

  ipcMain.handle('kbalService:fetchTags', async () => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.fetchTags.bind(kbalServiceInstance)();
  });

  ipcMain.handle('kbalService:createTag', async (event, tagData) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.createTag.bind(kbalServiceInstance)(tagData);
  });

  ipcMain.handle('kbalService:updateTag', async (event, tagId, tagData) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.updateTag.bind(kbalServiceInstance)(tagId, tagData);
  });

  ipcMain.handle('kbalService:deleteTag', async (event, tagId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.deleteTag.bind(kbalServiceInstance)(tagId);
  });

  ipcMain.handle('kbalService:fetchCategories', async () => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.fetchCategories.bind(kbalServiceInstance)();
  });

  ipcMain.handle('kbalService:createCategory', async (event, categoryData) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.createCategory.bind(kbalServiceInstance)(categoryData);
  });

  ipcMain.handle('kbalService:updateCategory', async (event, categoryId, categoryData) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.updateCategory.bind(kbalServiceInstance)(categoryId, categoryData);
  });

  ipcMain.handle('kbalService:deleteCategory', async (event, categoryId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.deleteCategory.bind(kbalServiceInstance)(categoryId);
  });

  ipcMain.handle('kbalService:askQuestion', async (event, itemId, question) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.askQuestion.bind(kbalServiceInstance)(itemId, question);
  });

  ipcMain.handle('kbalService:summarizeItem', async (event, itemId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.summarizeItem.bind(kbalServiceInstance)(itemId);
  });

  ipcMain.handle('kbalService:getConceptualLinks', async (event, itemId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.getConceptualLinks.bind(kbalServiceInstance)(itemId);
  });

  ipcMain.handle('kbalService:transformContent', async (event, itemId, transformationType, selectedText) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.transformContent.bind(kbalServiceInstance)(itemId, transformationType, selectedText);
  });

  ipcMain.handle('kbalService:getManualLinks', async (event, itemId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.getManualLinks.bind(kbalServiceInstance)(itemId);
  });

  ipcMain.handle('kbalService:addManualLink', async (event, sourceItemId, linkData) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.addManualLink.bind(kbalServiceInstance)(sourceItemId, linkData);
  });

  ipcMain.handle('kbalService:removeManualLink', async (event, sourceItemId, linkId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.removeManualLink.bind(kbalServiceInstance)(sourceItemId, linkId);
  });

  ipcMain.handle('kbalService:getCaptureSettings', async () => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.getCaptureSettings.bind(kbalServiceInstance)();
  });

  ipcMain.handle('kbalService:putCaptureSettings', async (event, settingsData) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.putCaptureSettings.bind(kbalServiceInstance)(settingsData);
  });

  ipcMain.handle('kbalService:fetchClippingTemplates', async () => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.fetchClippingTemplates.bind(kbalServiceInstance)();
  });

  ipcMain.handle('kbalService:createClippingTemplate', async (event, templateData) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.createClippingTemplate.bind(kbalServiceInstance)(templateData);
  });

  ipcMain.handle('kbalService:updateClippingTemplate', async (event, templateId, updates) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.updateClippingTemplate.bind(kbalServiceInstance)(templateId, updates);
  });

  ipcMain.handle('kbalService:deleteClippingTemplate', async (event, templateId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.deleteClippingTemplate.bind(kbalServiceInstance)(templateId);
  });

  ipcMain.handle('kbalService:setDefaultClippingTemplate', async (event, templateId) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.setDefaultClippingTemplate.bind(kbalServiceInstance)(templateId);
  });

  ipcMain.handle('kbalService:triggerExport', async (event, filePath) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.triggerExport.bind(kbalServiceInstance)(filePath);
  });

  ipcMain.handle('kbalService:triggerImport', async (event, filePath) => {
    if (!kbalServiceInstance) throw new Error('KbalService not initialized.');
    return kbalServiceInstance.triggerImport.bind(kbalServiceInstance)(filePath);
  });

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// In this file, you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
