// Knowledge Base Interaction & Insights Module
const { getSemanticSearchResultsFromAI, getSynthesizedAnswerFromAI, getAnswerFromContextAI, getAISummary, getAITransform, getAIConceptualLinks } = require('../ai-integration'); // Import AI helpers (added getAIConceptualLinks)

// --- Mock Data (Centralized) ---
// TODO: Replace with actual data source interaction
const MOCK_KB_ITEMS = [
  { id: 'item1', title: 'Test Item 1', date: new Date('2024-01-01'), source: 'web', type: 'article', tags: ['test'], content: 'Full content for item 1 about JavaScript.' },
  { id: 'item2', title: 'Another Test', date: new Date('2024-01-02'), source: 'note', type: 'text', tags: ['sample'], content: 'Full content for item 2 discussing Python.' },
  { id: 'item3', title: 'Newest Item', date: new Date('2024-01-03'), source: 'note', type: 'text', content: 'Full content for item 3 mentioning JavaScript and Python.' },
  { id: 'item4', title: 'Multi-Tag Item', date: new Date('2024-01-04'), source: 'web', type: 'article', tags: ['test', 'sample'], content: 'Content relevant to both test and sample tags.' }, // Added for multi-tag test
];
// --- End Mock Data ---

/**
 * Browses saved knowledge base items with sorting, filtering, and pagination.
 * Corresponds to tests KBI_FUNC_BR_*
 * @param {object} options - Filtering, sorting, and pagination options.
 * @param {string} [options.sortBy='date'] - Field to sort by (e.g., 'date', 'source', 'title').
 * @param {string} [options.sortOrder='desc'] - Sort order ('asc' or 'desc').
 * @param {object} [options.filterBy] - Filtering criteria (e.g., { tags: ['ai'], type: 'article' }).
 * @param {number} [options.page=1] - Page number for pagination.
 * @param {number} [options.limit=10] - Items per page.
 * @returns {Promise<{data: Array<object>, pagination: object}>} - Paginated list of items.
 */
async function browseItems(options = {}) {
  // TODO: Implement actual browsing logic (fetching, filtering, sorting, pagination)

  // Use the centralized mock data
  let processedItems = [...MOCK_KB_ITEMS]; // Start with a copy of the mock data

  // --- Filtering Logic ---
  const { filterBy } = options;
  if (filterBy) {
    // Tag filtering (AND logic)
    if (filterBy.tags && Array.isArray(filterBy.tags) && filterBy.tags.length > 0) {
      const filterTags = filterBy.tags;
      processedItems = processedItems.filter(item => {
        // Ensure item has tags and it's an array
        if (!item.tags || !Array.isArray(item.tags)) {
          return false;
        }
        // Check if all filterTags are present in the item's tags
        return filterTags.every(tag => item.tags.includes(tag));
      });
    }
    // Type filtering
    if (filterBy.type && typeof filterBy.type === 'string') {
       processedItems = processedItems.filter(item => item.type === filterBy.type);
    }
    // TODO: Add filtering logic for other fields later
  }


  // --- Sorting Logic (Applied after filtering) ---
  const { sortBy, sortOrder = 'desc' } = options;
  if (sortBy) {
    if (sortBy === 'date') {
      processedItems.sort((a, b) => {
        const dateA = a.date.getTime();
        const dateB = b.date.getTime();
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      });
    } else if (sortBy === 'source') {
      processedItems.sort((a, b) => {
        const sourceA = a.source || ''; // Handle potential missing source
        const sourceB = b.source || '';
        return sortOrder === 'asc'
          ? sourceA.localeCompare(sourceB)
          : sourceB.localeCompare(sourceA);
      });
    } else if (sortBy === 'title') {
      processedItems.sort((a, b) => {
        const titleA = a.title || ''; // Handle potential missing title
        const titleB = b.title || '';
        return sortOrder === 'asc'
          ? titleA.localeCompare(titleB)
          : titleB.localeCompare(titleA);
      });
    }
  }
  // If no sortBy is provided, processedItems remains in the original mockItems order.

  // --- Pagination Logic (applied after potential sorting) ---
  const totalItems = processedItems.length;
  const limit = options.limit || 10;
  const page = options.page || 1;
  const totalPages = Math.ceil(totalItems / limit);
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const paginatedData = processedItems.slice(startIndex, endIndex); // Use processedItems


  return {
    data: paginatedData, // Return sorted and paginated data
    pagination: {
      total: totalItems,
      page: page,
      limit: limit,
      totalPages: totalPages,
    },
  };
}

/**
 * Searches the knowledge base using natural language queries or keywords.
 * Corresponds to tests KBI_FUNC_SRCH_*
 * @param {string} query - The search query (keyword or natural language).
 * @param {object} options - Search options (e.g., semantic search flag).
 * @returns {Promise<Array<object>>} - List of relevant search results with snippets.
 */
async function searchItems(query, options = {}) {
  // TODO: Implement more advanced search (ranking, snippets)

  // Use AI helper for semantic search if query is provided
  if (!query || query.trim().length === 0) {
    return []; // Return empty if query is empty or only whitespace
  }

  try {
    // Call the AI helper (mocked in tests) to get relevant rich results
    // The mock should return an array of objects like: [{ id, snippet, relevance }, ...]
    const richResults = await getSemanticSearchResultsFromAI(query, MOCK_KB_ITEMS);

    // Return the rich results directly (assuming AI provides snippets and relevance)
    // TODO: In a real implementation, might need to fetch full item details if AI only returns IDs/snippets

    return richResults;
  } catch (error) {
    console.error("Error during semantic search:", error);
    // Fallback or error handling: return empty array for now
    return [];
  }
}


/**
 * Performs a semantic search and then synthesizes an answer based on the results.
 * Corresponds to test KBI_FUNC_SRCH_010
 * @param {string} query - The search query (keyword or natural language).
 * @returns {Promise<{answer: string, sources: Array<string>} | null>} - Synthesized answer with source IDs, or null on error/no results.
 */
async function searchAndSynthesize(query) {
  if (!query || query.trim().length === 0) {
    return { answer: "Please provide a valid search query.", sources: [] };
  }

  try {
    // Step 1: Perform the initial semantic search
    const searchResults = await searchItems(query); // Use existing searchItems

    // Step 2: Check if search yielded results
    if (!searchResults || searchResults.length === 0) {
      return { answer: "No relevant information found in the knowledge base for your query.", sources: [] };
    }

    // Step 3: Synthesize answer using the search results as context
    // The AI helper is expected to handle the synthesis and citation logic
    const synthesizedResult = await getSynthesizedAnswerFromAI(query, searchResults);

    return synthesizedResult;

  } catch (error) {
    console.error("Error during search and synthesis:", error);
    // Return a generic error message or null
    return { answer: "An error occurred while processing your request.", sources: [] };
  }
}


/**
 * Answers questions based on selected knowledge base item(s).
 * Corresponds to tests KBI_FUNC_QA_*
 * @param {string} question - The question to ask.
 * @param {Array<string>} itemIds - IDs of the items to use as context.
 * @param {function} [getItemDetailsFn=getItemDetails] - Function to fetch item details (for testing).
 * @returns {Promise<{answer: string, sources: Array<string>}>} - The answer and cited source IDs.
 */
async function askQuestion(question, itemIds, getItemDetailsFn = getItemDetails) {
  // --- Input Validation ---
  if (!question || typeof question !== 'string' || question.trim().length === 0) {
    return { answer: 'Please provide a valid question.', sources: [] };
  }
  if (!itemIds || !Array.isArray(itemIds) || itemIds.length === 0) {
    return { answer: 'Please select at least one knowledge base item to ask about.', sources: [] };
  }

  try {
    // --- Fetch Content for Context ---
    // Fetch details for all provided IDs concurrently
    const detailPromises = itemIds.map(id => getItemDetailsFn(id)); // Use injected function
    const items = await Promise.all(detailPromises);

    // Filter out null results (items not found) and prepare context for AI
    const contextItems = items
      .filter(item => item !== null && item.content) // Ensure item exists and has content
      .map(item => ({ id: item.id, content: item.content })); // Extract only ID and content

    if (contextItems.length === 0) {
      return { answer: 'Could not find the specified items or they have no content.', sources: [] };
    }

    // --- Call AI for Q&A ---
    // The AI helper is expected to use *only* the provided contextItems
    const result = await getAnswerFromContextAI(question, contextItems);

    // Return the result from the AI (which should include answer and sources)
    return result;

  } catch (error) {
    console.error("Error during Q&A:", error);
    // Return a generic error message
    return { answer: 'An error occurred while processing the question.', sources: [] };
  }
}

/**
 * Summarizes selected knowledge base item(s).
 * Corresponds to tests KBI_FUNC_SUM_*
 * @param {Array<string>} itemIds - IDs of the items to summarize.
 * @param {function} [getItemDetailsFn=getItemDetails] - Function to fetch item details (for testing).
 * @param {function} [getAISummaryFn=getAISummary] - Function to get AI summary (for testing).
 * @returns {Promise<string>} - The generated summary.
 */
async function summarizeItems(itemIds, getItemDetailsFn = getItemDetails, getAISummaryFn = getAISummary) {
  if (!itemIds || !Array.isArray(itemIds) || itemIds.length === 0) {
    return "No items selected for summarization.";
  }

  try {
    // Fetch details for all provided IDs concurrently
    const detailPromises = itemIds.map(id => getItemDetailsFn(id));
    const items = await Promise.all(detailPromises);

    // Filter out null results and extract content
    const validItems = items.filter(item => item !== null && item.content);

    if (validItems.length === 0) {
      return "Could not find valid content for the selected items.";
    }

    // Combine content (simple space separation for now)
    const combinedContent = validItems.map(item => item.content).join(' ');

    // Call AI for summarization
    const summary = await getAISummaryFn(combinedContent);
    return summary;

  } catch (error) {
    console.error("Error during summarization:", error);
    // Return a generic error message
    return "An error occurred during summarization.";
  }
}

/**
 * Transforms the content of a knowledge base item (e.g., extract facts, bullet points).
 * Corresponds to tests KBI_FUNC_TRN_*
 * @param {string} itemId - ID of the item to transform.
 * @param {string} transformationType - Type of transformation (e.g., 'extract-facts', 'to-bullets').
 * @param {function} [getItemDetailsFn=getItemDetails] - Function to fetch item details (for testing).
 * @param {function} [getAITransformFn=getAITransform] - Function to get AI transformation (for testing).
 * @returns {Promise<object|string>} - The transformed content.
 */
async function transformContent(itemId, transformationType, getItemDetailsFn = getItemDetails, getAITransformFn = getAITransform) {
  if (!itemId || !transformationType) {
    return "Missing item ID or transformation type.";
  }

  try {
    const item = await getItemDetailsFn(itemId);

    if (!item || !item.content) {
      return "Could not find the specified item or it has no content.";
    }

    // Call AI for content transformation
    const transformedContent = await getAITransformFn(item.content, transformationType);
    return transformedContent;

  } catch (error) {
    console.error("Error during content transformation:", error);
    return "An error occurred during content transformation.";
  }
}

/**
 * Suggests conceptual links between a selected item and others in the knowledge base.
 * Corresponds to tests KBI_FUNC_LINK_*
 * @param {string} itemId - ID of the item to find links for.
 * @param {function} [getItemDetailsFn=getItemDetails] - Function to fetch item details (for testing).
 * @param {function} [getAllItemsFn=() => MOCK_KB_ITEMS] - Function to get all KB items (for testing).
 * @param {function} [getAIConceptualLinksFn=getAIConceptualLinks] - Function to get AI links (for testing).
 * @returns {Promise<Array<{linkedItemId: string, justification: string}>>} - Suggested links with justifications.
 */
async function suggestLinks(
  itemId,
  getItemDetailsFn = getItemDetails,
  getAllItemsFn = () => MOCK_KB_ITEMS, // Default gets all mock items
  getAIConceptualLinksFn = getAIConceptualLinks
) {
  if (!itemId) {
    return []; // No item ID provided
  }

  try {
    // 1. Fetch the source item
    const sourceItem = await getItemDetailsFn(itemId);
    if (!sourceItem || !sourceItem.content) {
      console.warn(`SuggestLinks: Source item ${itemId} not found or has no content.`);
      return []; // Source item not found or lacks content
    }

    // 2. Get all other items to compare against
    const allItems = await getAllItemsFn(); // Use injected function
    const potentialLinkItems = allItems.filter(item => item.id !== itemId && item.content);

    if (potentialLinkItems.length === 0) {
      return []; // No other items to link to
    }

    // 3. Call AI helper to find conceptual links
    // The AI function needs the source item and the potential candidates
    const suggestedLinks = await getAIConceptualLinksFn(sourceItem, potentialLinkItems);

    // 4. Return the links provided by the AI
    // Assuming the AI returns the correct format: [{ linkedItemId, justification }, ...]
    return suggestedLinks || []; // Return suggestions or empty array if AI returns null/undefined

  } catch (error) {
    console.error(`Error suggesting links for item ${itemId}:`, error);
    return []; // Return empty array on error
  }
}

/**
 * Fetches the full details for a specific knowledge base item.
 * Corresponds to test KBI_FUNC_BR_008
 * @param {string} itemId - The ID of the item to fetch.
 * @returns {Promise<object|null>} - The full item object or null if not found.
 */
async function getItemDetails(itemId) {
  // TODO: Implement actual item fetching logic
  // Placeholder: Find item in the centralized mock data
  const item = MOCK_KB_ITEMS.find(i => i.id === itemId);
  return item || null; // Return found item or null
}


// --- Helper to get all items (primarily for suggestLinks) ---
// In a real scenario, this might involve a database query.
// Here, it just returns the mock data. Kept separate for clarity.
// Not exported, used internally or via injection.
// function getAllKnowledgeBaseItems() {
//   return MOCK_KB_ITEMS;
// }
// Note: Decided to inject the function via default param in suggestLinks instead.


module.exports = {
  browseItems,
  getItemDetails,
  searchItems,
  searchAndSynthesize,
  askQuestion,
  summarizeItems,
  transformContent,
  suggestLinks,
  // Note: MOCK_KB_ITEMS is not exported, accessed via default parameter function in suggestLinks
};