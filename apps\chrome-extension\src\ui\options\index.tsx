import React, { useState } from 'react';
import ReactDOM from 'react-dom/client';
import KnowledgeBaseView from './KnowledgeBaseView';
import SettingsPage from './SettingsPage'; // Import the new SettingsPage
import '@/styles/tailwind.css'; // Use alias for global Tailwind CSS

type View = 'knowledgeBase' | 'settings';

const OptionsApp: React.FC = () => {
  const [currentView, setCurrentView] = useState<View>('knowledgeBase');

  return (
    <React.StrictMode>
      <div className="min-h-screen bg-gray-50">
        <nav className="bg-blue-600 text-white p-4 shadow-md">
          <div className="container mx-auto flex justify-center space-x-4">
            <button
              onClick={() => setCurrentView('knowledgeBase')}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                currentView === 'knowledgeBase'
                  ? 'bg-blue-700 ring-2 ring-blue-300'
                  : 'hover:bg-blue-500'
              }`}
            >
              Knowledge Base
            </button>
            <button
              onClick={() => setCurrentView('settings')}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                currentView === 'settings'
                  ? 'bg-blue-700 ring-2 ring-blue-300'
                  : 'hover:bg-blue-500'
              }`}
            >
              Settings
            </button>
          </div>
        </nav>
        <main className="p-4">
          {currentView === 'knowledgeBase' && <KnowledgeBaseView />}
          {currentView === 'settings' && <SettingsPage />}
        </main>
      </div>
    </React.StrictMode>
  );
};

const rootElement = document.getElementById('root');
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(<OptionsApp />);
} else {
  console.error('Failed to find the root element for options page');
}