{"name": "chrome-extension", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -p tsconfig.build.json && vite build && copyfiles public/manifest.json public/popup.html public/options.html dist", "preview": "vite preview", "test": "jest --verbose", "test:e2e": "playwright test"}, "dependencies": {"@pkm-ai/knowledge-base-service": "workspace:*", "react": "^18.3.1", "react-dom": "^18.3.1", "react-window": "^1.8.11", "uuid": "^11.1.0", "webextension-polyfill": "^0.12.0"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1", "@jest/globals": "^29.7.0", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "14.6.1", "@types/chrome": "^0.0.323", "@types/jest": "^29.5.14", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@types/webextension-polyfill": "^0.12.3", "@vitejs/plugin-react": "^4.4.1", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-webextension-mock": "^4.0.0", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-node-polyfills": "^0.23.0"}}