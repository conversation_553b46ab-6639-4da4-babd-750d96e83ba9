module.exports = {
  testEnvironment: 'jest-environment-jsdom', // Use jsdom for React component testing
  setupFilesAfterEnv: ['./jest.setup.js'], // Setup file for jest-dom matchers
  moduleNameMapper: {
    // Handle CSS imports (if any)
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    // Handle static assets
    '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/__mocks__/fileMock.js',
  },
  // Automatically clear mock calls, instances and results before every test
  clearMocks: true,
  // The directory where Jest should output its coverage files
  coverageDirectory: 'coverage',
  // Indicates which provider should be used to instrument code for coverage
  coverageProvider: 'v8',
  // A list of paths to directories that <PERSON><PERSON> should use to search for files in
  roots: ['<rootDir>'],
  // The paths to modules that run some code to configure or set up the testing framework before each test
  // For Electron main process tests, you might need a different environment or specific setup.
  // jest-electron preset usually handles this, but we can customize if needed.
  // For now, focusing on renderer process tests.
  transform: {
    '^.+\\.jsx?$': 'babel-jest', // Assuming Babel is or will be configured for JSX
  },
  testPathIgnorePatterns: ['/node_modules/', '/build/'],
  collectCoverageFrom: [
    'renderer/**/*.{js,jsx}',
    'main.js',
    '!**/node_modules/**',
    '!**/vendor/**',
  ],
};