import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import PopupApp from './PopupApp';

// Mock setTimeout and clearTimeout
jest.useFakeTimers();

// Mock child components to isolate PopupApp logic if needed,
// or test integration if that's the goal. For this, we'll test integration.
// jest.mock('./CaptureTypeSelector', () => () => <div data-testid="capture-type-selector">MockedSelector</div>);
// jest.mock('./CaptureControls', () => () => <div data-testid="capture-controls">MockedControls</div>);


describe('PopupApp Component Integration Tests', () => {
  test('renders the main heading', () => {
    render(<PopupApp />);
    expect(screen.getByText(/Web Content Capture/i)).toBeInTheDocument();
  });

  test('renders CaptureTypeSelector and CaptureControls', () => {
    render(<PopupApp />);
    // Check for elements unique to CaptureTypeSelector
    expect(screen.getByText(/Select Capture Type:/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Full Page/i)).toBeInTheDocument();

    // Check for elements unique to CaptureControls
    expect(screen.getByRole('button', { name: /Capture Full Page/i })).toBeInTheDocument();
  });

  test('changing capture type in CaptureTypeSelector updates button text in CaptureControls', () => {
    render(<PopupApp />);
    
    // Initially, button should be for "Full Page"
    expect(screen.getByRole('button', { name: /Capture Full Page/i })).toBeInTheDocument();

    // Change to "Selection"
    const selectionRadio = screen.getByLabelText(/Selection/i);
    fireEvent.click(selectionRadio);
    
    // Button text should update
    expect(screen.getByRole('button', { name: /Capture Selection/i })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /Capture Full Page/i })).not.toBeInTheDocument();

    // Change to "Bookmark"
    const bookmarkRadio = screen.getByLabelText(/Bookmark/i);
    fireEvent.click(bookmarkRadio);

    // Button text should update
    expect(screen.getByRole('button', { name: /Capture Bookmark/i })).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /Capture Selection/i })).not.toBeInTheDocument();
  });

  test('simulates capture process via CaptureControls and reflects status', async () => {
    render(<PopupApp />);
    
    let captureButton = screen.getByRole('button', { name: /Capture Full Page/i });
    fireEvent.click(captureButton);

    // CaptureControls should show "Capturing..."
    captureButton = screen.getByRole('button', { name: /Capturing.../i });
    expect(captureButton).toBeDisabled();

    // Fast-forward timers to simulate the capture process
    jest.advanceTimersByTime(1500); // Timeout for capture simulation

    await waitFor(() => {
      // CaptureControls should show success message and "Capture More" button
      expect(screen.getByText(/Capture Successful!/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Capture More/i })).toBeInTheDocument();
    });

    // Fast-forward timers to simulate the status reset
    jest.advanceTimersByTime(2000); // Timeout for resetting status

    await waitFor(() => {
      // Status message should disappear, button should reset
      expect(screen.queryByText(/Capture Successful!/i)).not.toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Capture Full Page/i })).toBeInTheDocument(); // Assumes it resets to the current captureType
    });
  });
});