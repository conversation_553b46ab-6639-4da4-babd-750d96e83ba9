# Test Plan: Management & Configuration Module

**Version:** 1.0
**Date:** May 13, 2025
**Author:** <PERSON><PERSON>, AI Test Planner

## 1. Introduction

### 1.1 Purpose
This document outlines the test plan for the **Management & Configuration Module** of the Personalized AI Knowledge Companion & PKM Web Clipper application. The purpose of this plan is to define the scope, approach, resources, and schedule of testing activities to ensure the module meets the specified requirements and quality standards.

### 1.2 Scope

**In Scope:**

*   Testing the configuration of default capture settings (mode, format).
*   Testing the creation, viewing, editing, and deletion (CRUD) of Custom Clipping Templates, including matching criteria, extraction rules, and default organization settings.
*   Testing the CRUD operations for Tags, including merging functionality.
*   Testing the CRUD operations for organizational Categories/Projects, including potential hierarchical structures.
*   Testing the persistence and retrieval of all configurations and organizational structures.
*   Testing the user interface (UI) elements related to configuration and management for usability and clarity.
*   Testing the integration points with the Web Content Capture Module and the Intelligent Capture & Organization Assistance Module.
*   Testing non-functional aspects like data privacy (local storage), user control, and UI efficiency.

**Out of Scope:**

*   Testing AI-driven suggestions for configurations (handled by the Intelligent Capture module).
*   Testing advanced rule engines for templates beyond specified scope (CSS selectors, basic matching).
*   Testing configuration versioning or history.
*   Testing sharing or collaboration features for configurations.
*   Testing cross-device synchronization (unless part of a general system sync mechanism tested elsewhere).
*   End-to-end testing of the entire application workflow (covered in System Testing).
*   Performance load testing beyond basic responsiveness checks.

### 1.3 References
*   Feature Overview Specification: [`docs/specs/Management_Configuration_Module_overview.md`](../specs/Management_Configuration_Module_overview.md)
*   High-Level Architecture: [`docs/architecture/Management_Configuration_Module_architecture.md`](../architecture/Management_Configuration_Module_architecture.md)
*   Product Requirements Document (PRD): [`docs/PRD.md`](../PRD.md) (Sections 5.4, 7)

## 2. Test Strategy

### 2.1 Approach
A combination of manual and automated testing will be employed.
*   **Manual Testing:** Focus on usability, exploratory testing of UI interactions, and validation of complex scenarios like tag merging or template application logic.
*   **Automated Testing:**
    *   **Unit Tests:** Verify individual functions/methods within the Settings Manager, Template Manager, and Tag & Category Manager components (e.g., validation logic, data transformation). These should be written by developers.
    *   **Integration Tests:** Verify the interactions between the Management & Configuration module components (UI, Managers, Persistence) and its interfaces with external modules (Web Content Capture, Intelligent Capture). Focus on API contracts defined in the architecture.
    *   **UI Automation (Optional/Future):** Automate repetitive UI checks for settings application, template CRUD, and tag/category management if feasible within the project's UI framework and testing tools.

### 2.2 Test Levels
*   **Unit Testing:** Focus on individual code units within the module's components.
*   **Integration Testing:** Focus on the interfaces between the module's internal components (e.g., UI <-> Manager <-> Persistence) and external modules (e.g., Management Module <-> Web Capture Module).
*   **System Testing:** While largely out of scope for *this* plan, the functionality tested here forms a basis for broader system-level E2E tests verifying user workflows involving configuration.

### 2.3 Test Types
*   **Functional Testing:** Verify that the module behaves according to the functional requirements and acceptance criteria outlined in the specification.
*   **Non-Functional Testing:**
    *   **Usability Testing:** Evaluate the ease of use, clarity, and efficiency of the configuration UI based on NFR 6.6.1 and NFR 6.6.3.
    *   **Security/Privacy Testing:** Verify that configuration data is stored locally as per NFR 6.1.1 and architecture (e.g., check for unintended network calls related to config data).
    *   **Reliability/Recovery Testing:** Verify persistent storage and correct loading of settings after application restart. Test handling of potential data corruption in local JSON files (if feasible).
    *   **Compatibility Testing:** (If applicable) Test across different supported operating systems or browser environments if the application is cross-platform.

## 3. Test Environment & Prerequisites

### 3.1 Environment
*   **Operating System:** As specified for the target application (e.g., Windows 11, macOS Sonoma, specific Linux distributions).
*   **Application Build:** A stable build of the Personalized AI Knowledge Companion application including the Management & Configuration Module and its dependencies (Web Content Capture, Intelligent Capture, Persistence).
*   **Storage:** Access to the local file system where configuration JSON files (`settings.json`, `templates.json`, `tags.json`, `categories.json`) are stored.

### 3.2 Tools
*   **Test Case Management:** (e.g., Jira, TestRail, or Markdown files in repo).
*   **Bug Tracking:** (e.g., Jira, GitHub Issues).
*   **Developer Tools:** Browser dev tools or equivalent for inspecting UI elements, network requests (for privacy checks), and local storage.
*   **File System Explorer:** To inspect and potentially manipulate local JSON configuration files for specific test scenarios (e.g., testing recovery from corrupted file).
*   **Automation Framework:** (e.g., Jest for unit tests, Playwright/Cypress for UI/Integration tests if applicable).

### 3.3 Test Data
*   Sample website URLs for template matching.
*   Sample CSS selectors for template extraction rules.
*   Sets of tags and categories for management testing (including duplicates for merging, nested structures if applicable).
*   Pre-configured settings files (e.g., to test loading).
*   Corrupted/invalid JSON files (to test error handling).

## 4. Test Cases

*(Note: These are high-level test areas. Detailed test cases with steps, expected results, and specific data should be derived from these.)*

### 4.1 Functional Test Cases

**TC-MC-FUNC-001: Capture Settings Configuration (US1, AC1, FR 5.4.1)**
*   Verify accessing the Settings panel.
*   Verify selecting each available capture mode (Full Page, Article, Selection, Bookmark) as default.
*   Verify selecting each available content format (Markdown, HTML, Plain Text) as default.
*   Verify saving settings persists after closing and reopening the settings panel.
*   Verify saving settings persists after restarting the application.
*   Verify initiating a web capture uses the configured default mode.
*   Verify captured content (where applicable) defaults to the preferred format.
*   Verify changing settings updates the stored `settings.json`.
*   Negative: Test saving with invalid selections (if possible via UI manipulation).

**TC-MC-FUNC-002: Custom Clipping Template - CRUD (US2, AC2, FR 5.4.2)**
*   Verify accessing the Custom Clipping Templates management section.
*   Verify creating a new template with all required fields (Name, Match Criteria, Extraction Rules).
*   Verify creating a template with optional fields (Default Tags, Default Category).
*   Verify created templates appear in the list view.
*   Verify viewing the details of a specific template.
*   Verify editing all fields of an existing template (Name, Criteria, Rules, Defaults).
*   Verify saving edited templates persists changes.
*   Verify deleting a template removes it from the list.
*   Verify template data is correctly stored/updated/removed in `templates.json`.
*   Negative: Test creating a template with missing required fields.
*   Negative: Test creating a template with invalid CSS selectors or URL patterns.
*   Negative: Test creating templates with duplicate names (if disallowed).
*   Boundary: Test template matching with various URL patterns (exact match, starts with, contains, domain).
*   Boundary: Test template matching with complex URLs (query parameters, fragments).

**TC-MC-FUNC-003: Custom Clipping Template - Application (US2, AC2, FR 5.4.2)**
*   Verify that initiating a capture on a URL matching a template's criteria automatically applies the template (if configured).
*   Verify that initiating a capture on a URL matching a template's criteria suggests the template (if configured).
*   Verify that applying a template correctly extracts defined content (Title, Content, Author, Date) using CSS selectors.
*   Verify that applying a template correctly assigns default tags.
*   Verify that applying a template correctly assigns the default category/project.
*   Test scenarios with multiple matching templates (verify priority or selection mechanism).
*   Test scenarios where template selectors do not find matching elements on the page.

**TC-MC-FUNC-004: Tag Management - CRUD (US3, AC3, FR 5.4.3)**
*   Verify accessing the Tag Management section.
*   Verify viewing the list of existing tags.
*   Verify tag usage counts are displayed (if implemented).
*   Verify creating a new tag.
*   Verify renaming an existing tag.
*   Verify the name change is reflected on items previously using the old tag name (requires integration check or mock data).
*   Verify deleting a tag (test different handling strategies: remove tag, prompt user).
*   Verify tag data is correctly stored/updated/removed in `tags.json`.
*   Negative: Test creating tags with empty/invalid names.
*   Negative: Test renaming a tag to an already existing tag name (if disallowed).

**TC-MC-FUNC-005: Tag Management - Merge (US3, AC3, FR 5.4.3)**
*   Verify selecting two or more tags for merging.
*   Verify specifying a target tag name (either a new name or one of the selected tags).
*   Verify successful merge results in the source tags being removed/deactivated.
*   Verify items previously associated with source tags are now associated with the target tag.
*   Verify merge operation updates `tags.json` correctly.
*   Negative: Test merging a single tag.
*   Negative: Test merging into an invalid/empty target name.

**TC-MC-FUNC-006: Category/Project Management - CRUD (US3, AC3, FR 5.4.3)**
*   Verify accessing the Category/Project Management section.
*   Verify viewing the list/hierarchy of existing categories/projects.
*   Verify creating a new top-level category/project.
*   Verify renaming an existing category/project.
*   Verify deleting a category/project (test different handling strategies for contained items).
*   Verify category/project data is correctly stored/updated/removed in `categories.json`.
*   Negative: Test creating categories with empty/invalid names.
*   Negative: Test renaming a category to an already existing category name (if disallowed at the same level).

**TC-MC-FUNC-007: Category/Project Management - Hierarchy (US3, AC3, FR 5.4.3 - if implemented)**
*   Verify creating a sub-category/project under an existing one.
*   Verify moving a category/project to be nested under another.
*   Verify moving a category/project to the top level.
*   Verify deleting a parent category handles nested categories/items correctly based on the chosen strategy.
*   Verify hierarchical structure is correctly represented in the UI and stored in `categories.json`.

### 4.2 Non-Functional Test Cases

**TC-MC-NF-001: Usability (NFR 6.6.1, NFR 6.6.3)**
*   Verify navigation to and within the configuration/management sections is intuitive.
*   Verify labels, tooltips, and helper text are clear and understandable.
*   Verify UI controls (dropdowns, lists, buttons) are standard and easy to use.
*   Verify feedback on actions (save, delete, merge) is immediate and clear.
*   Verify list views (templates, tags, categories) are readable and efficiently navigable (consider scenarios with many items).
*   Verify the process for defining template extraction rules is as user-friendly as possible given the constraints.
*   Perform exploratory testing focused on ease of completing common tasks (e.g., changing default format, creating a simple template, renaming a tag).

**TC-MC-NF-002: Privacy & Security (NFR 6.1.1, Arch 10)**
*   Verify that `settings.json`, `templates.json`, `tags.json`, `categories.json` are created and stored in the expected local application data directory.
*   Using network monitoring tools, verify that no configuration data (settings, template content, tag names, category names) is transmitted over the network during configuration changes or application use, unless explicitly part of a defined (and tested elsewhere) sync feature.

**TC-MC-NF-003: Reliability & Persistence (NFR 6.3.2, Arch 4.5)**
*   Verify all settings, templates, tags, and categories persist after application restart.
*   Verify all settings, templates, tags, and categories persist after system reboot.
*   (Optional/Destructive) Manually corrupt one of the JSON config files (e.g., introduce syntax error, delete closing brace) and observe application behavior on startup. Does it handle the error gracefully (e.g., reset to defaults, notify user, crash)? Expected: Graceful handling or clear error message.
*   (Optional/Destructive) Manually delete one of the JSON config files and observe behavior on startup. Does it recreate default files? Expected: Recreate defaults or handle missing file gracefully.

**TC-MC-NF-004: Flexibility & User Control (NFR 6.5.1)**
*   Verify users can change any configuration setting provided in the UI.
*   Verify deletion operations provide clear confirmation prompts and explain the consequences.
*   Verify users are not forced into specific organizational structures (e.g., can use tags without categories, or vice-versa).

### 4.3 Integration Test Cases

**TC-MC-INT-001: Integration with Persistence Service (Internal)**
*   Verify `Settings Manager` successfully saves settings via `Persistence Service` (check `settings.json`).
*   Verify `Settings Manager` successfully loads settings via `Persistence Service`.
*   Verify `Template Manager` successfully saves/updates/deletes templates via `Persistence Service` (check `templates.json`).
*   Verify `Template Manager` successfully loads templates via `Persistence Service`.
*   Verify `Tag & Category Manager` successfully saves/updates/deletes tags/categories via `Persistence Service` (check `tags.json`, `categories.json`).
*   Verify `Tag & Category Manager` successfully loads tags/categories via `Persistence Service`.
*   Test error handling if the `Persistence Service` reports an error (e.g., disk full, permissions error - may require mocking).

**TC-MC-INT-002: Integration with Web Content Capture Module (Arch 6.1)**
*   Verify `Web Content Capture Module` correctly retrieves and uses the default capture mode set by `Settings Manager`.
*   Verify `Web Content Capture Module` correctly retrieves and uses the default save format set by `Settings Manager`.
*   Verify `Web Content Capture Module` can call `Template Manager` to find matching templates for a given URL.
*   Verify `Web Content Capture Module` receives the correct template data (or null) from `Template Manager`.
*   Test with various URLs (matching, non-matching, multiple matches) to ensure correct template retrieval logic.

**TC-MC-INT-003: Integration with Intelligent Capture & Organization Assistance Module (Arch 6.2)**
*   Verify `Intelligent Capture Module` can successfully retrieve the list of all tags from `Tag & Category Manager`.
*   Verify `Intelligent Capture Module` can successfully retrieve the list of all categories from `Tag & Category Manager`.
*   Verify `Intelligent Capture Module` can successfully add a new tag via `Tag & Category Manager` (simulating user accepting a suggestion). Check `tags.json`.
*   Verify `Intelligent Capture Module` can successfully add a new category via `Tag & Category Manager`. Check `categories.json`.
*   Test behavior when adding a tag/category that already exists.

## 5. Requirements Traceability Matrix

| Requirement ID        | Description                                       | Test Case ID(s)                                                                 |
| :-------------------- | :------------------------------------------------ | :------------------------------------------------------------------------------ |
| **User Stories**      |                                                   |                                                                                 |
| US1                   | Configure default capture settings                | TC-MC-FUNC-001                                                                  |
| US2                   | Create/manage custom clipping templates           | TC-MC-FUNC-002, TC-MC-FUNC-003                                                  |
| US3                   | Manage tags and categories/projects               | TC-MC-FUNC-004, TC-MC-FUNC-005, TC-MC-FUNC-006, TC-MC-FUNC-007                 |
| **Acceptance Crit.**  |                                                   |                                                                                 |
| AC1                   | Capture Settings Config                           | TC-MC-FUNC-001                                                                  |
| AC2                   | Custom Clipping Template Mgmt                     | TC-MC-FUNC-002, TC-MC-FUNC-003                                                  |
| AC3                   | Tag & Category/Project Mgmt                       | TC-MC-FUNC-004, TC-MC-FUNC-005, TC-MC-FUNC-006, TC-MC-FUNC-007                 |
| **Functional Req.**   |                                                   |                                                                                 |
| FR 5.4.1              | Configure capture settings                        | TC-MC-FUNC-001                                                                  |
| FR 5.4.2              | Manage Custom Clipping Templates                  | TC-MC-FUNC-002, TC-MC-FUNC-003                                                  |
| FR 5.4.3              | Manage Tags & Categories                          | TC-MC-FUNC-004, TC-MC-FUNC-005, TC-MC-FUNC-006, TC-MC-FUNC-007                 |
| **Non-Functional Req.**|                                                   |                                                                                 |
| NFR 6.1.1             | Privacy & Security (Local Storage)                | TC-MC-NF-002                                                                    |
| NFR 6.2.1 / 6.2.2     | Data Ownership (Implicitly tested by local store) | TC-MC-NF-002, TC-MC-NF-003                                                      |
| NFR 6.3.2             | Reliability (Persistence)                         | TC-MC-NF-003, TC-MC-INT-001                                                     |
| NFR 6.5.1             | Flexibility & User Control                        | TC-MC-NF-004                                                                    |
| NFR 6.6.1 / 6.6.3     | User Experience (Simplicity, Efficiency)          | TC-MC-NF-001                                                                    |
| **Architecture**      |                                                   |                                                                                 |
| Arch 4.1-4.4          | Component Responsibilities                        | TC-MC-FUNC-\*, TC-MC-INT-\*                                                     |
| Arch 4.5              | Persistence Service (JSON)                        | TC-MC-NF-002, TC-MC-NF-003, TC-MC-INT-001                                        |
| Arch 6.1              | Interaction: Web Content Capture                  | TC-MC-INT-002                                                                   |
| Arch 6.2              | Interaction: Intelligent Capture                  | TC-MC-INT-003                                                                   |

## 6. Exit Criteria

Testing for the Management & Configuration Module can be considered complete when:

*   All planned Unit tests pass.
*   All planned Integration tests pass.
*   All planned Functional test cases have been executed, and the pass rate is 100% for high-priority cases and >= 95% for medium/low priority cases.
*   All identified Critical and High severity defects have been fixed and verified.
*   All planned Non-Functional test cases (Usability, Security, Reliability) have been executed, and results meet acceptable standards.
*   Requirements Traceability Matrix shows coverage for all specified requirements.
*   Test summary report is generated and approved.