import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import SearchBar from '../renderer/components/SearchBar';

describe('SearchBar', () => {
  test('renders an input field', () => {
    render(<SearchBar onSearch={() => {}} />);
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  test('calls onSearch prop when text is entered and Enter is pressed', () => {
    const mockOnSearch = jest.fn();
    render(<SearchBar onSearch={mockOnSearch} />);
    const inputElement = screen.getByRole('textbox');
    fireEvent.change(inputElement, { target: { value: 'test query' } });
    fireEvent.keyDown(inputElement, { key: 'Enter', code: 'Enter', charCode: 13 });
    expect(mockOnSearch).toHaveBeenCalledWith('test query');
  });

  test('calls onSearch prop when search button is clicked', () => {
    const mockOnSearch = jest.fn();
    render(<SearchBar onSearch={mockOnSearch} />);
    const inputElement = screen.getByRole('textbox');
    const buttonElement = screen.getByRole('button', { name: /search/i });
    fireEvent.change(inputElement, { target: { value: 'another query' } });
    fireEvent.click(buttonElement);
    expect(mockOnSearch).toHaveBeenCalledWith('another query');
  });

  test('updates input value on change', () => {
    render(<SearchBar onSearch={() => {}} />);
    const inputElement = screen.getByRole('textbox');
    fireEvent.change(inputElement, { target: { value: 'new value' } });
    expect(inputElement.value).toBe('new value');
  });

  test('does not call onSearch if input is empty and Enter is pressed', () => {
    const mockOnSearch = jest.fn();
    render(<SearchBar onSearch={mockOnSearch} />);
    const inputElement = screen.getByRole('textbox');
    fireEvent.keyDown(inputElement, { key: 'Enter', code: 'Enter', charCode: 13 });
    expect(mockOnSearch).not.toHaveBeenCalled();
  });

  test('does not call onSearch if input is empty and button is clicked', () => {
    const mockOnSearch = jest.fn();
    render(<SearchBar onSearch={mockOnSearch} />);
    const buttonElement = screen.getByRole('button', { name: /search/i });
    fireEvent.click(buttonElement);
    expect(mockOnSearch).not.toHaveBeenCalled();
  });
});