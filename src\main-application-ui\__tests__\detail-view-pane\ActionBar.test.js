import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ActionBar from '../../renderer/components/detail-view-pane/ActionBar';

describe('ActionBar', () => {
  const mockOnInitiateAIQA = jest.fn();
  const mockOnInitiateContentTransformation = jest.fn();
  const mockOnViewConceptualLinks = jest.fn();
  const mockOnEditMetadata = jest.fn();

  const defaultProps = {
    onInitiateAIQA: mockOnInitiateAIQA,
    onInitiateContentTransformation: mockOnInitiateContentTransformation,
    onViewConceptualLinks: mockOnViewConceptualLinks,
    // onEditMetadata is optional, so not included in default test props unless specified
  };

  beforeEach(() => {
    mockOnInitiateAIQA.mockClear();
    mockOnInitiateContentTransformation.mockClear();
    mockOnViewConceptualLinks.mockClear();
    mockOnEditMetadata.mockClear();
  });

  test('renders all standard action buttons by default', () => {
    render(<ActionBar {...defaultProps} />);
    expect(screen.getByLabelText('Initiate AI Q&A')).toBeInTheDocument();
    expect(screen.getByLabelText('Initiate AI Content Transformation')).toBeInTheDocument();
    expect(screen.getByLabelText('View Suggested Conceptual Links')).toBeInTheDocument();
    expect(screen.queryByLabelText('Edit Metadata')).not.toBeInTheDocument(); // Optional, default off
  });

  test('calls onInitiateAIQA when "Ask AI" button is clicked', () => {
    render(<ActionBar {...defaultProps} />);
    fireEvent.click(screen.getByLabelText('Initiate AI Q&A'));
    expect(mockOnInitiateAIQA).toHaveBeenCalledTimes(1);
  });

  test('calls onInitiateContentTransformation when "Transform Content" button is clicked', () => {
    render(<ActionBar {...defaultProps} />);
    fireEvent.click(screen.getByLabelText('Initiate AI Content Transformation'));
    expect(mockOnInitiateContentTransformation).toHaveBeenCalledTimes(1);
  });

  test('calls onViewConceptualLinks when "View Links" button is clicked', () => {
    render(<ActionBar {...defaultProps} />);
    fireEvent.click(screen.getByLabelText('View Suggested Conceptual Links'));
    expect(mockOnViewConceptualLinks).toHaveBeenCalledTimes(1);
  });

  test('renders "Edit Metadata" button and calls onEditMetadata when enabled and prop provided', () => {
    render(
      <ActionBar
        {...defaultProps}
        onEditMetadata={mockOnEditMetadata}
        isEditMetadataEnabled={true}
      />
    );
    const editButton = screen.getByLabelText('Edit Metadata');
    expect(editButton).toBeInTheDocument();
    fireEvent.click(editButton);
    expect(mockOnEditMetadata).toHaveBeenCalledTimes(1);
  });

  test('does not render "Edit Metadata" button if onEditMetadata prop is not provided, even if enabled', () => {
    render(<ActionBar {...defaultProps} isEditMetadataEnabled={true} />); // No onEditMetadata
    expect(screen.queryByLabelText('Edit Metadata')).not.toBeInTheDocument();
  });

  test('does not render "Edit Metadata" button if isEditMetadataEnabled is false (default)', () => {
    render(<ActionBar {...defaultProps} onEditMetadata={mockOnEditMetadata} />); // isEditMetadataEnabled defaults to false
    expect(screen.queryByLabelText('Edit Metadata')).not.toBeInTheDocument();
  });

  // Tests for disabling buttons
  test('does not render AI Q&A button if isAIQAEnabled is false', () => {
    render(<ActionBar {...defaultProps} isAIQAEnabled={false} />);
    expect(screen.queryByLabelText('Initiate AI Q&A')).not.toBeInTheDocument();
  });

  test('does not render Content Transformation button if isContentTransformationEnabled is false', () => {
    render(<ActionBar {...defaultProps} isContentTransformationEnabled={false} />);
    expect(screen.queryByLabelText('Initiate AI Content Transformation')).not.toBeInTheDocument();
  });

  test('does not render Conceptual Links button if isConceptualLinksEnabled is false', () => {
    render(<ActionBar {...defaultProps} isConceptualLinksEnabled={false} />);
    expect(screen.queryByLabelText('View Suggested Conceptual Links')).not.toBeInTheDocument();
  });

  test('renders no buttons if all are disabled', () => {
    render(
      <ActionBar
        {...defaultProps}
        isAIQAEnabled={false}
        isContentTransformationEnabled={false}
        isConceptualLinksEnabled={false}
        isEditMetadataEnabled={false} // Explicitly false, though default
      />
    );
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });
});