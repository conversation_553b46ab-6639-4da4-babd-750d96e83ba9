// __tests__/KnowledgeGraphView.test.js
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import KnowledgeGraphView from '../renderer/components/KnowledgeGraphView';
import useStore from '../renderer/store/useStore'; // Default import

// Create inspectable mock functions for setNodes and setEdges
const mockSetNodes = jest.fn();
const mockSetEdges = jest.fn();
const mockReactFlowOnNodeClick = jest.fn();

// Mock React Flow and its components
jest.mock('reactflow', () => ({
  __esModule: true, // Indicate this is an ES module mock for default export
  default: jest.fn().mockImplementation(({ children, nodes, edges, onNodeClick, onNodesChange, onEdgesChange, onConnect }) => {
    // Store the passed onNodeClick to simulate it later if needed
    mockReactFlowOnNodeClick.mockImplementation(onNodeClick || jest.fn());
    return (
      <div data-testid="mock-react-flow">
        {nodes.map(node => (
          <div key={node.id} data-testid={`node-${node.id}`} onClick={(e) => mockReactFlowOnNodeClick(e, node)}>
            {node.data.label}
          </div>
        ))}
        {edges.map(edge => (
          <div key={edge.id} data-testid={`edge-${edge.id}`}>
            {`Edge from ${edge.source} to ${edge.target}`}
          </div>
        ))}
        {children}
      </div>
    );
  }),
  MiniMap: jest.fn().mockImplementation(() => <div data-testid="mock-minimap" />),
  Controls: jest.fn().mockImplementation(() => <div data-testid="mock-controls" />),
  Background: jest.fn().mockImplementation(() => <div data-testid="mock-background" />),
  useNodesState: (initialNodes) => [initialNodes, mockSetNodes, jest.fn()], // Use inspectable mock
  useEdgesState: (initialEdges) => [initialEdges, mockSetEdges, jest.fn()], // Use inspectable mock
  addEdge: jest.fn((params, eds) => [...eds, {id: `e-${params.source}-${params.target}`, ...params}]), // Simulate addEdge
}));

// Mock the Zustand store
const mockSelectItem = jest.fn();
jest.mock('../renderer/store/useStore', () => ({
  __esModule: true, // Indicate this is an ES module mock
  default: jest.fn(), // Mock the default export
}));


describe('KnowledgeGraphView', () => {
  const mockSelectedItem = {
    id: '1',
    title: 'Central Item',
    conceptualLinks: [
      { id: '2', title: 'Linked Item 1', relevance_score: 0.9 },
      { id: '3', title: 'Linked Item 2', relevance_score: 0.7 },
    ],
  };

  const mockEmptySelectedItem = {
    id: '4',
    title: 'Empty Links Item',
    conceptualLinks: [],
  };

  beforeEach(() => {
    mockSelectItem.mockClear();
    mockSetNodes.mockClear();
    mockSetEdges.mockClear();
    mockReactFlowOnNodeClick.mockClear();
    
    useStore.mockImplementation((selector) => {
      const state = {
        selectedItem: null,
        selectItem: mockSelectItem,
      };
      return selector ? selector(state) : state;
    });
    // Reset the default export mock of ReactFlow if it was changed by a specific test
    require('reactflow').default.mockClear(); 
  });

  test('renders empty state when no item is selected', () => {
    useStore.mockImplementation((selector) => {
      const state = { selectedItem: null, selectItem: mockSelectItem };
      return selector ? selector(state) : state;
    });
    render(<KnowledgeGraphView />);
    expect(screen.getByText('Select an item to see its knowledge graph.')).toBeInTheDocument();
    expect(mockSetNodes).toHaveBeenCalledWith([]);
    expect(mockSetEdges).toHaveBeenCalledWith([]);
  });

  test('correctly processes selected item and conceptual links into nodes and edges', () => {
    useStore.mockImplementation(selector => selector({ selectedItem: mockSelectedItem, selectItem: mockSelectItem }));
    render(<KnowledgeGraphView />);

    expect(mockSetNodes).toHaveBeenCalled();
    const nodesCall = mockSetNodes.mock.calls[0][0]; // Get the argument of the first call to setNodes
    expect(nodesCall).toHaveLength(1 + mockSelectedItem.conceptualLinks.length); // Central node + linked nodes
    expect(nodesCall).toContainEqual(expect.objectContaining({
      id: String(mockSelectedItem.id),
      data: { label: mockSelectedItem.title },
    }));
    mockSelectedItem.conceptualLinks.forEach(link => {
      expect(nodesCall).toContainEqual(expect.objectContaining({
        id: String(link.id),
        data: { label: link.title },
      }));
    });

    expect(mockSetEdges).toHaveBeenCalled();
    const edgesCall = mockSetEdges.mock.calls[0][0];
    expect(edgesCall).toHaveLength(mockSelectedItem.conceptualLinks.length);
    mockSelectedItem.conceptualLinks.forEach(link => {
      expect(edgesCall).toContainEqual(expect.objectContaining({
        id: `e-${mockSelectedItem.id}-${link.id}`,
        source: String(mockSelectedItem.id),
        target: String(link.id),
      }));
    });
    
    // Check that ReactFlow mock and its children are rendered (structural check)
    expect(screen.getByTestId('mock-react-flow')).toBeInTheDocument();
    expect(screen.getByTestId('mock-minimap')).toBeInTheDocument();
    expect(screen.getByTestId('mock-controls')).toBeInTheDocument();
    expect(screen.getByTestId('mock-background')).toBeInTheDocument();
  });

  test('correctly processes item with no conceptual links', () => {
    useStore.mockImplementation(selector => selector({ selectedItem: mockEmptySelectedItem, selectItem: mockSelectItem }));
    render(<KnowledgeGraphView />);

    expect(mockSetNodes).toHaveBeenCalled();
    const nodesCall = mockSetNodes.mock.calls[0][0];
    expect(nodesCall).toHaveLength(1); // Only central node
    expect(nodesCall).toContainEqual(expect.objectContaining({
      id: String(mockEmptySelectedItem.id),
      data: { label: mockEmptySelectedItem.title },
    }));

    expect(mockSetEdges).toHaveBeenCalledWith([]); // Edges should be empty
  });

  test('calls selectItem when a conceptual link node is clicked via ReactFlows onNodeClick', () => {
    useStore.mockImplementation(selector => selector({
        selectedItem: mockSelectedItem,
        selectItem: mockSelectItem,
    }));
    render(<KnowledgeGraphView />);

    const linkedNodeToClick = mockSelectedItem.conceptualLinks[0];
    
    // Simulate the onNodeClick call from ReactFlow mock
    // The mockReactFlowOnNodeClick was set up to be the onNodeClick prop passed to ReactFlow
    expect(mockReactFlowOnNodeClick).toBeDefined(); 
    mockReactFlowOnNodeClick(null, { id: String(linkedNodeToClick.id), data: { label: linkedNodeToClick.title } });


    expect(mockSelectItem).toHaveBeenCalledTimes(1);
    expect(mockSelectItem).toHaveBeenCalledWith(linkedNodeToClick.id);
  });

  test('does not call selectItem when the central node (selected item itself) is clicked via ReactFlows onNodeClick', () => {
     useStore.mockImplementation(selector => selector({
        selectedItem: mockSelectedItem,
        selectItem: mockSelectItem,
    }));
    render(<KnowledgeGraphView />);
    
    expect(mockReactFlowOnNodeClick).toBeDefined();
    mockReactFlowOnNodeClick(null, { id: String(mockSelectedItem.id), data: { label: mockSelectedItem.title } });

    expect(mockSelectItem).not.toHaveBeenCalled();
  });
});
