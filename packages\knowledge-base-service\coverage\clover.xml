<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1747804514110" clover="3.2.0">
  <project timestamp="1747804514110" name="All files">
    <metrics statements="299" coveredstatements="262" conditionals="63" coveredconditionals="44" methods="17" coveredmethods="16" elements="379" coveredelements="322" complexity="0" loc="299" ncloc="299" packages="2" files="3" classes="3"/>
    <package name="src">
      <metrics statements="232" coveredstatements="195" conditionals="46" coveredconditionals="27" methods="13" coveredmethods="12"/>
      <file name="KnowledgeBaseService.ts" path="D:\AI\pkmAI\packages\knowledge-base-service\src\KnowledgeBaseService.ts">
        <metrics statements="230" coveredstatements="195" conditionals="45" coveredconditionals="27" methods="12" coveredmethods="12"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="cond" truecount="0" falsecount="2"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="31" count="15" type="stmt"/>
        <line num="32" count="15" type="stmt"/>
        <line num="33" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="15" type="stmt"/>
        <line num="38" count="15" type="stmt"/>
        <line num="39" count="15" type="stmt"/>
        <line num="40" count="15" type="stmt"/>
        <line num="41" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="42" count="15" type="stmt"/>
        <line num="43" count="15" type="stmt"/>
        <line num="44" count="15" type="stmt"/>
        <line num="45" count="15" type="stmt"/>
        <line num="46" count="15" type="stmt"/>
        <line num="47" count="15" type="stmt"/>
        <line num="48" count="15" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="51" count="15" type="stmt"/>
        <line num="52" count="15" type="stmt"/>
        <line num="53" count="15" type="stmt"/>
        <line num="54" count="15" type="stmt"/>
        <line num="55" count="15" type="stmt"/>
        <line num="56" count="15" type="stmt"/>
        <line num="57" count="15" type="stmt"/>
        <line num="58" count="15" type="stmt"/>
        <line num="59" count="15" type="stmt"/>
        <line num="60" count="15" type="stmt"/>
        <line num="61" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="65" count="15" type="stmt"/>
        <line num="66" count="15" type="stmt"/>
        <line num="67" count="15" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="70" count="15" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="76" count="15" type="stmt"/>
        <line num="77" count="15" type="stmt"/>
        <line num="78" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="79" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="80" count="15" type="stmt"/>
        <line num="81" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="82" count="15" type="stmt"/>
        <line num="83" count="15" type="cond" truecount="0" falsecount="1"/>
        <line num="84" count="15" type="stmt"/>
        <line num="85" count="15" type="stmt"/>
        <line num="86" count="15" type="stmt"/>
        <line num="87" count="15" type="stmt"/>
        <line num="88" count="15" type="stmt"/>
        <line num="89" count="15" type="stmt"/>
        <line num="90" count="15" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="92" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="93" count="15" type="stmt"/>
        <line num="94" count="15" type="stmt"/>
        <line num="95" count="15" type="cond" truecount="2" falsecount="1"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="15" type="cond" truecount="1" falsecount="0"/>
        <line num="100" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="103" count="1" type="stmt"/>
        <line num="104" count="1" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="1" type="stmt"/>
        <line num="124" count="15" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="127" count="48" type="cond" truecount="0" falsecount="1"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="48" type="stmt"/>
        <line num="133" count="48" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="136" count="18" type="stmt"/>
        <line num="137" count="18" type="stmt"/>
        <line num="138" count="18" type="stmt"/>
        <line num="139" count="18" type="stmt"/>
        <line num="140" count="18" type="stmt"/>
        <line num="141" count="18" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="144" count="9" type="stmt"/>
        <line num="145" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="146" count="9" type="stmt"/>
        <line num="147" count="9" type="stmt"/>
        <line num="148" count="9" type="stmt"/>
        <line num="149" count="9" type="stmt"/>
        <line num="150" count="9" type="stmt"/>
        <line num="151" count="9" type="stmt"/>
        <line num="152" count="9" type="stmt"/>
        <line num="153" count="9" type="stmt"/>
        <line num="154" count="9" type="stmt"/>
        <line num="155" count="9" type="stmt"/>
        <line num="156" count="9" type="stmt"/>
        <line num="157" count="9" type="stmt"/>
        <line num="158" count="9" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="161" count="4" type="stmt"/>
        <line num="162" count="4" type="stmt"/>
        <line num="163" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="164" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="165" count="4" type="stmt"/>
        <line num="166" count="1" type="stmt"/>
        <line num="167" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="168" count="2" type="stmt"/>
        <line num="169" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="170" count="2" type="stmt"/>
        <line num="171" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="172" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="173" count="1" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="175" count="1" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="177" count="1" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
        <line num="179" count="1" type="stmt"/>
        <line num="180" count="1" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="182" count="1" type="stmt"/>
        <line num="183" count="2" type="stmt"/>
        <line num="184" count="2" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="186" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="187" count="2" type="stmt"/>
        <line num="188" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="189" count="2" type="stmt"/>
        <line num="190" count="2" type="stmt"/>
        <line num="191" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="192" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="1" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="2" type="stmt"/>
        <line num="198" count="2" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
        <line num="200" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="201" count="7" type="stmt"/>
        <line num="202" count="7" type="stmt"/>
        <line num="203" count="7" type="stmt"/>
        <line num="204" count="7" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="207" count="24" type="stmt"/>
        <line num="208" count="24" type="cond" truecount="1" falsecount="0"/>
        <line num="209" count="24" type="cond" truecount="0" falsecount="1"/>
        <line num="210" count="24" type="stmt"/>
        <line num="211" count="24" type="stmt"/>
        <line num="212" count="24" type="stmt"/>
        <line num="213" count="24" type="stmt"/>
        <line num="214" count="24" type="stmt"/>
        <line num="215" count="24" type="stmt"/>
        <line num="216" count="24" type="stmt"/>
        <line num="217" count="24" type="stmt"/>
        <line num="218" count="24" type="stmt"/>
        <line num="219" count="24" type="stmt"/>
        <line num="220" count="24" type="cond" truecount="0" falsecount="1"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="24" type="stmt"/>
        <line num="229" count="24" type="stmt"/>
        <line num="230" count="1" type="stmt"/>
      </file>
      <file name="index.ts" path="D:\AI\pkmAI\packages\knowledge-base-service\src\index.ts">
        <metrics statements="2" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="2" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.adapters">
      <metrics statements="67" coveredstatements="67" conditionals="17" coveredconditionals="17" methods="4" coveredmethods="4"/>
      <file name="ChromeStorageLocalAdapter.ts" path="D:\AI\pkmAI\packages\knowledge-base-service\src\adapters\ChromeStorageLocalAdapter.ts">
        <metrics statements="67" coveredstatements="67" conditionals="17" coveredconditionals="17" methods="4" coveredmethods="4"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="12" count="9" type="stmt"/>
        <line num="13" count="9" type="stmt"/>
        <line num="14" count="9" type="stmt"/>
        <line num="15" count="9" type="stmt"/>
        <line num="16" count="9" type="stmt"/>
        <line num="17" count="9" type="stmt"/>
        <line num="18" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="19" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="20" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="23" count="8" type="stmt"/>
        <line num="24" count="9" type="stmt"/>
        <line num="25" count="9" type="stmt"/>
        <line num="26" count="9" type="stmt"/>
        <line num="27" count="9" type="stmt"/>
        <line num="28" count="9" type="stmt"/>
        <line num="29" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="30" count="4" type="cond" truecount="3" falsecount="0"/>
        <line num="31" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="35" count="3" type="stmt"/>
        <line num="36" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="4" type="stmt"/>
        <line num="47" count="9" type="stmt"/>
        <line num="48" count="9" type="stmt"/>
        <line num="49" count="9" type="stmt"/>
        <line num="50" count="9" type="stmt"/>
        <line num="51" count="9" type="stmt"/>
        <line num="52" count="9" type="stmt"/>
        <line num="53" count="9" type="cond" truecount="1" falsecount="0"/>
        <line num="54" count="3" type="cond" truecount="3" falsecount="0"/>
        <line num="55" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="59" count="2" type="stmt"/>
        <line num="60" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="3" type="stmt"/>
        <line num="67" count="9" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
