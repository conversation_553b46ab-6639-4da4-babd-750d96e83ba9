import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import KnowledgeBaseView from '../renderer/components/KnowledgeBaseView';

// Mock child components
jest.mock('../renderer/components/SearchBar', () => ({ onSearch }) => (
  <div data-testid="search-bar">
    <input type="text" onChange={(e) => onSearch(e.target.value)} data-testid="search-input" />
    <button onClick={() => onSearch('mock search from button')}>Search</button>
  </div>
));

jest.mock('../renderer/components/knowledge-base-view/ContentList', () => {
  const MockContentList = ({ items, onSelectItem }) => (
    <div data-testid="content-list">
      <h4>Content List</h4>
      <ul role="list"> {/* Explicit role for clarity, matches getByRole('list') */}
        {items.map(item => (
          <li
            key={item.id}
            onClick={() => onSelectItem(item.id)} // Original mock passed item.id
            data-testid={`item-${item.id}`}
            aria-label={`View details for ${item.title}`} // For getByRole('listitem', {name: ...})
            // role="listitem" is implicit for li in ul
          >
            <div className="content-list-item-title">{item.title}</div> {/* For querySelector('.content-list-item-title') */}
          </li>
        ))}
      </ul>
    </div>
  );
  return MockContentList;
});

jest.mock('../renderer/components/knowledge-base-view/FilterSortBar', () => ({ availableTags, availableCategories, initialFilters, initialSort, onFilterChange, onSortChange }) => (
  <div data-testid="filter-sort-bar">
    <h4>Filter Sort Bar</h4>
    <div>Tags: {availableTags.join(', ')}</div>
    <div>Categories: {availableCategories.join(', ')}</div>
    <button data-testid="mock-filter-change" onClick={() => onFilterChange({ tags: ['mockTag'] })}>Mock Filter</button>
    <button data-testid="mock-sort-change" onClick={() => onSortChange({ by: 'title', order: 'asc' })}>Mock Sort</button>
  </div>
));

jest.mock('../renderer/components/knowledge-base-view/PaginationControl', () => ({ currentPage, totalPages, onPageChange, itemsPerPage, totalItems }) => (
  <div data-testid="pagination-control">
    <h4>Pagination Control</h4>
    <p>Page {currentPage} of {totalPages}</p>
    <p>Items {itemsPerPage} per page, Total {totalItems}</p>
    <button onClick={() => onPageChange(currentPage + 1)} disabled={currentPage === totalPages}>Next</button>
  </div>
));


describe('KnowledgeBaseView', () => {
  const mockItems = [
    { id: '1', title: 'Item 1', snippet: 'Snippet 1', tags: ['a'], timestamp: '2023-01-01', source: 'Source1' },
    { id: '2', title: 'Item 2', snippet: 'Snippet 2', tags: ['b'], timestamp: '2023-01-02', source: 'Source2' },
  ];
  const mockSearchResults = [
    { id: 'sr1', title: 'Search Result 1', snippet: 'SR Snippet 1', tags: ['search'], timestamp: '2023-01-03', source: 'SearchSource1' },
  ];

  const mockProps = {
    items: mockItems,
    searchResults: [],
    currentSearchTerm: '',
    onSearchTermChange: jest.fn(),
    onPerformSearch: jest.fn(),
    availableTags: [{id: 't1', name: 'Tag1'}, {id: 't2', name: 'Tag2'}],
    availableCategories: [{id: 'c1', name: 'Category1'}],
    filters: { tags: [], category: '', date: '', source: '' },
    sort: { by: 'date', order: 'desc' },
    onFiltersChange: jest.fn(),
    onSortChange: jest.fn(),
    onSelectItem: jest.fn(),
    currentPage: 1,
    totalPages: 1,
    onPageChange: jest.fn(),
    itemsPerPage: 10,
    totalItems: mockItems.length,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the main title and all mocked sub-components', () => {
    render(<KnowledgeBaseView {...mockProps} />);
    expect(screen.getByText('Knowledge Base')).toBeInTheDocument();
    expect(screen.getByTestId('search-bar')).toBeInTheDocument();
    expect(screen.getByTestId('filter-sort-bar')).toBeInTheDocument();
    expect(screen.getByTestId('content-list')).toBeInTheDocument();
    expect(screen.getByTestId('pagination-control')).toBeInTheDocument();
  });

  test('calls onSearchTermChange and onPerformSearch when SearchBar initiates a search', () => {
    render(<KnowledgeBaseView {...mockProps} />);
    const searchInput = screen.getByTestId('search-input');
    fireEvent.change(searchInput, { target: { value: 'test query' } });
    expect(mockProps.onSearchTermChange).toHaveBeenCalledWith('test query');
    expect(mockProps.onPerformSearch).toHaveBeenCalledWith('test query');
  });
  
  test('passes correct props to FilterSortBar', () => {
    render(<KnowledgeBaseView {...mockProps} />);
    // Check if props are passed (via mock rendering)
    expect(screen.getByText('Tags: Tag1, Tag2')).toBeInTheDocument();
    expect(screen.getByText('Categories: Category1')).toBeInTheDocument();
    
    fireEvent.click(screen.getByTestId('mock-filter-change'));
    expect(mockProps.onFiltersChange).toHaveBeenCalledWith({ tags: ['mockTag'] });

    fireEvent.click(screen.getByTestId('mock-sort-change'));
    expect(mockProps.onSortChange).toHaveBeenCalledWith({ by: 'title', order: 'asc' });
  });

  test('passes correct items to ContentList and displays them', () => {
    render(<KnowledgeBaseView {...mockProps} />);
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
  });

  test('displays searchResults in ContentList when currentSearchTerm is present', () => {
    const propsWithSearch = {
      ...mockProps,
      currentSearchTerm: 'query',
      searchResults: mockSearchResults,
      totalItems: mockSearchResults.length, // Update totalItems for pagination if it reflects search results
    };
    render(<KnowledgeBaseView {...propsWithSearch} />);
    expect(screen.getByText('Search Result 1')).toBeInTheDocument();
    expect(screen.queryByText('Item 1')).not.toBeInTheDocument();
  });
  
  test('calls onSelectItem with item ID when an item is clicked in ContentList', () => {
    render(<KnowledgeBaseView {...mockProps} />);
    const itemElement = screen.getByTestId('item-1'); // Mock ContentList uses item.id
    fireEvent.click(itemElement);
    expect(mockProps.onSelectItem).toHaveBeenCalledWith('1');
  });

  test('passes correct props to PaginationControl', () => {
    const paginatedProps = {
      ...mockProps,
      currentPage: 2,
      totalPages: 5,
      totalItems: 45,
      itemsPerPage: 10,
    }
    render(<KnowledgeBaseView {...paginatedProps} />);
    expect(screen.getByText('Page 2 of 5')).toBeInTheDocument();
    expect(screen.getByText('Items 10 per page, Total 45')).toBeInTheDocument();
    
    const nextButton = screen.getByRole('button', { name: 'Next' });
    fireEvent.click(nextButton);
    expect(paginatedProps.onPageChange).toHaveBeenCalledWith(3);
  });
});