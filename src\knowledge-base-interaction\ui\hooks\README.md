# UI Hooks

This directory contains custom React hooks for the Knowledge Base Interaction & Insights Module's UI layer.

Hooks are used to encapsulate stateful logic and side effects, making them reusable across multiple components or views. This helps in keeping components lean and focused on presentation.

## Examples of Hooks:
- `useSearch.js`: A hook to manage search state, execute searches, and handle results.
- `useKBNavigation.js`: A hook to manage the state and logic for navigating the knowledge base tree.
- `useQASession.js`: A hook to manage the state of a Q&A session, including history and API calls.
- `useContentTransformation.js`: A hook to handle requests and state for content transformations.

Each hook should be a single `.js` file and clearly document its purpose, parameters, and returned values.
They should be designed to be testable, often by mocking their dependencies (like service calls).