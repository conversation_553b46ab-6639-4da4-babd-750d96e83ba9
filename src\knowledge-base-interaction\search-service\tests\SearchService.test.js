// src/knowledge-base-interaction/search-service/tests/SearchService.test.js

const SearchService = require('../core/SearchService');

// Mocks for dependencies
const mockKbalService = {
    getContent: jest.fn(async (params) => {
        // AI-Verifiable: Mock KBAL service simulates data retrieval
        if (params && params.type === 'keyword' && params.keywords && params.keywords.includes('testkeyword')) {
            return [{ id: 'kbal1', text: 'This is a test keyword document.', title: 'Keyword Doc 1' }];
        }
        if (params && params.type === 'semantic' && params.queryText === 'find semantic test') {
            return [{ id: 'kbal2', text: 'A document for semantic testing.', title: 'Semantic Doc 1' }];
        }
        return [];
    }),
    // Add other KBAL methods if SearchService uses them directly
};

const mockKeywordSearch = {
    performSearch: jest.fn(async (query, kbal) => {
        // AI-Verifiable: Mock KeywordSearch simulates search
        if (query === 'testkeyword') {
            const kbalResults = await kbal.getContent({ type: 'keyword', keywords: ['testkeyword'] });
            return kbalResults.map(r => ({ ...r, source: 'keyword-mock', score: 0.7, type: 'keyword' }));
        }
        return [];
    }),
};

const mockSemanticSearch = {
    performSearch: jest.fn(async (query, kbal) => {
        // AI-Verifiable: Mock SemanticSearch simulates search
        if (query === 'find semantic test') {
            const kbalResults = await kbal.getContent({ type: 'semantic', queryText: 'find semantic test' });
            return kbalResults.map(r => ({ ...r, source: 'semantic-mock', score: 0.9, type: 'semantic' }));
        }
        return [];
    }),
};

const mockIndexingService = {
    handleRequest: jest.fn(async (action, data) => {
        // AI-Verifiable: Mock IndexingService simulates actions
        if (action === 'queryIndex' && data.query === 'indexed query') {
            return { results: [{ id: 'idx1', text: 'Indexed content.', title: 'Indexed Doc' }], message: 'Indexed query successful' };
        }
        return { message: `Action ${action} simulated.`};
    }),
    getStatus: jest.fn(async () => ({ status: 'mock_ok' })),
};

describe('SearchService', () => {
    let searchService;
    let searchServiceWithIndex;

    beforeEach(() => {
        // AI-Verifiable: Test setup initializes SearchService
        jest.clearAllMocks(); // Clear mocks before each test
        searchService = new SearchService(mockKbalService, mockKeywordSearch, mockSemanticSearch);
        searchServiceWithIndex = new SearchService(mockKbalService, mockKeywordSearch, mockSemanticSearch, mockIndexingService);
    });

    // AI-Verifiable: Test case for constructor
    test('should be instantiated correctly', () => {
        expect(searchService).toBeInstanceOf(SearchService);
        expect(searchService.kbalService).toBe(mockKbalService);
        expect(searchService.keywordSearch).toBe(mockKeywordSearch);
        expect(searchService.semanticSearch).toBe(mockSemanticSearch);
        expect(searchService.indexingService).toBeNull(); // Default
        expect(searchServiceWithIndex.indexingService).toBe(mockIndexingService);
    });

    // AI-Verifiable: Test case for basic keyword search
    describe('search method - keyword', () => {
        test('should perform a keyword search successfully', async () => {
            const query = 'testkeyword';
            const results = await searchService.search(query, 'keyword');
            expect(mockKeywordSearch.performSearch).toHaveBeenCalledWith(query, mockKbalService);
            expect(mockSemanticSearch.performSearch).not.toHaveBeenCalled();
            expect(results).toEqual(expect.arrayContaining([
                expect.objectContaining({ id: 'kbal1', source: 'keyword-mock' })
            ]));
            // AI-Verifiable: Check if KBAL was called by the mock keyword search
            expect(mockKbalService.getContent).toHaveBeenCalledWith({ type: 'keyword', keywords: ['testkeyword'] });
        });
    });

    // AI-Verifiable: Test case for basic semantic search
    describe('search method - semantic', () => {
        test('should perform a semantic search successfully', async () => {
            const query = 'find semantic test';
            const results = await searchService.search(query, 'semantic');
            expect(mockSemanticSearch.performSearch).toHaveBeenCalledWith(query, mockKbalService);
            expect(mockKeywordSearch.performSearch).not.toHaveBeenCalled();
            expect(results).toEqual(expect.arrayContaining([
                expect.objectContaining({ id: 'kbal2', source: 'semantic-mock' })
            ]));
            // AI-Verifiable: Check if KBAL was called by the mock semantic search
            expect(mockKbalService.getContent).toHaveBeenCalledWith({ type: 'semantic', queryText: 'find semantic test' });
        });
    });

    // AI-Verifiable: Test case for hybrid search
    describe('search method - hybrid', () => {
        test('should perform a hybrid search and combine results', async () => {
            mockKeywordSearch.performSearch.mockResolvedValueOnce([{ id: 'k1', text: 'Hybrid keyword', title: 'Hybrid K', source: 'keyword-mock', score: 0.6, type: 'keyword' }]);
            mockSemanticSearch.performSearch.mockResolvedValueOnce([{ id: 's1', text: 'Hybrid semantic', title: 'Hybrid S', source: 'semantic-mock', score: 0.8, type: 'semantic' }]);
            
            const query = 'hybrid query';
            const results = await searchService.search(query, 'hybrid');
            
            expect(mockKeywordSearch.performSearch).toHaveBeenCalledWith(query, mockKbalService);
            expect(mockSemanticSearch.performSearch).toHaveBeenCalledWith(query, mockKbalService);
            expect(results).toHaveLength(2);
            expect(results).toEqual(expect.arrayContaining([
                expect.objectContaining({ id: 'k1' }),
                expect.objectContaining({ id: 's1' })
            ]));
        });

        test('should handle deduplication for hybrid search if items have same ID', async () => {
            mockKeywordSearch.performSearch.mockResolvedValueOnce([{ id: 'common1', text: 'Common keyword', title: 'Common K', source: 'keyword-mock', score: 0.6, type: 'keyword' }]);
            mockSemanticSearch.performSearch.mockResolvedValueOnce([{ id: 'common1', text: 'Common semantic', title: 'Common S', source: 'semantic-mock', score: 0.8, type: 'semantic' }]);
            
            const query = 'common query';
            const results = await searchService.search(query, 'hybrid');
            
            expect(results).toHaveLength(1); // Deduplicated
            expect(results[0].id).toBe('common1');
            // Depending on deduplication strategy, one of the versions will be kept.
            // The current simple deduplication keeps the first encountered.
        });
    });
    
    // AI-Verifiable: Test case for search input validation
    test('search method should throw error for invalid query', async () => {
        await expect(searchService.search(null, 'keyword')).rejects.toThrow('Search query must be a non-empty string.');
        await expect(searchService.search('', 'semantic')).rejects.toThrow('Search query must be a non-empty string.');
        await expect(searchService.search(123, 'hybrid')).rejects.toThrow('Search query must be a non-empty string.');
    });

    // AI-Verifiable: Test case for error handling in search
    test('search method should propagate errors from underlying search components', async () => {
        mockKeywordSearch.performSearch.mockRejectedValueOnce(new Error('Keyword search component failed'));
        await expect(searchService.search('error query', 'keyword')).rejects.toThrow('Search operation failed.');
    });

    // AI-Verifiable: Test cases for manageIndex
    describe('manageIndex method', () => {
        test('should call indexingService.handleRequest when service is configured', async () => {
            const action = 'queryIndex';
            const data = { query: 'indexed query' };
            const response = await searchServiceWithIndex.manageIndex(action, data);
            
            expect(mockIndexingService.handleRequest).toHaveBeenCalledWith(action, data);
            expect(response).toEqual({ results: [{ id: 'idx1', text: 'Indexed content.', title: 'Indexed Doc' }], message: 'Indexed query successful' });
        });

        test('should warn and return null if indexingService is not configured', async () => {
            const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
            const response = await searchService.manageIndex('queryIndex', { query: 'test' });
            
            expect(mockIndexingService.handleRequest).not.toHaveBeenCalled();
            expect(response).toBeNull();
            expect(consoleWarnSpy).toHaveBeenCalledWith('Indexing service not configured.');
            consoleWarnSpy.mockRestore();
        });
    });
});