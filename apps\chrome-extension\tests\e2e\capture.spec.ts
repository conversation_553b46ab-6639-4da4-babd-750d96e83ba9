import { test, expect, type BrowserContext, type Worker } from '@playwright/test';
import path from 'path';
import { fileURLToPath } from 'url';

let serviceWorker: Worker; // Declare serviceWorker at the top level, use Worker type

test.beforeEach(async ({ context: fixtureContext }) => {
  // Use the context provided by the Playwright fixture, which is configured to load the extension
  // For Manifest V3, background pages are replaced by service workers.
  // We need to wait for the service worker to be available.
  // The service worker handles chrome.storage.local and lowdb interactions.
  // We can send a message to the service worker to clear storage.
  serviceWorker = await fixtureContext.waitForEvent('serviceworker', { timeout: 60000 }); // Increased timeout to 60 seconds
  console.log('Service worker detected.');

  // Evaluate code in the service worker context to clear storage
  await serviceWorker.evaluate(async () => {
    // @ts-ignore - chrome is available in the service worker context
    await chrome.storage.local.clear();
    // Add logic here to clear or mock lowdb if necessary for test isolation
    // Example: await self.kbService.clearDatabase(); // Assuming kbService is exposed globally in the service worker
  });
});

test.describe('Web Content Capture', () => {
  // AI Verifiable End Result: Basic capture functionality is tested.
  // This test verifies Test Case 1.1 from Master Acceptance Test Plan.
  test('should capture and store web content via popup', async ({ context }) => {
    const page = await context.newPage();
    // Navigate to a test page
    await page.goto('https://playwright.dev/docs/extensions');

    // Get the extension ID from the service worker URL
    const extensionId = serviceWorker.url().split('/')[2]; // Extract ID from 'chrome-extension://<extension_id>/...'

    // Open the popup page directly using the extension ID
    const popupPage = await context.newPage();
    await popupPage.goto(`chrome-extension://${extensionId}/popup.html`);

    // Wait for the popup UI to load and the capture button to be visible
    const captureButton = popupPage.locator('#capture-bookmark-button'); // Corrected ID
    await expect(captureButton).toBeVisible();

    // Click the capture button
    await captureButton.click();

    // Wait for the capture process to complete (e.g., by waiting for a success message or UI change)
    // This might need refinement based on the actual UI feedback.
    // For now, wait for a short duration or a specific element to appear/disappear.
    // A more robust approach would be to wait for a message from the service worker confirming save.
    await popupPage.waitForTimeout(1000); // Arbitrary wait, replace with a proper condition

    // Evaluate code in the service worker to retrieve the captured data from lowdb
    // This assumes a method like 'getKnowledgeBaseData' is exposed by the service worker
    // to access lowdb for testing purposes. This might need to be implemented in the service worker.
    const storedData = await serviceWorker.evaluate(async () => {
      // @ts-ignore - Accessing exposed kbService in the service worker
      if ((self as any).kbService && typeof (self as any).kbService.getAllEntries === 'function') {
        return (self as any).kbService.getAllEntries();
      }
      // Fallback if kbService or getAllEntries is not available, try chrome.storage.local
      // @ts-ignore
      return chrome.storage.local.get(null); // Get all items from storage.local
    });

    // Assert that the retrieved data is correct
    // This assertion needs to match the actual data structure stored by the capture process.
    // Assuming captured content includes url and title and is stored in an array under a key like 'knowledgeItems'.
    expect(storedData).toBeDefined();
    // Check if storedData contains an array of knowledge items
    const knowledgeItems = storedData.knowledgeItems || Object.values(storedData).find(val => Array.isArray(val));

    expect(knowledgeItems).toBeDefined();
    expect(knowledgeItems.length).toBeGreaterThan(0);

    const capturedItem = knowledgeItems.find(item => item.url === 'https://playwright.dev/docs/extensions');
    expect(capturedItem).toBeDefined();
    expect(capturedItem.title).toContain('Playwright'); // Check if title contains expected text
    // Add more assertions based on the expected captured data structure (e.g., content, tags, categories)

    console.log('Basic capture and store test completed.');
  });

  // AI Verifiable End Result: Error handling for popup initialization is tested.
  // This test verifies Test Case 1.2 from Master Acceptance Test Plan.
  // It simulates a chrome.runtime.lastError scenario.
  test('should handle chrome.runtime.lastError during popup initialization', async ({ context }) => {
    // Get the extension ID from the service worker URL
    const extensionId = serviceWorker.url().split('/')[2]; // Extract ID from 'chrome-extension://<extension_id>/...'

    // To simulate chrome.runtime.lastError during popup initialization,
    // we need to trigger an error condition *before* the popup script runs.
    // This is tricky in Playwright's extension testing.
    // A common cause of chrome.runtime.lastError is a failed message send.
    // We can try to simulate this by attempting to send a message to a non-existent receiver
    // from the test environment or by mocking the chrome.runtime.sendMessage function
    // in the background script to set chrome.runtime.lastError.

    // A simpler approach for testing the *popup's handling* of the error,
    // assuming the error is already set by the background script, is to
    // navigate to the popup and check its UI state.

    // For a more direct test of the popup's error handling *mechanism*,
    // we could potentially inject a script into the popup page context
    // that sets chrome.runtime.lastError before the main popup script executes.
    // However, this might be complex.

    // Let's refine the approach: Assume the background script *would* set
    // chrome.runtime.lastError if a critical message failed during popup setup.
    // We will simulate the *effect* of this error being set by navigating to
    // a page that the extension might try to interact with, causing an error,
    // and then opening the popup to see how it reacts. This is an indirect test
    // but verifies the popup's ability to detect and react to the error.

    // Navigate to a page that might cause an extension error upon load
    // (This is a hypothetical scenario to try and trigger chrome.runtime.lastError)
    const page = await context.newPage();
    // Attempting to send a message to a non-existent tab or with invalid parameters
    // might set chrome.runtime.lastError in the background script.
    // We can try sending a message from the test context to the background script
    // with invalid parameters to see if it triggers the error.
    try {
        await serviceWorker.evaluate(async () => {
            // Attempt to send a message that is likely to fail
            // @ts-ignore
            chrome.runtime.sendMessage('invalid_extension_id', { type: 'trigger_error' }, (response) => {
                // This callback will be called, and chrome.runtime.lastError might be set
                console.log('Attempted to trigger error message send.');
                // The actual error is set on chrome.runtime.lastError if the send fails
            });
        });
         // Wait a moment for the potential error to be set
        await page.waitForTimeout(500);
    } catch (e) {
        console.log(`Error attempting to trigger message send: ${e}`);
        // This catch block handles errors in the evaluate call itself, not chrome.runtime.lastError
    }


    // Now, open the popup page directly
    const popupPage = await context.newPage();
    await popupPage.goto(`chrome-extension://${extensionId}/popup.html`);

    // Wait for an element that indicates an error state in the popup UI
    // This assumes the popup UI displays a specific element or message when chrome.runtime.lastError is detected.
    const errorMessageElement = popupPage.locator('.error-message'); // Assuming a class 'error-message' for the error display
    await expect(errorMessageElement).toBeVisible();
    const errorMessageText = await errorMessageElement.textContent();
    expect(errorMessageText).toContain('Error loading extension'); // Assuming a generic error message

    // Verify that the main content of the popup is hidden or disabled
    const mainContentElement = popupPage.locator('#main-popup-content'); // Assuming an ID 'main-popup-content'
    await expect(mainContentElement).not.toBeVisible();

    console.log('chrome.runtime.lastError handling test completed.');
  });
});
