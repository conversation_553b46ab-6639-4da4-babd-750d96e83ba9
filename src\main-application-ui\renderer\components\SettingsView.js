import React, { useState, useEffect } from 'react';
import './SettingsView.css';
import useStore from '../store/useStore'; // Import the Zustand store

function SettingsView({ onBack }) {
  const [activeTab, setActiveTab] = useState('capture');

  // Destructure state and actions from the Zustand store
  const {
    captureSettings,
    captureSettingsLoading,
    captureSettingsError,
    captureSettingsSaving,
    captureSettingsSaveError,
    fetchCaptureSettings,
    updateCaptureSetting,
    saveCaptureSettings,
    clearCaptureSettingsStatus,

    allTags,
    tagOperationLoading,
    tagOperationError,
    fetchTags,
    createTag,
    updateTag,
    deleteTag,

    allCategories,
    categoryOperationLoading,
    categoryOperationError,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,

    clippingTemplates,
    clippingTemplatesLoading,
    clippingTemplatesError,
    clippingTemplateCreating,
    clippingTemplateCreateError,
    clippingTemplateUpdating,
    clippingTemplateUpdateError,
    clippingTemplateDeleting,
    clippingTemplateDeleteError,
    clippingTemplateSettingDefault,
    clippingTemplateSetDefaultError,
    fetchClippingTemplates,
    createClippingTemplate,
    updateClippingTemplate,
    deleteClippingTemplate,
    setDefaultClippingTemplate,
    clearClippingTemplateStatus,

    exportInProgress,
    exportError,
    importInProgress,
    importError,
    exportData,
    importData,
    clearDataManagementStatus,
  } = useStore();

  // Fetch initial settings, tags, categories, and templates on component mount
  useEffect(() => {
    fetchCaptureSettings();
    fetchTags();
    fetchCategories();
    fetchClippingTemplates();
  }, [fetchCaptureSettings, fetchTags, fetchCategories, fetchClippingTemplates]);

  const handleCaptureSettingChange = (e) => {
    const { name, value, type, checked } = e.target;
    updateCaptureSetting(name, type === 'checkbox' ? checked : value);
  };

  const handleSaveCaptureSettings = () => {
    saveCaptureSettings();
  };

  const renderCaptureSettings = () => {
    if (captureSettingsLoading) return <p>Loading capture settings...</p>;
    if (captureSettingsError) return <p className="error">Error: {captureSettingsError}</p>;

    return (
      <div className="settings-section">
        <h2>Capture Settings</h2>
        <div className="form-group">
          <label htmlFor="defaultFormat">Default Save Format:</label>
          <select
            id="defaultFormat"
            name="defaultFormat"
            value={captureSettings.defaultFormat}
            onChange={handleCaptureSettingChange}
          >
            <option value="markdown">Markdown</option>
            <option value="html">HTML</option>
            <option value="plaintext">Plain Text</option>
          </select>
        </div>
        <div className="form-group">
          <label htmlFor="extractTitle">
            <input
              type="checkbox"
              id="extractTitle"
              name="extractTitle"
              checked={captureSettings.extractTitle}
              onChange={handleCaptureSettingChange}
            />
            Extract Title
          </label>
        </div>
        <div className="form-group">
          <label htmlFor="extractUrl">
            <input
              type="checkbox"
              id="extractUrl"
              name="extractUrl"
              checked={captureSettings.extractUrl}
              onChange={handleCaptureSettingChange}
            />
            Extract URL
          </label>
        </div>
        <div className="form-group">
          <label htmlFor="extractPublicationDate">
            <input
              type="checkbox"
              id="extractPublicationDate"
              name="extractPublicationDate"
              checked={captureSettings.extractPublicationDate}
              onChange={handleCaptureSettingChange}
            />
            Extract Publication Date
          </label>
        </div>
        <div className="form-group">
          <label htmlFor="enableAiAutoTagging">
            <input
              type="checkbox"
              id="enableAiAutoTagging"
              name="enableAiAutoTagging"
              checked={captureSettings.enableAiAutoTagging}
              onChange={handleCaptureSettingChange}
            />
            Enable AI Auto-Tagging Suggestions
          </label>
        </div>
        <button
          className="settings-button-primary"
          onClick={handleSaveCaptureSettings}
          disabled={captureSettingsSaving}
        >
          {captureSettingsSaving ? 'Saving...' : 'Save Capture Settings'}
        </button>
        {captureSettingsSaveError && <p className="error">Save Error: {captureSettingsSaveError}</p>}
        {!captureSettingsSaving && !captureSettingsSaveError && captureSettingsLoading === false && (
          <p className="success">Settings saved successfully!</p>
        )}
      </div>
    );
  };

  const renderClippingTemplates = () => {
    if (clippingTemplatesLoading) return <p>Loading clipping templates...</p>;
    if (clippingTemplatesError) return <p className="error">Error: {clippingTemplatesError}</p>;

    // Placeholder for template editing/creation modal
    const handleEditTemplate = (template) => {
      alert(`Edit template: ${template.name}`);
      // In a real app, this would open a modal for editing
    };

    const handleDeleteTemplate = async (templateId) => {
      if (window.confirm('Are you sure you want to delete this template?')) {
        try {
          await deleteClippingTemplate(templateId);
          alert('Template deleted successfully!');
        } catch (error) {
          // Error handled by store, but can add local feedback
        }
      }
    };

    const handleCreateNewTemplate = () => {
      alert('Create new template');
      // In a real app, this would open a modal for creating a new template
    };

    const handleSetDefaultTemplate = async (templateId) => {
      try {
        await setDefaultClippingTemplate(templateId);
        alert('Default template set!');
      } catch (error) {
        // Error handled by store
      }
    };

    return (
      <div className="settings-section">
        <h2>Clipping Templates</h2>
        {clippingTemplateCreating && <p>Creating template...</p>}
        {clippingTemplateCreateError && <p className="error">Error creating template: {clippingTemplateCreateError}</p>}
        {clippingTemplateUpdating && <p>Updating template...</p>}
        {clippingTemplateUpdateError && <p className="error">Error updating template: {clippingTemplateUpdateError}</p>}
        {clippingTemplateDeleting && <p>Deleting template...</p>}
        {clippingTemplateDeleteError && <p className="error">Error deleting template: {clippingTemplateDeleteError}</p>}
        {clippingTemplateSettingDefault && <p>Setting default template...</p>}
        {clippingTemplateSetDefaultError && <p className="error">Error setting default template: {clippingTemplateSetDefaultError}</p>}

        <ul className="settings-list">
          {clippingTemplates.map(template => (
            <li key={template.id} className="settings-list-item">
              <span>{template.name} {template.isDefault && '(Default)'}</span>
              <div>
                <button className="settings-button-secondary" onClick={() => handleEditTemplate(template)}>Edit</button>
                <button className="settings-button-danger" onClick={() => handleDeleteTemplate(template.id)}>Delete</button>
                {!template.isDefault && (
                  <button className="settings-button-secondary" onClick={() => handleSetDefaultTemplate(template.id)}>Set Default</button>
                )}
              </div>
            </li>
          ))}
        </ul>
        <button className="settings-button-primary" onClick={handleCreateNewTemplate}>Create New Template</button>
      </div>
    );
  };

  const renderManagementSection = (title, items, itemTypeSingular, searchPlaceholder, createAction, updateAction, deleteAction, loading, error) => {
    const [newItemName, setNewItemName] = useState('');
    const [editItemId, setEditItemId] = useState(null);
    const [editItemName, setEditItemName] = useState('');

    const handleCreateItem = async () => {
      if (newItemName.trim() === '') return;
      try {
        await createAction(newItemName);
        setNewItemName('');
      } catch (e) {
        // Error handled by store
      }
    };

    const handleStartEdit = (item) => {
      setEditItemId(item.id);
      setEditItemName(item.name);
    };

    const handleSaveEdit = async (itemId) => {
      if (editItemName.trim() === '') return;
      try {
        await updateAction(itemId, editItemName);
        setEditItemId(null);
        setEditItemName('');
      } catch (e) {
        // Error handled by store
      }
    };

    const handleDeleteItem = async (itemId) => {
      if (window.confirm(`Are you sure you want to delete this ${itemTypeSingular}?`)) {
        try {
          await deleteAction(itemId);
        } catch (e) {
          // Error handled by store
        }
      }
    };

    return (
      <div className="settings-section">
        <h2>{title}</h2>
        {loading && <p>Loading {itemTypeSingular.toLowerCase()}s...</p>}
        {error && <p className="error">Error: {error}</p>}

        <div className="create-new-item">
          <input
            type="text"
            placeholder={`New ${itemTypeSingular} Name`}
            value={newItemName}
            onChange={(e) => setNewItemName(e.target.value)}
          />
          <button className="settings-button-primary" onClick={handleCreateItem}>
            Create New {itemTypeSingular}
          </button>
        </div>

        <ul className="settings-list">
          {items.map(item => (
            <li key={item.id} className="settings-list-item">
              {editItemId === item.id ? (
                <>
                  <input
                    type="text"
                    value={editItemName}
                    onChange={(e) => setEditItemName(e.target.value)}
                  />
                  <button className="settings-button-secondary" onClick={() => handleSaveEdit(item.id)}>Save</button>
                  <button className="settings-button-secondary" onClick={() => setEditItemId(null)}>Cancel</button>
                </>
              ) : (
                <>
                  <span>{item.name}</span>
                  <div>
                    <button className="settings-button-secondary" onClick={() => handleStartEdit(item)}>Rename</button>
                    <button className="settings-button-danger" onClick={() => handleDeleteItem(item.id)}>Delete</button>
                  </div>
                </>
              )}
            </li>
          ))}
        </ul>
        {/* Merge functionality can be added later if needed */}
        {/* <button className="settings-button-secondary">Merge Selected {itemTypeSingular}s</button> */}
      </div>
    );
  };

  const renderTagManagement = () => renderManagementSection(
    "Tag Management",
    allTags,
    "Tag",
    "Search/Filter Tags...", // Placeholder for search functionality
    createTag,
    updateTag,
    deleteTag,
    tagOperationLoading,
    tagOperationError
  );

  const renderCategoryManagement = () => renderManagementSection(
    "Category Management",
    allCategories,
    "Category",
    "Search/Filter Categories...", // Placeholder for search functionality
    createCategory,
    updateCategory,
    deleteCategory,
    categoryOperationLoading,
    categoryOperationError
  );

  const renderDataManagement = () => {
    const handleExport = async () => {
      try {
        await exportData();
        alert('Data export initiated. Check your downloads or specified location.');
      } catch (error) {
        // Error handled by store
      }
    };

    const handleImport = async () => {
      try {
        await importData();
        alert('Data import initiated. Application may reload.');
      } catch (error) {
        // Error handled by store
      }
    };

    return (
      <div className="settings-section">
        <h2>Data Management</h2>
        {exportInProgress && <p>Exporting data...</p>}
        {exportError && <p className="error">Export Error: {exportError}</p>}
        {importInProgress && <p>Importing data...</p>}
        {importError && <p className="error">Import Error: {importError}</p>}

        <button className="settings-button-primary" onClick={handleExport} disabled={exportInProgress}>
          {exportInProgress ? 'Exporting...' : 'Export Data'}
        </button>
        <button className="settings-button-primary" onClick={handleImport} disabled={importInProgress}>
          {importInProgress ? 'Importing...' : 'Import Data'}
        </button>
        {/* Backup Data button can be added if a separate backup mechanism is implemented */}
      </div>
    );
  };

  const renderAboutHelp = () => (
    <div className="settings-section">
      <h2>About & Help</h2>
      <p>Personalized AI Knowledge Companion & PKM Web Clipper</p>
      <p>Version: 1.0.0 (Placeholder)</p>
      <p>For support, please visit our <a href="#help">Help Center</a> (Placeholder Link).</p>
    </div>
  );

  return (
    <div className="settings-view-container">
      <div className="settings-header">
        <h1>Application Settings</h1>
        <button onClick={onBack} className="settings-back-button">
          &larr; Back to App
        </button>
      </div>

      <div className="settings-navigation">
        <button onClick={() => setActiveTab('capture')} className={activeTab === 'capture' ? 'active' : ''}>Capture</button>
        <button onClick={() => setActiveTab('templates')} className={activeTab === 'templates' ? 'active' : ''}>Templates</button>
        <button onClick={() => setActiveTab('tags')} className={activeTab === 'tags' ? 'active' : ''}>Tags</button>
        <button onClick={() => setActiveTab('categories')} className={activeTab === 'categories' ? 'active' : ''}>Categories</button>
        <button onClick={() => setActiveTab('data')} className={activeTab === 'data' ? 'active' : ''}>Data</button>
        <button onClick={() => setActiveTab('about')} className={activeTab === 'about' ? 'active' : ''}>About</button>
      </div>

      <div className="settings-content">
        {activeTab === 'capture' && renderCaptureSettings()}
        {activeTab === 'templates' && renderClippingTemplates()}
        {activeTab === 'tags' && renderTagManagement()}
        {activeTab === 'categories' && renderCategoryManagement()}
        {activeTab === 'data' && renderDataManagement()}
        {activeTab === 'about' && renderAboutHelp()}
      </div>
    </div>
  );
}

export default SettingsView;