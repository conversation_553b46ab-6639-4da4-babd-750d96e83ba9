/* Global Styles */
body, html {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f4f7f9; /* Light background for the entire app */
  color: #333; /* Default text color */
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: inherit;
}

/* Styles for App */
.app-container {
  display: flex;
  height: 100vh; /* Full viewport height */
  position: relative; /* For positioning the settings button */
}

.main-content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden; /* Prevent content from overflowing the main area */
  padding-top: 40px; /* Add padding to prevent overlap with absolute positioned settings button */
}

/* Styling for the panes */
.navigation-pane {
  flex: 0 0 280px; /* Slightly wider fixed width for navigation */
  background-color: #ffffff; /* White background for clarity */
  border-right: 1px solid #e0e0e0; /* Softer border color */
  padding: 15px;
  overflow-y: auto; /* Allow scrolling if content exceeds height */
  box-shadow: 0 2px 4px rgba(0,0,0,0.05); /* Subtle shadow for depth */
}

.item-list-pane {
  flex: 1 1 400px; /* Base width, can grow and shrink */
  background-color: #f9fafb; /* Slightly off-white background */
  border-right: 1px solid #e0e0e0;
  padding: 15px;
  overflow-y: auto;
}

.detail-view-pane {
  flex: 2 1 600px; /* Takes up more space, base width */
  background-color: #ffffff;
  padding: 20px; /* More padding for content */
  overflow-y: auto;
}

/* Settings Button Styling */
.settings-button {
  position: absolute;
  top: 10px;
  right: 15px;
  z-index: 1000; /* Ensure it's above other content */
  padding: 8px 15px;
  background-color: #007bff; /* Primary button color */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: background-color 0.2s ease-in-out;
}

.settings-button:hover {
  background-color: #0056b3; /* Darker shade on hover */
}

/* General component styling (can be moved to individual component CSS files later) */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  color: #2c3e50; /* Darker color for headings */
}