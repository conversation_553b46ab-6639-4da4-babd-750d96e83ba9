# Diagnosis Report: Knowledge Base UI Test Failures (Task 2.3)

**Date:** 2025-05-19
**Feature:** Adapt Knowledge Base Interaction & Insights Module UI (Task 2.3)

## 1. Overview

This report details the diagnosis of unit test (Jest/RTL) and E2E test (Playwright) failures related to the Knowledge Base UI components:
*   [`apps/chrome-extension/src/ui/options/KnowledgeBaseView.tsx`](apps/chrome-extension/src/ui/options/KnowledgeBaseView.tsx)
*   [`apps/chrome-extension/src/ui/options/ContentList.tsx`](apps/chrome-extension/src/ui/options/ContentList.tsx)
*   [`apps/chrome-extension/src/ui/options/DetailViewPane.tsx`](apps/chrome-extension/src/ui/options/DetailViewPane.tsx)

The primary issues identified are related to ESM module mocking in Jest and service worker initialization in Playwright.

## 2. Unit Test Failures (Jest/RTL ESM Mocking)

**Symptoms:** Mocks for `@pkm-ai/knowledge-base-service` and `suggestionService` do not override actual implementations, or tests fail due to data not being rendered (mock failure).

### 2.1. Mocking `@pkm-ai/knowledge-base-service`

*   **Root Cause Analysis:**
    *   The component [`KnowledgeBaseView.tsx`](apps/chrome-extension/src/ui/options/KnowledgeBaseView.tsx:2) directly imports and instantiates `KnowledgeBaseService` if not provided via props ([`apps/chrome-extension/src/ui/options/KnowledgeBaseView.tsx:25`](apps/chrome-extension/src/ui/options/KnowledgeBaseView.tsx:25)).
    *   The test file [`KnowledgeBaseView.test.tsx`](apps/chrome-extension/src/ui/options/__tests__/KnowledgeBaseView.test.tsx) currently uses prop injection for mocking, which is a valid strategy for this component.
    *   However, the reported problem "Mocks don't override actual implementations" likely refers to attempts to use `jest.mock('@pkm-ai/knowledge-base-service')`.
    *   The primary reason for `jest.mock()` failing for this ESM package is the `moduleNameMapper` entry in [`apps/chrome-extension/jest.config.js`](apps/chrome-extension/jest.config.js:11):
        ```javascript
        '^@pkm-ai/knowledge-base-service$': '<rootDir>/../../packages/knowledge-base-service/dist/index.js',
        ```
        This direct mapping bypasses Jest's module resolution for mocks (i.e., looking in `__mocks__` or using factory mocks). Jest resolves the module to its `dist` path *before* the mocking mechanism can intercept it.

*   **Recommendations:**
    1.  **Modify `jest.config.js`:**
        *   Remove the specific `moduleNameMapper` entry:
            ```diff
            -    '^@pkm-ai/knowledge-base-service$': '<rootDir>/../../packages/knowledge-base-service/dist/index.js',
            ```
        *   For general TypeScript path alias handling (like `@/*`), use `ts-jest`'s `pathsToModuleNameMapper` utility. This is more robust for monorepos and ESM. Add to [`apps/chrome-extension/jest.config.js`](apps/chrome-extension/jest.config.js):
            ```javascript
            // At the top of jest.config.js
            import { pathsToModuleNameMapper } from 'ts-jest';
            // Make sure the path to tsconfig.json is correct from jest.config.js
            import { compilerOptions } from './tsconfig.json';

            // Inside the export default { ... }
            moduleNameMapper: {
              '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
              // Spread the mapped paths here. Ensure the prefix points to your <rootDir>
              // The <rootDir> for apps/chrome-extension/jest.config.js is apps/chrome-extension/
              ...pathsToModuleNameMapper(compilerOptions.paths, { prefix: '<rootDir>/' })
            },
            ```
            The `tsconfig.json` path for `@pkm-ai/knowledge-base-service` ([`apps/chrome-extension/tsconfig.json:19`](apps/chrome-extension/tsconfig.json:19)) points to `../../packages/knowledge-base-service/dist`. `pathsToModuleNameMapper` will convert this. For Jest to pick up a manual mock, the module name itself (`@pkm-ai/knowledge-base-service`) must be mockable before it's resolved to a specific file path by a too-aggressive `moduleNameMapper`.
    2.  **Ensure Manual Mock Usage:**
        *   Keep the manual mock at [`apps/chrome-extension/__mocks__/@pkm-ai/knowledge-base-service.ts`](apps/chrome-extension/__mocks__/@pkm-ai/knowledge-base-service.ts).
        *   In test files where this service needs to be auto-mocked (i.e., not just prop-injected), call `jest.mock('@pkm-ai/knowledge-base-service');` at the top of the test file.

### 2.2. Mocking `suggestionService`

*   **Root Cause Analysis:**
    *   The `suggestionService` ([`apps/chrome-extension/src/organization/suggestionService.ts`](apps/chrome-extension/src/organization/suggestionService.ts)) exports named functions (`getMockTagSuggestions`, `getMockCategorySuggestions`).
    *   The UI components reviewed (`KnowledgeBaseView`, `ContentList`, `DetailViewPane`) do not directly import this service. The issue likely arises in tests for other components or a shared context/setup that *does* import it.
    *   Failures in mocking local ESM modules with named exports can occur due to:
        *   Incorrect relative path in `jest.mock('./path/to/suggestionService')`.
        *   The manual mock (if used) not correctly exporting `jest.fn()` for each named function.
        *   Potential interference from broad `moduleNameMapper` aliases (e.g., if `@/*` was mapped in a way that conflicts, though the standard `pathsToModuleNameMapper` for `@/*` should be fine).

*   **Recommendations:**
    1.  **Correct Mocking Strategy for Named Exports:**
        *   If using a manual mock (e.g., in `apps/chrome-extension/src/organization/__mocks__/suggestionService.ts` or a similar path Jest can find):
            ```typescript
            // Example: apps/chrome-extension/src/organization/__mocks__/suggestionService.ts
            import { jest } from '@jest/globals';
            export const getMockTagSuggestions = jest.fn();
            export const getMockCategorySuggestions = jest.fn();
            ```
        *   In the test file:
            ```typescript
            import { getMockTagSuggestions, getMockCategorySuggestions } from '../../../organization/suggestionService'; // Adjust path as needed
            jest.mock('../../../organization/suggestionService'); // Path relative to test file

            // Inside tests or beforeEach:
            (getMockTagSuggestions as jest.Mock).mockReturnValue(['mocked-tag']);
            // etc.
            ```
    2.  **Verify `jest.mock()` Path:** Double-check the relative path used in `jest.mock()` calls in the relevant test files.
    3.  **Isolation:** Ensure no overly broad `moduleNameMapper` rules are unintentionally catching and misdirecting the `suggestionService` path.

## 3. Playwright E2E Test Failures

**Symptom:** Tests in [`tests/e2e/knowledge_base_ui.spec.ts`](tests/e2e/knowledge_base_ui.spec.ts) fail with `TimeoutError: browserContext.waitForEvent: Timeout 10000ms exceeded while waiting for event "serviceworker"`.

*   **Root Cause Analysis:**
    1.  **Problematic `uuid` Import in Service Worker Mock:**
        *   The background script [`apps/chrome-extension/src/background/index.ts`](apps/chrome-extension/src/background/index.ts:6) imports a mock `KnowledgeBaseService` from [`apps/chrome-extension/src/mocks/KnowledgeBaseService.ts`](apps/chrome-extension/src/mocks/KnowledgeBaseService.ts).
        *   This mock service ([`apps/chrome-extension/src/mocks/KnowledgeBaseService.ts:1`](apps/chrome-extension/src/mocks/KnowledgeBaseService.ts:1)) imports `uuid`: `import { v4 as uuidv4 } from 'uuid';`.
        *   Importing `uuid` (especially if it resolves to a Node-centric version or a version not fully compatible with the strict service worker environment) directly into a script destined for a service worker is highly likely to cause the script to fail during initial evaluation. This prevents the service worker from registering or activating, leading to the `waitForEvent('serviceworker')` timeout.
    2.  **Service Worker Wait in `ExtensionHelper`:**
        *   The [`ExtensionHelper.launchBrowser`](tests/e2e/helpers/extension-helper.ts:24) method in [`tests/e2e/helpers/extension-helper.ts`](tests/e2e/helpers/extension-helper.ts) does not explicitly call `await this.context.waitForEvent('serviceworker')` after launching the persistent context. While the error indicates this event *is* being waited for (possibly implicitly by Playwright or in another part of the test setup), making this wait explicit in the helper improves robustness and clarity.

*   **Recommendations:**
    1.  **Modify `apps/chrome-extension/src/mocks/KnowledgeBaseService.ts` (Highest Priority):**
        *   Remove the dependency on the `uuid` package to ensure browser/service worker compatibility for this mock.
        *   Replace:
            ```diff
            - import { v4 as uuidv4 } from 'uuid';
            ```
        *   And in the `createEntry` method ([`apps/chrome-extension/src/mocks/KnowledgeBaseService.ts:68`](apps/chrome-extension/src/mocks/KnowledgeBaseService.ts:68)):
            ```diff
            - id: uuidv4(),
            + id: self.crypto.randomUUID(),
            ```
            `self.crypto.randomUUID()` is a standard browser API available in service workers for generating UUIDs.
    2.  **Enhance `tests/e2e/helpers/extension-helper.ts`:**
        *   In the `launchBrowser` method, explicitly wait for the service worker after the context is launched:
            ```typescript
            // Inside ExtensionHelper.launchBrowser(), after this.context is assigned:
            try {
              console.log('Waiting for service worker to be ready...');
              const serviceWorker = await this.context.waitForEvent('serviceworker', { timeout: 15000 }); // Optional: slightly longer timeout
              console.log(`Service worker is active: ${serviceWorker.url()}`);
              // Optionally store serviceWorker instance if needed: this.serviceWorker = serviceWorker;
            } catch (swError) {
              console.error('Service worker failed to start or timed out:', swError);
              // Attempt to gather more debug info if possible
              this.context.on('console', msg => console.log(`[SW Console ${msg.type()}]: ${msg.text()}`));
              // Consider re-throwing or handling more gracefully
              throw new Error(`Service worker did not initialize: ${swError.message}`);
            }
            ```
            This makes the helper more robust by ensuring the service worker is confirmed active before any other operations.

## 4. Conclusion

Addressing the `moduleNameMapper` for Jest ESM mocking and removing the `uuid` dependency from the service worker's mock `KnowledgeBaseService` are the primary actions recommended. Enhancing the Playwright helper to explicitly wait for the service worker will also improve test stability. These changes should resolve the described test failures.