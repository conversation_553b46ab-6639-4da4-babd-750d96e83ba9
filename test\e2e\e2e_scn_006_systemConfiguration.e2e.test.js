// test/e2e/e2e_scn_006_systemConfiguration.e2e.test.js

// Mock Management & Configuration Module's services
const mockGetSetting = jest.fn();
const mockSetSetting = jest.fn();
const mockAddClippingTemplate = jest.fn();
const mockGetClippingTemplates = jest.fn(); // To verify template was added
const mockAddTag = jest.fn(); // For managing custom tags
const mockGetTags = jest.fn(); // To verify tag was added

// Simulate a settings store
let settingsStore = {
  defaultCaptureFormat: 'Markdown',
  // other settings...
};
let clippingTemplatesStore = [];
let tagsStore = []; // For custom tags managed by user

jest.mock('../../src/management-configuration/services/settingsService.js', () => ({
  getSetting: (key) => {
    mockGetSetting(key);
    return settingsStore[key];
  },
  setSetting: (key, value) => {
    mockSetSetting(key, value);
    settingsStore[key] = value;
    return Promise.resolve();
  },
}), { virtual: true });

jest.mock('../../src/management-configuration/services/templateService.js', () => ({
  addTemplate: (template) => {
    mockAddClippingTemplate(template);
    const newTemplateWithId = { id: `template-${Date.now()}`, ...template };
    clippingTemplatesStore.push(newTemplateWithId);
    return Promise.resolve(newTemplateWithId);
  },
  getTemplates: () => {
    mockGetClippingTemplates();
    return Promise.resolve(clippingTemplatesStore);
  }
}), { virtual: true });

jest.mock('../../src/management-configuration/services/tagManagementService.js', () => ({
  addCustomTag: (tag) => {
    mockAddTag(tag);
    if (!tagsStore.find(t => t.name === tag.name)) {
      tagsStore.push({ id: `tag-${Date.now()}`, ...tag});
    }
    return Promise.resolve();
  },
  getCustomTags: () => {
    mockGetTags();
    return Promise.resolve(tagsStore);
  }
}), { virtual: true });


// Mocks for a subsequent capture operation to verify settings impact
const mockCaptureWebContent = jest.fn();
const mockGetWebContentMetadata = jest.fn();
jest.mock('../../src/web-content-capture/index.js', () => ({
  captureWebContent: mockCaptureWebContent,
  getWebContentMetadata: mockGetWebContentMetadata,
}), { virtual: true });

const mockSaveCapturedItem = jest.fn();
jest.mock('../../src/knowledge-base-interaction/kbal/services/kbalService.js', () => ({
  saveItem: mockSaveCapturedItem,
}), { virtual: true });

// AI suggestions mock for the capture part
const mockGetAISuggestions = { getAITags: jest.fn(), getAICategories: jest.fn() };
jest.mock('../../src/ai-integration.js', () => mockGetAISuggestions, { virtual: true });


// Helper function to simulate the workflow for E2E_SCN_006
async function simulateConfigurationAndImpactWorkflow({
  newDefaultFormat,
  newClippingTemplate, // { name, contentFormat }
  newCustomTag, // { name, color }
  captureDetailsToVerify, // { captureUrl, captureMode, simulatedWebContent, simulatedMetadata, aiSuggestedTags, aiSuggestedCategories, userActionsWithNewTag }
}) {
  // 1. User navigates to settings (implicit)
  // 2. User modifies capture setting (default save format)
  await require('../../src/management-configuration/services/settingsService.js').setSetting('defaultCaptureFormat', newDefaultFormat);

  // 3. User creates a new custom clipping template
  const addedTemplate = await require('../../src/management-configuration/services/templateService.js').addTemplate(newClippingTemplate);

  // 4. User adds a new custom tag
  await require('../../src/management-configuration/services/tagManagementService.js').addCustomTag(newCustomTag);
  
  // 5. User saves settings (implicit, as setSetting is per-key)

  // --- Simulate a new content capture to verify impact ---
  // 6. User performs a new content capture
  mockCaptureWebContent.mockResolvedValue(captureDetailsToVerify.simulatedWebContent);
  mockGetWebContentMetadata.mockResolvedValue(captureDetailsToVerify.simulatedMetadata);
  
  // Simulate AI suggestions for this capture
  mockGetAISuggestions.getAITags.mockResolvedValue(captureDetailsToVerify.aiSuggestedTags);
  mockGetAISuggestions.getAICategories.mockResolvedValue(captureDetailsToVerify.aiSuggestedCategories);

  const capturedContent = await require('../../src/web-content-capture/index.js').captureWebContent(captureDetailsToVerify.captureMode, captureDetailsToVerify.captureUrl);
  const metadata = await require('../../src/web-content-capture/index.js').getWebContentMetadata(captureDetailsToVerify.captureUrl);
  
  const currentDefaultFormat = require('../../src/management-configuration/services/settingsService.js').getSetting('defaultCaptureFormat');
  
  // Simulate applying the new custom tag during intelligent capture
  let finalTags = [...(captureDetailsToVerify.aiSuggestedTags || [])];
  if (captureDetailsToVerify.userActionsWithNewTag.newTags) {
      finalTags.push(...captureDetailsToVerify.userActionsWithNewTag.newTags);
  }
  finalTags = [...new Set(finalTags)];
  const finalCategory = captureDetailsToVerify.userActionsWithNewTag.chosenCategory || (captureDetailsToVerify.aiSuggestedCategories ? captureDetailsToVerify.aiSuggestedCategories[0] : 'Uncategorized');


  const itemToSave = {
    id: `item-config-${Date.now()}`,
    ...metadata,
    content: capturedContent,
    format: currentDefaultFormat, // Should reflect the new setting
    tags: finalTags, // Should include the new custom tag if applied
    category: finalCategory,
    // clippingTemplateId: appliedTemplateId, // If a template was "used"
  };
  await require('../../src/knowledge-base-interaction/kbal/services/kbalService.js').saveItem(itemToSave);

  return { savedItem: itemToSave, addedTemplateId: addedTemplate.id };
}

describe('E2E_SCN_006: System Configuration Management', () => {
  beforeEach(() => {
    mockGetSetting.mockClear();
    mockSetSetting.mockClear();
    mockAddClippingTemplate.mockClear();
    mockGetClippingTemplates.mockClear();
    mockAddTag.mockClear();
    mockGetTags.mockClear();
    mockCaptureWebContent.mockClear();
    mockGetWebContentMetadata.mockClear();
    mockSaveCapturedItem.mockClear();
    mockGetAISuggestions.getAITags.mockClear();
    mockGetAISuggestions.getAICategories.mockClear();

    // Reset stores
    settingsStore = { defaultCaptureFormat: 'Markdown' };
    clippingTemplatesStore = [];
    tagsStore = [];
  });

  test('should allow user to modify settings, and changes should impact subsequent operations', async () => {
    const testParams = {
      newDefaultFormat: 'HTML',
      newClippingTemplate: { name: 'Brief Note Template', contentFormat: 'TextSnippet', fields: ['title', 'url', 'snippet'] },
      newCustomTag: { name: 'Urgent Review', color: '#FF0000' },
      captureDetailsToVerify: {
        captureUrl: 'http://example.com/config-test',
        captureMode: 'article',
        simulatedWebContent: 'Content to test configuration impact.',
        simulatedMetadata: { url: 'http://example.com/config-test', title: 'Config Test Article', capturedDate: new Date().toISOString() },
        aiSuggestedTags: ['General'],
        aiSuggestedCategories: ['Inbox'],
        userActionsWithNewTag: {
            newTags: ['Urgent Review'], // Applying the newly created custom tag
            chosenCategory: 'Follow Up'
        }
      },
    };

    const { savedItem, addedTemplateId } = await simulateConfigurationAndImpactWorkflow(testParams);

    // Verify settings changes
    expect(mockSetSetting).toHaveBeenCalledWith('defaultCaptureFormat', testParams.newDefaultFormat);
    expect(settingsStore.defaultCaptureFormat).toBe(testParams.newDefaultFormat);

    expect(mockAddClippingTemplate).toHaveBeenCalledWith(testParams.newClippingTemplate);
    const templates = await require('../../src/management-configuration/services/templateService.js').getTemplates();
    expect(templates.some(t => t.id === addedTemplateId && t.name === testParams.newClippingTemplate.name)).toBe(true);
    
    expect(mockAddTag).toHaveBeenCalledWith(testParams.newCustomTag);
    const customTags = await require('../../src/management-configuration/services/tagManagementService.js').getCustomTags();
    expect(customTags.some(t => t.name === testParams.newCustomTag.name)).toBe(true);

    // Verify impact on subsequent capture
    expect(mockSaveCapturedItem).toHaveBeenCalledTimes(1);
    const actualSavedItem = mockSaveCapturedItem.mock.calls[0][0];
    
    // 7. User verifies the content is saved in the new default format (HTML)
    expect(actualSavedItem.format).toBe(testParams.newDefaultFormat); 
    
    // 9. User attempts to apply the new custom tag/category during intelligent capture
    expect(actualSavedItem.tags).toContain(testParams.newCustomTag.name);
    expect(actualSavedItem.category).toBe(testParams.captureDetailsToVerify.userActionsWithNewTag.chosenCategory);

    // Verification for using new clipping template (step 8) is more complex to simulate here
    // as it depends on how templates are applied during capture. We've verified it's added.
  });
});