import { Adapter } from 'lowdb';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
declare const chrome: any; // Declare chrome for global availability

/**
 * A lowdb adapter for persisting data using chrome.storage.local.
 *
 * @template T The type of the data to be stored.
 */
export class ChromeStorageLocalAdapter<T> implements Adapter<T> {
  private readonly storageKey: string;

  /**
   * Creates an instance of ChromeStorageLocalAdapter.
   * @param storageKey The key under which the data will be stored in chrome.storage.local.
   */
  constructor(storageKey: string) {
    if (!storageKey) {
      throw new Error('A storageKey must be provided for ChromeStorageLocalAdapter.');
    }
    this.storageKey = storageKey;
  }

  /**
   * Reads data from chrome.storage.local.
   * @returns A promise that resolves with the stored data or null if no data is found.
   */
  async read(): Promise<T | null> {
    if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.local) {
      console.warn('Chrome storage API is not available. Returning null.');
      return null;
    }
    try {
      const result = await chrome.storage.local.get(this.storageKey);
      if (result && Object.prototype.hasOwnProperty.call(result, this.storageKey)) {
        return result[this.storageKey] as T;
      }
      return null;
    } catch (error) {
      console.error('Error reading from chrome.storage.local:', error);
      // In case of an error (e.g., QUOTA_BYTES_PER_ITEM exceeded during a previous write, or other storage errors),
      // it's safer to return null to allow lowdb to potentially initialize with default data.
      return null;
    }
  }

  /**
   * Writes data to chrome.storage.local.
   * @param data The data to be written.
   * @returns A promise that resolves when the data has been written.
   */
  async write(data: T): Promise<void> {
    if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.local) {
      console.warn('Chrome storage API is not available. Write operation skipped.');
      return;
    }
    try {
      await chrome.storage.local.set({ [this.storageKey]: data });
    } catch (error) {
      console.error('Error writing to chrome.storage.local:', error);
      // Rethrow the error so that the caller (lowdb) is aware of the failure.
      // lowdb might have its own error handling or retry mechanisms.
      throw error;
    }
  }
}