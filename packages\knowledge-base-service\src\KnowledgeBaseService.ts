import { Low, Adapter } from 'lowdb';
// import { JSONFile } from 'lowdb/node'; // Will be dynamically imported
import { ChromeStorageLocalAdapter } from './adapters/ChromeStorageLocalAdapter.js';
import { KnowledgeBaseEntry, KnowledgeBaseData } from './types.js';
import { v4 as uuidv4 } from 'uuid';
import { Mutex } from 'async-mutex';

// Node.js specific imports will be dynamically imported if needed.
// import path from 'node:path';
// import fs from 'node:fs';
// import { fileURLToPath } from 'node:url';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
declare const chrome: any; // For checking chrome environment

const IS_CHROME_EXTENSION = typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local;

export class KnowledgeBaseService {
  private db: Low<KnowledgeBaseData>;
  private adapter: Adapter<KnowledgeBaseData>;
  private dbWriteMutex = new Mutex();
  private initialized = false;
  private initializationPromise: Promise<void> | null = null;
  private storageIdentifier: string; // File path for Node, storage key for Chrome

  private static readonly DEFAULT_DATA: KnowledgeBaseData = { entries: [] };
  private static readonly DEFAULT_CHROME_STORAGE_KEY = 'knowledgeBaseV1';
  private static readonly DEFAULT_NODE_DB_FILENAME = 'db.json';

  constructor(storageIdentifier?: string) {
    // Adapter and db will be set asynchronously in _initializeService for Node.js
    // For Chrome, it can be set synchronously here.
    if (IS_CHROME_EXTENSION) {
      this.storageIdentifier = storageIdentifier || KnowledgeBaseService.DEFAULT_CHROME_STORAGE_KEY;
      this.adapter = new ChromeStorageLocalAdapter<KnowledgeBaseData>(this.storageIdentifier);
      this.db = new Low<KnowledgeBaseData>(this.adapter, { ...KnowledgeBaseService.DEFAULT_DATA });
    } else {
      // For Node.js, storageIdentifier is set, but adapter/db are deferred to async init
      // This is a placeholder, will be overwritten by async init.
      // The actual storageIdentifier for Node will be determined in _initializeNodeAdapter.
      this.storageIdentifier = storageIdentifier || KnowledgeBaseService.DEFAULT_NODE_DB_FILENAME;
      // @ts-ignore - adapter and db will be set by _initializeService
      this.adapter = null;
      // @ts-ignore
      this.db = null;
    }
    this.initializationPromise = this._initializeService();
  }

  private async _initializeNodeAdapter(storageIdentifier?: string): Promise<void> {
    const path = (await import('node:path')).default;
    const fs = (await import('node:fs')).default;
    const { fileURLToPath } = await import('node:url');
    const { JSONFile: NodeJSONFileAdapter } = await import('lowdb/node');

    const currentFileUrl = import.meta.url;
    const currentFilePath = fileURLToPath(currentFileUrl);
    const currentDir = path.dirname(currentFilePath);

    const dataDirPath = path.join(currentDir, '..', 'data');
    if (!fs.existsSync(dataDirPath)) {
      fs.mkdirSync(dataDirPath, { recursive: true });
    }
    this.storageIdentifier = storageIdentifier || path.join(dataDirPath, KnowledgeBaseService.DEFAULT_NODE_DB_FILENAME);
    this.adapter = new NodeJSONFileAdapter<KnowledgeBaseData>(this.storageIdentifier);
    this.db = new Low<KnowledgeBaseData>(this.adapter, { ...KnowledgeBaseService.DEFAULT_DATA });
  }

  private async _initializeService(): Promise<void> {
    if (this.initializationPromise && !this.initialized) {
      // Another call is already in progress, wait for it
      await this.initializationPromise;
      return;
    }
    if (this.initialized) return;

    // Create the promise that others can await
    const promise = this.dbWriteMutex.runExclusive(async () => {
      if (this.initialized) return; // Double check inside mutex

      if (!IS_CHROME_EXTENSION && (!this.adapter || !this.db)) {
        // Pass the original constructor storageIdentifier if provided
        await this._initializeNodeAdapter(this.storageIdentifier === KnowledgeBaseService.DEFAULT_NODE_DB_FILENAME ? undefined : this.storageIdentifier);
      }
      await this.initializeDatabaseInternal();
      this.initialized = true;
    });
    this.initializationPromise = promise;
    return promise;
  }
  
  private async initializeDatabaseInternal(): Promise<void> {
    try {
      await this.db.read();
      if (this.db.data === null || typeof this.db.data !== 'object' || !Array.isArray(this.db.data.entries)) {
        this.db.data = { ...KnowledgeBaseService.DEFAULT_DATA };
        await this.db.write();
      }
    } catch (error) {
      console.error(`Error reading database from ${this.storageIdentifier} during initializeDatabaseInternal, attempting to repair:`, error);
      if (!IS_CHROME_EXTENSION) {
        // Check if adapter is an instance of the dynamically imported JSONFile
        // This requires careful type handling or checking constructor name if possible,
        // For now, we assume if !IS_CHROME_EXTENSION, it's the Node adapter.
        try {
          const fs = (await import('node:fs')).default; // Dynamic import for fs
          fs.writeFileSync(this.storageIdentifier, JSON.stringify(KnowledgeBaseService.DEFAULT_DATA, null, 2));
          this.db.data = { ...KnowledgeBaseService.DEFAULT_DATA }; // Ensure in-memory is also set
        } catch (writeError) {
          console.error(`Failed to repair ${this.storageIdentifier} by direct write:`, writeError);
          this.db.data = { ...KnowledgeBaseService.DEFAULT_DATA };
        }
      } else {
        this.db.data = { ...KnowledgeBaseService.DEFAULT_DATA };
        if (IS_CHROME_EXTENSION) {
            try {
                await this.db.write();
            } catch (chromeWriteError) {
                console.error(`Failed to write default data to Chrome storage during repair:`, chromeWriteError);
            }
        }
      }
    }
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.initializationPromise) {
        // This case should ideally not be hit if constructor always calls _initializeService
        console.warn("ensureInitialized called before initializationPromise was set. Initializing now.");
        this.initializationPromise = this._initializeService();
    }
    await this.initializationPromise;
  }

  private convertEntryDates(entry: KnowledgeBaseEntry): KnowledgeBaseEntry { // More specific type
    return {
      ...entry,
      createdAt: new Date(entry.createdAt),
      updatedAt: new Date(entry.updatedAt),
    };
  }

  async createEntry(data: Omit<KnowledgeBaseEntry, 'id' | 'createdAt' | 'updatedAt'>): Promise<KnowledgeBaseEntry> {
    await this.ensureInitialized();
    return this.dbWriteMutex.runExclusive(async () => {
      // await this.db.read(); // Removed: Data should be in memory
      const now = new Date();
      const newEntry: KnowledgeBaseEntry = {
        id: uuidv4(),
        ...data,
        createdAt: now,
        updatedAt: now,
      };
      this.db.data.entries.push(newEntry);
      await this.db.write();
      return this.convertEntryDates(newEntry);
    });
  }

  async getEntryById(id: string): Promise<KnowledgeBaseEntry | undefined> {
    await this.ensureInitialized();
    // await this.db.read(); // Removed: Data should be in memory
    const entry = this.db.data.entries.find(e => e.id === id);
    return entry ? this.convertEntryDates(entry) : undefined;
  }

  async updateEntry(id: string, data: Partial<Omit<KnowledgeBaseEntry, 'id' | 'createdAt' | 'updatedAt'>>): Promise<KnowledgeBaseEntry | undefined> {
    await this.ensureInitialized();
    return this.dbWriteMutex.runExclusive(async () => {
      // await this.db.read(); // Removed: Data should be in memory
      const entryIndex = this.db.data.entries.findIndex(e => e.id === id);
      if (entryIndex === -1) {
        return undefined;
      }
      const updatedEntry = {
        ...this.db.data.entries[entryIndex],
        ...data,
        updatedAt: new Date(),
      };
      this.db.data.entries[entryIndex] = updatedEntry;
      await this.db.write();
      return this.convertEntryDates(updatedEntry);
    });
  }

  async deleteEntry(id: string): Promise<boolean> {
    await this.ensureInitialized();
    return this.dbWriteMutex.runExclusive(async () => {
      // await this.db.read(); // Removed: Data should be in memory
      const initialLength = this.db.data.entries.length;
      this.db.data.entries = this.db.data.entries.filter(e => e.id !== id);
      if (this.db.data.entries.length < initialLength) {
        await this.db.write();
        return true;
      }
      return false;
    });
  }

  async getAllEntries(): Promise<KnowledgeBaseEntry[]> {
    await this.ensureInitialized();
    // await this.db.read(); // Removed: Data should be in memory
    return this.db.data.entries.map(this.convertEntryDates);
  }

  async clearDatabase(): Promise<void> {
    await this.ensureInitialized(); // Ensure service is initialized
    await this.dbWriteMutex.runExclusive(async () => {
      console.log(`[KnowledgeBaseService] clearDatabase: Attempting to clear ${this.storageIdentifier}. Current items (before local reset): ${this.db.data?.entries?.length ?? 'N/A'}`);
      this.db.data = { ...KnowledgeBaseService.DEFAULT_DATA };
      console.log(`[KnowledgeBaseService] clearDatabase: In-memory data reset. Attempting write to ${this.storageIdentifier}.`);
      try {
        await this.db.write();
        console.log(`[KnowledgeBaseService] clearDatabase: Successfully cleared and wrote default data to ${this.storageIdentifier}. Items now: ${this.db.data.entries.length}`);
        // Database is now in a valid, initialized (default) state.
        this.initialized = true;
        // Ensure future calls to ensureInitialized resolve immediately for this known good state.
        // Replace current initializationPromise with one that's already resolved.
        this.initializationPromise = Promise.resolve();
      } catch (error) {
        console.error(`[KnowledgeBaseService] clearDatabase: Failed to write default data to ${this.storageIdentifier} during clearDatabase:`, error);
        // If write fails, the persistent state is unknown/bad.
        // Mark as uninitialized to force full re-read/repair on the next operation.
        this.initialized = false;
        this.initializationPromise = null; // Allow _initializeService to create a new promise
        throw error; // Propagate the error, so the caller knows clear failed.
      }
    });
  }
}