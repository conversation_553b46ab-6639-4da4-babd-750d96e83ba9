import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import MetadataDisplay from '../../renderer/components/detail-view-pane/MetadataDisplay';

describe('MetadataDisplay', () => {
  const fullMetadata = {
    sourceURL: 'https://example.com/article',
    captureDate: '2023-03-15T10:30:00.000Z',
    tags: ['react', 'testing', 'javascript'],
    categories: ['Technology', 'Web Development'],
  };

  const partialMetadata = {
    sourceURL: 'https://anotherexample.com',
    tags: ['ui'],
  };

  const emptyMetadata = {};

  test('renders "No metadata available." when metadata prop is null or undefined', () => {
    const { rerender } = render(<MetadataDisplay metadata={null} />);
    expect(screen.getByText('No metadata available.')).toBeInTheDocument();

    rerender(<MetadataDisplay />); // metadata is undefined, defaults to null
    expect(screen.getByText('No metadata available.')).toBeInTheDocument();
  });

  test('renders all metadata fields when provided', () => {
    render(<MetadataDisplay metadata={fullMetadata} />);

    // Check Source URL
    const sourceLink = screen.getByText(fullMetadata.sourceURL);
    expect(sourceLink).toBeInTheDocument();
    expect(sourceLink).toHaveAttribute('href', fullMetadata.sourceURL);
    expect(screen.getByText('Source:')).toBeInTheDocument();

    // Check Capture Date
    expect(screen.getByText('Captured:')).toBeInTheDocument();
    expect(screen.getByText(new Date(fullMetadata.captureDate).toLocaleString())).toBeInTheDocument();

    // Check Tags
    expect(screen.getByText('Tags:')).toBeInTheDocument();
    expect(screen.getByText(fullMetadata.tags.join(', '))).toBeInTheDocument();

    // Check Categories
    expect(screen.getByText('Categories:')).toBeInTheDocument();
    expect(screen.getByText(fullMetadata.categories.join(', '))).toBeInTheDocument();
  });

  test('renders only provided metadata fields for partial data', () => {
    render(<MetadataDisplay metadata={partialMetadata} />);

    // Check Source URL (present)
    const sourceLink = screen.getByText(partialMetadata.sourceURL);
    expect(sourceLink).toBeInTheDocument();
    expect(sourceLink).toHaveAttribute('href', partialMetadata.sourceURL);

    // Check Tags (present)
    expect(screen.getByText('Tags:')).toBeInTheDocument();
    expect(screen.getByText(partialMetadata.tags.join(', '))).toBeInTheDocument();

    // Check Capture Date (absent)
    expect(screen.queryByText('Captured:')).not.toBeInTheDocument();

    // Check Categories (absent)
    expect(screen.queryByText('Categories:')).not.toBeInTheDocument();
  });

  test('renders "No metadata details provided." when metadata object is empty', () => {
    render(<MetadataDisplay metadata={emptyMetadata} />);
    expect(screen.getByText('No metadata details provided.')).toBeInTheDocument();
    expect(screen.queryByText('Source:')).not.toBeInTheDocument();
    expect(screen.queryByText('Captured:')).not.toBeInTheDocument();
    expect(screen.queryByText('Tags:')).not.toBeInTheDocument();
    expect(screen.queryByText('Categories:')).not.toBeInTheDocument();
  });

  test('handles metadata with empty tags or categories arrays gracefully', () => {
    const metadataWithEmptyArrays = {
      sourceURL: 'https://example.com',
      captureDate: '2023-01-01T00:00:00.000Z',
      tags: [],
      categories: [],
    };
    render(<MetadataDisplay metadata={metadataWithEmptyArrays} />);

    expect(screen.getByText(metadataWithEmptyArrays.sourceURL)).toBeInTheDocument();
    expect(screen.getByText(new Date(metadataWithEmptyArrays.captureDate).toLocaleString())).toBeInTheDocument();
    expect(screen.queryByText('Tags:')).not.toBeInTheDocument();
    expect(screen.queryByText('Categories:')).not.toBeInTheDocument();
  });

   test('sourceURL is a clickable link opening in a new tab', () => {
    render(<MetadataDisplay metadata={fullMetadata} />);
    const link = screen.getByText(fullMetadata.sourceURL);
    expect(link.tagName).toBe('A');
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveAttribute('rel', 'noopener noreferrer');
  });

  test('matches snapshot with sanitized content and validated URL', () => {
    const { container } = render(<MetadataDisplay metadata={fullMetadata} />);
    expect(container.firstChild).toMatchSnapshot();
  });

  describe('XSS Sanitization and URL Validation', () => {
    const maliciousScript = "<script>alert('XSS')</script>";
    const maliciousImg = "<img src=x onerror=alert('XSS') />";
    const benignText = "Benign Text";

    beforeEach(() => {
      global.alert = jest.fn();
    });

    describe('URL Validation for sourceURL', () => {
      test('allows http, https, mailto, ftp protocols', () => {
        const validURLs = [
          'http://example.com',
          'https://secure.example.com/path?query=value',
          'mailto:<EMAIL>',
          'ftp://user:<EMAIL>/resource.txt',
        ];
        validURLs.forEach(url => {
          render(<MetadataDisplay metadata={{ sourceURL: url }} />);
          const link = screen.getByText(url);
          expect(link).toHaveAttribute('href', url);
          // screen.unmount() removed; rely on automatic cleanup
        });
      });

      test('disallows javascript URLs, falling back to #', () => {
        const jsURL = "javascript:alert('XSS')";
        render(<MetadataDisplay metadata={{ sourceURL: jsURL }} />);
        const link = screen.getByText(jsURL); // Text content should still be the original URL
        expect(link).toHaveAttribute('href', '#');
        expect(global.alert).not.toHaveBeenCalled();
      });

      test('disallows other potentially unsafe protocols, falling back to #', () => {
        const otherURLs = ['data:text/html,<script>alert(1)</script>', 'blob:http://example.com/guid'];
        otherURLs.forEach(url => {
          render(<MetadataDisplay metadata={{ sourceURL: url }} />);
          const link = screen.getByText(url);
          expect(link).toHaveAttribute('href', '#');
          // screen.unmount() removed
        });
        expect(global.alert).not.toHaveBeenCalled();
      });

      test('handles malformed URLs, falling back to #', () => {
        const malformedURL = "htps://malformed";
        render(<MetadataDisplay metadata={{ sourceURL: malformedURL }} />);
        const link = screen.getByText(malformedURL);
        expect(link).toHaveAttribute('href', '#');
      });

      test('handles empty or null sourceURL gracefully (no link rendered)', () => {
        render(<MetadataDisplay metadata={{ sourceURL: '' }} />);
        expect(screen.queryByRole('link')).not.toBeInTheDocument();
        // screen.unmount() removed
        render(<MetadataDisplay metadata={{ sourceURL: null }} />); // Re-render for the null case
        expect(screen.queryByRole('link')).not.toBeInTheDocument();
      });

      test('link text for sourceURL is rendered as is (React escapes it)', () => {
        const urlWithHtmlChars = 'https://example.com/path?param=<value>';
        render(<MetadataDisplay metadata={{ sourceURL: urlWithHtmlChars }} />);
        // Text content is escaped by React by default
        expect(screen.getByText(urlWithHtmlChars)).toBeInTheDocument();
        // Check that the href is correct
        expect(screen.getByRole('link')).toHaveAttribute('href', urlWithHtmlChars);
      });
    });

    describe('Sanitization for Tags and Categories', () => {
      const xssMetadata = {
        tags: [`safeTag1${maliciousScript}`, `${maliciousImg}safeTag2`],
        categories: [`safeCat1${maliciousImg}`, `safeCat2${maliciousScript}`],
        sourceURL: 'http://safe.com' // to ensure component renders these sections
      };

      test('sanitizes tags before rendering', () => {
        render(<MetadataDisplay metadata={xssMetadata} />);
        // DOMPurify(`safeTag1${maliciousScript}`) -> "safeTag1"
        // DOMPurify(`${maliciousImg}safeTag2`) -> "<img src="x">safeTag2"
        // Joined: "safeTag1, <img src="x">safeTag2"
        const tagsElementContainer = screen.getByText((content, element) => {
            return element.tagName.toLowerCase() === 'strong' && content === 'Tags:';
        }).parentElement; // Get the div container for tags
        
        expect(tagsElementContainer).toHaveTextContent('Tags: safeTag1, <img src="x">safeTag2'); // textContent will decode <
        expect(tagsElementContainer.innerHTML).toContain('safeTag1, &lt;img src="x"&gt;safeTag2'); // innerHTML will show encoded
        expect(tagsElementContainer.innerHTML).not.toContain('<script');
        expect(tagsElementContainer.innerHTML).not.toContain('onerror');
        expect(global.alert).not.toHaveBeenCalled();
      });

      test('sanitizes categories before rendering', () => {
        render(<MetadataDisplay metadata={xssMetadata} />);
        // DOMPurify(`safeCat1${maliciousImg}`) -> "safeCat1<img src="x">"
        // DOMPurify(`safeCat2${maliciousScript}`) -> "safeCat2"
        // Joined: "safeCat1<img src="x">, safeCat2"
         const categoriesElementContainer = screen.getByText((content, element) => {
            return element.tagName.toLowerCase() === 'strong' && content === 'Categories:';
        }).parentElement;

        expect(categoriesElementContainer).toHaveTextContent('Categories: safeCat1<img src="x">, safeCat2');
        expect(categoriesElementContainer.innerHTML).toContain('safeCat1&lt;img src="x"&gt;, safeCat2');
        expect(categoriesElementContainer.innerHTML).not.toContain('<script');
        expect(categoriesElementContainer.innerHTML).not.toContain('onerror');
        expect(global.alert).not.toHaveBeenCalled();
      });

      test('handles null/undefined in tags/categories arrays during sanitization', () => {
        const metadataWithNulls = {
          tags: [null, 'safeTag', undefined, maliciousScript],
          categories: [undefined, maliciousImg, 'safeCategory', null],
          sourceURL: 'http://example.com'
        };
        render(<MetadataDisplay metadata={metadataWithNulls} />);
        
        const tagsContainer = screen.getByText('Tags:').parentElement;
        // For tags: [null, 'safeTag', undefined, maliciousScript]
        // Sanitized array: ["", "safeTag", "", ""]
        // Joined string: ", safeTag, , "
        // Expected textContent of the container: "Tags: , safeTag, , " (note spaces)
        expect(tagsContainer.textContent).toBe('Tags: , safeTag, , ');

        const categoriesContainer = screen.getByText('Categories:').parentElement;
        // For categories: [undefined, maliciousImg, 'safeCategory', null]
        // Sanitized array: ["", "<img src="x">", "safeCategory", ""]
        // Joined string: ", <img src="x">, safeCategory, "
        // Expected textContent: "Categories: , <img src="x">, safeCategory, "
        expect(categoriesContainer.textContent).toBe('Categories: , <img src="x">, safeCategory, ');
        expect(categoriesContainer.innerHTML).not.toContain('onerror');
        expect(categoriesContainer.innerHTML).not.toContain('<script');
        expect(global.alert).not.toHaveBeenCalled();
      });
    });
  });
});