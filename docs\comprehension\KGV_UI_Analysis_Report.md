# Code Comprehension Report: Knowledge Graph Visualization (KGV) UI

**Date:** 2025-05-15
**Analyzer:** <PERSON><PERSON> (AI Code Comprehension Assistant)
**Scope:** Analysis of KGV UI components and tests in relation to [`docs/debugging/Knowledge_Graph_Visualization_UI_Test_Failures_Diagnosis.md`](docs/debugging/Knowledge_Graph_Visualization_UI_Test_Failures_Diagnosis.md).

## 1. Overview

This report provides a detailed analysis of the Knowledge Graph Visualization (KGV) UI feature's codebase, specifically focusing on the components and tests highlighted in the user request and the associated diagnosis report ([`docs/debugging/Knowledge_Graph_Visualization_UI_Test_Failures_Diagnosis.md`](docs/debugging/Knowledge_Graph_Visualization_UI_Test_Failures_Diagnosis.md)). The primary goal is to understand the functionality, structure, and identify areas related to diagnosed test failures, thereby informing subsequent test refactoring and fixing efforts. The analysis involved static code review of the provided JavaScript files (React components and Jest tests) and cross-referencing with the issues outlined in the diagnosis document, such as problems with Cytoscape.js mocking, event simulation, UI element selectors, and component prop handling.

## 2. Component Analysis

### 2.1. [`GraphRenderingArea.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js)

*   **Purpose & Functionality:** This component is responsible for rendering the actual knowledge graph using the Cytoscape.js library. It takes graph data (nodes and edges), layout instructions, and visual encoding definitions as props. It handles the initialization of the Cytoscape instance, applies styles to nodes and edges based on encodings, manages graph layouts, and captures user interactions within the graph canvas, such as node/edge selections and zoom/pan events, forwarding these interactions to parent components via callbacks.
*   **Key Code Sections (re: Diagnosis):**
    *   **Cytoscape Initialization:** The Cytoscape instance is initialized within a `useEffect` hook, specifically when `cyInstanceRef.current` is null ([`GraphRenderingArea.js:83-100`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js:83-100)). This is where the container div, initial elements, style, and layout are provided.
    *   **Event Handling:** Event listeners for Cytoscape events (e.g., `tap` on nodes/edges, `zoom`, `pan`) are attached using `cy.on()` ([`GraphRenderingArea.js:102-115`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js:102-115)). These handlers then call the appropriate callback props (`onNodeSelect`, `onEdgeSelect`, `onCanvasInteraction`).
    *   **Layout Application:** The initial layout is set during Cytoscape initialization ([`GraphRenderingArea.js:89`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js:89)). Subsequent layout changes triggered by the `layout` prop are handled in a separate `useEffect` hook that calls `cyInstanceRef.current.layout({ name: layout }).run()` ([`GraphRenderingArea.js:140-146`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js:140-146)).
    *   **Style Mapping & Application:** Visual styles for nodes and edges are generated by the `mapEncodingsToStyle` helper function ([`GraphRenderingArea.js:7-59`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js:7-59)) based on the `visualEncodings` prop. These styles are applied during initialization ([`GraphRenderingArea.js:88`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js:88)) and when the instance is updated ([`GraphRenderingArea.js:120`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js:120)).
*   **Testing Approach ([`GraphRenderingArea.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js)):**
    *   The test suite attempts to mock the Cytoscape instance via a `graphInstanceRef` prop ([`GraphRenderingArea.test.js:43-56`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:43-56)), which is a superficial mock not representative of the actual Cytoscape API.
    *   Canvas interactions like zoom and pan are simulated using `fireEvent` (e.g., `fireEvent.wheel`, `fireEvent.mouseDown/mouseMove/mouseUp`) directly on the component's container div ([`GraphRenderingArea.test.js:119-121`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:119-121), [`GraphRenderingArea.test.js:128-132`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:128-132)).
    *   Node selection is tested by trying to find the mocked `on` handler and invoking it manually ([`GraphRenderingArea.test.js:143-155`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:143-155)).
*   **Clarity & Issues (re: Diagnosis):**
    *   The component's internal logic for using Cytoscape.js is reasonably clear.
    *   The main problem, as highlighted by the diagnosis report, lies in the test suite's inadequate mocking of Cytoscape.js. The tests do not interact with a realistic mock of the Cytoscape API, and attempting to simulate canvas events via `fireEvent` on a div is not an effective way to test Cytoscape's behavior. This directly aligns with the diagnosis report's call for a proper `jest.mock('cytoscape')` and testing via the mocked instance's API.

### 2.2. [`ControlPanel.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js)

*   **Purpose & Functionality:** This component provides users with UI controls to manipulate the knowledge graph's presentation and filtering. It allows changing layout algorithms, defining and applying attribute-based filters, and toggling the visibility of different node and edge types. It receives various options and current states as props and uses callback functions to inform the parent container of user-initiated changes.
*   **Key Code Sections (re: Diagnosis):**
    *   **"Apply Defined Filters" Button:** The button for applying filters is rendered with the text "Apply Defined Filters" and an `aria-label="Apply Defined Filters Button"` ([`ControlPanel.js:59`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js:59)).
    *   **"Define Attribute Filters" Heading:** The section for attribute filters is headed by "Define Attribute Filters" ([`ControlPanel.js:46`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js:46)).
    *   **Layout Selection:** A `select` dropdown allows users to choose a layout ([`ControlPanel.js:34-41`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js:34-41)).
    *   **Filter Input:** Input fields are provided for users to enter filter criteria for attributes ([`ControlPanel.js:47-57`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js:47-57)).
*   **Testing Approach ([`ControlPanel.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js)):**
    *   The tests use React Testing Library's query functions like `screen.getByText`, `screen.getByLabelText`, and `screen.getByRole` to find UI elements.
    *   User interactions are simulated using `fireEvent` (e.g., `fireEvent.change`, `fireEvent.click`).
*   **Clarity & Issues (re: Diagnosis):**
    *   The component itself is straightforward and its functionality is clear.
    *   The test failures, as identified in the diagnosis report, stem from mismatches between the selectors used in the tests and the actual text or ARIA labels in the component:
        *   `TC_KGV_CP_003` (Apply Filters Button): The test uses `screen.getByRole('button', { name: /Apply Filters/i })` ([`ControlPanel.test.js:49`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js:49)), while the component renders a button with the text "Apply Defined Filters" and `aria-label="Apply Defined Filters Button"` ([`ControlPanel.js:59`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js:59)).
        *   `TC_KGV_CP_001` (Filters Heading): The test expects `screen.getByText(/Filters/i)` ([`ControlPanel.test.js:29`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js:29)), but the component renders the heading "Define Attribute Filters" ([`ControlPanel.js:46`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js:46)).

### 2.3. [`KnowledgeGraphVisualizationContainer.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/KnowledgeGraphVisualizationContainer.js)

*   **Purpose & Functionality:** This is the main orchestrating component for the KGV feature. It manages the overall state, including the raw graph data, the data to be displayed (after filtering), the current layout choice, selected nodes/edges, search terms, active filters, and visibility states for node/edge types. It passes data and callback functions to its child components (`GraphRenderingArea`, `ControlPanel`, `InformationDisplayPanel`, `SearchFilterBar`, `Legend`) and handles the logic for updating the graph display based on user interactions in these child components.
*   **Key Code Sections (re: Diagnosis):**
    *   **Default Layout State:** The `layout` state is initialized to `'cose'` using `useState('cose')` ([`KnowledgeGraphVisualizationContainer.js:12`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/KnowledgeGraphVisualizationContainer.js:12)).
    *   **Passing `layout` Prop:** The `layout` state is passed as a prop to the `GraphRenderingArea` component ([`KnowledgeGraphVisualizationContainer.js:205`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/KnowledgeGraphVisualizationContainer.js:205)).
    *   **Layout Change Handler:** The `handleLayoutChange` callback ([`KnowledgeGraphVisualizationContainer.js:99-102`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/KnowledgeGraphVisualizationContainer.js:99-102)) updates the `layout` state when the `ControlPanel` signals a change.
*   **Testing Approach ([`KnowledgeGraphVisualizationContainer.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js)):**
    *   The test suite mocks all child components ([`KnowledgeGraphVisualizationContainer.test.js:6-45`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js:6-45)) to isolate the container's logic.
    *   It tests interactions by verifying that the correct props are passed to these mocked children and by simulating callbacks from the mocks to check the container's response.
*   **Clarity & Issues (re: Diagnosis):**
    *   The container's logic for state management and prop delegation is generally clear.
    *   The test failure `TC_KGV_CNT_002` is due to an outdated expectation regarding the initial layout. The test asserts that the mocked `GraphRenderingArea` initially displays "Layout: force-directed" ([`KnowledgeGraphVisualizationContainer.test.js:86`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js:86)). However, the container component now defaults to and passes `layout='cose'` ([`KnowledgeGraphVisualizationContainer.js:12`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/KnowledgeGraphVisualizationContainer.js:12)). The mock for `GraphRenderingArea` ([`KnowledgeGraphVisualizationContainer.test.js:14-21`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js:14-21)) correctly renders the `layout` prop it receives, so the mismatch is in the test's expectation.

## 3. Test Setup Analysis

### 3.1. [`jest.setup.js`](src/main-application-ui/jest.setup.js)

*   **Purpose & Functionality:** This file configures the global Jest testing environment for the main application UI. It includes polyfills, mocks, and other setup required for tests to run correctly.
*   **Key Code Sections (re: Diagnosis):**
    *   **`jest-canvas-mock`:** The line `import 'jest-canvas-mock';` ([`jest.setup.js:1`](src/main-application-ui/jest.setup.js)) is present. This library is essential for enabling components that use the HTML5 Canvas API (like Cytoscape.js) to be tested within the JSDOM environment, which doesn't natively support canvas. The diagnosis report specifically mentions ensuring this is properly configured.
*   **Clarity & Issues (re: Diagnosis):**
    *   The inclusion of `jest-canvas-mock` addresses one of the prerequisites for testing Cytoscape.js effectively. The diagnosis report's recommendation to "Ensure `jest-canvas-mock` is properly configured" appears to be met by its presence. However, `jest-canvas-mock` alone is not sufficient; it needs to be paired with a robust mock of the Cytoscape.js library itself within the specific test files (like `GraphRenderingArea.test.js`), which is currently lacking.

## 4. Summary of Issues in Relation to Diagnosis Report

The analysis of the codebase confirms the key issues identified in the [`docs/debugging/Knowledge_Graph_Visualization_UI_Test_Failures_Diagnosis.md`](docs/debugging/Knowledge_Graph_Visualization_UI_Test_Failures_Diagnosis.md):

*   **Cytoscape.js Mocking (`GraphRenderingArea.test.js`):** This is the most critical issue. The existing `graphInstanceRef` mock ([`GraphRenderingArea.test.js:43-56`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:43-56)) is a superficial placeholder. It does not accurately simulate the Cytoscape.js API, preventing meaningful testing of interactions. The diagnosis correctly recommends using `jest.mock('cytoscape')` to create a comprehensive mock.
*   **Event Handling (`GraphRenderingArea.test.js`):** Tests attempt to simulate Cytoscape interactions (zoom, pan, clicks) using `fireEvent` on the component's container div (e.g., [`GraphRenderingArea.test.js:119-121`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:119-121), [`GraphRenderingArea.test.js:128-132`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:128-132)). This approach is ineffective for canvas-based libraries like Cytoscape. The diagnosis correctly points out that these events should be triggered via the mocked Cytoscape instance's API. The component itself correctly uses `cy.on(...)` ([`GraphRenderingArea.js:102-115`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js:102-115)) for event listening.
*   **Selectors (`ControlPanel.test.js`):** Mismatches between test selectors and the actual component's rendered text and ARIA attributes are confirmed:
    *   **Apply Filters Button:** Test uses `/Apply Filters/i` ([`ControlPanel.test.js:49`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js:49)). Actual component has "Apply Defined Filters" and `aria-label="Apply Defined Filters Button"` ([`ControlPanel.js:59`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js:59)).
    *   **Filters Heading:** Test uses `/Filters/i` ([`ControlPanel.test.js:29`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js:29)). Actual component has "Define Attribute Filters" ([`ControlPanel.js:46`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js:46)).
*   **Layout Prop Handling (`KnowledgeGraphVisualizationContainer.test.js`):** The test expectation for the initial layout prop passed to the mocked `GraphRenderingArea` is `'force-directed'` ([`KnowledgeGraphVisualizationContainer.test.js:86`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js:86)). However, the `KnowledgeGraphVisualizationContainer` component now defaults to and passes `'cose'` as the layout ([`KnowledgeGraphVisualizationContainer.js:12`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/KnowledgeGraphVisualizationContainer.js:12)).

## 5. Self-Reflection on Clarity

*   **Component Code:**
    *   [`GraphRenderingArea.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js): The component's structure and use of Cytoscape.js are generally clear. The separation of concerns (initialization, updates, layout changes in different `useEffect` hooks) is logical. The Cytoscape API is used in a standard manner.
    *   [`ControlPanel.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js): This component is clear and simple, primarily consisting of standard React form elements and callback props for event handling.
    *   [`KnowledgeGraphVisualizationContainer.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/KnowledgeGraphVisualizationContainer.js): The role of this component as a state manager and orchestrator is well-defined. State variables, callback handlers, and prop drilling to child components are understandable. The filtering logic within `applyFiltersAndSearch` ([`KnowledgeGraphVisualizationContainer.js:37-72`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/KnowledgeGraphVisualizationContainer.js:37-72)) is somewhat intricate but follows a discernible pattern.
*   **Test Code:**
    *   [`GraphRenderingArea.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js): The clarity of these tests is significantly undermined by the inadequate Cytoscape.js mocking strategy. Tests attempting to verify complex interactions (like `TC_KGV_CI_001` for zoom ([`GraphRenderingArea.test.js:115-124`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:115-124)) or `TC_KGV_CI_004` for node selection ([`GraphRenderingArea.test.js:138-156`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:138-156))) are confusing because they interact with a mock that doesn't support these operations realistically. Comments within the tests (e.g., "This is tricky for canvas-based rendering," "This depends heavily on the actual graph library's API") explicitly acknowledge these limitations.
    *   [`ControlPanel.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js): The tests are clear in their intent (testing UI interactions). The failures are due to selectors not matching the current UI, suggesting a need for better maintenance or that UI text changed after tests were written.
    *   [`KnowledgeGraphVisualizationContainer.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js): The strategy of mocking child components is clear and appropriate for testing the container's integration logic. The single test failure is a straightforward case of an outdated expectation.

## 6. Quantitative Assessment (Problematic Code Sections re: Diagnosis)

Based on the diagnosis report and code review, the following problematic sections directly contribute to test failures:

*   **[`GraphRenderingArea.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js):**
    *   Inadequate mock definition for Cytoscape: 1 section ([`GraphRenderingArea.test.js:43-56`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:43-56)).
    *   Tests using `fireEvent` for Cytoscape interactions (affecting zoom, pan): At least 2 tests, impacting ~3 distinct code blocks (e.g., `TC_KGV_CI_001` ([`GraphRenderingArea.test.js:115-124`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:115-124)), `TC_KGV_CI_003` ([`GraphRenderingArea.test.js:126-136`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:126-136))).
    *   Tests with flawed assertions for node/edge rendering or selection due to the poor mock: At least 3 tests (e.g., `TC_KGV_GR_001` for nodes/edges ([`GraphRenderingArea.test.js:76-87`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:76-87), [`GraphRenderingArea.test.js:89-94`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:89-94)), `TC_KGV_CI_004` for node selection ([`GraphRenderingArea.test.js:138-156`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:138-156))).
    *   Layout tests potentially failing due to incorrect mock interaction: 1 test (`TC_KGV_GR_004` ([`GraphRenderingArea.test.js:96-100`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:96-100))).
    *   **Sub-total for `GraphRenderingArea.test.js`: Approximately 7 problematic sections/tests.**
*   **[`ControlPanel.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js):**
    *   Incorrect selector for "Apply Filters" button: 1 section ([`ControlPanel.test.js:49`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js:49)).
    *   Incorrect selector for "Filters" heading: 1 section ([`ControlPanel.test.js:29`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js:29)).
    *   **Sub-total for `ControlPanel.test.js`: 2 problematic selector sections.**
*   **[`KnowledgeGraphVisualizationContainer.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js):**
    *   Outdated layout prop expectation in test: 1 section ([`KnowledgeGraphVisualizationContainer.test.js:86`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js:86)).
    *   **Sub-total for `KnowledgeGraphVisualizationContainer.test.js`: 1 problematic assertion.**

**Total identified problematic code sections directly related to the diagnosis: 10**

## 7. Conclusion and Path Forward

This code comprehension effort confirms the accuracy of the diagnosis report ([`docs/debugging/Knowledge_Graph_Visualization_UI_Test_Failures_Diagnosis.md`](docs/debugging/Knowledge_Graph_Visualization_UI_Test_Failures_Diagnosis.md)). The most substantial set of issues, leading to 8 test failures, is concentrated in [`GraphRenderingArea.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js) and stems directly from an inadequate and superficial mocking strategy for the Cytoscape.js library. This prevents tests from correctly simulating user interactions or verifying component behavior through the library's API.

The failures in [`ControlPanel.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js) (2 failures) are due to straightforward mismatches in UI element selectors (text and ARIA labels), which should be relatively simple to correct. The single failure in [`KnowledgeGraphVisualizationContainer.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js) is due to an outdated expectation regarding a default prop value.

The path forward involves:
1.  **For [`GraphRenderingArea.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js):** Implementing a comprehensive mock for the `cytoscape` module itself (e.g., using `jest.mock('cytoscape')`). Tests should then be refactored to interact with this mock's API to simulate events (e.g., node taps, zoom, pan) and to verify outcomes (e.g., checking calls to `mockedCyInstance.add()`, `mockedCyInstance.style()`, `mockedCyInstance.layout().run()`).
2.  **For [`ControlPanel.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js):** Updating the selectors to accurately match the current text and ARIA labels of the UI elements. Consider using `data-testid` attributes for more robust selectors as suggested in the diagnosis.
3.  **For [`KnowledgeGraphVisualizationContainer.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js):** Updating the test expectation for the initial layout prop to reflect the component's current default ('cose').

Addressing these points, particularly the Cytoscape.js mocking strategy, will be key to resolving the 11 test failures and improving the reliability of the KGV UI test suite. The presence of `jest-canvas-mock` in [`jest.setup.js`](src/main-application-ui/jest.setup.js) is a good foundation, but it must be complemented by proper library-specific mocking in the tests themselves.