# AI Services Gateway - Content Summarization

This component acts as an intermediary between the application and external AI services
(like Gemini) for content summarization.

## Responsibilities

*   Receive summarization requests from the Query Understanding Engine.
*   Format the request according to the specific AI service's API requirements.
*   Handle API key management and authentication securely.
*   Make API calls to the external AI service.
*   Process the response from the AI service, extracting the summary and relevant metadata.
*   Handle errors from the AI service and transform them into a standardized error format.
*   Return the summarization result or error to the Query Understanding Engine.

## Key Files

*   `aiServiceGateway.js`: Contains the logic for interacting with the AI summarization service.
*   `geminiClient.js` (or similar for other AI services): A dedicated client for a specific AI service.