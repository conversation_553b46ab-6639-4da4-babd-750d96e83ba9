import React from 'react';
import { render, screen, fireEvent, within } from '@testing-library/react';
import KnowledgeGraphVisualizationContainer from '../components/KnowledgeGraphVisualizationContainer';

// Mock child components to simplify testing the container's logic
jest.mock('../components/ControlPanel', () => (props) => (
  <div data-testid="mock-control-panel">
    <button onClick={() => props.onLayoutChange('new-layout')}>Change Layout</button>
    {/* Changed to apply a filter that matches the default filterAttributes in the component */}
    <button data-testid="apply-detail-filter-button" onClick={() => props.onFilterChange({ detail: 'Detail A' })}>Apply Detail Filter</button>
    <button onClick={() => props.onNodeTypeToggle('typeA', !props.nodeTypes.find(n => n.id === 'typeA').visible)}>Toggle Node TypeA</button>
  </div>
));

jest.mock('../components/GraphRenderingArea', () => (props) => (
  <div data-testid="mock-graph-rendering-area">
    <span>Layout: {props.layout}</span>
    <span>Nodes: {props.graphData.nodes.length}</span>
    <button onClick={() => props.onNodeSelect(['n1'])}>Select Node n1</button>
    <button onClick={() => props.onCanvasInteraction({ type: 'zoom', level: 1.5 })}>Zoom In</button>
  </div>
));

jest.mock('../components/InformationDisplayPanel', () => (props) => (
  <div data-testid="mock-info-display-panel">
    {props.selectedItem ? `Selected: ${props.selectedItem.id} (${props.itemType})` : 'No item selected'}
  </div>
));

jest.mock('../components/Legend', () => (props) => (
  <div data-testid="mock-legend">
    {Object.keys(props.visualEncodings.nodeTypes).length} Node Types in Legend
  </div>
));

jest.mock('../components/SearchFilterBar', () => (props) => (
  <div data-testid="mock-search-filter-bar">
    <input
      type="text"
      value={props.currentSearchTerm}
      onChange={(e) => props.onSearchTermChange(e.target.value)}
      data-testid="search-input"
    />
    <button onClick={() => props.onFilterApply({ searchTerm: props.currentSearchTerm })}>Search</button>
  </div>
));


// Sample initial props for the container, including graph data
const mockInitialGraphData = {
  nodes: [
    { id: 'n1', label: 'Node 1', type: 'typeA', attributes: { detail: 'Detail A' } },
    { id: 'n2', label: 'Node 2', type: 'typeB', attributes: { detail: 'Detail B' } },
  ],
  edges: [
    { id: 'e1', source: 'n1', target: 'n2', type: 'relX', attributes: { strength: 5 } },
  ],
  filterAttributes: [
    { id: 'detail', name: 'Detail', type: 'string' },
  ],
};

const mockVisualEncodings = {
  nodeTypes: { typeA: { label: 'Type A', color: 'blue' }, typeB: { label: 'Type B', color: 'green' } },
  edgeTypes: { relX: { label: 'Relation X', color: 'gray' } },
};


describe('KnowledgeGraphVisualizationContainer Component (Integration Tests)', () => {
  let container;

  beforeEach(() => {
    // For components that manage internal state and might be re-rendered
    // it's often useful to render once and then interact.
    // However, for these tests, re-rendering per test is fine to ensure isolation.
  });

  test('TC_KGV_CNT_001: should render all child components', () => {
    render(<KnowledgeGraphVisualizationContainer initialGraphData={mockInitialGraphData} visualEncodings={mockVisualEncodings} />);
    expect(screen.getByTestId('mock-control-panel')).toBeInTheDocument();
    expect(screen.getByTestId('mock-graph-rendering-area')).toBeInTheDocument();
    expect(screen.getByTestId('mock-info-display-panel')).toBeInTheDocument();
    expect(screen.getByTestId('mock-legend')).toBeInTheDocument();
    expect(screen.getByTestId('mock-search-filter-bar')).toBeInTheDocument();
  });

  test('TC_KGV_CNT_002: ControlPanel layout change should update GraphRenderingArea (corresponds to TC_KGV_GR_005)', () => {
    render(<KnowledgeGraphVisualizationContainer initialGraphData={mockInitialGraphData} visualEncodings={mockVisualEncodings} />);
    const graphArea = screen.getByTestId('mock-graph-rendering-area');
    expect(within(graphArea).getByText(/Layout: cose/i)).toBeInTheDocument(); // Default layout is 'cose'

    const changeLayoutButton = within(screen.getByTestId('mock-control-panel')).getByText('Change Layout');
    fireEvent.click(changeLayoutButton);
    
    expect(within(graphArea).getByText(/Layout: new-layout/i)).toBeInTheDocument();
  });

  test('TC_KGV_CNT_003: GraphRenderingArea node selection should update InformationDisplayPanel (corresponds to TC_KGV_ID_004)', () => {
    render(<KnowledgeGraphVisualizationContainer initialGraphData={mockInitialGraphData} visualEncodings={mockVisualEncodings} />);
    const infoPanel = screen.getByTestId('mock-info-display-panel');
    expect(within(infoPanel).getByText(/No item selected/i)).toBeInTheDocument();

    const selectNodeButton = within(screen.getByTestId('mock-graph-rendering-area')).getByText('Select Node n1');
    fireEvent.click(selectNodeButton);

    expect(within(infoPanel).getByText(/Selected: n1 \(node\)/i)).toBeInTheDocument();
  });

  test('TC_KGV_CNT_004: SearchFilterBar search term change and apply should filter GraphRenderingArea (corresponds to TC_KGV_CM_001, TC_KGV_CM_002)', async () => {
    // This test is more complex as it involves async state updates and filtering logic
    // which is internal to the container. The mock GraphRenderingArea needs to reflect filtered data.
    // For simplicity, we'll assume the container filters `mockInitialGraphData` and passes a subset.
    // This requires the container to actually implement filtering.

    render(<KnowledgeGraphVisualizationContainer initialGraphData={mockInitialGraphData} visualEncodings={mockVisualEncodings} />);
    const graphArea = screen.getByTestId('mock-graph-rendering-area');
    // Initially, 2 nodes
    expect(within(graphArea).getByText(/Nodes: 2/i)).toBeInTheDocument();

    const searchInput = screen.getByTestId('search-input');
    const searchButton = within(screen.getByTestId('mock-search-filter-bar')).getByText('Search');

    // Simulate typing a search term that would match only "Node 1"
    fireEvent.change(searchInput, { target: { value: 'Node 1' } });
    fireEvent.click(searchButton);
    
    // After filtering, expect 1 node (this assumes the container's filtering logic works)
    // The mock GraphRenderingArea will receive the filtered data.
    // We need to wait for the state update if it's async.
    // For this example, let's assume synchronous update for simplicity of the mock.
    // A real test might need `waitFor`.
    expect(await within(graphArea).findByText(/Nodes: 1/i)).toBeInTheDocument(); // "Node 1"
  });

  test('TC_KGV_CNT_005: ControlPanel filter change should update GraphRenderingArea (corresponds to TC_KGV_CM_001, TC_KGV_CM_002)', async () => {
    render(<KnowledgeGraphVisualizationContainer initialGraphData={mockInitialGraphData} visualEncodings={mockVisualEncodings} />);
    const graphArea = screen.getByTestId('mock-graph-rendering-area');
    expect(within(graphArea).getByText(/Nodes: 2/i)).toBeInTheDocument();

    // Use the new button that applies a filter based on 'detail' attribute
    const applyFilterButton = screen.getByTestId('apply-detail-filter-button');
    fireEvent.click(applyFilterButton); // Applies a filter for { detail: 'Detail A' }

    // This filter should match "Node 1" which has attributes: { detail: 'Detail A' }
    // So, 1 node should remain.
    expect(await within(graphArea).findByText(/Nodes: 1/i)).toBeInTheDocument();
  });
  
  test('TC_KGV_CNT_006: ControlPanel node type toggle should update GraphRenderingArea and Legend (corresponds to TC_KGV_CM_003, TC_KGV_CM_005)', async () => {
    render(<KnowledgeGraphVisualizationContainer initialGraphData={mockInitialGraphData} visualEncodings={mockVisualEncodings} />);
    const graphArea = screen.getByTestId('mock-graph-rendering-area');
    const legend = screen.getByTestId('mock-legend');

    // Initial state: 2 nodes, 2 node types in legend
    expect(within(graphArea).getByText(/Nodes: 2/i)).toBeInTheDocument();
    expect(within(legend).getByText(/2 Node Types in Legend/i)).toBeInTheDocument(); // Based on mockVisualEncodings

    const toggleNodeTypeButton = within(screen.getByTestId('mock-control-panel')).getByText('Toggle Node TypeA');
    fireEvent.click(toggleNodeTypeButton); // Hides TypeA

    // After hiding TypeA: 1 node (n2, typeB), Legend might still show all types or update based on visibility
    // This depends on how the container manages visualEncodings for the legend vs. filtering for the graph.
    // Let's assume the graph filters, and legend reflects available types.
    expect(await within(graphArea).findByText(/Nodes: 1/i)).toBeInTheDocument(); // Only n2 (typeB) remains
    // The legend mock might need to be smarter or the container passes filtered encodings.
    // For simplicity, let's assume the legend still shows all defined types unless explicitly filtered.
    // Or, if the container updates the `visualEncodings` prop to Legend based on visibility:
    // expect(await within(legend).findByText(/1 Node Types in Legend/i)).toBeInTheDocument(); 
    // This part needs careful consideration of the actual implementation.
  });

  // Add more integration tests:
  // - Interaction between SearchFilterBar quick filters and GraphRenderingArea
  // - Zoom/Pan from GraphRenderingArea (verify onCanvasInteraction is handled if needed by container)
  // - Deselection of nodes and its effect on InformationDisplayPanel
  // - Edge selection and InformationDisplayPanel update
  // - Complex filter combinations from ControlPanel and SearchFilterBar
  // - Initial loading of graph data and correct display in GraphRenderingArea
  // - Handling of empty initialGraphData
  // - Error handling (e.g., if graph data is malformed)
});