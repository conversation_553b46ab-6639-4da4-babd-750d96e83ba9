{"numFailedTestSuites": 2, "numFailedTests": 19, "numPassedTestSuites": 0, "numPassedTests": 15, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 2, "numTotalTests": 34, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1747606935962, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Web Content Capture Popup UI Logic", "Initialization and Default State"], "duration": 50, "failureDetails": [{"matcherResult": {"actual": false, "expected": true, "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js:130:92)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Popup UI Logic Initialization and Default State should set default capture mode and load settings on DOMContentLoaded", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should set default capture mode and load settings on DOMContentLoaded"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Initialization and Default State"], "duration": 21, "failureDetails": [{"matcherResult": {"actual": "none", "expected": "inline-block", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"inline-block\"\u001b[39m\nReceived: \u001b[31m\"none\"\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"inline-block\"\u001b[39m\nReceived: \u001b[31m\"none\"\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js:140:73)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Popup UI Logic Initialization and Default State should display PDF button if isPdf is true and default mode is pdf", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should display PDF button if isPdf is true and default mode is pdf"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Capture Mode Selection"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Capture Mode Selection should update active button and UI on mode selection", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should update active button and UI on mode selection"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Capture Mode Selection"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Capture Mode Selection should hide preview for modes other than article/selection", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should hide preview for modes other than article/selection"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Capture Mode Selection"], "duration": 11, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32mObjectContaining {\"tabId\": 123, \"type\": \"ACTIVATE_SELECTION_MODE\"}\u001b[39m, \u001b[32mAny<Function>\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32mObjectContaining {\"tabId\": 123, \"type\": \"ACTIVATE_SELECTION_MODE\"}\u001b[39m, \u001b[32mAny<Function>\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js:209:48)\n    at Promise.then.completed (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:70:21)\n    at runTestInternal (D:\\AI\\pkmAI\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\AI\\pkmAI\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (D:\\AI\\pkmAI\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "Web Content Capture Popup UI Logic Capture Mode Selection should send ACTIVATE_SELECTION_MODE message when selection mode is chosen", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should send ACTIVATE_SELECTION_MODE message when selection mode is chosen"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Capture Mode Selection"], "duration": 14, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32mObjectContaining {\"tabId\": 123, \"type\": \"DEACTIVATE_SELECTION_MODE\"}\u001b[39m, \u001b[32mAny<Function>\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32mObjectContaining {\"tabId\": 123, \"type\": \"DEACTIVATE_SELECTION_MODE\"}\u001b[39m, \u001b[32mAny<Function>\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js:221:48)\n    at Promise.then.completed (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:70:21)\n    at runTestInternal (D:\\AI\\pkmAI\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\AI\\pkmAI\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (D:\\AI\\pkmAI\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "Web Content Capture Popup UI Logic Capture Mode Selection should send DEACTIVATE_SELECTION_MODE message when switching from selection mode", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should send DEACTIVATE_SELECTION_MODE message when switching from selection mode"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Capture Mode Selection"], "duration": 16, "failureDetails": [{"matcherResult": {"actual": "none", "expected": "inline-block", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"inline-block\"\u001b[39m\nReceived: \u001b[31m\"none\"\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"inline-block\"\u001b[39m\nReceived: \u001b[31m\"none\"\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js:253:45)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Popup UI Logic Capture Mode Selection should select PDF mode when PDF button is clicked (if visible)", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should select PDF mode when PDF button is clicked (if visible)"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Metadata Display"], "duration": 37, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Metadata Display should update metadata elements correctly", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should update metadata elements correctly"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Content Preview"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Content Preview should display content preview for article/selection mode", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should display content preview for article/selection mode"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Content Preview"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Content Preview should show \"No preview available\" if content is empty for previewable modes", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should show \"No preview available\" if content is empty for previewable modes"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Status Messages"], "duration": 140, "failureDetails": [{"matcherResult": {"actual": "Test message", "expected": "", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"\"\u001b[39m\nReceived: \u001b[31m\"Test message\"\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"\"\u001b[39m\nReceived: \u001b[31m\"Test message\"\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js:334:74)\n    at Promise.then.completed (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\AI\\pkmAI\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:70:21)\n    at runTestInternal (D:\\AI\\pkmAI\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\AI\\pkmAI\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (D:\\AI\\pkmAI\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "Web Content Capture Popup UI Logic Status Messages should display status message and clear after timeout", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "failed", "title": "should display status message and clear after timeout"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Erro<PERSON>"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Error Handling should display error message when handleError is called with an object", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should display error message when handleError is called with an object"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Erro<PERSON>"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Error Handling should display string error when handleError is called with a string", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should display string error when handleError is called with a string"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Background Script Communication (Initiate and Save)"], "duration": 10, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledTimes\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected number of calls: \u001b[32m2\u001b[39m\nReceived number of calls: \u001b[31m1\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledTimes\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected number of calls: \u001b[32m2\u001b[39m\nReceived number of calls: \u001b[31m1\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js:458:48)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Popup UI Logic Background Script Communication (Initiate and Save) initiateCaptureAndSave should send INITIATE_CAPTURE and then SAVE_CAPTURE message", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "initiateCaptureAndSave should send INITIATE_CAPTURE and then SAVE_CAPTURE message"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Background Script Communication (Initiate and Save)"], "duration": 14, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Error: Capture failed from test\"\u001b[39m\nReceived string:    \u001b[31m\"Error: Test Error: Expected SAVE_CAPTURE, got INITIATE_CAPTURE\"\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Error: Capture failed from test\"\u001b[39m\nReceived string:    \u001b[31m\"Error: Test Error: Expected SAVE_CAPTURE, got INITIATE_CAPTURE\"\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js:488:74)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Popup UI Logic Background Script Communication (Initiate and Save) initiateCaptureAndSave should handle error from INITIATE_CAPTURE", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "initiateCaptureAndSave should handle error from INITIATE_CAPTURE"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Background Script Communication (Initiate and Save)"], "duration": 11, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Error: Save failed miserably from test\"\u001b[39m\nReceived string:    \u001b[31m\"Error: Capture failed from test\"\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected substring: \u001b[32m\"Error: Save failed miserably from test\"\u001b[39m\nReceived string:    \u001b[31m\"Error: Capture failed from test\"\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js:509:74)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Popup UI Logic Background Script Communication (Initiate and Save) initiateCaptureAndSave should handle error from SAVE_CAPTURE", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "initiateCaptureAndSave should handle error from SAVE_CAPTURE"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Handling Messages from <PERSON> Sc<PERSON>"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Handling Messages from Background Script should update preview and metadata on CONTENT_PREVIEW_DATA", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should update preview and metadata on CONTENT_PREVIEW_DATA"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Handling Messages from <PERSON> Sc<PERSON>"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Handling Messages from Background Script should update metadata on METADATA_UPDATED", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should update metadata on METADATA_UPDATED"}, {"ancestorTitles": ["Web Content Capture Popup UI Logic", "Handling Messages from <PERSON> Sc<PERSON>"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Popup UI Logic Handling Messages from Background Script should display status on CAPTURE_STATUS_UPDATE", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should display status on CAPTURE_STATUS_UPDATE"}], "endTime": 1747606941645, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Popup UI Logic › Initialization and Default State › should set default capture mode and load settings on DOMContentLoaded\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32mtrue\u001b[39m\n    Received: \u001b[31mfalse\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 128 |\u001b[39m             \u001b[22m\n\u001b[2m     \u001b[90m 129 |\u001b[39m             \u001b[90m// Assertions after initialization promise has resolved\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 130 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'captureArticle'\u001b[39m)\u001b[33m.\u001b[39mclassList\u001b[33m.\u001b[39mcontains(\u001b[32m'active'\u001b[39m))\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 131 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'saveFormat'\u001b[39m)\u001b[33m.\u001b[39mvalue)\u001b[33m.\u001b[39mtoBe(\u001b[32m'markdown'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 132 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'metaUrl'\u001b[39m)\u001b[33m.\u001b[39mtextContent)\u001b[33m.\u001b[39mtoBe(\u001b[32m'https://example.com'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 133 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'metaTitle'\u001b[39m)\u001b[33m.\u001b[39mtextContent)\u001b[33m.\u001b[39mtoBe(\u001b[32m'Example Page'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/popup.test.js\u001b[39m\u001b[0m\u001b[2m:130:92)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Popup UI Logic › Initialization and Default State › should display PDF button if isPdf is true and default mode is pdf\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"inline-block\"\u001b[39m\n    Received: \u001b[31m\"none\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 138 |\u001b[39m             \u001b[90m// Ensure settings reflect the desire to default to PDF mode\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 139 |\u001b[39m             \u001b[36mawait\u001b[39m setupTestEnvironment({ success\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m tabInfo\u001b[33m:\u001b[39m mockTabInfo\u001b[33m,\u001b[39m isPdf\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m settings\u001b[33m:\u001b[39m { defaultCaptureMode\u001b[33m:\u001b[39m \u001b[32m'pdf'\u001b[39m\u001b[33m,\u001b[39m defaultSaveFormat\u001b[33m:\u001b[39m \u001b[32m'markdown'\u001b[39m } })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 140 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'capturePdf'\u001b[39m)\u001b[33m.\u001b[39mstyle\u001b[33m.\u001b[39mdisplay)\u001b[33m.\u001b[39mtoBe(\u001b[32m'inline-block'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 141 |\u001b[39m             \u001b[90m// Also check if 'pdf' mode was selected due to settings\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 142 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'capturePdf'\u001b[39m)\u001b[33m.\u001b[39mclassList\u001b[33m.\u001b[39mcontains(\u001b[32m'active'\u001b[39m))\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 143 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/popup.test.js\u001b[39m\u001b[0m\u001b[2m:140:73)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Popup UI Logic › Capture Mode Selection › should send ACTIVATE_SELECTION_MODE message when selection mode is chosen\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: \u001b[32mObjectContaining {\"tabId\": 123, \"type\": \"ACTIVATE_SELECTION_MODE\"}\u001b[39m, \u001b[32mAny<Function>\u001b[39m\n\n    Number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 207 |\u001b[39m         test(\u001b[32m'should send ACTIVATE_SELECTION_MODE message when selection mode is chosen'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 208 |\u001b[39m             popupModule\u001b[33m.\u001b[39mselectCaptureMode(\u001b[32m'selection'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 209 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mruntime\u001b[33m.\u001b[39msendMessage)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 210 |\u001b[39m                 expect\u001b[33m.\u001b[39mobjectContaining({ type\u001b[33m:\u001b[39m \u001b[32m'ACTIVATE_SELECTION_MODE'\u001b[39m\u001b[33m,\u001b[39m tabId\u001b[33m:\u001b[39m mockTabInfoForSelectionTests\u001b[33m.\u001b[39mid })\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 211 |\u001b[39m                 expect\u001b[33m.\u001b[39many(\u001b[33mFunction\u001b[39m) \u001b[90m// The wrapper sendMessageToBackground uses a callback\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 212 |\u001b[39m             )\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/popup.test.js\u001b[39m\u001b[0m\u001b[2m:209:48)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Popup UI Logic › Capture Mode Selection › should send DEACTIVATE_SELECTION_MODE message when switching from selection mode\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: \u001b[32mObjectContaining {\"tabId\": 123, \"type\": \"DEACTIVATE_SELECTION_MODE\"}\u001b[39m, \u001b[32mAny<Function>\u001b[39m\n\n    Number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 219 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 220 |\u001b[39m             popupModule\u001b[33m.\u001b[39mselectCaptureMode(\u001b[32m'fullPage'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 221 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mruntime\u001b[33m.\u001b[39msendMessage)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 222 |\u001b[39m                 expect\u001b[33m.\u001b[39mobjectContaining({ type\u001b[33m:\u001b[39m \u001b[32m'DEACTIVATE_SELECTION_MODE'\u001b[39m\u001b[33m,\u001b[39m tabId\u001b[33m:\u001b[39m mockTabInfoForSelectionTests\u001b[33m.\u001b[39mid })\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 223 |\u001b[39m                 expect\u001b[33m.\u001b[39many(\u001b[33mFunction\u001b[39m)\u001b[22m\n\u001b[2m     \u001b[90m 224 |\u001b[39m             )\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/popup.test.js\u001b[39m\u001b[0m\u001b[2m:221:48)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Popup UI Logic › Capture Mode Selection › should select PDF mode when PDF button is clicked (if visible)\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"inline-block\"\u001b[39m\n    Received: \u001b[31m\"none\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 251 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 252 |\u001b[39m             \u001b[36mconst\u001b[39m pdfButton \u001b[33m=\u001b[39m document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'capturePdf'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 253 |\u001b[39m             expect(pdfButton\u001b[33m.\u001b[39mstyle\u001b[33m.\u001b[39mdisplay)\u001b[33m.\u001b[39mtoBe(\u001b[32m'inline-block'\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Verify it's visible\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 254 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 255 |\u001b[39m             \u001b[90m// Simulate that the previous mode might have been 'selection' to test deactivation path\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 256 |\u001b[39m             popupModule\u001b[33m.\u001b[39mselectCaptureMode(\u001b[32m'selection'\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Set previous mode\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/popup.test.js\u001b[39m\u001b[0m\u001b[2m:253:45)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Popup UI Logic › Status Messages › should display status message and clear after timeout\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"\"\u001b[39m\n    Received: \u001b[31m\"Test message\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 332 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'statusMessage'\u001b[39m)\u001b[33m.\u001b[39mclassName)\u001b[33m.\u001b[39mtoContain(\u001b[32m'success'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 333 |\u001b[39m             jest\u001b[33m.\u001b[39mrunAllTimers()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 334 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'statusMessage'\u001b[39m)\u001b[33m.\u001b[39mtextContent)\u001b[33m.\u001b[39mtoBe(\u001b[32m''\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 335 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'statusMessage'\u001b[39m)\u001b[33m.\u001b[39mclassName)\u001b[33m.\u001b[39mnot\u001b[33m.\u001b[39mtoContain(\u001b[32m'success'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 336 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 337 |\u001b[39m         jest\u001b[33m.\u001b[39museRealTimers()\u001b[33m;\u001b[39m \u001b[90m// Important to reset timers for other tests\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/popup.test.js\u001b[39m\u001b[0m\u001b[2m:334:74)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Popup UI Logic › Background Script Communication (Initiate and Save) › initiateCaptureAndSave should send INITIATE_CAPTURE and then SAVE_CAPTURE message\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledTimes\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected number of calls: \u001b[32m2\u001b[39m\n    Received number of calls: \u001b[31m1\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 456 |\u001b[39m             \u001b[36mawait\u001b[39m nextTick()\u001b[33m;\u001b[39m \u001b[90m// Allow promise chain in initiateCaptureAndSave to progress\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 457 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 458 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mruntime\u001b[33m.\u001b[39msendMessage)\u001b[33m.\u001b[39mtoHaveBeenCalledTimes(\u001b[35m2\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 459 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mruntime\u001b[33m.\u001b[39msendMessage)\u001b[33m.\u001b[39mtoHaveBeenNthCalledWith(\u001b[35m1\u001b[39m\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 460 |\u001b[39m                 expect\u001b[33m.\u001b[39mobjectContaining({type\u001b[33m:\u001b[39m \u001b[32m'INITIATE_CAPTURE'\u001b[39m})\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 461 |\u001b[39m                 expect\u001b[33m.\u001b[39many(\u001b[33mFunction\u001b[39m)\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/popup.test.js\u001b[39m\u001b[0m\u001b[2m:458:48)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Popup UI Logic › Background Script Communication (Initiate and Save) › initiateCaptureAndSave should handle error from INITIATE_CAPTURE\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\n    Expected substring: \u001b[32m\"Error: Capture failed from test\"\u001b[39m\n    Received string:    \u001b[31m\"Error: Test Error: Expected SAVE_CAPTURE, got INITIATE_CAPTURE\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 486 |\u001b[39m             \u001b[36mawait\u001b[39m nextTick()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 487 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 488 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'statusMessage'\u001b[39m)\u001b[33m.\u001b[39mtextContent)\u001b[33m.\u001b[39mtoContain(\u001b[32m'Error: Capture failed from test'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 489 |\u001b[39m             expect(saveBtn\u001b[33m.\u001b[39mdisabled)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 490 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 491 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/popup.test.js\u001b[39m\u001b[0m\u001b[2m:488:74)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Popup UI Logic › Background Script Communication (Initiate and Save) › initiateCaptureAndSave should handle error from SAVE_CAPTURE\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\n    Expected substring: \u001b[32m\"Error: Save failed miserably from test\"\u001b[39m\n    Received string:    \u001b[31m\"Error: Capture failed from test\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 507 |\u001b[39m             \u001b[36mawait\u001b[39m nextTick()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 508 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 509 |\u001b[39m             expect(document\u001b[33m.\u001b[39mgetElementById(\u001b[32m'statusMessage'\u001b[39m)\u001b[33m.\u001b[39mtextContent)\u001b[33m.\u001b[39mtoContain(\u001b[32m'Error: Save failed miserably from test'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 510 |\u001b[39m             expect(saveBtn\u001b[33m.\u001b[39mdisabled)\u001b[33m.\u001b[39mtoBe(\u001b[36mfalse\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 511 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 512 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/popup.test.js\u001b[39m\u001b[0m\u001b[2m:509:74)\u001b[22m\u001b[2m\u001b[22m\n", "name": "D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\popup.test.js", "startTime": 1747606939247, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Web Content Capture Background Script Logic", "handlePopupInit"], "duration": 17, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalled\u001b[2m()\u001b[22m\n\nExpected number of calls: >= \u001b[32m1\u001b[39m\nReceived number of calls:    \u001b[31m0\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalled\u001b[2m()\u001b[22m\n\nExpected number of calls: >= \u001b[32m1\u001b[39m\nReceived number of calls:    \u001b[31m0\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:141:39)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handlePopupInit should query active tab, get settings, and respond with tab info, PDF status, and settings", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should query active tab, get settings, and respond with tab info, PDF status, and settings"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handlePopupInit"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Background Script Logic handlePopupInit should correctly identify a PDF URL", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should correctly identify a PDF URL"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handlePopupInit"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Background Script Logic handlePopupInit should handle errors during POPUP_INIT when tabs.query fails", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle errors during POPUP_INIT when tabs.query fails"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handleInitiateCapture"], "duration": 523, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32m{\"mode\": \"article\", \"tabInfo\": {\"id\": 1, \"title\": \"Test Article\", \"url\": \"https://example.com\"}}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32m{\"mode\": \"article\", \"tabInfo\": {\"id\": 1, \"title\": \"Test Article\", \"url\": \"https://example.com\"}}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:221:35)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handleInitiateCapture should call ContentProcessingService and respond with processed data", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should call ContentProcessingService and respond with processed data"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handleInitiateCapture"], "duration": 512, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32mObjectContaining {\"selectedText\": \"<PERSON><PERSON> selected text from content script\"}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32mObjectContaining {\"selectedText\": \"<PERSON>ck selected text from content script\"}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:245:35)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handleInitiateCapture should fetch selection from content script for \"selection\" mode", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should fetch selection from content script for \"selection\" mode"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handleInitiateCapture"], "duration": 517, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32mObjectContaining {\"fullHtml\": \"<html><body>Mock Full HTML</body></html>\"}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32mObjectContaining {\"fullHtml\": \"<html><body>Mock Full HTML</body></html>\"}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:269:35)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handleInitiateCapture should fetch full HTML from content script for \"fullPage\" mode", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should fetch full HTML from content script for \"fullPage\" mode"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handleInitiateCapture"], "duration": 520, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[32m- Expected\u001b[39m\n\u001b[31m+ Received\u001b[39m\n\n\u001b[2m  Object {\u001b[22m\n\u001b[32m-   \"error\": \"CPS Processing Error From Test\",\u001b[39m\n\u001b[32m-   \"success\": false,\u001b[39m\n\u001b[31m+   \"data\": Object {\u001b[39m\n\u001b[31m+     \"content\": \"Article content for: Test Article. Lorem ipsum dolor sit amet...\",\u001b[39m\n\u001b[31m+     \"contentPreview\": \"Article content for: Test Article. Lorem ipsum dolor sit amet......\",\u001b[39m\n\u001b[31m+     \"metadata\": Object {\u001b[39m\n\u001b[31m+       \"author\": \"Mock Author for Test Article\",\u001b[39m\n\u001b[31m+       \"captureDate\": \"2025-05-18T22:22:23.198Z\",\u001b[39m\n\u001b[31m+       \"originalTitle\": \"Test Article\",\u001b[39m\n\u001b[31m+       \"originalUrl\": \"https://example.com\",\u001b[39m\n\u001b[31m+       \"publicationDate\": \"2025-05-13T22:22:23.198Z\",\u001b[39m\n\u001b[31m+     },\u001b[39m\n\u001b[31m+   },\u001b[39m\n\u001b[31m+   \"success\": true,\u001b[39m\n\u001b[2m  }\u001b[22m,\n\nNumber of calls: \u001b[31m1\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[32m- Expected\u001b[39m\n\u001b[31m+ Received\u001b[39m\n\n\u001b[2m  Object {\u001b[22m\n\u001b[32m-   \"error\": \"CPS Processing Error From Test\",\u001b[39m\n\u001b[32m-   \"success\": false,\u001b[39m\n\u001b[31m+   \"data\": Object {\u001b[39m\n\u001b[31m+     \"content\": \"Article content for: Test Article. Lorem ipsum dolor sit amet...\",\u001b[39m\n\u001b[31m+     \"contentPreview\": \"Article content for: Test Article. Lorem ipsum dolor sit amet......\",\u001b[39m\n\u001b[31m+     \"metadata\": Object {\u001b[39m\n\u001b[31m+       \"author\": \"Mock Author for Test Article\",\u001b[39m\n\u001b[31m+       \"captureDate\": \"2025-05-18T22:22:23.198Z\",\u001b[39m\n\u001b[31m+       \"originalTitle\": \"Test Article\",\u001b[39m\n\u001b[31m+       \"originalUrl\": \"https://example.com\",\u001b[39m\n\u001b[31m+       \"publicationDate\": \"2025-05-13T22:22:23.198Z\",\u001b[39m\n\u001b[31m+     },\u001b[39m\n\u001b[31m+   },\u001b[39m\n\u001b[31m+   \"success\": true,\u001b[39m\n\u001b[2m  }\u001b[22m,\n\nNumber of calls: \u001b[31m1\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:291:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handleInitiateCapture should handle failure from ContentProcessingService", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle failure from ContentProcessingService"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handleInitiateCapture"], "duration": 508, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[32m- Expected\u001b[39m\n\u001b[31m+ Received\u001b[39m\n\n\u001b[2m  Object {\u001b[22m\n\u001b[32m-   \"error\": \"CPS Rejected Big Fail From Test\",\u001b[39m\n\u001b[32m-   \"success\": false,\u001b[39m\n\u001b[31m+   \"data\": Object {\u001b[39m\n\u001b[31m+     \"content\": \"Article content for: Test Article. Lorem ipsum dolor sit amet...\",\u001b[39m\n\u001b[31m+     \"contentPreview\": \"Article content for: Test Article. Lorem ipsum dolor sit amet......\",\u001b[39m\n\u001b[31m+     \"metadata\": Object {\u001b[39m\n\u001b[31m+       \"author\": \"Mock Author for Test Article\",\u001b[39m\n\u001b[31m+       \"captureDate\": \"2025-05-18T22:22:23.710Z\",\u001b[39m\n\u001b[31m+       \"originalTitle\": \"Test Article\",\u001b[39m\n\u001b[31m+       \"originalUrl\": \"https://example.com\",\u001b[39m\n\u001b[31m+       \"publicationDate\": \"2025-05-13T22:22:23.710Z\",\u001b[39m\n\u001b[31m+     },\u001b[39m\n\u001b[31m+   },\u001b[39m\n\u001b[31m+   \"success\": true,\u001b[39m\n\u001b[2m  }\u001b[22m,\n\nNumber of calls: \u001b[31m1\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[32m- Expected\u001b[39m\n\u001b[31m+ Received\u001b[39m\n\n\u001b[2m  Object {\u001b[22m\n\u001b[32m-   \"error\": \"CPS Rejected Big Fail From Test\",\u001b[39m\n\u001b[32m-   \"success\": false,\u001b[39m\n\u001b[31m+   \"data\": Object {\u001b[39m\n\u001b[31m+     \"content\": \"Article content for: Test Article. Lorem ipsum dolor sit amet...\",\u001b[39m\n\u001b[31m+     \"contentPreview\": \"Article content for: Test Article. Lorem ipsum dolor sit amet......\",\u001b[39m\n\u001b[31m+     \"metadata\": Object {\u001b[39m\n\u001b[31m+       \"author\": \"Mock Author for Test Article\",\u001b[39m\n\u001b[31m+       \"captureDate\": \"2025-05-18T22:22:23.710Z\",\u001b[39m\n\u001b[31m+       \"originalTitle\": \"Test Article\",\u001b[39m\n\u001b[31m+       \"originalUrl\": \"https://example.com\",\u001b[39m\n\u001b[31m+       \"publicationDate\": \"2025-05-13T22:22:23.710Z\",\u001b[39m\n\u001b[31m+     },\u001b[39m\n\u001b[31m+   },\u001b[39m\n\u001b[31m+   \"success\": true,\u001b[39m\n\u001b[2m  }\u001b[22m,\n\nNumber of calls: \u001b[31m1\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:309:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handleInitiateCapture should handle error during capture process (e.g., CPS rejects)", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle error during capture process (e.g., CPS rejects)"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handleInitiateCapture"], "duration": 514, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32m{\"mode\": \"pdf\", \"tabInfo\": {\"id\": 3, \"title\": \"Test PDF Document\", \"url\": \"https://example.com/document.pdf\"}}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32m{\"mode\": \"pdf\", \"tabInfo\": {\"id\": 3, \"title\": \"Test PDF Document\", \"url\": \"https://example.com/document.pdf\"}}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:339:35)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handleInitiateCapture should correctly handle \"pdf\" mode in initiateCapture", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should correctly handle \"pdf\" mode in initiateCapture"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handleSaveCapture"], "duration": 315, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32m{\"content\": \"Final content to save\", \"format\": \"markdown\", \"metadata\": {\"title\": \"Saved Item Title\"}, \"sourceUrl\": \"https://example.com/save-source\"}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: \u001b[32m{\"content\": \"Final content to save\", \"format\": \"markdown\", \"metadata\": {\"title\": \"Saved Item Title\"}, \"sourceUrl\": \"https://example.com/save-source\"}\u001b[39m\n\nNumber of calls: \u001b[31m0\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:368:32)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handleSaveCapture should call StorageInterface and respond with save result", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should call StorageInterface and respond with save result"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handleSaveCapture"], "duration": 308, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[32m- Expected\u001b[39m\n\u001b[31m+ Received\u001b[39m\n\n\u001b[2m  Object {\u001b[22m\n\u001b[32m-   \"error\": \"Storage Interface Error From Test\",\u001b[39m\n\u001b[32m-   \"success\": false,\u001b[39m\n\u001b[31m+   \"data\": Object {\u001b[39m\n\u001b[31m+     \"savedId\": \"mock_id_1747606944849\",\u001b[39m\n\u001b[31m+     \"success\": true,\u001b[39m\n\u001b[31m+   },\u001b[39m\n\u001b[31m+   \"success\": true,\u001b[39m\n\u001b[2m  }\u001b[22m,\n\nNumber of calls: \u001b[31m1\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[32m- Expected\u001b[39m\n\u001b[31m+ Received\u001b[39m\n\n\u001b[2m  Object {\u001b[22m\n\u001b[32m-   \"error\": \"Storage Interface Error From Test\",\u001b[39m\n\u001b[32m-   \"success\": false,\u001b[39m\n\u001b[31m+   \"data\": Object {\u001b[39m\n\u001b[31m+     \"savedId\": \"mock_id_1747606944849\",\u001b[39m\n\u001b[31m+     \"success\": true,\u001b[39m\n\u001b[31m+   },\u001b[39m\n\u001b[31m+   \"success\": true,\u001b[39m\n\u001b[2m  }\u001b[22m,\n\nNumber of calls: \u001b[31m1\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:389:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handleSaveCapture should handle failure from StorageInterface", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle failure from StorageInterface"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "handleSaveCapture"], "duration": 312, "failureDetails": [{"matcherResult": {"message": "\u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[32m- Expected\u001b[39m\n\u001b[31m+ Received\u001b[39m\n\n\u001b[2m  Object {\u001b[22m\n\u001b[32m-   \"error\": \"Storage Save Crash From Test\",\u001b[39m\n\u001b[32m-   \"success\": false,\u001b[39m\n\u001b[31m+   \"data\": Object {\u001b[39m\n\u001b[31m+     \"savedId\": \"mock_id_1747606945162\",\u001b[39m\n\u001b[31m+     \"success\": true,\u001b[39m\n\u001b[31m+   },\u001b[39m\n\u001b[31m+   \"success\": true,\u001b[39m\n\u001b[2m  }\u001b[22m,\n\nNumber of calls: \u001b[31m1\u001b[39m", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n\u001b[32m- Expected\u001b[39m\n\u001b[31m+ Received\u001b[39m\n\n\u001b[2m  Object {\u001b[22m\n\u001b[32m-   \"error\": \"Storage Save Crash From Test\",\u001b[39m\n\u001b[32m-   \"success\": false,\u001b[39m\n\u001b[31m+   \"data\": Object {\u001b[39m\n\u001b[31m+     \"savedId\": \"mock_id_1747606945162\",\u001b[39m\n\u001b[31m+     \"success\": true,\u001b[39m\n\u001b[31m+   },\u001b[39m\n\u001b[31m+   \"success\": true,\u001b[39m\n\u001b[2m  }\u001b[22m,\n\nNumber of calls: \u001b[31m1\u001b[39m\n    at Object.<anonymous> (D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js:408:34)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "Web Content Capture Background Script Logic handleSaveCapture should handle error during save process (e.g., StorageInterface rejects)", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle error during save process (e.g., StorageInterface rejects)"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "Selection Mode Activation/Deactivation"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Background Script Logic Selection Mode Activation/Deactivation ACTIVATE_SELECTION_MODE should forward message to content script and respond", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "ACTIVATE_SELECTION_MODE should forward message to content script and respond"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "Selection Mode Activation/Deactivation"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Background Script Logic Selection Mode Activation/Deactivation DEACTIVATE_SELECTION_MODE should forward message to content script and respond", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "DEACTIVATE_SELECTION_MODE should forward message to content script and respond"}, {"ancestorTitles": ["Web Content Capture Background Script Logic", "Selection Mode Activation/Deactivation"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Web Content Capture Background Script Logic Selection Mode Activation/Deactivation ACTIVATE_SELECTION_MODE should handle error if no tabId and respond", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "ACTIVATE_SELECTION_MODE should handle error if no tabId and respond"}], "endTime": 1747606945258, "message": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handlePopupInit › should query active tab, get settings, and respond with tab info, PDF status, and settings\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalled\u001b[2m()\u001b[22m\n\n    Expected number of calls: >= \u001b[32m1\u001b[39m\n    Received number of calls:    \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 139 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 140 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mtabs\u001b[33m.\u001b[39mquery)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({ active\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m currentWindow\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 141 |\u001b[39m             expect(mockGetSettingsFn)\u001b[33m.\u001b[39mtoHaveBeenCalled()\u001b[33m;\u001b[39m \u001b[90m// Check the top-level mock\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 142 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({\u001b[22m\n\u001b[2m     \u001b[90m 143 |\u001b[39m                 success\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 144 |\u001b[39m                 tabInfo\u001b[33m:\u001b[39m { id\u001b[33m:\u001b[39m mockSenderTab\u001b[33m.\u001b[39mid\u001b[33m,\u001b[39m url\u001b[33m:\u001b[39m mockSenderTab\u001b[33m.\u001b[39murl\u001b[33m,\u001b[39m title\u001b[33m:\u001b[39m mockSenderTab\u001b[33m.\u001b[39mtitle }\u001b[33m,\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:141:39)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handleInitiateCapture › should call ContentProcessingService and respond with processed data\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: \u001b[32m{\"mode\": \"article\", \"tabInfo\": {\"id\": 1, \"title\": \"Test Article\", \"url\": \"https://example.com\"}}\u001b[39m\n\n    Number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 219 |\u001b[39m             }\u001b[22m\n\u001b[2m     \u001b[90m 220 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 221 |\u001b[39m             expect(mockProcessFn)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(mockPayloadBase)\u001b[33m;\u001b[39m \u001b[90m// Check top-level\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 222 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({ success\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m data\u001b[33m:\u001b[39m mockProcessedData })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 223 |\u001b[39m             \u001b[90m// Check if status update was sent to popup\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 224 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mruntime\u001b[33m.\u001b[39msendMessage)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(expect\u001b[33m.\u001b[39mobjectContaining({ type\u001b[33m:\u001b[39m \u001b[32m'CAPTURE_STATUS_UPDATE'\u001b[39m }))\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:221:35)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handleInitiateCapture › should fetch selection from content script for \"selection\" mode\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: \u001b[32mObjectContaining {\"selectedText\": \"Mock selected text from content script\"}\u001b[39m\n\n    Number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 243 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 244 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mtabs\u001b[33m.\u001b[39msendMessage)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(selectionPayload\u001b[33m.\u001b[39mtabInfo\u001b[33m.\u001b[39mid\u001b[33m,\u001b[39m { type\u001b[33m:\u001b[39m \u001b[32m'GET_SELECTED_CONTENT'\u001b[39m })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 245 |\u001b[39m             expect(mockProcessFn)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(expect\u001b[33m.\u001b[39mobjectContaining({ \u001b[90m// Check top-level\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 246 |\u001b[39m                 selectedText\u001b[33m:\u001b[39m \u001b[32m'Mock selected text from content script'\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 247 |\u001b[39m             }))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 248 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(expect\u001b[33m.\u001b[39mobjectContaining({ success\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m }))\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:245:35)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handleInitiateCapture › should fetch full HTML from content script for \"fullPage\" mode\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: \u001b[32mObjectContaining {\"fullHtml\": \"<html><body>Mock Full HTML</body></html>\"}\u001b[39m\n\n    Number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 267 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 268 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mtabs\u001b[33m.\u001b[39msendMessage)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(fullPagePayload\u001b[33m.\u001b[39mtabInfo\u001b[33m.\u001b[39mid\u001b[33m,\u001b[39m { type\u001b[33m:\u001b[39m \u001b[32m'GET_FULL_PAGE_HTML'\u001b[39m })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 269 |\u001b[39m             expect(mockProcessFn)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(expect\u001b[33m.\u001b[39mobjectContaining({ \u001b[90m// Check top-level\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 270 |\u001b[39m                 fullHtml\u001b[33m:\u001b[39m \u001b[32m'<html><body>Mock Full HTML</body></html>'\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 271 |\u001b[39m             }))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 272 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(expect\u001b[33m.\u001b[39mobjectContaining({ success\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m }))\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:269:35)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handleInitiateCapture › should handle failure from ContentProcessingService\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    \u001b[32m- Expected\u001b[39m\n    \u001b[31m+ Received\u001b[39m\n\n    \u001b[2m  Object {\u001b[22m\n    \u001b[32m-   \"error\": \"CPS Processing Error From Test\",\u001b[39m\n    \u001b[32m-   \"success\": false,\u001b[39m\n    \u001b[31m+   \"data\": Object {\u001b[39m\n    \u001b[31m+     \"content\": \"Article content for: Test Article. Lorem ipsum dolor sit amet...\",\u001b[39m\n    \u001b[31m+     \"contentPreview\": \"Article content for: Test Article. Lorem ipsum dolor sit amet......\",\u001b[39m\n    \u001b[31m+     \"metadata\": Object {\u001b[39m\n    \u001b[31m+       \"author\": \"Mock Author for Test Article\",\u001b[39m\n    \u001b[31m+       \"captureDate\": \"2025-05-18T22:22:23.198Z\",\u001b[39m\n    \u001b[31m+       \"originalTitle\": \"Test Article\",\u001b[39m\n    \u001b[31m+       \"originalUrl\": \"https://example.com\",\u001b[39m\n    \u001b[31m+       \"publicationDate\": \"2025-05-13T22:22:23.198Z\",\u001b[39m\n    \u001b[31m+     },\u001b[39m\n    \u001b[31m+   },\u001b[39m\n    \u001b[31m+   \"success\": true,\u001b[39m\n    \u001b[2m  }\u001b[22m,\n\n    Number of calls: \u001b[31m1\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 289 |\u001b[39m             }\u001b[22m\n\u001b[2m     \u001b[90m 290 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 291 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({ success\u001b[33m:\u001b[39m \u001b[36mfalse\u001b[39m\u001b[33m,\u001b[39m error\u001b[33m:\u001b[39m \u001b[32m'CPS Processing Error From Test'\u001b[39m })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 292 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 293 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 294 |\u001b[39m         test(\u001b[32m'should handle error during capture process (e.g., CPS rejects)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:291:34)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handleInitiateCapture › should handle error during capture process (e.g., CPS rejects)\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    \u001b[32m- Expected\u001b[39m\n    \u001b[31m+ Received\u001b[39m\n\n    \u001b[2m  Object {\u001b[22m\n    \u001b[32m-   \"error\": \"CPS Rejected Big Fail From Test\",\u001b[39m\n    \u001b[32m-   \"success\": false,\u001b[39m\n    \u001b[31m+   \"data\": Object {\u001b[39m\n    \u001b[31m+     \"content\": \"Article content for: Test Article. Lorem ipsum dolor sit amet...\",\u001b[39m\n    \u001b[31m+     \"contentPreview\": \"Article content for: Test Article. Lorem ipsum dolor sit amet......\",\u001b[39m\n    \u001b[31m+     \"metadata\": Object {\u001b[39m\n    \u001b[31m+       \"author\": \"Mock Author for Test Article\",\u001b[39m\n    \u001b[31m+       \"captureDate\": \"2025-05-18T22:22:23.710Z\",\u001b[39m\n    \u001b[31m+       \"originalTitle\": \"Test Article\",\u001b[39m\n    \u001b[31m+       \"originalUrl\": \"https://example.com\",\u001b[39m\n    \u001b[31m+       \"publicationDate\": \"2025-05-13T22:22:23.710Z\",\u001b[39m\n    \u001b[31m+     },\u001b[39m\n    \u001b[31m+   },\u001b[39m\n    \u001b[31m+   \"success\": true,\u001b[39m\n    \u001b[2m  }\u001b[22m,\n\n    Number of calls: \u001b[31m1\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 307 |\u001b[39m                 \u001b[36mawait\u001b[39m nextTick()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 308 |\u001b[39m             }\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 309 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({ success\u001b[33m:\u001b[39m \u001b[36mfalse\u001b[39m\u001b[33m,\u001b[39m error\u001b[33m:\u001b[39m \u001b[32m'CPS Rejected Big Fail From Test'\u001b[39m })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 310 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 311 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 312 |\u001b[39m         test(\u001b[32m'should correctly handle \"pdf\" mode in initiateCapture'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:309:34)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handleInitiateCapture › should correctly handle \"pdf\" mode in initiateCapture\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: \u001b[32m{\"mode\": \"pdf\", \"tabInfo\": {\"id\": 3, \"title\": \"Test PDF Document\", \"url\": \"https://example.com/document.pdf\"}}\u001b[39m\n\n    Number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 337 |\u001b[39m             }\u001b[22m\n\u001b[2m     \u001b[90m 338 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 339 |\u001b[39m             expect(mockProcessFn)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(pdfPayload)\u001b[33m;\u001b[39m \u001b[90m// Check top-level\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 340 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({ success\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m data\u001b[33m:\u001b[39m mockPdfProcessedData })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 341 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mruntime\u001b[33m.\u001b[39msendMessage)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(expect\u001b[33m.\u001b[39mobjectContaining({ type\u001b[33m:\u001b[39m \u001b[32m'CAPTURE_STATUS_UPDATE'\u001b[39m }))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 342 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:339:35)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handleSaveCapture › should call StorageInterface and respond with save result\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: \u001b[32m{\"content\": \"Final content to save\", \"format\": \"markdown\", \"metadata\": {\"title\": \"Saved Item Title\"}, \"sourceUrl\": \"https://example.com/save-source\"}\u001b[39m\n\n    Number of calls: \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 366 |\u001b[39m             }\u001b[22m\n\u001b[2m     \u001b[90m 367 |\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 368 |\u001b[39m             expect(mockSaveFn)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(mockSavePayload)\u001b[33m;\u001b[39m \u001b[90m// Check top-level\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 369 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({ success\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m data\u001b[33m:\u001b[39m { success\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m savedId\u001b[33m:\u001b[39m \u001b[32m'xyz789-saved'\u001b[39m } })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 370 |\u001b[39m             expect(chrome\u001b[33m.\u001b[39mruntime\u001b[33m.\u001b[39msendMessage)\u001b[33m.\u001b[39mtoHaveBeenCalledWith(expect\u001b[33m.\u001b[39mobjectContaining({ type\u001b[33m:\u001b[39m \u001b[32m'CAPTURE_STATUS_UPDATE'\u001b[39m }))\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 371 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:368:32)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handleSaveCapture › should handle failure from StorageInterface\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    \u001b[32m- Expected\u001b[39m\n    \u001b[31m+ Received\u001b[39m\n\n    \u001b[2m  Object {\u001b[22m\n    \u001b[32m-   \"error\": \"Storage Interface Error From Test\",\u001b[39m\n    \u001b[32m-   \"success\": false,\u001b[39m\n    \u001b[31m+   \"data\": Object {\u001b[39m\n    \u001b[31m+     \"savedId\": \"mock_id_1747606944849\",\u001b[39m\n    \u001b[31m+     \"success\": true,\u001b[39m\n    \u001b[31m+   },\u001b[39m\n    \u001b[31m+   \"success\": true,\u001b[39m\n    \u001b[2m  }\u001b[22m,\n\n    Number of calls: \u001b[31m1\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 387 |\u001b[39m                 \u001b[36mawait\u001b[39m nextTick()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 388 |\u001b[39m             }\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 389 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({ success\u001b[33m:\u001b[39m \u001b[36mfalse\u001b[39m\u001b[33m,\u001b[39m error\u001b[33m:\u001b[39m \u001b[32m'Storage Interface Error From Test'\u001b[39m })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 390 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 391 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 392 |\u001b[39m         test(\u001b[32m'should handle error during save process (e.g., StorageInterface rejects)'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:389:34)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWeb Content Capture Background Script Logic › handleSaveCapture › should handle error during save process (e.g., StorageInterface rejects)\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mjest.fn()\u001b[39m\u001b[2m).\u001b[22mtoHaveBeenCalledWith\u001b[2m(\u001b[22m\u001b[32m...expected\u001b[39m\u001b[2m)\u001b[22m\n\n    \u001b[32m- Expected\u001b[39m\n    \u001b[31m+ Received\u001b[39m\n\n    \u001b[2m  Object {\u001b[22m\n    \u001b[32m-   \"error\": \"Storage Save Crash From Test\",\u001b[39m\n    \u001b[32m-   \"success\": false,\u001b[39m\n    \u001b[31m+   \"data\": Object {\u001b[39m\n    \u001b[31m+     \"savedId\": \"mock_id_1747606945162\",\u001b[39m\n    \u001b[31m+     \"success\": true,\u001b[39m\n    \u001b[31m+   },\u001b[39m\n    \u001b[31m+   \"success\": true,\u001b[39m\n    \u001b[2m  }\u001b[22m,\n\n    Number of calls: \u001b[31m1\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 406 |\u001b[39m                 \u001b[36mawait\u001b[39m nextTick()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 407 |\u001b[39m             }\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 408 |\u001b[39m             expect(sendResponse)\u001b[33m.\u001b[39mtoHaveBeenCalledWith({ success\u001b[33m:\u001b[39m \u001b[36mfalse\u001b[39m\u001b[33m,\u001b[39m error\u001b[33m:\u001b[39m \u001b[32m'Storage Save Crash From Test'\u001b[39m })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 409 |\u001b[39m         })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 410 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 411 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36msrc/browser-extension-ui/__tests__/background.test.js\u001b[39m\u001b[0m\u001b[2m:408:34)\u001b[22m\u001b[2m\u001b[22m\n", "name": "D:\\AI\\pkmAI\\src\\browser-extension-ui\\__tests__\\background.test.js", "startTime": 1747606939261, "status": "failed", "summary": ""}], "wasInterrupted": false}