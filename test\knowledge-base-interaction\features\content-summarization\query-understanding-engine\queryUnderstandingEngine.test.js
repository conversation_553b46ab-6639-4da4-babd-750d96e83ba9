// test/knowledge-base-interaction/features/content-summarization/query-understanding-engine/queryUnderstandingEngine.test.js

import { sendToQueryUnderstandingEngine } from '../../../../../src/knowledge-base-interaction/features/content-summarization/query-understanding-engine/queryUnderstandingEngine';
import { summarizeContentWithAIService } from '../../../../../src/knowledge-base-interaction/features/content-summarization/ai-services-gateway/aiServiceGateway';
import { logError, logInfo } from '../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger';
import { convertHtmlToText, extractPdfText } from '../../../../../src/knowledge-base-interaction/features/content-summarization/utils/contentProcessor';

jest.mock('../../../../../src/knowledge-base-interaction/features/content-summarization/ai-services-gateway/aiServiceGateway');
jest.mock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/logger');
jest.mock('../../../../../src/knowledge-base-interaction/features/content-summarization/utils/contentProcessor');

const MAX_CONTENT_LENGTH = 10000; // As defined in the module

describe('sendToQueryUnderstandingEngine', () => {
  const mockBaseRequestPayload = {
    query: 'Summarize this please',
    content: 'Original plain text content.',
    contentType: 'text/plain',
    options: { summaryLength: 'short' },
  };

  const mockAIServiceResponse = {
    summary: 'This is a summary.',
    contentType: 'text/plain', // AI service always returns plain text summary
    model: 'gemini-test-model',
  };

  beforeEach(() => {
    summarizeContentWithAIService.mockClear();
    logError.mockClear();
    logInfo.mockClear();
    convertHtmlToText.mockClear();
    extractPdfText.mockClear();

    // Default mock implementations
    summarizeContentWithAIService.mockResolvedValue(mockAIServiceResponse);
    convertHtmlToText.mockImplementation(async (html) => `text from html: ${html}`);
    extractPdfText.mockImplementation(async (pdf) => `text from pdf: ${pdf}`);
  });

  test('should correctly route summarization request and call AI service (Test Case 5.2.1 & 5.3.1)', async () => {
    const result = await sendToQueryUnderstandingEngine(mockBaseRequestPayload);

    expect(logInfo).toHaveBeenCalledWith('Query Understanding Engine: Received request.', mockBaseRequestPayload);
    expect(summarizeContentWithAIService).toHaveBeenCalledTimes(1);
    expect(summarizeContentWithAIService).toHaveBeenCalledWith({
      content: mockBaseRequestPayload.content,
      contentType: 'text/plain', // Always text/plain to AI service
      options: mockBaseRequestPayload.options,
    });
    expect(logInfo).toHaveBeenCalledWith('Query Understanding Engine: Sending request to AI Services Gateway.', {
      content: mockBaseRequestPayload.content,
      contentType: 'text/plain',
      options: mockBaseRequestPayload.options,
    });
    expect(logInfo).toHaveBeenCalledWith('Query Understanding Engine: Received response from AI Services Gateway.', mockAIServiceResponse);
    expect(result).toEqual(mockAIServiceResponse); // Verifies response structure to UI (Test Case 5.1.2 aspect)
  });

  test('should return error if intent is not summarization', async () => {
    const nonSummarizationRequest = { ...mockBaseRequestPayload, query: 'What is the weather?' };
    const expectedError = { error: 'Request is not for summarization.', summary: null };

    const result = await sendToQueryUnderstandingEngine(nonSummarizationRequest);

    // Updated to expect the actual intent identified by the new getIntent
    expect(logError).toHaveBeenCalledWith('Query Understanding Engine: Intent is not SUMMARIZATION.', { intent: 'QUESTION_ANSWERING' });
    expect(summarizeContentWithAIService).not.toHaveBeenCalled();
    expect(result).toEqual(expectedError);
  });

  test('should convert HTML content to text before sending to AI service', async () => {
    const htmlContent = '<p>This is HTML.</p>';
    const htmlRequest = { ...mockBaseRequestPayload, content: htmlContent, contentType: 'text/html' };
    const expectedProcessedContent = `text from html: ${htmlContent}`;
    convertHtmlToText.mockResolvedValue(expectedProcessedContent);


    await sendToQueryUnderstandingEngine(htmlRequest);

    expect(convertHtmlToText).toHaveBeenCalledWith(htmlContent);
    expect(summarizeContentWithAIService).toHaveBeenCalledWith({
      content: expectedProcessedContent,
      contentType: 'text/plain',
      options: mockBaseRequestPayload.options,
    });
  });

  test('should extract text from PDF content before sending to AI service', async () => {
    const pdfContent = 'PDF binary data or path'; // Mocking as string for simplicity
    const pdfRequest = { ...mockBaseRequestPayload, content: pdfContent, contentType: 'application/pdf' };
    const expectedProcessedContent = `text from pdf: ${pdfContent}`;
    extractPdfText.mockResolvedValue(expectedProcessedContent);

    await sendToQueryUnderstandingEngine(pdfRequest);

    expect(extractPdfText).toHaveBeenCalledWith(pdfContent);
    expect(summarizeContentWithAIService).toHaveBeenCalledWith({
      content: expectedProcessedContent,
      contentType: 'text/plain',
      options: mockBaseRequestPayload.options,
    });
  });

  test('should truncate content exceeding MAX_CONTENT_LENGTH', async () => {
    const longContent = 'a'.repeat(MAX_CONTENT_LENGTH + 100);
    const longContentRequest = { ...mockBaseRequestPayload, content: longContent };
    const expectedTruncatedContent = longContent.substring(0, MAX_CONTENT_LENGTH);

    await sendToQueryUnderstandingEngine(longContentRequest);

    expect(logInfo).toHaveBeenCalledWith(`Query Understanding Engine: Content exceeds ${MAX_CONTENT_LENGTH} characters. Truncating.`);
    expect(summarizeContentWithAIService).toHaveBeenCalledWith(
      expect.objectContaining({ content: expectedTruncatedContent })
    );
  });

  test('should handle errors during HTML content processing', async () => {
    const htmlContent = '<p>HTML</p>';
    const htmlRequest = { ...mockBaseRequestPayload, content: htmlContent, contentType: 'text/html' };
    const processingError = new Error('HTML processing failed');
    convertHtmlToText.mockRejectedValue(processingError);
    const expectedError = { error: 'Failed to process text/html content.', summary: null, details: processingError.message };

    const result = await sendToQueryUnderstandingEngine(htmlRequest);

    expect(logError).toHaveBeenCalledWith('Query Understanding Engine: Error processing content.', { contentType: 'text/html', error: processingError });
    expect(summarizeContentWithAIService).not.toHaveBeenCalled();
    expect(result).toEqual(expectedError);
  });

  test('should handle errors during PDF content processing', async () => {
    const pdfContent = 'PDF data';
    const pdfRequest = { ...mockBaseRequestPayload, content: pdfContent, contentType: 'application/pdf' };
    const processingError = new Error('PDF processing failed');
    extractPdfText.mockRejectedValue(processingError);
    const expectedError = { error: 'Failed to process application/pdf content.', summary: null, details: processingError.message };

    const result = await sendToQueryUnderstandingEngine(pdfRequest);

    expect(logError).toHaveBeenCalledWith('Query Understanding Engine: Error processing content.', { contentType: 'application/pdf', error: processingError });
    expect(summarizeContentWithAIService).not.toHaveBeenCalled();
    expect(result).toEqual(expectedError);
  });

  test('should handle errors from summarizeContentWithAIService', async () => {
    const aiServiceError = new Error('AI service unavailable');
    summarizeContentWithAIService.mockRejectedValue(aiServiceError);
    const expectedError = { error: 'Failed to get summary from AI service.', summary: null, details: aiServiceError.message };

    const result = await sendToQueryUnderstandingEngine(mockBaseRequestPayload);

    expect(logError).toHaveBeenCalledWith('Query Understanding Engine: Error communicating with AI Services Gateway.', aiServiceError);
    expect(result).toEqual(expectedError);
  });

   test('should use "summarize" in query to identify summarization intent', async () => {
    const request = { ...mockBaseRequestPayload, query: "Can you summarize this for me?" };
    await sendToQueryUnderstandingEngine(request);
    expect(summarizeContentWithAIService).toHaveBeenCalled();
  });

  test('should use "summary" in query to identify summarization intent', async () => {
    const request = { ...mockBaseRequestPayload, query: "Give me a summary of this document." };
    await sendToQueryUnderstandingEngine(request);
    expect(summarizeContentWithAIService).toHaveBeenCalled();
  });

});