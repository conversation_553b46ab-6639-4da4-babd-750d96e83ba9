.clipping-templates-container {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
  margin-bottom: 20px;
}

.clipping-templates-container h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.template-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.template-form h4 {
  margin-top: 0;
  color: #555;
}

.template-form input[type="text"],
.template-form textarea {
  width: calc(100% - 22px); /* Account for padding and border */
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  box-sizing: border-box;
}

.template-form textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 10px;
}

.btn {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease-in-out;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}
.btn-primary:hover {
  background-color: #0056b3;
}

.btn-success {
  background-color: #28a745;
  color: white;
}
.btn-success:hover {
  background-color: #1e7e34;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}
.btn-secondary:hover {
  background-color: #545b62;
}

.btn-info {
  background-color: #17a2b8;
  color: white;
}
.btn-info:hover {
  background-color: #117a8b;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}
.btn-danger:hover {
  background-color: #b02a37;
}
.btn-danger:disabled {
  background-color: #efa2a9;
  cursor: not-allowed;
}


.btn-warning {
  background-color: #ffc107;
  color: #212529;
}
.btn-warning:hover {
  background-color: #d39e00;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 0.8rem;
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}


.templates-list {
  list-style-type: none;
  padding: 0;
}

.template-item {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Align items to the top for better layout with multi-line content */
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.template-item.default-template {
  border-left: 5px solid #007bff; /* Highlight default template */
}

.template-info {
  flex-grow: 1;
  margin-right: 15px; /* Space between info and actions */
}

.template-info strong {
  font-size: 1.1rem;
  color: #333;
}

.default-badge {
  background-color: #007bff;
  color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  margin-left: 8px;
  vertical-align: middle;
}

.template-content-preview {
  font-size: 0.9rem;
  color: #666;
  margin-top: 5px;
  white-space: pre-wrap; /* Allow wrapping */
  word-break: break-word; /* Break long words */
  max-height: 60px; /* Limit preview height */
  overflow: hidden;
  text-overflow: ellipsis;
}


.template-actions {
  display: flex;
  flex-direction: column; /* Stack buttons vertically on smaller screens or if preferred */
  gap: 8px; /* Space between buttons */
  align-items: flex-end; /* Align buttons to the right */
}

/* Responsive adjustments for actions if needed */
@media (min-width: 600px) {
  .template-actions {
    flex-direction: row; /* Horizontal layout for wider screens */
    align-items: center;
  }
}


.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
  margin-bottom: 15px;
}