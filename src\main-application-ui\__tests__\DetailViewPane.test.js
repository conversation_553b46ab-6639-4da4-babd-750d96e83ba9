import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import DetailViewPane from '../renderer/components/DetailViewPane';

// Mock child components
jest.mock('../renderer/components/detail-view-pane/ContentRenderer', () => ({ content, contentType }) => (
  <div data-testid="content-renderer">
    <h4>Content Renderer</h4>
    <p>Type: {contentType}</p>
    <div data-testid="rendered-content">{content}</div> 
  </div>
));

jest.mock('../renderer/components/detail-view-pane/MetadataDisplay', () => ({ metadata }) => (
  <div data-testid="metadata-display">
    <h4>Metadata Display</h4>
    {metadata && metadata.sourceURL && <p>Source: <a href={metadata.sourceURL}>{metadata.sourceURL}</a></p>}
    {metadata && metadata.captureDate && <p>Captured: {new Date(metadata.captureDate).toLocaleString()}</p>}
    {metadata && metadata.tags && metadata.tags.length > 0 && <p>Tags: {metadata.tags.join(', ')}</p>}
    {metadata && metadata.categories && metadata.categories.length > 0 && <p>Categories: {metadata.categories.join(', ')}</p>}
  </div>
));

jest.mock('../renderer/components/detail-view-pane/ActionBar', () => ({ 
    onInitiateAIQA, 
    onInitiateContentTransformation, 
    onViewConceptualLinks, 
    onEditMetadata,
    isEditMetadataEnabled 
  }) => (
  <div data-testid="action-bar">
    <h4>Action Bar</h4>
    <button onClick={onInitiateAIQA}>Ask AI</button>
    <button onClick={onInitiateContentTransformation}>Transform Content</button>
    <button onClick={onViewConceptualLinks}>View Links</button>
    {isEditMetadataEnabled && onEditMetadata && <button onClick={onEditMetadata}>Edit Metadata</button>}
  </div>
));


describe('DetailViewPane', () => {
  const mockItem = {
    id: '1',
    title: 'Test Item Title',
    content: '<h1>Test Content</h1><p>Some paragraph.</p>', // No script for this mock test
    contentType: 'html', // Explicitly set for testing
    sourceURL: 'http://example.com',
    captureDate: '2023-01-01T12:00:00Z',
    tags: ['tag1', 'tag2'],
    categories: ['category1'],
  };

  const mockOnInitiateAIQA = jest.fn();
  const mockOnInitiateContentTransformation = jest.fn();
  const mockOnViewConceptualLinks = jest.fn();
  const mockOnEditMetadata = jest.fn();

  const defaultProps = {
    item: mockItem,
    onInitiateAIQA: mockOnInitiateAIQA,
    onInitiateContentTransformation: mockOnInitiateContentTransformation,
    onViewConceptualLinks: mockOnViewConceptualLinks,
    // onEditMetadata and enable flags are optional
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the item title and all mocked sub-components', () => {
    render(<DetailViewPane {...defaultProps} />);
    expect(screen.getByText(mockItem.title)).toBeInTheDocument();
    expect(screen.getByTestId('content-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('metadata-display')).toBeInTheDocument();
    expect(screen.getByTestId('action-bar')).toBeInTheDocument();
  });

  test('passes correct content and contentType to ContentRenderer', () => {
    const { rerender, container } = render(<DetailViewPane {...defaultProps} item={{...mockItem, contentType: 'markdown', content: "# MD"}} />);
    let renderedContent = screen.getByTestId('rendered-content');
    expect(renderedContent).toHaveTextContent('# MD');
    expect(screen.getByText('Type: markdown')).toBeInTheDocument();
    
    rerender(<DetailViewPane {...defaultProps} item={{...mockItem, content: "Plain text", contentType: undefined}} />);
    // Default contentType logic in DetailViewPane should infer 'text' or 'html'
    // Based on current logic, it will be 'text' if not starting with '<'
    renderedContent = screen.getByTestId('rendered-content'); // Re-query after rerender
    expect(screen.getByText('Type: text')).toBeInTheDocument();
    expect(renderedContent).toHaveTextContent('Plain text');
  });

  test('infers contentType from sourceURL', () => {
    const { rerender } = render(<DetailViewPane {...defaultProps} item={{...mockItem, contentType: undefined, sourceURL: 'http://example.com/page.html'}} />);
    expect(screen.getByText('Type: html')).toBeInTheDocument();

    rerender(<DetailViewPane {...defaultProps} item={{...mockItem, contentType: undefined, sourceURL: 'http://example.com/doc.md'}} />);
    expect(screen.getByText('Type: markdown')).toBeInTheDocument();

    rerender(<DetailViewPane {...defaultProps} item={{...mockItem, contentType: undefined, sourceURL: 'http://example.com/document.txt'}} />);
    // Assuming .txt defaults to 'text' or 'plain' - verify against component's actual logic
    expect(screen.getByText('Type: text')).toBeInTheDocument();

    rerender(<DetailViewPane {...defaultProps} item={{...mockItem, contentType: undefined, sourceURL: 'http://example.com/image.jpg'}} />);
    // Assuming unknown extensions default to 'text' or 'plain'
    expect(screen.getByText('Type: text')).toBeInTheDocument();
  });

  test('infers contentType from content sniffing', () => {
    const { rerender } = render(<DetailViewPane {...defaultProps} item={{...mockItem, contentType: undefined, sourceURL: undefined, content: '<html><body>Hello</body></html>'}} />);
    expect(screen.getByText('Type: html')).toBeInTheDocument();

    rerender(<DetailViewPane {...defaultProps} item={{...mockItem, contentType: undefined, sourceURL: undefined, content: '# Markdown Header'}} />);
    // Assuming content starting with # is sniffed as markdown
    expect(screen.getByText('Type: markdown')).toBeInTheDocument();

    rerender(<DetailViewPane {...defaultProps} item={{...mockItem, contentType: undefined, sourceURL: undefined, content: 'Just plain text.'}} />);
    // Assuming plain text defaults to 'text'
    expect(screen.getByText('Type: text')).toBeInTheDocument();
  });

  test('prefers explicit contentType over inference', () => {
    render(<DetailViewPane {...defaultProps} item={{...mockItem, contentType: 'explicit-type', sourceURL: 'http://example.com/page.html', content: '<html>...</html>'}} />);
    expect(screen.getByText('Type: explicit-type')).toBeInTheDocument();
  });

  test('passes correct metadata to MetadataDisplay', () => {
    render(<DetailViewPane {...defaultProps} />);
    // Check for "Source:" text and then the link separately due to mock structure
    expect(screen.getByText((content, element) => content.startsWith('Source:') && element.tagName.toLowerCase() === 'p')).toBeInTheDocument();
    const sourceLink = screen.getByRole('link', { name: mockItem.sourceURL });
    expect(sourceLink).toBeInTheDocument();
    expect(sourceLink).toHaveAttribute('href', mockItem.sourceURL);

    expect(screen.getByText(`Captured: ${new Date(mockItem.captureDate).toLocaleString()}`)).toBeInTheDocument();
    expect(screen.getByText(`Tags: ${mockItem.tags.join(', ')}`)).toBeInTheDocument();
    expect(screen.getByText(`Categories: ${mockItem.categories.join(', ')}`)).toBeInTheDocument();
  });

  test('calls correct handlers from ActionBar with item.id', () => {
    render(<DetailViewPane {...defaultProps} />);
    
    fireEvent.click(screen.getByRole('button', { name: 'Ask AI' }));
    expect(mockOnInitiateAIQA).toHaveBeenCalledWith(mockItem.id);

    fireEvent.click(screen.getByRole('button', { name: 'Transform Content' }));
    expect(mockOnInitiateContentTransformation).toHaveBeenCalledWith(mockItem.id);
    
    fireEvent.click(screen.getByRole('button', { name: 'View Links' }));
    expect(mockOnViewConceptualLinks).toHaveBeenCalledWith(mockItem.id);
  });

  test('renders Edit Metadata button and calls handler if enabled and provided', () => {
    render(
      <DetailViewPane 
        {...defaultProps} 
        onEditMetadata={mockOnEditMetadata} 
        isEditMetadataEnabled={true} 
      />
    );
    const editButton = screen.getByRole('button', { name: 'Edit Metadata' });
    expect(editButton).toBeInTheDocument();
    fireEvent.click(editButton);
    expect(mockOnEditMetadata).toHaveBeenCalledWith(mockItem.id);
  });

  test('does not render Edit Metadata button if not enabled or handler not provided', () => {
    const { rerender } = render(
      <DetailViewPane {...defaultProps} onEditMetadata={mockOnEditMetadata} isEditMetadataEnabled={false} />
    );
    expect(screen.queryByRole('button', { name: 'Edit Metadata' })).not.toBeInTheDocument();

    rerender(
      <DetailViewPane {...defaultProps} isEditMetadataEnabled={true} /> // No onEditMetadata handler
    );
    expect(screen.queryByRole('button', { name: 'Edit Metadata' })).not.toBeInTheDocument();
  });

  test('displays "No item selected." when item is null', () => {
    render(
      <DetailViewPane 
        item={null} 
        onInitiateAIQA={mockOnInitiateAIQA}
        onInitiateContentTransformation={mockOnInitiateContentTransformation}
        onViewConceptualLinks={mockOnViewConceptualLinks}
      />
    );
    expect(screen.getByText('No item selected.')).toBeInTheDocument();
  });
});
