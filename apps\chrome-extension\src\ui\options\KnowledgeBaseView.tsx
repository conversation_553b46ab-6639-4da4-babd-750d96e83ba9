import React, { useState, useEffect, useCallback } from 'react';
import { KnowledgeBaseEntry as KnowledgeBaseItem, KnowledgeBaseEntry } from '@pkm-ai/knowledge-base-service';
import ContentList from './ContentList';
import DetailViewPane from './DetailViewPane';
import { CreateEntryData, UpdateEntryData } from '../../types/messaging'; // Assuming types for messaging

// Helper to send messages to background script
export const sendMessageToBackground = <TResponse = any, TMessage = any>(message: TMessage): Promise<TResponse> => {
  return new Promise((resolve, reject) => {
    if (chrome && chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          console.error('Error sending message:', chrome.runtime.lastError.message, 'Message:', message);
          reject(new Error(chrome.runtime.lastError.message));
        } else if (response && response.success) {
          resolve(response);
        } else {
          console.error('Failed operation:', response?.error || 'Unknown error', 'Message:', message, 'Response:', response);
          reject(new Error(response?.error || 'Unknown error during background operation'));
        }
      });
    } else {
      console.error('chrome.runtime.sendMessage is not available. Message:', message);
      reject(new Error('Chrome runtime not available to send message.'));
    }
  });
};


const KnowledgeBaseView: React.FC = () => {
  const [items, setItems] = useState<KnowledgeBaseItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<KnowledgeBaseItem | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchItems = useCallback(async () => {
    console.log('[KBView] fetchItems called');
    setIsLoading(true);
    setError(null);
    try {
      const response = await sendMessageToBackground<{ success: boolean, entries?: KnowledgeBaseItem[], error?: string }>({ action: 'getKnowledgeBaseEntries' });
      console.log('[KBView] fetchItems response:', response);
      if (response.entries) {
        console.log('[KBView] fetchItems received entries:', response.entries.length);
        // Convert date strings to Date objects
        const processedEntries = response.entries.map(entry => ({
          ...entry,
          createdAt: entry.createdAt ? new Date(entry.createdAt) : new Date(),
          updatedAt: entry.updatedAt ? new Date(entry.updatedAt) : new Date(),
        }));
        setItems(processedEntries);
      } else {
        throw new Error(response.error || 'Failed to fetch entries: No entries returned.');
      }
    } catch (err: any) {
      console.error('Failed to fetch knowledge base items:', err);
      setError(err.message || 'Failed to load knowledge base items. Please try again later.');
      setItems([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchItems();
  }, [fetchItems]);

  const handleSelectItem = useCallback(async (item: KnowledgeBaseItem | string) => {
    if (typeof item === 'string') { // If ID is passed, fetch the full item
      setIsLoading(true);
      try {
        const response = await sendMessageToBackground<{ success: boolean, entry?: KnowledgeBaseItem, error?: string }>({ action: 'getKnowledgeBaseEntryById', data: { id: item } });
        if (response.entry) {
          const processedEntry = {
            ...response.entry,
            createdAt: response.entry.createdAt ? new Date(response.entry.createdAt) : new Date(),
            updatedAt: response.entry.updatedAt ? new Date(response.entry.updatedAt) : new Date(),
          };
          setSelectedItem(processedEntry);
        } else {
          throw new Error(response.error || `Failed to fetch entry ${item}`);
        }
      } catch (err: any) {
        console.error(`Failed to fetch item ${item}:`, err);
        setError(`Failed to load item details: ${err.message}`);
        setSelectedItem(null); // Clear selection on error
      } finally {
        setIsLoading(false);
      }
    } else {
      setSelectedItem(item);
    }
  }, []);


  const handleCreateEntry = useCallback(async (entryData: CreateEntryData) => {
    console.log('[KBView] handleCreateEntry called with:', entryData);
    setIsLoading(true);
    setError(null); // Clear previous errors
    try {
      console.log('[KBView] handleCreateEntry: sending message to background...');
      const createResponse = await sendMessageToBackground<{ success: boolean, entry?: KnowledgeBaseItem, error?: string }>({
        action: 'createKnowledgeBaseEntry',
        data: entryData
      });
      console.log('[KBView] handleCreateEntry: background response:', createResponse);
      await fetchItems(); // Refresh list
      setSelectedItem(null); // Clear selection or select the new item
    } catch (err: any) {
      console.error('[KBView] Failed to create entry:', err);
      setError(`Failed to create entry: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [fetchItems]);

  const handleUpdateEntry = useCallback(async (id: string, updateData: UpdateEntryData) => {
    setIsLoading(true);
    try {
      const response = await sendMessageToBackground<{ success: boolean, entry?: KnowledgeBaseItem, error?: string }>({
        action: 'updateKnowledgeBaseEntry',
        data: { id, updateData }
      });
      await fetchItems(); // Refresh list
      if (response.entry) {
        const processedEntry = {
            ...response.entry,
            createdAt: response.entry.createdAt ? new Date(response.entry.createdAt) : new Date(),
            updatedAt: response.entry.updatedAt ? new Date(response.entry.updatedAt) : new Date(),
          };
        setSelectedItem(processedEntry); // Update selected item if it was the one edited
      } else {
        setSelectedItem(null); // Or clear if update failed to return entry
      }
    } catch (err: any) {
      console.error(`Failed to update entry ${id}:`, err);
      setError(`Failed to update entry: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [fetchItems]);

  const handleDeleteEntry = useCallback(async (id: string) => {
    setIsLoading(true);
    try {
      await sendMessageToBackground<{ success: boolean, id?: string, error?: string }>({
        action: 'deleteKnowledgeBaseEntry',
        data: { id }
      });
      await fetchItems(); // Refresh list
      if (selectedItem?.id === id) {
        setSelectedItem(null); // Clear selection if deleted item was selected
      }
    } catch (err: any) {
      console.error(`Failed to delete entry ${id}:`, err);
      setError(`Failed to delete entry: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [fetchItems, selectedItem]);


  const filteredItems = items.filter(item =>
    (item.title?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (item.content?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (item.url?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (item.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))) ||
    (item.type?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex flex-col h-screen p-4 bg-gray-100">
      <h1 className="text-2xl font-bold mb-4 text-gray-800">Knowledge Base</h1>
      <div className="mb-4">
        <input
          type="text"
          placeholder="Search by title, content, URL, tags, or type..."
          className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      <div className="flex flex-1 overflow-hidden">
          <div className="w-1/3 pr-2 flex flex-col bg-white shadow rounded-md overflow-hidden">
            {/* Removed problematic console.log from JSX: console.log(`[KBView] Rendering list: isLoading=${isLoading}, error=${error}, filteredItems.length=${filteredItems.length}, items.length=${items.length}, searchTerm="${searchTerm}"`) */}
            {isLoading && !error && <p className="p-4 text-gray-500" data-testid="loading-items-message">Loading items...</p>}
            {error && <p className="p-4 text-red-500" data-testid="error-message">{error}</p>}
            {!isLoading && !error && (
            <ContentList items={filteredItems} onItemSelect={handleSelectItem} selectedItemId={selectedItem?.id} onDelete={handleDeleteEntry} />
          )}
          {!isLoading && !error && filteredItems.length === 0 && searchTerm && (
            <p className="p-4 text-gray-500" data-testid="no-search-results-message">No items match your search.</p>
          )}
           {!isLoading && !error && filteredItems.length === 0 && !searchTerm && items.length === 0 && (
            <p className="p-4 text-gray-500" data-testid="no-items-message">No items found in the knowledge base. Try adding one!</p>
          )}
        </div>
        <div className="w-2/3 pl-2 bg-white shadow rounded-md overflow-y-auto">
          <DetailViewPane
            item={selectedItem}
            onUpdate={handleUpdateEntry}
            onDelete={handleDeleteEntry}
            onRefresh={fetchItems} // Pass fetchItems to allow DetailViewPane to trigger a refresh if needed
          />
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBaseView;