/* Styles for NavigationFiltersPane */
.navigation-filters-pane-component {
  background-color: #f0f0f0;
  /* height: 100%; */ /* The parent .navigation-pane already handles height and overflow */
  /* width is controlled by .navigation-pane in App.css */
  /* padding is controlled by .navigation-pane in App.css */
  /* border-right is controlled by .navigation-pane in App.css */
  box-sizing: border-box;
  overflow-y: auto; /* Allow scrolling if content exceeds height */
}

.navigation-filters-pane-component h3 {
  margin-top: 0;
  margin-bottom: 20px; /* Increased margin for spacing */
  color: #333;
  font-size: 1.25em; /* Slightly larger heading */
  border-bottom: 1px solid #ccc; /* Slightly darker separator line */
  padding-bottom: 12px; /* Spacing for separator */
  font-weight: 600; /* Bolder heading */
}

.navigation-filters-pane-component .filter-section {
  margin-bottom: 25px; /* Increased space between sections */
}

.navigation-filters-pane-component .filter-section h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #333; /* Darker for better contrast */
  font-size: 1.05em; /* Slightly larger */
  font-weight: 600; /* Bolder sub-headings */
}

.navigation-filters-pane-component p {
  font-size: 0.9em;
  color: #555;
  margin-top: 5px; /* Add some space above paragraph messages */
}

.navigation-filters-pane-component ul {
  list-style-type: none;
  padding-left: 0;
  margin-top: 0; /* Remove default ul margin */
}

.navigation-filters-pane-component li {
  padding: 8px 10px; /* Adjusted padding */
  cursor: pointer;
  font-size: 0.95em;
  color: #444; /* Slightly darker text */
  border-radius: 4px; /* Consistent border-radius */
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out; /* Smooth transition */
}

.navigation-filters-pane-component li:hover {
  background-color: #e9ecef; /* Lighter hover, consistent with Bootstrap */
  color: #000; /* Darken text on hover */
}

.navigation-filters-pane-component button,
.navigation-filters-pane-component .search-button,
.navigation-filters-pane-component .clear-filters-button {
  padding: 9px 15px; /* Slightly larger padding */
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.95em; /* Consistent font size */
  transition: background-color 0.2s ease-in-out;
}

.navigation-filters-pane-component button:hover,
.navigation-filters-pane-component .search-button:hover,
.navigation-filters-pane-component .clear-filters-button:hover {
  background-color: #0056b3;
}

.navigation-filters-pane-component .error-message {
  color: #d9534f; /* Bootstrap's danger color */
  font-size: 0.85em;
  margin-top: 5px;
  margin-bottom: 10px;
}
.navigation-filters-pane-component li.active {
  background-color: #007bff;
  color: white;
  font-weight: bold;
}

.navigation-filters-pane-component li.active:hover {
  background-color: #0056b3;
  color: white;
}

/* Search section specific styles */
.navigation-filters-pane-component .search-section {
  margin-bottom: 20px;
}

.navigation-filters-pane-component .search-section form {
  display: flex;
  gap: 8px; /* Space between input and button */
}

.navigation-filters-pane-component .search-input {
  flex-grow: 1; /* Input takes available space */
  padding: 8px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9em;
  box-sizing: border-box;
}

.navigation-filters-pane-component .search-input:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.navigation-filters-pane-component .search-button {
  /* padding: 8px 12px; */ /* Handled by general button style */
  /* background-color: #007bff; */ /* Handled by general button style */
  /* color: white; */ /* Handled by general button style */
  /* border: none; */ /* Handled by general button style */
  /* border-radius: 4px; */ /* Handled by general button style */
  /* cursor: pointer; */ /* Handled by general button style */
  /* font-size: 0.9em; */ /* Handled by general button style */
  white-space: nowrap; /* Prevent button text from wrapping */
  margin-top: 0; /* Override general button margin-top */
}

/* .navigation-filters-pane-component .search-button:hover { */
  /* background-color: #0056b3; */ /* Handled by general button style */
/* } */

.navigation-filters-pane-component .search-button:disabled {
  background-color: #6c757d; /* Bootstrap's secondary color for disabled state */
  cursor: not-allowed;
}

.navigation-filters-pane-component .search-error-message {
  margin-top: 8px; /* Space above search error message */
  color: #dc3545; /* Bootstrap's danger color */
  font-size: 0.85em;
}

/* Styles for Advanced Filters */
.navigation-filters-pane-component .filter-options-group {
  display: flex;
  flex-direction: column;
  gap: 6px; /* Space between checkbox options */
  max-height: 150px; /* Limit height and make scrollable if many options */
  overflow-y: auto;
  padding-right: 5px; /* Space for scrollbar */
  border: 1px solid #e0e0e0; /* Optional: border around the group */
  border-radius: 4px;
  padding: 8px;
}

.navigation-filters-pane-component .filter-option {
  display: flex;
  align-items: center;
  gap: 8px; /* Space between checkbox and label */
}

.navigation-filters-pane-component .filter-option input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
  transform: scale(1.1); /* Slightly larger checkboxes */
}

.navigation-filters-pane-component .filter-option label {
  font-size: 0.9em;
  color: #333; /* Darker label text */
  cursor: pointer;
  user-select: none; /* Prevent text selection on label click */
}

.navigation-filters-pane-component .date-range-filter {
  display: flex;
  flex-direction: column;
  gap: 10px; /* Space between date input groups */
}

.navigation-filters-pane-component .date-input-group {
  display: flex;
  align-items: center;
  gap: 8px; /* Space between label and date input */
}

.navigation-filters-pane-component .date-input-group label {
  font-size: 0.9em;
  color: #333; /* Darker label text */
  min-width: 45px; /* Align 'From:' and 'To:' labels */
}

.navigation-filters-pane-component .date-input {
  padding: 8px 10px; /* Consistent padding */
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9em;
  box-sizing: border-box;
  flex-grow: 1;
}

.navigation-filters-pane-component .date-input:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.navigation-filters-pane-component .clear-filters-button {
  background-color: #6c757d; /* Bootstrap secondary/gray color */
  /* color: white; */ /* Handled by general button style */
  /* padding: 8px 12px; */ /* Handled by general button style */
  /* border: none; */ /* Handled by general button style */
  /* border-radius: 4px; */ /* Handled by general button style */
  /* cursor: pointer; */ /* Handled by general button style */
  /* font-size: 0.9em; */ /* Handled by general button style */
  width: 100%; /* Make button full width of its container */
  margin-top: 0; /* Reset margin, will be handled by filter-actions-section */
}

.navigation-filters-pane-component .clear-filters-button:hover {
  background-color: #5a6268;
}

.navigation-filters-pane-component .filter-actions-section {
  margin-top: 20px; /* Add space above the clear button section */
}

/* Styles for Tag Logic Radio Buttons */
.navigation-filters-pane-component .tag-logic-options {
  display: flex;
  gap: 15px; /* Space between radio buttons */
  margin-bottom: 12px; /* Space below the radio group */
}

.navigation-filters-pane-component .tag-logic-options label {
  display: flex;
  align-items: center;
  gap: 6px; /* Space between radio input and its text label */
  font-size: 0.9em;
  color: #333; /* Darker label text */
  cursor: pointer;
}

.navigation-filters-pane-component .tag-logic-options input[type="radio"] {
  margin: 0;
  cursor: pointer;
  transform: scale(1.1); /* Slightly larger radio buttons */
}

/* Styles for Source Input */
.navigation-filters-pane-component .source-input {
  width: 100%; /* Make input take full width */
  padding: 8px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9em;
  box-sizing: border-box; /* Include padding and border in the element's total width and height */
  margin-top: 5px; /* Space above the input if needed */
}

.navigation-filters-pane-component .source-input:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Styles for Sort Select Dropdown */
.navigation-filters-pane-component .sort-select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9em;
  background-color: white;
  cursor: pointer;
  box-sizing: border-box;
  margin-top: 5px;
}

.navigation-filters-pane-component .sort-select:focus {
  border-color: #007bff;
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}