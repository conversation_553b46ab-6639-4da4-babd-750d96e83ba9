// __tests__/client.test.js
import * as apiClient from '../renderer/api/client';

// Mock global.fetch
global.fetch = jest.fn();

describe('API Client', () => {
  beforeEach(() => {
    // Clear all instances and calls to constructor and all methods:
    fetch.mockClear();
  });

  describe('getItems', () => {
    test('should resolve with mock data', async () => {
      const mockItems = [
        { id: 101, name: 'Fetched Item A', source: 'API' },
        { id: 102, name: 'Fetched Item B', source: 'API' },
      ];
      apiClient.getItems = jest.fn().mockResolvedValue(mockItems);
      const items = await apiClient.getItems();
      expect(items).toEqual(mockItems);
      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('createItem', () => {
    const newItemData = { name: 'New Test Item', description: 'A new item.' };
    test('should resolve with mock data', async () => {
      const mockCreatedItem = { ...newItemData, id: 103, message: 'Item created via mock API' };
      apiClient.createItem = jest.fn().mockResolvedValue(mockCreatedItem);
      const createdItem = await apiClient.createItem(newItemData);
      expect(createdItem).toEqual(mockCreatedItem);
      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('getItemById', () => {
    const itemId = 123;
    test('should resolve with mock data', async () => {
      const mockItem = { id: itemId, name: `Fetched Item ${itemId}`, description: 'Details from mock API', source: 'API' };
      apiClient.getItemById = jest.fn().mockResolvedValue(mockItem);
      const item = await apiClient.getItemById(itemId);
      expect(item).toEqual(mockItem);
      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('updateItem', () => {
    const itemId = 456;
    const updatedData = { name: 'Updated Item Name' };
    test('should resolve with mock data', async () => {
      const mockUpdatedItem = { id: itemId, ...updatedData, message: 'Item updated via mock API' };
      apiClient.updateItem = jest.fn().mockResolvedValue(mockUpdatedItem);
      const updatedItem = await apiClient.updateItem(itemId, updatedData);
      expect(updatedItem).toEqual(mockUpdatedItem);
      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('deleteItem', () => {
    const itemId = 789;
    test('should resolve with mock data', async () => {
      const mockResult = { message: `Item ${itemId} deleted via mock API` };
      apiClient.deleteItem = jest.fn().mockResolvedValue(mockResult);
      const result = await apiClient.deleteItem(itemId);
      expect(result).toEqual(mockResult);
      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('searchItems', () => {
    const query = 'test query';
    test('should resolve with mock search results', async () => {
      const mockResults = [
        { id: 201, name: `Search Result for ${query} 1`, relevance: 0.9 },
        { id: 202, name: `Search Result for ${query} 2`, relevance: 0.8 },
      ];
      apiClient.searchItems = jest.fn().mockResolvedValue(mockResults);
      const results = await apiClient.searchItems(query);
      expect(results).toEqual(mockResults);
      expect(fetch).not.toHaveBeenCalled();
    });
  });
});
