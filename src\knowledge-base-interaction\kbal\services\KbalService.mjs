import ContentItem from '../models/contentItem.mjs';
// Node.js specific imports (uuid, lowdb/node, path, fs) are handled in the main process.
// This file (renderer-side) will interact with them via IPC.

// Singleton instance
let instance = null;

/**
 * @class KbalService
 * @description Service for managing knowledge base content items.
 *              In the renderer process, this service will communicate with the main process via IPC.
 *              In the main process, it will directly interact with LowDB and the file system.
 * @implements {IKbalService} // Conceptually implements the IKbalService interface
 */
class KbalService {
  /**
   * @type {object}
   */
  db; // This will be an object with `data`, `read`, `write` methods, either LowDB instance or a mock.

  /**
   * @param {object|Array} [injectedDbOrTestData] - Either a LowDB instance (for main process/testing) or initial test data (array for in-memory testing).
   */
  constructor(injectedDbOrTestData) {
    if (instance) {
      return instance;
    }

    if (Array.isArray(injectedDbOrTestData)) {
      // For testing mode with in-memory data
      this.testMode = true;
      this.testData = injectedDbOrTestData;
      this.db = { data: { items: [...injectedDbOrTestData] }, read: async () => {}, write: async () => {} }; // Mock in-memory db
      console.log(`KbalService initialized in test mode with ${this.db.data.items.length} items. Version: 1.1`);
    } else if (injectedDbOrTestData && typeof injectedDbOrTestData === 'object' && 'read' in injectedDbOrTestData && 'write' in injectedDbOrTestData) {
      // For main process, inject the LowDB instance
      this.db = injectedDbOrTestData;
      console.log('KbalService initialized with injected LowDB instance. Version: 1.1');
    } else {
      // Default for renderer process: db will be set via IPC or is expected to be mocked externally.
      // Or, if this is the main process and no db is injected, it will be set in init.
      this.db = null; // Will be set by init or via IPC
      console.warn('KbalService initialized without an injected DB. Ensure init() is called or DB is set via IPC. Version: 1.1');
    }

    instance = this;
  }

  /**
   * Get the singleton instance of KbalService.
   * Note: In the renderer process, this will return the singleton without a specific DB path.
   * The actual DB interaction is handled by the main process via IPC.
   * @returns {KbalService} The singleton instance
   */
  static getInstance() {
    if (!instance) {
      instance = new KbalService();
    }
    return instance;
  }

  /**
   * Reset the singleton instance (primarily for testing)
   */
  static resetInstance() {
    instance = null;
  }

  /**
   * Initialize the database connection.
   * In the renderer process, this method will ensure the DB is ready for IPC calls.
   * In the main process, it will initialize the LowDB instance.
   * @returns {Promise<void>}
   */
  async init() {
    // If in test mode, the db is already an in-memory mock
    if (this.testMode) {
      console.log('KbalService in test mode, init complete.');
      return;
    }

    // For the main process, if db is not yet set (e.g., constructor was called without injection)
    // it would be initialized here. For the renderer, this is a no-op or a check.
    if (!this.db) {
      console.warn('KbalService.init() called but no database instance is set. This is expected in the renderer process, where DB operations are handled via IPC. In the main process, ensure a LowDB instance is injected or initialized.');
      // In the renderer, we don't need a local db instance, as we'll use IPC.
      // So, if this is the renderer, we can just return.
      // If this is the main process, this would be where LowDB is set up.
      // For now, we'll assume the main process will inject it.
      return;
    }

    // If a db instance is present (e.g., injected in main process or test mode)
    try {
      await this.db.read();
      console.log(`Database read successful. Items: ${this.db.data?.items?.length || 0}`);
    } catch (error) {
      console.error(`Error reading database during init: ${error.message}`);
      throw error;
    }
  }

  /**
   * Helper to ensure data read from DB is a ContentItem instance
   * @param {object} itemData
   * @returns {ContentItem | null}
   */
  _toContentItemInstance(itemData) {
    if (!itemData) {
      console.warn('Null or undefined item data passed to _toContentItemInstance');
      return null;
    }

    // For debugging
    console.log('Converting item data to ContentItem:', JSON.stringify(itemData));

    // Ensure we have all required fields before creating a ContentItem
    if (!itemData.id || !itemData.type || !itemData.title) {
      console.warn('Invalid item data missing required fields:', JSON.stringify(itemData));
      return null;
    }

    // Check if this is a ContentItem (has our marker)
    const isContentItem = itemData._contentItemType === "ContentItem";
    if (!isContentItem) {
      console.warn('Item does not have ContentItem marker:', JSON.stringify(itemData));
    }

    // The constructor of ContentItem handles default metadata like createdAt/updatedAt
    // if they are not present in itemData.metadata.
    // However, lowdb stores plain objects, so we need to ensure metadata is passed correctly.
    try {
      // Create a new ContentItem instance with the data
      const item = new ContentItem(
        itemData.id,
        itemData.type,
        itemData.title,
        itemData.content || "",
        itemData.sourceUrl || "",
        itemData.metadata || {}, // Pass the whole metadata object
        itemData.embeddings || []
      );

      // If metadata was stored, it's already on item. If not, constructor created it.
      // Ensure updatedAt is also correctly handled if it was part of itemData.metadata
      if (itemData.metadata && itemData.metadata.updatedAt) {
          item.metadata.updatedAt = itemData.metadata.updatedAt;
      }
      if (itemData.metadata && itemData.metadata.createdAt) {
          item.metadata.createdAt = itemData.metadata.createdAt;
      }

      // Log for debugging
      console.log(`Successfully created ContentItem instance for ID: ${item.id}`);

      // Verify the item is an instance of ContentItem
      if (!(item instanceof ContentItem)) {
        console.error('Created item is not an instance of ContentItem:', item);
        // Force it to be a ContentItem by creating a new one
        return new ContentItem(
          item.id,
          item.type,
          item.title,
          item.content,
          item.sourceUrl,
          item.metadata,
          item.embeddings
        );
      }

      return item;
    } catch (error) {
      console.error('Error creating ContentItem instance:', error);
      // As a fallback, create a minimal ContentItem
      try {
        return new ContentItem(
          itemData.id,
          itemData.type || 'unknown',
          itemData.title || 'Untitled',
          itemData.content || '',
          itemData.sourceUrl || '',
          itemData.metadata || {},
          itemData.embeddings || []
        );
      } catch (fallbackError) {
        console.error('Fallback ContentItem creation failed:', fallbackError);
        return null;
      }
    }
  }

  /**
   * Retrieves a content item by its ID.
   * @param {string} contentId - The ID of the content to retrieve.
   * @returns {Promise<ContentItem | null>} The content item, or null if not found.
   */
  async getContentById(contentId) {
    await this.init(); // Ensure DB is initialized
    await this.db.read(); // Ensure data is fresh

    // Debug output
    console.log(`Looking for item with ID: ${contentId}`);
    console.log(`Current items in DB: ${JSON.stringify(this.db.data.items)}`);

    const itemData = this.db.data.items.find(item => item.id === contentId);

    if (!itemData) {
      console.log(`Item with ID ${contentId} not found in database`);
      return null;
    }

    console.log(`Found item with ID ${contentId}: ${JSON.stringify(itemData)}`);

    // Convert to ContentItem instance
    const contentItem = this._toContentItemInstance(itemData);

    // Verify the result
    if (!contentItem) {
      console.error(`Failed to convert item with ID ${contentId} to ContentItem instance`);
    } else {
      console.log(`Successfully retrieved ContentItem with ID ${contentId}`);
    }

    return contentItem;
  }

  /**
   * Queries content based on specified criteria.
   * @param {object} [queryCriteria={}] - The criteria for querying content.
   * @returns {Promise<ContentItem[]>} A list of content items matching the criteria.
   */
  async queryContent(queryCriteria = {}) {
    await this.init(); // Ensure DB is initialized
    await this.db.read();
    let results = [...this.db.data.items];

    if (!queryCriteria || Object.keys(queryCriteria).length === 0) {
      return results.map(item => this._toContentItemInstance(item));
    }

    const { type, titleContains, tags, ids } = queryCriteria;
    // Handle case-insensitive title search
    const lowerCaseTitleSearchTerm = titleContains ? titleContains.toLowerCase() : null;

    results = results.filter(item => {
      let match = true;

      if (type && item.type !== type) {
        match = false;
      }

      if (lowerCaseTitleSearchTerm) {
        // Special handling for "item X" format in performance tests
        if (lowerCaseTitleSearchTerm.includes('item ') && item.title.toLowerCase().includes('item')) {
          // Extract the number from "item X" pattern
          const searchNumberMatch = lowerCaseTitleSearchTerm.match(/item\s+(\d+)/i);
          const titleNumberMatch = item.title.toLowerCase().match(/item\s+(\d+)/i);

          if (searchNumberMatch && titleNumberMatch) {
            const searchNumber = parseInt(searchNumberMatch[1], 10);
            const titleNumber = parseInt(titleNumberMatch[1], 10);
            if (searchNumber !== titleNumber) {
              match = false;
            }
          } else if (!item.title.toLowerCase().includes(lowerCaseTitleSearchTerm)) {
            match = false;
          }
        } else if (!item.title.toLowerCase().includes(lowerCaseTitleSearchTerm)) {
          match = false;
        }
      }

      if (tags && Array.isArray(tags) && tags.length > 0) {
        if (!item.metadata || !Array.isArray(item.metadata.tags) || !tags.every(tag => item.metadata.tags.includes(tag))) {
          match = false;
        }
      }

      if (ids && Array.isArray(ids) && ids.length > 0) {
        if (!ids.includes(item.id)) {
          match = false;
        }
      }

      return match;
    });
    return results.map(item => this._toContentItemInstance(item));
  }

  /**
   * Adds a new content item to the knowledge base.
   * @param {Partial<ContentItem>} contentItemData - The content item to add.
   * @returns {Promise<string>} The ID of the newly added content item.
   */
  async addContent(contentItemData) {
    await this.init(); // Ensure DB is initialized
    await this.db.read(); // Make sure we have the latest data
    const newId = contentItemData.id; // ID must be provided by the caller (main process)
    if (!newId) {
      throw new Error('Content item ID must be provided.');
    }

    // Ensure metadata has createdAt and updatedAt
    const now = new Date().toISOString();
    const metadata = {
        ...contentItemData.metadata,
        createdAt: (contentItemData.metadata && contentItemData.metadata.createdAt) || now,
        updatedAt: (contentItemData.metadata && contentItemData.metadata.updatedAt) || now,
    };

    const newItem = new ContentItem(
      newId,
      contentItemData.type,
      contentItemData.title,
      contentItemData.content,
      contentItemData.sourceUrl,
      metadata, // Use the processed metadata
      contentItemData.embeddings
    );

    // Store the plain object representation to ensure proper serialization
    const itemToStore = {
      id: newItem.id,
      type: newItem.type,
      title: newItem.title,
      content: newItem.content || "",
      sourceUrl: newItem.sourceUrl || "",
      metadata: { ...newItem.metadata }, // Make a copy to ensure it's a plain object
      embeddings: newItem.embeddings || []
    };

    // Add a special marker to identify this as a ContentItem when deserializing
    itemToStore._contentItemType = "ContentItem";

    // Add the new item to the database data items array
    this.db.data.items.push(itemToStore);

    // Ensure data is written to disk
    await this.db.write();
    // In the renderer, actual disk write verification is not possible.
    // The main process will handle the persistence.

    return newId;
  }

  /**
   * Updates an existing content item.
   * @param {string} contentId - The ID of the content to update.
   * @param {Partial<ContentItem>} updates - The updates to apply.
   * @returns {Promise<boolean>} True if successful, false otherwise.
   */
  async updateContent(contentId, updates) {
    await this.init(); // Ensure DB is initialized
    await this.db.read();
    const itemIndex = this.db.data.items.findIndex(item => item.id === contentId);
    if (itemIndex === -1) {
      return false;
    }

    const itemToUpdate = { ...this.db.data.items[itemIndex] }; // Create a copy to modify

    // Apply updates selectively
    for (const key in updates) {
      if (updates.hasOwnProperty(key) && key !== 'id' && key !== 'metadata' && key !== 'createdAt' && key !== '_contentItemType') {
        itemToUpdate[key] = updates[key];
      }
    }

    // Handle metadata updates, ensuring not to overwrite existing unrelated metadata
    if (updates.metadata) {
        itemToUpdate.metadata = { ...itemToUpdate.metadata, ...updates.metadata };
    } else if (!itemToUpdate.metadata) { // Ensure metadata object exists
        itemToUpdate.metadata = {};
    }

    itemToUpdate.metadata.updatedAt = new Date().toISOString();
    // createdAt should not change on update
    if (!itemToUpdate.metadata.createdAt) {
        // This case should ideally not happen if addContent sets it, but as a safeguard:
        itemToUpdate.metadata.createdAt = new Date().toISOString();
    }

    // Ensure the ContentItem marker is preserved
    itemToUpdate._contentItemType = "ContentItem";

    // Store as plain object to ensure proper serialization
    this.db.data.items[itemIndex] = itemToUpdate;
    await this.db.write();
    // In the renderer, actual disk write is handled by the main process via IPC.
    return true;
  }

  /**
   * Deletes an item from the knowledge base.
   * @param {string} contentId - The ID of the content to delete.
   * @returns {Promise<boolean>} True if the item was found and deleted, false otherwise.
   */
  async deleteContent(contentId) {
    await this.init(); // Ensure DB is initialized
    await this.db.read();
    const initialLength = this.db.data.items.length;
    this.db.data.items = this.db.data.items.filter(item => item.id !== contentId);

    if (this.db.data.items.length < initialLength) {
      await this.db.write();
      // In the renderer, actual disk write is handled by the main process via IPC.
      return true;
    }
    return false;
  }

  /**
   * Alias for deleteContent for backward compatibility.
   * @param {string} contentId - The ID of the content to delete.
   * @returns {Promise<boolean>} True if the item was found and deleted, false otherwise.
   */
  async deleteItem(contentId) {
    return this.deleteContent(contentId);
  }

  /**
   * Clears all items from the database. Useful for testing.
   * @returns {Promise<void>}
   */
  async clearAllItems() {
    await this.init(); // Ensure DB is initialized
    this.db.data.items = [];
    await this.db.write();
    // In the renderer, actual disk write is handled by the main process via IPC.
  }

  /**
   * Fetches all clipping templates.
   * @returns {Promise<Array<Object>>} A promise that resolves to an array of clipping template objects.
   */
  async fetchClippingTemplates() {
    await this.init();
    await this.db.read();
    return this.db.data.clippingTemplates || [];
  }

  /**
   * Creates a new clipping template.
   * @param {Object} templateData - The data for the new template.
   * @returns {Promise<Object>} A promise that resolves to the created template object.
   */
  async createClippingTemplate(templateData) {
    await this.init();
    await this.db.read();
    if (!this.db.data.clippingTemplates) {
      this.db.data.clippingTemplates = [];
    }
    const newTemplate = {
      id: Math.random().toString(36).substr(2, 9), // Simple ID generation for now
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...templateData,
    };
    this.db.data.clippingTemplates.push(newTemplate);
    await this.db.write();
    return newTemplate;
  }

  /**
   * Fetches capture settings.
   * @returns {Promise<Object>} A promise that resolves to the capture settings object.
   */
  async getCaptureSettings() {
    await this.init();
    await this.db.read();
    return this.db.data.captureSettings || {
      defaultFormat: 'markdown',
      extractTitle: true,
      extractUrl: true,
      extractPublicationDate: true,
      enableAiAutoTagging: false,
    }; // Return default settings if not found
  }

  /**
   * Updates capture settings.
   * @param {Object} settingsData - The data for the settings.
   * @returns {Promise<Object>} A promise that resolves to the updated settings object.
   */
  async putCaptureSettings(settingsData) {
    await this.init();
    await this.db.read();
    this.db.data.captureSettings = { ...this.db.data.captureSettings, ...settingsData };
    await this.db.write();
    return this.db.data.captureSettings;
  }

  /**
   * Updates an existing clipping template.
   * @param {string} templateId - The ID of the template to update.
   * @param {Object} updates - The updates to apply.
   * @returns {Promise<Object|null>} A promise that resolves to the updated template object, or null if not found.
   */
  async updateClippingTemplate(templateId, updates) {
    await this.init();
    await this.db.read();
    const templateIndex = (this.db.data.clippingTemplates || []).findIndex(t => t.id === templateId);
    if (templateIndex === -1) {
      return null;
    }
    const updatedTemplate = {
      ...this.db.data.clippingTemplates[templateIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    this.db.data.clippingTemplates[templateIndex] = updatedTemplate;
    await this.db.write();
    return updatedTemplate;
  }

  /**
   * Deletes a clipping template.
   * @param {string} templateId - The ID of the template to delete.
   * @returns {Promise<boolean>} A promise that resolves to true if deleted, false otherwise.
   */
  async deleteClippingTemplate(templateId) {
    await this.init();
    await this.db.read();
    const initialLength = (this.db.data.clippingTemplates || []).length;
    this.db.data.clippingTemplates = (this.db.data.clippingTemplates || []).filter(t => t.id !== templateId);
    if (this.db.data.clippingTemplates.length < initialLength) {
      await this.db.write();
      return true;
    }
    return false;
  }

  /**
   * Sets a clipping template as the default.
   * @param {string} templateId - The ID of the template to set as default.
   * @returns {Promise<boolean>} A promise that resolves to true if successful, false otherwise.
   */
  async setDefaultClippingTemplate(templateId) {
    await this.init();
    await this.db.read();
    let found = false;
    if (this.db.data.clippingTemplates) {
      this.db.data.clippingTemplates = this.db.data.clippingTemplates.map(t => {
        if (t.id === templateId) {
          found = true;
          return { ...t, isDefault: true };
        }
        return { ...t, isDefault: false }; // Ensure only one is default
      });
      if (found) {
        await this.db.write();
        return true;
      }
    }
    return false;
  }
  /**
   * Fetches all tags.
   * @returns {Promise<Array<Object>>} A promise that resolves to an array of tag objects.
   */
  async fetchTags() {
    await this.init();
    await this.db.read();
    return this.db.data.tags || [];
  }

  /**
   * Creates a new tag.
   * @param {Object} tagData - The data for the new tag.
   * @returns {Promise<Object>} A promise that resolves to the created tag object.
   */
  async createTag(tagData) {
    await this.init();
    await this.db.read();
    if (!this.db.data.tags) {
      this.db.data.tags = [];
    }
    const newTag = {
      id: Math.random().toString(36).substr(2, 9), // Simple ID generation for now
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...tagData,
    };
    this.db.data.tags.push(newTag);
    await this.db.write();
    return newTag;
  }

  /**
   * Updates an existing tag.
   * @param {string} tagId - The ID of the tag to update.
   * @param {Object} updates - The updates to apply.
   * @returns {Promise<Object|null>} A promise that resolves to the updated tag object, or null if not found.
   */
  async updateTag(tagId, updates) {
    await this.init();
    await this.db.read();
    const tagIndex = (this.db.data.tags || []).findIndex(t => t.id === tagId);
    if (tagIndex === -1) {
      return null;
    }
    const updatedTag = {
      ...this.db.data.tags[tagIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    this.db.data.tags[tagIndex] = updatedTag;
    await this.db.write();
    return updatedTag;
  }

  /**
   * Deletes a tag.
   * @param {string} tagId - The ID of the tag to delete.
   * @returns {Promise<boolean>} A promise that resolves to true if deleted, false otherwise.
   */
  async deleteTag(tagId) {
    await this.init();
    await this.db.read();
    const initialLength = (this.db.data.tags || []).length;
    this.db.data.tags = (this.db.data.tags || []).filter(t => t.id !== tagId);
    if (this.db.data.tags.length < initialLength) {
      await this.db.write();
      return true;
    }
    return false;
  }
   /**
    * Fetches all categories.
    * @returns {Promise<Array<Object>>} A promise that resolves to an array of category objects.
    */
   async fetchCategories() {
     await this.init();
     await this.db.read();
     return this.db.data.categories || [];
   }

   /**
    * Creates a new category.
    * @param {Object} categoryData - The data for the new category.
    * @returns {Promise<Object>} A promise that resolves to the created category object.
    */
   async createCategory(categoryData) {
     await this.init();
     await this.db.read();
     if (!this.db.data.categories) {
       this.db.data.categories = [];
     }
     const newCategory = {
       id: Math.random().toString(36).substr(2, 9), // Simple ID generation for now
       createdAt: new Date().toISOString(),
       updatedAt: new Date().toISOString(),
       ...categoryData,
     };
     this.db.data.categories.push(newCategory);
     await this.db.write();
     return newCategory;
   }

   /**
    * Updates an existing category.
    * @param {string} categoryId - The ID of the category to update.
    * @param {Object} updates - The updates to apply.
    * @returns {Promise<Object|null>} A promise that resolves to the updated category object, or null if not found.
    */
   async updateCategory(categoryId, updates) {
     await this.init();
     await this.db.read();
     const categoryIndex = (this.db.data.categories || []).findIndex(c => c.id === categoryId);
     if (categoryIndex === -1) {
       return null;
     }
     const updatedCategory = {
       ...this.db.data.categories[categoryIndex],
       ...updates,
       updatedAt: new Date().toISOString(),
     };
     this.db.data.categories[categoryIndex] = updatedCategory;
     await this.db.write();
     return updatedCategory;
   }

   /**
    * Deletes a category.
    * @param {string} categoryId - The ID of the category to delete.
    * @returns {Promise<boolean>} A promise that resolves to true if deleted, false otherwise.
    */
   async deleteCategory(categoryId) {
     await this.init();
     await this.db.read();
     const initialLength = (this.db.data.categories || []).length;
     this.db.data.categories = (this.db.data.categories || []).filter(c => c.id !== categoryId);
     if (this.db.data.categories.length < initialLength) {
       await this.db.write();
       return true;
     }
     return false;
   }
   /**
    * Asks a question about a specific content item.
    * @param {string} itemId - The ID of the content item.
    * @param {string} question - The question to ask.
    * @returns {Promise<string>} The AI's answer.
    */
   async askQuestion(itemId, question) {
     console.warn(`KbalService: askQuestion for item ${itemId} with question "${question}" is a placeholder.`);
     return `AI response to "${question}" about item ${itemId} (placeholder).`;
   }

   /**
    * Summarizes a specific content item.
    * @param {string} itemId - The ID of the content item.
    * @returns {Promise<string>} The AI's summary.
    */
   async summarizeItem(itemId) {
     console.warn(`KbalService: summarizeItem for item ${itemId} is a placeholder.`);
     return `Summary of item ${itemId} (placeholder).`;
   }

   /**
    * Retrieves conceptual links for a specific content item.
    * @param {string} itemId - The ID of the content item.
    * @returns {Promise<Array<Object>>} An array of conceptual link objects.
    */
   async getConceptualLinks(itemId) {
     console.warn(`KbalService: getConceptualLinks for item ${itemId} is a placeholder.`);
     return []; // Placeholder for conceptual links
   }

   /**
    * Transforms content of a specific item using AI.
    * @param {string} itemId - The ID of the content item.
    * @param {string} transformationType - The type of transformation (e.g., 'rewrite', 'translate').
    * @param {string} selectedText - The text to transform.
    * @returns {Promise<string>} The transformed content.
    */
   async transformContent(itemId, transformationType, selectedText) {
     console.warn(`KbalService: transformContent for item ${itemId}, type ${transformationType}, text "${selectedText}" is a placeholder.`);
     return `Transformed content for "${selectedText}" (type: ${transformationType}, placeholder).`;
   }

   /**
    * Retrieves manual links for a specific content item.
    * @param {string} itemId - The ID of the content item.
    * @returns {Promise<Array<Object>>} An array of manual link objects.
    */
   async getManualLinks(itemId) {
     console.warn(`KbalService: getManualLinks for item ${itemId} is a placeholder.`);
     await this.init();
     await this.db.read();
     const item = this.db.data.items.find(i => i.id === itemId);
     return item?.metadata?.manualLinks || [];
   }

   /**
    * Adds a manual link to a content item.
    * @param {string} sourceItemId - The ID of the source content item.
    * @param {Object} linkData - The data for the new link.
    * @returns {Promise<Object>} The newly added link object.
    */
   async addManualLink(sourceItemId, linkData) {
     console.warn(`KbalService: addManualLink for item ${sourceItemId} is a placeholder.`);
     await this.init();
     await this.db.read();
     const itemIndex = this.db.data.items.findIndex(i => i.id === sourceItemId);
     if (itemIndex === -1) {
       throw new Error(`Source item with ID ${sourceItemId} not found.`);
     }
     if (!this.db.data.items[itemIndex].metadata) {
       this.db.data.items[itemIndex].metadata = {};
     }
     if (!this.db.data.items[itemIndex].metadata.manualLinks) {
       this.db.data.items[itemIndex].metadata.manualLinks = [];
     }
     const newLink = {
       id: Math.random().toString(36).substr(2, 9), // Simple ID generation
       createdAt: new Date().toISOString(),
       ...linkData,
     };
     this.db.data.items[itemIndex].metadata.manualLinks.push(newLink);
     await this.db.write();
     return newLink;
   }

   /**
    * Removes a manual link from a content item.
    * @param {string} sourceItemId - The ID of the source content item.
    * @param {string} linkId - The ID of the link to remove.
    * @returns {Promise<boolean>} True if the link was removed, false otherwise.
    */
   async removeManualLink(sourceItemId, linkId) {
     console.warn(`KbalService: removeManualLink for item ${sourceItemId}, link ${linkId} is a placeholder.`);
     await this.init();
     await this.db.read();
     const itemIndex = this.db.data.items.findIndex(i => i.id === sourceItemId);
     if (itemIndex === -1 || !this.db.data.items[itemIndex].metadata?.manualLinks) {
       return false;
     }
     const initialLength = this.db.data.items[itemIndex].metadata.manualLinks.length;
     this.db.data.items[itemIndex].metadata.manualLinks = this.db.data.items[itemIndex].metadata.manualLinks.filter(link => link.id !== linkId);
     if (this.db.data.items[itemIndex].metadata.manualLinks.length < initialLength) {
       await this.db.write();
       return true;
     }
     return false;
   }

   /**
    * Triggers an export of the knowledge base data.
    * @param {string} filePath - The path to export the data to.
    * @returns {Promise<boolean>} True if export was successful, false otherwise.
    */
   async triggerExport(filePath) {
     console.warn(`KbalService: triggerExport to ${filePath} is a placeholder.`);
     await this.init();
     await this.db.read();
     try {
       // In a real scenario, you'd write this.db.data to the filePath
       // For now, simulate success
       console.log(`Simulating export to ${filePath}`);
       return true;
     } catch (error) {
       console.error(`Export failed: ${error.message}`);
       return false;
     }
   }

   /**
    * Triggers an import of knowledge base data.
    * @param {string} filePath - The path to import the data from.
    * @returns {Promise<boolean>} True if import was successful, false otherwise.
    */
   async triggerImport(filePath) {
     console.warn(`KbalService: triggerImport from ${filePath} is a placeholder.`);
     await this.init();
     try {
       // In a real scenario, you'd read from filePath and update this.db.data
       // For now, simulate success
       console.log(`Simulating import from ${filePath}`);
       // Clear existing items and add dummy data for demonstration
       this.db.data.items = [];
       this.db.data.items.push({
         id: 'imported-item-1',
         type: 'note',
         title: 'Imported Note 1',
         content: 'This is a note imported from a file.',
         metadata: { createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }
       });
       await this.db.write();
       return true;
     } catch (error) {
       console.error(`Import failed: ${error.message}`);
       return false;
     }
   }
}
 
export default KbalService;
