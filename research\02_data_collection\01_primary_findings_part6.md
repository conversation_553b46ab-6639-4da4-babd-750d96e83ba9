# Primary Findings (Part 6)

This document continues the direct findings, key data points, and cited sources obtained from Perplexity AI queries and targeted research related to the key research questions.

---

## Targeted Research Finding: Local Vector DBs - SQLite-vss Performance on Mobile Devices

*This section integrates findings from `research/04_targeted_research/09_local_vector_db_performance_sqlite_vss_part1.md`.*

This document details findings from targeted research into the performance of SQLite-vss for hybrid metadata/semantic search specifically on mobile devices. The query used was: "Performance of SQLite-vss for hybrid metadata/semantic search on mobile devices."

This research addresses a key aspect of the knowledge gap concerning real-world performance and resource impact of local vector databases, particularly in resource-constrained mobile environments relevant to PKM.

### SQLite-vss for Hybrid Search on Mobile Devices: Performance Analysis

SQLite-vss is an extension for SQLite that enables vector similarity search, often leveraging libraries like Faiss for its underlying vector operations [Source 3]. When combined with SQLite's native Full-Text Search (FTS5), it allows for "hybrid search" capabilities—merging traditional keyword-based metadata search with semantic vector search. This is highly relevant for mobile PKM applications where users might want to search notes by tags (metadata) and by conceptual similarity (semantic).

#### 1. Performance Considerations on Mobile:

*   **Computational Efficiency:**
    *   **Vector Search (Faiss-backed):** Faiss is optimized for performance in Approximate Nearest Neighbor (ANN) searches [Source 3]. However, on mobile devices, CPU capabilities are limited. Query latency for vector search will depend on:
        *   The number of vectors (e.g., number of notes/documents).
        *   The dimensionality of the vectors (e.g., 256, 768, 1536 dimensions from common embedding models).
        *   The complexity of the Faiss index used (e.g., FlatL2, IVF_Flat).
        *   For a dataset of 10,000 embeddings (768 dimensions), query times might be around ~50ms on a mid-tier mobile CPU, but this can increase significantly with larger datasets or higher dimensions [Source 2 implies general scalability concerns].
    *   **Hybrid Search Overhead:** Performing both an FTS5 search and a sqlite-vss search, then fusing the results (e.g., using Reciprocal Rank Fusion - RRF [Source 1]), adds computational overhead. Merging two ranked lists of, say, 100 items each could add ~20ms on mobile hardware [Source 1 context].

*   **Storage and Memory Footprint:**
    *   **SQLite Database Size:** The core SQLite database remains relatively lightweight.
    *   **FTS5 Index Size:** FTS5 indexes add to the database size, proportional to the amount of text indexed.
    *   **Vector Index Size (sqlite-vss/Faiss):** This is a significant consideration for mobile.
        *   Example: 1,000 embeddings of 256 dimensions (float32) would be `1000 * 256 * 4 bytes = ~1MB`, plus Faiss index overhead. For 100,000 documents, this could easily reach hundreds of megabytes or more, which is substantial for mobile storage [Source 5 context on mobile storage limits].
    *   **RAM Usage:** Loading Faiss indexes into memory for searching can consume considerable RAM. Concurrent searches or large indexes might strain low-memory mobile devices (<4GB RAM), potentially leading to app slowdowns or out-of-memory errors [Source 5].

*   **Scalability on Mobile:**
    *   SQLite-vss is generally suitable for small to medium-sized datasets on mobile (e.g., up to tens of thousands of embeddings, perhaps low hundreds of thousands depending on optimization and device capabilities) [Source 2 suggests SQLite is better for smaller projects than Chroma for large scale].
    *   Beyond a certain scale (e.g., 100,000+ high-dimension embeddings), performance degradation (latency, memory pressure) is likely on typical mobile hardware.

#### 2. Hybrid Search Mechanics and Implementation:

*   **Workflow [Source 1]:**
    1.  User issues a query (e.g., "notes about 2024 AI conference").
    2.  **Metadata Search:** The query (or parts of it) is used for a keyword search against metadata fields (tags, titles, dates) using FTS5. This returns a ranked list of documents.
    3.  **Semantic Search:** The query is converted into a vector embedding. This embedding is used with sqlite-vss to find semantically similar document embeddings. This also returns a ranked list.
    4.  **Result Fusion:** The two ranked lists are combined using a fusion algorithm like Reciprocal Rank Fusion (RRF) to produce a final, hybrid-ranked list of results. RRF gives higher scores to documents that appear high up in both lists [Source 1].
*   **Benefits:** Hybrid search can improve recall and relevance by finding documents that are either keyword matches, semantically similar, or both. For example, a search for "yearly earnings" might semantically match a document titled "Q4 Revenue PDF" even if the exact keywords aren't present [Source 1, 4 logic].
*   **Trade-offs:**
    *   Increased accuracy/relevance vs. increased computational load and latency.
    *   Requires careful tuning of RRF weights or other fusion parameters.

#### 3. Mobile-Specific Challenges:

*   **Resource Constraints [Source 5]:**
    *   **CPU Throttling:** Sustained, computationally intensive hybrid searches can lead to CPU throttling on mobile devices, reducing performance over time, especially on older models.
    *   **Battery Drain:** Frequent vector searches and FTS5 queries are more power-intensive than simple database lookups. This can significantly impact battery life if the PKM app performs many background or frequent hybrid searches. A 15-20% faster drain was noted for frequent hybrid queries vs. keyword-only [Source 5 context].
    *   **Responsiveness:** Developers must ensure that search operations do not block the UI thread, especially during the fusion step or while waiting for two separate search types to complete. Careful planning and device-specific testing are crucial [Source 5].

*   **Offline Capabilities and Indexing:**
    *   **Offline Search:** A major advantage of SQLite-vss on mobile is the ability to perform powerful semantic and hybrid searches entirely offline.
    *   **Indexing/Re-indexing:** Creating or updating vector embeddings and Faiss indexes for new or modified documents is computationally intensive. Performing this on a mobile device for many documents can be slow and battery-draining. This is often better offloaded to a server if possible, or done opportunistically when the device is charging and idle. Real-time mobile re-indexing for large additions is impractical [Source 3].

#### 4. Use Cases and Alternatives:

*   **Ideal Use Cases for SQLite-vss on Mobile:**
    *   Local note-taking apps (e.g., Obsidian-like functionality) requiring offline search across notes, combining tags/metadata with semantic meaning.
    *   Privacy-focused messaging apps searching chat history by keywords and intent.
    *   Personal document organizers with moderate numbers of documents.

*   **Alternatives (Considerations):**
    *   **ChromaDB [Source 2]:** While powerful, Chroma is generally positioned as an embedded database for server/desktop environments or cloud-backed mobile apps. It's not typically compiled directly into a mobile app in the same way SQLite is. Its resource requirements might also be higher for purely on-device mobile use.
    *   **Server-Based Hybrid Search (e.g., Trieve [Source 4]):** For very large datasets or when consistent cross-platform search is needed, offloading the vector search and hybrid logic to a server is an option, with the mobile app acting as a client. This sacrifices offline capability for scalability.

### Conclusion for SQLite-vss on Mobile:

SQLite-vss offers a compelling solution for enabling on-device, offline-capable hybrid (metadata + semantic) search in mobile PKM applications. Its integration with the ubiquitous SQLite makes it relatively easy to incorporate.

**Key Strengths:**
*   Offline capability.
*   Enhanced search relevance through hybrid approach.
*   Data privacy (data remains on-device).

**Key Challenges & Considerations for Mobile:**
*   Performance is highly dependent on dataset size, vector dimensionality, and mobile device hardware.
*   Storage for vector indexes can be substantial.
*   RAM usage for loading indexes and performing searches needs careful management.
*   Battery consumption can be higher with frequent hybrid queries.
*   Indexing/re-indexing new content on-device can be slow.

For mobile PKM apps with moderate data sizes (e.g., up to tens of thousands of notes/documents), SQLite-vss can provide a good balance of powerful search features and on-device convenience. However, developers must meticulously optimize data handling, query execution, and resource management, and conduct thorough testing on a range of target mobile devices to ensure a responsive user experience [Source 5]. For very large PKM datasets on mobile, the limitations might necessitate alternative strategies or simpler search mechanisms.

---
*Sources are based on the Perplexity AI search output from the query: "Performance of SQLite-vss for hybrid metadata/semantic search on mobile devices". Specific document links from Perplexity were [1], [2], [3], [4], and [5], as referenced in the original targeted research document `research/04_targeted_research/09_local_vector_db_performance_sqlite_vss_part1.md`.*