// AI-VERIFIABLE: Placeholder test file for IntentRecognizer.
// These tests will verify the logic for identifying user intent.

import IntentRecognizer from '../intent-recognition/intentRecognizer';

describe('IntentRecognizer - Unit Tests', () => {
    let recognizer;

    beforeEach(() => {
        recognizer = new IntentRecognizer();
    });

    // Helper to create a mock parsedQuery object
    const createParsedQuery = (original, tokensOverride = null, keywordsOverride = null) => {
        // Simulate basic parsing if overrides aren't provided
        const cleaned = original.toLowerCase().replace(/[.,?!;:()[\]{}]/g, ' ').replace(/['"`]/g, '').replace(/\s+/g, ' ').trim();
        const defaultTokens = cleaned.split(' ').filter(t => t.length > 0);
        const defaultKeywords = defaultTokens.filter(t => !recognizer.stopwords?.has(t) && t.length > 0); // recognizer.stopwords might not be available if constructor changes

        return {
            original: original,
            cleanedQuery: cleaned,
            tokens: tokensOverride || defaultTokens,
            keywords: keywordsOverride || defaultKeywords,
        };
    };


    test('AI-VERIFIABLE: should recognize "search" intent from keywords', async () => {
        const parsedQuery = createParsedQuery(
            "find documents about AI",
            ["find", "documents", "about", "ai"],
            ["find", "documents", "ai"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.SEARCH);
        expect(result.confidence).toBeGreaterThanOrEqual(0.6); // Adjusted based on new logic
    });

    test('AI-VERIFIABLE: should recognize "question_answering" intent from question words', async () => {
        const parsedQuery = createParsedQuery(
            "what is machine learning?",
            ["what", "is", "machine", "learning"], // ? removed by mock parser
            ["what", "machine", "learning"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.QUESTION_ANSWERING);
        expect(result.confidence).toBeGreaterThanOrEqual(0.75);
    });

    test('AI-VERIFIABLE: should recognize "question_answering" intent from question mark', async () => {
        const parsedQuery = createParsedQuery(
            "tell me about TDD?",
            ["tell", "me", "about", "tdd"], // ? removed by mock parser
            ["tell", "tdd"] // "about", "me" are stopwords
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.QUESTION_ANSWERING);
        expect(result.confidence).toBe(0.85); // Question mark gives higher confidence
    });

    test('AI-VERIFIABLE: should recognize "summarize" intent', async () => {
        const parsedQuery = createParsedQuery(
            "summarize this article for me",
            ["summarize", "this", "article", "for", "me"],
            ["summarize", "article"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.SUMMARIZE);
        expect(result.confidence).toBeGreaterThanOrEqual(0.7);
    });

    test('AI-VERIFIABLE: should recognize "summarize" intent with "tl;dr"', async () => {
        const parsedQuery = createParsedQuery(
            "tl;dr on quantum computing",
            ["tldr", "on", "quantum", "computing"],
            ["tldr", "quantum", "computing"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.SUMMARIZE);
        expect(result.confidence).toBeGreaterThanOrEqual(0.7);
    });


    test('AI-VERIFIABLE: should recognize "content_transformation" intent for "extract facts"', async () => {
        const parsedQuery = createParsedQuery(
            "extract key facts from this document",
            ["extract", "key", "facts", "from", "this", "document"],
            ["extract", "key", "facts", "document"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.CONTENT_TRANSFORMATION);
        expect(result.confidence).toBeGreaterThanOrEqual(0.75);
    });

    test('AI-VERIFIABLE: should recognize "conceptual_linking" intent', async () => {
        const parsedQuery = createParsedQuery(
            "show relationship between AI and ethics",
            ["show", "relationship", "between", "ai", "and", "ethics"],
            ["show", "relationship", "ai", "ethics"] // "between", "and" are stopwords
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.CONCEPTUAL_LINKING);
        expect(result.confidence).toBeGreaterThanOrEqual(0.75);
    });

    test('AI-VERIFIABLE: should default to "search" for ambiguous queries with keywords', async () => {
        const parsedQuery = createParsedQuery(
            "random words together",
            ["random", "words", "together"],
            ["random", "words", "together"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.SEARCH); // Changed from UNKNOWN
        expect(result.confidence).toBe(0.6); // Adjusted based on current logic for general keyword presence
    });
    
    test('AI-VERIFIABLE: should return "unknown" if no keywords and no strong patterns', async () => {
        const parsedQuery = createParsedQuery(
            "the of and if", // All stopwords
            ["the", "of", "and", "if"],
            [] // No keywords
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.UNKNOWN);
        expect(result.confidence).toBe(0.3);
    });


    test('AI-VERIFIABLE: should throw an error if parsedQuery is invalid or missing required fields', async () => {
        const errorMsg = 'Parsed query object with original, tokens, and keywords is required for intent recognition.';
        await expect(recognizer.recognizeIntent(null)).rejects.toThrow(errorMsg);
        await expect(recognizer.recognizeIntent({})).rejects.toThrow(errorMsg);
        await expect(recognizer.recognizeIntent({ original: "test" })).rejects.toThrow(errorMsg);
        await expect(recognizer.recognizeIntent({ original: "test", tokens: [] })).rejects.toThrow(errorMsg);
        await expect(recognizer.recognizeIntent({ original: "test", keywords: [] })).rejects.toThrow(errorMsg);
    });

    test('AI-VERIFIABLE: Q&A intent should take precedence over search if question words are present', async () => {
        const parsedQuery = createParsedQuery(
            "How to find good articles?",
            ["how", "to", "find", "good", "articles"],
            ["how", "find", "good", "articles"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.QUESTION_ANSWERING);
        expect(result.confidence).toBe(0.85); // Has question mark
    });

    test('AI-VERIFIABLE: Summarize intent should take precedence over general transformation', async () => {
        const parsedQuery = createParsedQuery(
            "Summarize and transform this text", // "transform" is also a transformation keyword
            ["summarize", "and", "transform", "this", "text"],
            ["summarize", "transform", "text"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.SUMMARIZE);
        expect(result.confidence).toBeGreaterThanOrEqual(0.7); // Based on "summarize"
    });
    
    test('AI-VERIFIABLE: "explain" should trigger Q&A intent', async () => {
        const parsedQuery = createParsedQuery(
            "explain the concept of recursion",
            ["explain", "the", "concept", "of", "recursion"],
            ["explain", "concept", "recursion"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.QUESTION_ANSWERING);
        expect(result.confidence).toBe(0.75);
    });

    test('AI-VERIFIABLE: query with only "search" keyword', async () => {
        const parsedQuery = createParsedQuery(
            "search",
            ["search"],
            ["search"]
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        expect(result.type).toBe(recognizer.knownIntents.SEARCH);
        expect(result.confidence).toBe(0.7); // "search" keyword
    });

    test('AI-VERIFIABLE: query with only "show me" keyword (which is not a stopword)', async () => {
        // Need to adjust createParsedQuery or stopwords in IntentRecognizer for "show me" to be a keyword
        // For now, assuming "show" and "me" are processed. "me" is a stopword.
         const parsedQuery = createParsedQuery(
            "show me the files",
            ["show", "me", "the", "files"],
            ["show", "files"] // "me", "the" are stopwords
        );
        const result = await recognizer.recognizeIntent(parsedQuery);
        // "show" is not explicitly a search keyword in the current list, so it might default or have lower confidence
        // The logic is: if (searchKeywords.some(kw => keywords.includes(kw)) || keywords.length > 0)
        // searchKeywords = ['find', 'search', 'look', 'locate', 'show me', 'get me'];
        // "show" is not in searchKeywords. "files" is a keyword.
        // So it will fall into the `keywords.length > 0` part of the OR.
        // Confidence will be Math.min(0.8, Math.max(0.5, 0.1 * 0 + 0.6)) = Math.min(0.8, 0.6) = 0.6
        expect(result.type).toBe(recognizer.knownIntents.SEARCH);
        expect(result.confidence).toBe(0.6);
    });


    // Add more tests for different phrasing, confidence scores,
    // and edge cases as the intent recognition logic becomes more sophisticated.
});

// AI-VERIFIABLE: End of intentRecognizer.test.js