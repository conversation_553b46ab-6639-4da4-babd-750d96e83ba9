import React, { useState } from 'react';

interface NotesInputProps {
  initialNotes: string;
  onNotesChange: (notes: string) => void;
}

const NotesInput: React.FC<NotesInputProps> = ({ initialNotes, onNotesChange }) => {
  const [notes, setNotes] = useState<string>(initialNotes);

  const handleNotesChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newNotes = event.target.value;
    setNotes(newNotes);
    onNotesChange(newNotes);
  };

  return (
    <div data-testid="notes-input">
      <h4>Notes</h4>
      <textarea
        data-testid="notes-textarea"
        value={notes}
        onChange={handleNotesChange}
        placeholder="Add your personal notes here"
        rows={4}
        cols={40}
      />
    </div>
  );
};

export default NotesInput;