// AI-VERIFIABLE: Placeholder for RequestRouter logic.
// This class will determine where to send the processed query.
import IntentRecognizer from '../intent-recognition/intentRecognizer.js'; // To access knownIntents

class RequestRouter {
    constructor() {
        // Initialization logic, e.g., defining routing rules or service endpoints.
        console.log('[RequestRouter] Initialized.');
        this.services = {
            SEARCH_SERVICE: 'SearchService',
            AI_SERVICES_GATEWAY: 'AIServicesGateway',
            CONCEPTUAL_LINKING_ENGINE: 'ConceptualLinkingEngine', // As per architecture
            DEFAULT_HANDLER: 'DefaultHandler',
        };
        // Access known intents directly from the source for consistency
        this.knownIntents = new IntentRecognizer().knownIntents;
    }

    /**
     * Routes a processed query to the appropriate downstream service.
     * @param {object} intent - The recognized intent object from IntentRecognizer.
     * @param {object} entities - The extracted entities object from EntityExtractor.
     *                          Expected to contain: keywords, namedEntities, dates, authors, relationships.
     * @param {object} parsedQuery - The original parsed query from QueryParser.
     * @returns {object} An object describing the routing decision,
     *                   e.g., { service: 'SearchService', params: {...} }.
     */
    routeRequest(intent, entities, parsedQuery) {
        if (!intent || typeof intent !== 'object' || !intent.type) {
            throw new Error('Intent object with type is required for request routing.');
        }
        if (!entities || typeof entities !== 'object' || !entities.keywords) { // Ensure keywords array exists
            throw new Error('Entities object with at least a keywords array is required for request routing.');
        }
        if (!parsedQuery || typeof parsedQuery !== 'object' || !parsedQuery.original) {
            throw new Error('Parsed query object with original query is required for request routing.');
        }

        console.log('[RequestRouter] Routing request for intent:', intent.type, 'with entities:', JSON.stringify(entities, null, 2));

        let targetService = null;
        let requestParameters = {
            originalQuery: parsedQuery.original,
            cleanedQuery: parsedQuery.cleanedQuery,
            intent: intent, // Pass the full intent object (type, confidence, details)
            entities: entities, // Pass the full entities object
        };

        switch (intent.type) {
            case this.knownIntents.SEARCH:
                targetService = this.services.SEARCH_SERVICE;
                requestParameters.searchTerms = [...(entities.keywords || [])]; // Ensure it's an array
                requestParameters.filters = {};
                if (entities.authors && entities.authors.length > 0) {
                    requestParameters.filters.author = entities.authors.join(', '); // Or handle as array if service supports
                }
                if (entities.dates && entities.dates.length > 0) {
                    // Simplistic: use the first date for now. Could be more complex (ranges, etc.)
                    requestParameters.filters.date = entities.dates[0];
                }
                // Add other named entities as potential filterable concepts/topics
                if (entities.namedEntities && entities.namedEntities.length > 0) {
                    requestParameters.filters.concepts = entities.namedEntities
                        .filter(ne => ne.type !== 'DATE_NE') // DATE_NE is already handled
                        .map(ne => ne.text);
                }
                // Add relationship-based filters if applicable
                if (entities.relationships && entities.relationships.length > 0) {
                    entities.relationships.forEach(rel => {
                        if (rel.type === 'topicOf' && rel.topic) {
                            requestParameters.filters.topic = rel.topic;
                        }
                        if (rel.type === 'publishedAfter' && rel.date) {
                            requestParameters.filters.publishedAfter = rel.date;
                        }
                        // 'authoredBy' is handled by entities.authors
                    });
                }
                break;
            case this.knownIntents.QUESTION_ANSWERING:
                targetService = this.services.AI_SERVICES_GATEWAY;
                requestParameters.task = 'qa';
                requestParameters.question = parsedQuery.original;
                requestParameters.contextEntities = entities; // Provide entities for context
                break;
            case this.knownIntents.SUMMARIZE:
                targetService = this.services.AI_SERVICES_GATEWAY;
                requestParameters.task = 'summarize';
                // Downstream service will need to know what to summarize (e.g., based on current view or search results scope)
                // For now, just pass the query that triggered it.
                requestParameters.sourceQuery = parsedQuery.original;
                break;
            case this.knownIntents.CONTENT_TRANSFORMATION:
                targetService = this.services.AI_SERVICES_GATEWAY;
                requestParameters.task = 'transform';
                requestParameters.transformationDetails = {
                    // Details could be inferred from entities or specific keywords
                    // e.g., "extract facts", "convert to bullet points"
                    query: parsedQuery.original,
                    entities: entities,
                };
                break;
            case this.knownIntents.CONCEPTUAL_LINKING:
                // As per architecture, CLE is local, but advanced might use AI_SERVICES_GATEWAY
                // For now, assume core linking is handled by a dedicated engine.
                targetService = this.services.CONCEPTUAL_LINKING_ENGINE;
                requestParameters.linkFocus = entities.keywords; // Primary keywords to find links for/between
                requestParameters.contextEntities = entities.namedEntities;
                break;
            case this.knownIntents.UNKNOWN:
            default:
                console.warn(`[RequestRouter] Unknown or unhandled intent type: ${intent.type}. Routing to DefaultHandler.`);
                targetService = this.services.DEFAULT_HANDLER;
                requestParameters.error = `Unable to determine appropriate service for the query intent: ${intent.type}.`;
                // Fallback to a general search if there are keywords
                if (entities.keywords && entities.keywords.length > 0) {
                    targetService = this.services.SEARCH_SERVICE;
                    requestParameters.searchTerms = entities.keywords;
                    requestParameters.filters = {}; // No specific filters for unknown intent fallback
                    requestParameters.info = 'Fallback to general search due to unhandled intent.';
                     delete requestParameters.error; // Remove error if falling back to search
                }
                break;
        }

        const routingDecision = {
            service: targetService,
            params: requestParameters,
            timestamp: new Date().toISOString(),
        };

        console.log('[RequestRouter] Routing decision:', JSON.stringify(routingDecision, null, 2));
        return routingDecision;
    }
}

export default RequestRouter;
// AI-VERIFIABLE: End of RequestRouter.js