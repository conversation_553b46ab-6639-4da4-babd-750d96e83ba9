.capture-controls {
  margin-top: 20px;
  text-align: center;
}

.capture-button {
  background-color: #28a745; /* Green for capture */
  color: white;
  border: none;
  padding: 12px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 1.05em;
  cursor: pointer;
  border-radius: 5px;
  transition: background-color 0.3s ease, opacity 0.3s ease;
  min-width: 150px; /* Ensure button has a decent width */
}

.capture-button:hover {
  background-color: #218838; /* Darker green */
}

.capture-button:disabled,
.capture-button.capturing {
  background-color: #6c757d; /* Grey when disabled or capturing */
  cursor: not-allowed;
  opacity: 0.7;
}

.status-message {
  margin-top: 12px;
  font-weight: bold;
  font-size: 0.95em;
}

.status-message.success {
  color: #28a745; /* Green */
}

.status-message.error {
  color: #dc3545; /* Red */
}