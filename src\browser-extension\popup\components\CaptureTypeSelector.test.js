import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import CaptureTypeSelector from './CaptureTypeSelector';

describe('CaptureTypeSelector Component', () => {
  const mockOnTypeChange = jest.fn();

  beforeEach(() => {
    mockOnTypeChange.mockClear();
  });

  test('renders the heading', () => {
    render(<CaptureTypeSelector selectedType="fullPage" onTypeChange={mockOnTypeChange} />);
    expect(screen.getByText(/Select Capture Type:/i)).toBeInTheDocument();
  });

  test('renders all capture type options', () => {
    render(<CaptureTypeSelector selectedType="fullPage" onTypeChange={mockOnTypeChange} />);
    expect(screen.getByLabelText(/Full Page/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Selection/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Bookmark/i)).toBeInTheDocument();
  });

  test('correctly checks the initially selected type', () => {
    render(<CaptureTypeSelector selectedType="selection" onTypeChange={mockOnTypeChange} />);
    expect(screen.getByLabelText(/Selection/i)).toBeChecked();
    expect(screen.getByLabelText(/Full Page/i)).not.toBeChecked();
    expect(screen.getByLabelText(/Bookmark/i)).not.toBeChecked();
  });

  test('calls onTypeChange with the correct value when a new type is selected', () => {
    render(<CaptureTypeSelector selectedType="fullPage" onTypeChange={mockOnTypeChange} />);
    
    const bookmarkRadio = screen.getByLabelText(/Bookmark/i);
    fireEvent.click(bookmarkRadio);
    
    expect(mockOnTypeChange).toHaveBeenCalledTimes(1);
    expect(mockOnTypeChange).toHaveBeenCalledWith('bookmark');
  });

  test('updates checked status when props change (though not typical user interaction)', () => {
    const { rerender } = render(
      <CaptureTypeSelector selectedType="fullPage" onTypeChange={mockOnTypeChange} />
    );
    expect(screen.getByLabelText(/Full Page/i)).toBeChecked();

    rerender(<CaptureTypeSelector selectedType="bookmark" onTypeChange={mockOnTypeChange} />);
    expect(screen.getByLabelText(/Bookmark/i)).toBeChecked();
    expect(screen.getByLabelText(/Full Page/i)).not.toBeChecked();
  });
});