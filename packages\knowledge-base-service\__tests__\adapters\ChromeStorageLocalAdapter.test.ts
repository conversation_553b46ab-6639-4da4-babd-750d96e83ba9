import { jest, describe, it, expect, beforeEach } from '@jest/globals';
import { ChromeStorageLocalAdapter } from '../../src/adapters/ChromeStorageLocalAdapter';

// Mock chrome.storage.local
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let mockStorage: { [key: string]: any } = {};

const mockChrome = {
  storage: {
    local: {
      get: jest.fn(async (keyOrKeys) => {
        if (typeof keyOrKeys === 'string') {
          return { [keyOrKeys]: mockStorage[keyOrKeys] };
        }
        if (Array.isArray(keyOrKeys)) {
          const result: { [key: string]: unknown } = {};
          keyOrKeys.forEach(k => {
            result[k] = mockStorage[k];
          });
          return result;
        }
        // For "get all" scenario (keyOrKeys is null or undefined)
        if (keyOrKeys === null || typeof keyOrKeys === 'undefined') {
          return { ...mockStorage };
        }
        // For object scenario
        const result: { [key: string]: unknown } = {};
        Object.keys(keyOrKeys).forEach(k => {
          result[k] = mockStorage[k] || keyOrKeys[k]; // Return default if not found
        });
        return result;

      }),
      set: jest.fn(async (items: { [key: string]: unknown; }) => { // Added type for items
        mockStorage = { ...mockStorage, ...items };
      }),
      clear: jest.fn(async () => {
        mockStorage = {};
      }),
      // Add other methods if your adapter uses them, e.g., remove
    },
  },
  runtime: {
    lastError: null as { message?: string } | null,
  },
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
(global as any).chrome = mockChrome;

describe('ChromeStorageLocalAdapter', () => {
  const storageKey = 'testKnowledgeBase';
  let adapter: ChromeStorageLocalAdapter<{ testData: string }>;

  beforeEach(() => {
    // Reset mocks and storage before each test
    mockStorage = {};
    mockChrome.storage.local.get.mockClear();
    mockChrome.storage.local.set.mockClear();
    mockChrome.storage.local.clear.mockClear();
    mockChrome.runtime.lastError = null;
    adapter = new ChromeStorageLocalAdapter(storageKey);
  });

  it('should throw an error if storageKey is not provided', () => {
    expect(() => new ChromeStorageLocalAdapter('')).toThrow('A storageKey must be provided for ChromeStorageLocalAdapter.');
  });

  describe('read', () => {
    it('should return null if chrome.storage.local is not available', async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const originalChrome = (global as any).chrome;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (global as any).chrome = undefined;
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => { /* do nothing */});
      
      const data = await adapter.read();
      expect(data).toBeNull();
      expect(consoleWarnSpy).toHaveBeenCalledWith('Chrome storage API is not available. Returning null.');
      
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (global as any).chrome = originalChrome; // Restore
      consoleWarnSpy.mockRestore();
    });

    it('should return null if the key does not exist in storage', async () => {
      mockChrome.storage.local.get.mockResolvedValueOnce({});
      const data = await adapter.read();
      expect(data).toBeNull();
      expect(mockChrome.storage.local.get).toHaveBeenCalledWith(storageKey);
    });

    it('should return the data if the key exists in storage', async () => {
      const testData = { testData: 'hello world' };
      mockStorage[storageKey] = testData;
      // No need to mockResolvedValueOnce here as the default mock behavior will pick it up
      
      const data = await adapter.read();
      expect(data).toEqual(testData);
      expect(mockChrome.storage.local.get).toHaveBeenCalledWith(storageKey);
    });

    it('should return null and log an error if chrome.storage.local.get throws an error', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => { /* do nothing */});
      const testError = new Error('Storage get error');
      mockChrome.storage.local.get.mockRejectedValueOnce(testError);

      const data = await adapter.read();
      expect(data).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error reading from chrome.storage.local:', testError);
      consoleErrorSpy.mockRestore();
    });
  });

  describe('write', () => {
    it('should not write and warn if chrome.storage.local is not available', async () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const originalChrome = (global as any).chrome;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (global as any).chrome = undefined;
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => { /* do nothing */});

      const testData = { testData: 'write test' };
      await adapter.write(testData);

      expect(mockChrome.storage.local.set).not.toHaveBeenCalled();
      expect(consoleWarnSpy).toHaveBeenCalledWith('Chrome storage API is not available. Write operation skipped.');
      
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (global as any).chrome = originalChrome; // Restore
      consoleWarnSpy.mockRestore();
    });

    it('should write the data to storage under the specified key', async () => {
      const testData = { testData: 'write test' };
      await adapter.write(testData);
      expect(mockChrome.storage.local.set).toHaveBeenCalledWith({ [storageKey]: testData });
      expect(mockStorage[storageKey]).toEqual(testData);
    });

    it('should throw an error and log if chrome.storage.local.set throws an error', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => { /* do nothing */});
      const testError = new Error('Storage set error');
      mockChrome.storage.local.set.mockRejectedValueOnce(testError);
      const testData = { testData: 'error on write' };

      await expect(adapter.write(testData)).rejects.toThrow(testError);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error writing to chrome.storage.local:', testError);
      consoleErrorSpy.mockRestore();
    });
  });
});