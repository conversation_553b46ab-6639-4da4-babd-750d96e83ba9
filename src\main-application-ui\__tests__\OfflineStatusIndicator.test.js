import React from 'react';
import { render, screen } from '@testing-library/react';
import OfflineStatusIndicator from '../renderer/components/OfflineStatusIndicator';

describe('OfflineStatusIndicator', () => {
  test('displays "Online" when isOnline is true', () => {
    render(<OfflineStatusIndicator isOnline={true} />);
    expect(screen.getByText('Status: Online')).toBeInTheDocument();
    expect(screen.queryByText('Status: Offline')).not.toBeInTheDocument();
  });

  test('displays "Offline" when isOnline is false', () => {
    render(<OfflineStatusIndicator isOnline={false} />);
    expect(screen.getByText('Status: Offline')).toBeInTheDocument();
    expect(screen.queryByText('Status: Online')).not.toBeInTheDocument();
  });

  test('applies "online" class when isOnline is true', () => {
    render(<OfflineStatusIndicator isOnline={true} />);
    expect(screen.getByTestId('offline-status-indicator')).toHaveClass('status-indicator online');
  });

  test('applies "offline" class when isOnline is false', () => {
    render(<OfflineStatusIndicator isOnline={false} />);
    expect(screen.getByTestId('offline-status-indicator')).toHaveClass('status-indicator offline');
  });

  // Test for default prop value (assuming default is online)
  test('displays "Online" by default if isOnline prop is not provided', () => {
    render(<OfflineStatusIndicator />);
    expect(screen.getByText('Status: Online')).toBeInTheDocument();
    expect(screen.getByTestId('offline-status-indicator')).toHaveClass('status-indicator online');
  });
});