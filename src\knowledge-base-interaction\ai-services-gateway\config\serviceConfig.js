// src/knowledge-base-interaction/ai-services-gateway/config/serviceConfig.js

/**
 * @file Manages configurations for various AI services.
 *
 * This includes model names, endpoints (if not part of a client library),
 * default parameters, and other service-specific settings.
 */

// AI-verifiable: Check for the existence of this placeholder structure.

const serviceConfig = {
    // Configuration for Question & Answering services
    qa: {
        defaultProvider: 'gemini', // e.g., 'gemini', 'openai', 'local_model_A'
        providers: {
            gemini: {
                model: 'gemini-pro', // Specific model for Q&A
                // endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent', // Handled by SDK
                defaultParams: {
                    temperature: 0.7,
                    maxOutputTokens: 256,
                },
            },
            // Example for a local model
            // local_model_A: {
            //     modelPath: '/path/to/local_model_A_weights',
            //     adapter: 'localQAServiceAdapter.js', // A custom adapter might be needed
            //     defaultParams: { /* ... */ },
            // },
            // Example for another cloud provider
            // openAI: {
            //     model: 'gpt-3.5-turbo-instruct',
            //     defaultParams: { /* ... */ },
            // }
        },
    },

    // Configuration for Content Transformation services
    transform: {
        defaultProvider: 'gemini',
        providers: {
            gemini: {
                model: 'gemini-pro', // Can use the same base model or a specialized one
                // endpoint: '...',
                defaultParams: {
                    temperature: 0.5,
                    // Parameters specific to transformation tasks
                },
            },
            // Add other providers or local models as needed
        },
    },

    // Configuration for Conceptual Linking services
    link: {
        defaultProvider: 'gemini', // Or a specialized graph/NLP service
        providers: {
            gemini: { // Gemini might be used for entity extraction as part of linking
                model: 'gemini-pro',
                defaultParams: {
                    // Parameters for entity recognition, relationship extraction
                },
            },
            // Example for a dedicated graph service
            // knowledgeGraphService: {
            //     endpoint: 'https://api.knowledgegraph.com/v1/link',
            //     defaultParams: { /* ... */ },
            // }
        },
    },

    // Add configurations for other AI services as the gateway expands
    // e.g., summarization (though excluded for now), sentiment analysis, etc.
};

/**
 * Loads or retrieves service configurations.
 * In a real application, parts of this might be dynamically loaded
 * or overridden by user settings or environment variables.
 *
 * @async
 * @returns {Promise<object>} The service configurations.
 */
async function loadServiceConfig() {
    // AI-verifiable: Log that service config loading is attempted.
    console.log('AI Services Gateway Config: Attempting to load service configurations (placeholder)...');

    // For this boilerplate, we just return the placeholder object.
    // In a real app, you might fetch this from a remote config service,
    // merge with local defaults, etc.

    // AI-verifiable: Confirm service config structure is available.
    console.log('AI Services Gateway Config: Service configurations loaded (placeholder).');
    return Promise.resolve(serviceConfig);
}

// To be used by the gateway or handlers after loading.
// Example:
// import { loadServiceConfig } from './serviceConfig.js';
// let loadedConfig;
// loadServiceConfig().then(config => loadedConfig = config);

export { serviceConfig, loadServiceConfig };