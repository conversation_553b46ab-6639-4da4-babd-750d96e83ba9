import React, { useState, useEffect } from 'react';
import './App.css'; // Import the CSS file
// import NavigationFiltersPane from './components/NavigationFiltersPane'; // To be used within KnowledgeBaseView
// import ItemListPane from './components/ItemListPane'; // To be used within KnowledgeBaseView
import DetailViewPane from './components/DetailViewPane';
import SettingsView from './components/SettingsView'; // Import SettingsView
import useStore from './store/useStore'; // Import the Zustand store

import KnowledgeBaseView from './components/KnowledgeBaseView';
import AIInteractionPanel from './components/AIInteractionPanel';
import ConceptualLinksDisplay from './components/ConceptualLinksDisplay';
import OfflineStatusIndicator from './components/OfflineStatusIndicator';

function App() {
  const {
    items, // All items, or initial set of items
    selectedItemId,
    selectItem,
    clearSelectedItem,
    searchTerm,
    setSearchTerm, // Will be used by KnowledgeBaseView internally or via prop
    searchResults,
    performSearch,
    // Filter related state and actions
    filterTags, // Available tags for filtering UI
    selectedTags,
    setSelectedTags,
    filterCategories, // Available categories for filtering UI
    selectedCategories,
    setSelectedCategories,
    dateRangeFilter, // { startDate, endDate }
    setDateRangeFilter,
    sourceFilter,
    setSourceFilter,
    sortCriteria, // { by: string, order: string }
    setSortCriteria,
  } = useStore();

  const [showSettings, setShowSettings] = useState(false);
  const [showAiPanel, setShowAiPanel] = useState(false);
  const [aiPanelArgs, setAiPanelArgs] = useState(null); // e.g., { type: 'qa', itemId: '123' }
  const {
    conceptualLinks,
    fetchConceptualLinks,
    clearConceptualLinks,
    conceptualLinksLoading,
    conceptualLinksError,
    isOnline, // From store, managed by OfflineAccessHandler
    checkOnlineStatus,
    qaQuestion,
    qaAnswer,
    qaLoading,
    qaError,
    setQaQuestion,
    submitQaQuestion,
    clearQa,
    summary,
    summaryLoading,
    summaryError,
    summaryGenerated,
    fetchSummary,
    clearSummary,
    selectedTransformationType,
    transformedContent,
    transformationLoading,
    transformationError,
    setSelectedTransformationType,
    transformItemContent,
    clearTransformationState,
    savingNewItemLoading,
    savingNewItemError,
    saveTransformedContentAsNewItem,
    clearSaveNewItemStatus,
  } = useStore();

  const [showConceptualLinks, setShowConceptualLinks] = useState(false);

  useEffect(() => {
    if (selectedItemId) {
      setShowConceptualLinks(true);
      fetchConceptualLinks(selectedItemId);
    } else {
      setShowConceptualLinks(false);
      clearConceptualLinks();
    }
  }, [selectedItemId, fetchConceptualLinks, clearConceptualLinks]);

  // Check online status on mount and periodically
  useEffect(() => {
    checkOnlineStatus();
    const interval = setInterval(checkOnlineStatus, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [checkOnlineStatus]);

  // Fetch initial items or perform initial search
  useEffect(() => {
    performSearch(searchTerm);
  }, [performSearch, searchTerm]);

  const handleSelectItem = (item) => {
    if (item && item.id) {
      selectItem(item.id);
      setShowConceptualLinks(true); // Always show conceptual links if an item is selected
    } else {
      clearSelectedItem();
      setShowConceptualLinks(false);
    }
  };

  const handleDateFilterChange = (value) => {
    // Assuming value is a string like 'today', 'last7days', etc.
    // You might need to convert this to actual date objects for the store's dateRangeFilter
    // For simplicity, let's just pass the string for now and handle conversion in performSearch or store.
    setStore((state) => ({
      dateRangeFilter: {
        ...state.dateRangeFilter,
        selectedDateOption: value, // Add a new state to store the selected option string
      },
    }));
    performSearch(searchTerm);
  };

  const handleSourceFilterChange = (value) => {
    setSourceFilter(value);
    performSearch(searchTerm);
  };

  const handleSortByChange = (value) => {
    setStore((state) => ({
      sortCriteria: {
        ...state.sortCriteria,
        by: value,
      },
    }));
    performSearch(searchTerm);
  };

  const handleSortOrderChange = (value) => {
    setStore((state) => ({
      sortCriteria: {
        ...state.sortCriteria,
        order: value,
      },
    }));
    performSearch(searchTerm);
  };

  const handleShowSettings = () => {
    setShowSettings(true);
  };

  const handleHideSettings = () => {
    setShowSettings(false);
  };

  // Handlers for AI Panel
  const handleOpenAiPanel = (args) => {
    setAiPanelArgs(args);
    setShowAiPanel(true);
    // Clear previous AI states when opening panel for a new interaction
    clearQa();
    clearSummary();
    clearTransformationState();
    clearSaveNewItemStatus();
  };

  const handleCloseAiPanel = () => {
    setShowAiPanel(false);
    setAiPanelArgs(null);
    clearQa();
    clearSummary();
    clearTransformationState();
    clearSaveNewItemStatus();
  };

  const handleAiSubmit = async (inputText) => {
    if (!currentSelectedItem || !currentSelectedItem.id) {
      console.error('No item selected for AI interaction.');
      return;
    }

    if (aiPanelArgs.type === 'qa') {
      setQaQuestion(inputText); // Update question in store
      await submitQaQuestion(currentSelectedItem.id, inputText);
    } else if (aiPanelArgs.type === 'summarize') {
      await fetchSummary(currentSelectedItem.id);
    } else if (aiPanelArgs.type === 'transform') {
      setSelectedTransformationType(inputText); // inputText is the transformation type
      await transformItemContent(currentSelectedItem.id, inputText);
    }
    // No direct feedback handler for now, feedback is implicit via UI actions
  };

  const handleConceptualLinkClick = (linkId) => {
    console.log('Conceptual link clicked:', linkId);
    selectItem(linkId); // Example: selecting the linked item
  };

  const currentSelectedItem = selectedItemId ? (searchResults.find(item => item.id === selectedItemId) || items.find(item => item.id === selectedItemId)) : null;

  if (showSettings) {
    return <SettingsView onBack={handleHideSettings} />;
  }

  return (
    <div className="app-container">
      <button
        onClick={handleShowSettings}
        className="settings-button"
      >
        Settings
      </button>
      <KnowledgeBaseView
        items={items}
        searchResults={searchResults}
        currentSearchTerm={searchTerm}
        onSearchTermChange={setSearchTerm}
        onPerformSearch={performSearch}
        availableTags={filterTags}
        selectedTags={selectedTags}
        onSelectedTagsChange={setSelectedTags}
        availableCategories={filterCategories}
        selectedCategories={selectedCategories}
        onSelectedCategoriesChange={setSelectedCategories}
        selectedDateFilter={dateRangeFilter.selectedDateOption || ''} // Pass the selected option
        onDateFilterChange={handleDateFilterChange}
        selectedSourceFilter={sourceFilter}
        onSourceFilterChange={handleSourceFilterChange}
        selectedSortBy={sortCriteria.by}
        onSortByChange={handleSortByChange}
        selectedSortOrder={sortCriteria.order}
        onSortOrderChange={handleSortOrderChange}
        onSelectItem={handleSelectItem}
        selectedItemId={selectedItemId}
      />
      <div className="main-content-area">
        <DetailViewPane
          className="detail-view-pane"
          item={currentSelectedItem}
          onInitiateAIQA={() => handleOpenAiPanel({ type: 'qa', itemId: currentSelectedItem.id })}
          onInitiateContentTransformation={() => handleOpenAiPanel({ type: 'transform', itemId: currentSelectedItem.id })}
          onViewConceptualLinks={() => setShowConceptualLinks(true)}
          // onEditMetadata will be implemented later if needed
        />
        {showAiPanel && currentSelectedItem && (
          <AIInteractionPanel
            aiPanelArgs={aiPanelArgs}
            onSubmit={handleAiSubmit}
            onClose={handleCloseAiPanel}
            // Pass AI states from store
            qaQuestion={qaQuestion}
            qaAnswer={qaAnswer}
            qaLoading={qaLoading}
            qaError={qaError}
            summary={summary}
            summaryLoading={summaryLoading}
            summaryError={summaryError}
            summaryGenerated={summaryGenerated}
            transformedContent={transformedContent}
            transformationLoading={transformationLoading}
            transformationError={transformationError}
            selectedTransformationType={selectedTransformationType}
            savingNewItemLoading={savingNewItemLoading}
            savingNewItemError={savingNewItemError}
            onSaveTransformedContent={saveTransformedContentAsNewItem}
            onClearSaveNewItemStatus={clearSaveNewItemStatus}
          />
        )}
        {showConceptualLinks && currentSelectedItem && (
          <KnowledgeGraphView />
        )}
      </div>
      <OfflineStatusIndicator isOnline={isOnline} />
    </div>
  );
}

export default App;