import React from 'react';

interface SuggestionDisplayProps {
  title: string;
  suggestions: string[];
  onSuggestionClick?: (suggestion: string) => void;
}

const SuggestionDisplay: React.FC<SuggestionDisplayProps> = ({ title, suggestions, onSuggestionClick }) => {
  return (
    <div data-testid={`suggestion-display-${title.toLowerCase().replace(/\s+/g, '-')}`}>
      <h4>{title}</h4>
      <div>
        {suggestions.map((suggestion, index) => (
          <span
            key={index}
            data-testid={`suggestion-item-${title.toLowerCase().replace(/\s+/g, '-')}-${index}`}
            style={{ marginRight: '8px', cursor: onSuggestionClick ? 'pointer' : 'default', textDecoration: onSuggestionClick ? 'underline' : 'none' }}
            onClick={() => onSuggestionClick && onSuggestionClick(suggestion)}
          >
            {suggestion}
          </span>
        ))}
      </div>
    </div>
  );
};

export default SuggestionDisplay;