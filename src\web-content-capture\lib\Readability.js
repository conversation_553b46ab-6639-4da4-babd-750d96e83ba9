/*
 * Copyright (c) 2010 Arc90 Inc
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * This code is heavily based on Arc90's readability.js (1.7.1) script
 * available at: http://code.google.com/p/arc90labs-readability
 */

/**
 * Public constructor.
 * @param {HTMLDocument} doc The document to parse.
 * @param {Object} options The options object.
 */
function Readability(doc, options) {
  // In some older versions, people passed a URI as the first argument. Cope:
  if (options && options.documentElement) {
    doc = options;
    options = arguments[2];
  } else if (!doc || !doc.documentElement) {
    throw new Error("First argument to Readability constructor should be a document object.");
  }
  options = options || {};

  this._doc = doc;
  this._docJSDOMParser = this._doc.firstChild && this._doc.firstChild.ownerDocument &&
                        this._doc.firstChild.ownerDocument.defaultView &&
                        this._doc.firstChild.ownerDocument.defaultView.JSDOMParser;
  this._articleTitle = null;
  this._articleByline = null;
  this._articleDir = null;
  this._articleSiteName = null;
  this._attempts = [];

  // Configurable options
  this._debug = !!options.debug;
  this._maxElemsToParse = options.maxElemsToParse || this.DEFAULT_MAX_ELEMS_TO_PARSE;
  this._nbTopCandidates = options.nbTopCandidates || this.DEFAULT_N_TOP_CANDIDATES;
  this._charThreshold = options.charThreshold || this.DEFAULT_CHAR_THRESHOLD;
  this._classesToPreserve = this.CLASSES_TO_PRESERVE.concat(options.classesToPreserve || []);
  this._keepClasses = !!options.keepClasses;
  this._serializer = options.serializer || function(el) {
    return el.innerHTML;
  };
  this._disableJSONLD = !!options.disableJSONLD;

  // Start with all flags set
  this._flags = this.FLAG_STRIP_UNLIKELYS |
                this.FLAG_WEIGHT_CLASSES |
                this.FLAG_CLEAN_CONDITIONALLY;

  var logEl;
  if (this._debug) {
    logEl = function(e) {
      var rv = e.nodeName + " ";
      if (e.id) {
        rv += "id: " + e.id + " ";
      }
      if (e.className) {
        rv += "class: " + e.className + " ";
      }
      if (e.style && e.style.cssText) {
        rv += "style: " + e.style.cssText + " ";
      }
      return rv;
    };
    this.log = function() {
      if (typeof console !== "undefined") {
        var args = Array.from(arguments).map(function(arg) {
          if (arg && arg.nodeName) {
            return logEl(arg);
          }
          return arg;
        });
        args.unshift("Readability:");
        console.log.apply(console, args);
      } else if (typeof dump !== "undefined") {
        var msg = Array.prototype.slice.call(arguments).join(" ");
        dump("Readability: " + msg + "\n");
      }
    };
  } else {
    this.log = function() {};
  }
}

Readability.prototype = {
  FLAG_STRIP_UNLIKELYS: 0x1,
  FLAG_WEIGHT_CLASSES: 0x2,
  FLAG_CLEAN_CONDITIONALLY: 0x4,

  // Max number of nodes supported by this parser. Default: 0 (no limit)
  DEFAULT_MAX_ELEMS_TO_PARSE: 0,

  // The number of top candidates to consider when analysing how
  // tight the competition is among candidates.
  DEFAULT_N_TOP_CANDIDATES: 5,

  // Element tags to score by default.
  DEFAULT_TAGS_TO_SCORE: "section,h2,h3,h4,h5,h6,p,td,pre".toUpperCase().split(","),

  // The default number of chars an article must have in order to return a result
  DEFAULT_CHAR_THRESHOLD: 500,

  // All of the regular expressions in use within readability.
  // Defined up here so we don't instantiate them repeatedly in loops.
  REGEXPS: {
    // NOTE: These two regular expressions are duplicated in
    // Readability-readerable.js. Please keep them in sync.
    unlikelyCandidates: /-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,
    okMaybeItsACandidate: /and|article|body|column|content|main|shadow/i,

    positive: /article|body|content|entry|hentry|h-entry|main|page|pagination|post|text|blog|story/i,
    negative: /hidden|^hid$|hid$|hid |banner|combx|comment|com-|contact|foot|footer|footnote|gdpr|masthead|media|meta|outbrain|promo|related|scroll|share|shoutbox|sidebar|skyscraper|sponsor|shopping|tags|tool|widget/i,
    extraneous: /print|archive|comment|discuss|e[\-]?mail|share|reply|all|login|sign|single|utility/i,
    byline: /byline|author|dateline|writtenby|p-author/i,
    replaceFonts: /<(\/?)font[^>]*>/gi,
    normalize: /\s{2,}/g,
    videos: /\/\/(www\.)?((dailymotion|youtube|youtube-nocookie|player\.vimeo|v\.qq)\.com|(archive|upload\.wikimedia)\.org|player\.twitch\.tv)/i,
    shareElements: /(\b|_)(share|sharedaddy)(\b|_)/i,
    nextLink: /(next|weiter|continue|>([^\|]|$)|»([^\|]|$))/i,
    prevLink: /(prev|earl|old|new|<|«)/i,
    tokenize: /\W+/g,
    whitespace: /^\s*$/,
    hasContent: /\S$/,
    hashUrl: /^#.+/,
    srcsetUrl: /(\S+)(\s+[\d.]+[xw])?(\s*(?:,|$))/g,
    b64DataUrl: /^data:\s*([^\s;,]+)\s*;\s*base64\s*,/i,
    // Commas as used in Latin, English, and many other languages.
    commas: /\u002C|\u060C|\uFE50|\uFE51|\uFF0C/g,
    // Unlikely phrases in links, determined by comparing a script that crawls
    // the top 1000 sites on the internet and counts the number of links with
    // certain phrases.
    unlikelyLinkTextPhrases: [
      "subscribe", "terms of service", "privacy policy", "careers", "contact us",
      "help", "feedback", "about us", "advertise", "sitemap", "faq", "support",
      "editorial guidelines", "community guidelines", "site map", "accessibility",
      "press", "legal", "disclaimer", "cookie policy", "user agreement",
      "privacy settings", "manage cookies", "terms of use", "accessibility statement",
      "code of conduct", "ethics policy", "corrections policy", "send a tip",
      "advertise with us", "media kit", "licensing", "reprints", "permissions",
      "masthead", "staff", "contribute", "write for us", "submit a story",
      "newsletters", "alerts", "podcasts", "mobile apps", "facebook", "twitter",
      "instagram", "youtube", "linkedin", "pinterest", "rss feeds", "jobs",
      "internships", "scholarships", "contests", "events", "store", "shop",
      "merchandise", "donations", "giveaways", "archives", "search", "login",
      "register", "sign in", "sign up", "my account", "profile", "settings",
      "preferences", "dashboard", "logout", "customer service", "order status",
      "shipping", "returns", "gift cards", "wishlist", "cart", "checkout",
      "view cart", "track order", "customer support", "live chat", "help center",
      "knowledge base", "tutorials", "documentation", "api", "developer",
      "partners", "affiliates", "investors", "media relations", "press releases",
      "sponsorship", "advertising", "media inquiries", "press inquiries",
      "business development", "sales", "partnerships", "investor relations",
      "public relations", "media", "press room", "newsroom", "blog", "forum",
      "community", "discussion", "message boards", "groups", "chat", "comments",
      "reviews", "ratings", "testimonials", "feedback form", "survey", "poll",
      "quiz", "games", "contests", "sweepstakes", "giveaway", "free trial",
      "demo", "webinar", "podcast", "video", "gallery", "slideshow", "infographic",
      "white paper", "case study", "ebook", "report", "newsletter signup",
      "email signup", "join our mailing list", "get updates", "stay informed",
      "follow us", "connect with us", "share this", "print this", "email this",
      "add to favorites", "bookmark this", "save this", "download pdf",
      "view pdf", "open pdf", "read more", "learn more", "continue reading",
      "next page", "previous page", "last page", "first page", "go to top",
      "back to top", "skip to content", "skip to main content",
      "skip to navigation", "skip to footer", "accessibility help",
      "text version", "high contrast", "font size", "translate", "language",
      "select language", "change language", "español", "français", "deutsch",
      "italiano", "português", "русский", "中文", "日本語", "한국어", "العربية",
      "हिन्दी", "বাংলা", "ਪੰਜਾਬੀ", "ગુજરાતી", "தமிழ்", "తెలుగు", "ಕನ್ನಡ",
      "മലയാളം", "සිංහල", "ไทย", "tiếng việt", "bahasa indonesia", "bahasa melayu",
      "türkçe", "polski", "čeština", "slovenčina", "magyar", "română", "български",
      "српски", "hrvatski", "slovenščina", "latviešu", "lietuvių", "eesti",
      "suomi", "svenska", "norsk", "dansk", "íslenska", "ελληνικά", "עברית",
      "فارسی", "اردو", "پښتو", "کوردی", "ქართული", "հայերեն", "azərbaycan",
      "қазақ", "oʻzbekcha", "монгол", "ខ្មែរ", "ລາວ", "မြန်မာ", "नेपाली",
      "मराठी", "ଓଡ଼ିଆ", "অসমীয়া", "कोंकणी", "سنڌي", "ދިވެހިބަސް", "ትግርኛ",
      "አማርኛ", "afaan oromoo", "kiswahili", "hausa", "yoruba", "igbo", "zulu",
      "xhosa", "sesotho", "setswana", "ndebele", "swati", "tsonga", "venda",
      "shona", "kinyarwanda", "kirundi", "lingala", "luganda", "sango", "wolof",
      "fulfulde", "bambara", "malagasy", "somali", "berber", "tamaziɣt",
      "kabyle", "tachelhit", "rifian", "chleuh", "amazigh", "tamazight",
      "tifinagh", "guarani", "quechua", "aymara", "nahuatl", "maya", "mapuche",
      "navajo", "cherokee", "choctaw", "creek", "ojibwe", "inuktitut", "cree",
      "hawaiian", "samoan", "tongan", "fijian", "maori", "tahitian", "chamorro",
      "marshallese", "palauan", "yapese", "kosraean", "pohnpeian", "chuukese",
      "nauruan", "kiribati", "tuvaluan", "tokelauan", "niuean", "cook islands maori",
      "rapanui", "marquesan", "austral", "mangarevan", "hawaiʻi pidgin",
      "bislama", "tok pisin", "pijin", "solomon islands pijin", "norfuk",
      "pitkern", "kriol", "cape verdean creole", "papiamento", "sranan tongo",
      "sarama"
    ].map(function(phrase) {
      return phrase.trim().toLowerCase();
    })
  },

  UNLIKELY_ROLES: ["menu", "menubar", "complementary", "navigation", "alert", "alertdialog", "dialog"],

  // List of tags that should not be stripped.
  TAGS_TO_SKIP: [
    "iframe", "object", "embed", "video", "audio", "figure", "picture", "svg", "math", "table", "form", "fieldset", "input", "textarea", "select", "button"
  ],

  // List of classes to preserve, in addition to any that the user provides.
  CLASSES_TO_PRESERVE: ["page"],

  // These are the classes that readability sets up for suitable content.
  CLASSES_TO_SET: ["readability-page"],

  /**
   * Run any post-process modifications to article content as a final pass. This is executed after
   * regular cleaning operations, but before serialization.
   *
   * @param Element
   *     An Element object of the article content that this function may modify.
   * @return void
   */
  _postProcessContent: function(articleContent) {
    // Readability cannot open relative uris so we convert them to absolute uris.
    this._fixRelativeUris(articleContent);
    this._simplifyNestedElements(articleContent);

    if (!this._keepClasses) {
      // Remove classes.
      this._cleanClasses(articleContent);
    }
  },

  /**
   * Iterates over a NodeList, calls `filterFn` for each node and removes node
   * if function returned `true`.
   *
   * If function is not passed, removes all the nodes in node list.
   *
   * @param NodeList nodeList The nodes to operate on
   * @param Function filterFn the function to use as a filter
   * @return void
   */
  _removeNodes: function(nodeList, filterFn) {
    // Avoid ever operating on live node lists.
    if (this._docJSDOMParser && nodeList._isLiveNodeList) {
      throw new Error("Do not pass live node lists to _removeNodes");
    }
    for (var i = nodeList.length - 1; i >= 0; i--) {
      var node = nodeList[i];
      var parentNode = node.parentNode;
      if (parentNode) {
        if (!filterFn || filterFn.call(this, node, i, nodeList)) {
          parentNode.removeChild(node);
        }
      }
    }
  },

  /**
   * Iterates over a NodeList, and calls _setNodeTag for each node.
   *
   * @param NodeList nodeList The nodes to operate on
   * @param String newTag the new tag name
   * @return void
   */
  _replaceNodeTags: function(nodeList, newTag) {
    // Avoid ever operating on live node lists.
    if (this._docJSDOMParser && nodeList._isLiveNodeList) {
      throw new Error("Do not pass live node lists to _replaceNodeTags");
    }
    for (const node of nodeList) {
      this._setNodeTag(node, newTag);
    }
  },

  /**
   * Iterate over a NodeList, which doesn't natively fully implement the Array
   * interface.
   *
   * For convenience, the current object context is applied to the provided
   * iterate function.
   *
   * @param  NodeList nodeList The NodeList.
   * @param  Function fn       The iterate function.
   * @return void
   */
  _forEachNode: function(nodeList, fn) {
    // Avoid ever operating on live node lists.
    if (this._docJSDOMParser && nodeList._isLiveNodeList) {
      throw new Error("Do not pass live node lists to _forEachNode");
    }
    Array.prototype.forEach.call(nodeList, fn, this);
  },

  /**
   * Iterate over a NodeList, and return the first node that passes
   * the supplied test function
   *
   * For convenience, the current object context is applied to the provided
   * test function.
   *
   * @param  NodeList nodeList The NodeList.
   * @param  Function fn       The test function.
   * @return void
   */
  _findNode: function(nodeList, fn) {
    // Avoid ever operating on live node lists.
    if (this._docJSDOMParser && nodeList._isLiveNodeList) {
      throw new Error("Do not pass live node lists to _findNode");
    }
    return Array.prototype.find.call(nodeList, fn, this);
  },

  /**
   * Iterate over a NodeList, return true if any of the provided iterate
   * function calls returns true, false otherwise.
   *
   * For convenience, the current object context is applied to the
   * provided iterate function.
   *
   * @param  NodeList nodeList The NodeList.
   * @param  Function fn       The iterate function.
   * @return Boolean
   */
  _someNode: function(nodeList, fn) {
    // Avoid ever operating on live node lists.
    if (this._docJSDOMParser && nodeList._isLiveNodeList) {
      throw new Error("Do not pass live node lists to _someNode");
    }
    return Array.prototype.some.call(nodeList, fn, this);
  },

  /**
   * Iterate over a NodeList, return true if all of the provided iterate
   * function calls return true, false otherwise.
   *
   * For convenience, the current object context is applied to the
   * provided iterate function.
   *
   * @param  NodeList nodeList The NodeList.
   * @param  Function fn       The iterate function.
   * @return Boolean
   */
  _everyNode: function(nodeList, fn) {
    // Avoid ever operating on live node lists.
    if (this._docJSDOMParser && nodeList._isLiveNodeList) {
      throw new Error("Do not pass live node lists to _everyNode");
    }
    return Array.prototype.every.call(nodeList, fn, this);
  },

  /**
   * Concat all nodelists passed as arguments.
   *
   * @return ...NodeList
   * @return Array
   */
  _concatNodeLists: function() {
    var slice = Array.prototype.slice;
    var args = slice.call(arguments);
    var nodeLists = args.map(function(list) {
      return slice.call(list);
    });
    return Array.prototype.concat.apply([], nodeLists);
  },

  _getAllNodesWithTag: function(node, tagNames) {
    if (node.querySelectorAll) {
      return node.querySelectorAll(tagNames.join(","));
    }
    return [].concat.apply([], tagNames.map(function(tag) {
      var collection = node.getElementsByTagName(tag);
      return Array.isArray(collection) ? collection : Array.from(collection);
    }));
  },

  /**
   * Removes the class="" attribute from every element in the given
   * subtree, except those that match CLASSES_TO_PRESERVE and
   * the classesToPreserve array from the options object.
   *
   * @param Element
   * @return void
   */
  _cleanClasses: function(node) {
    var classesToPreserve = this._classesToPreserve;
    var className = (node.getAttribute("class") || "")
      .split(/\s+/)
      .filter(function(cls) {
        return classesToPreserve.indexOf(cls) != -1;
      })
      .join(" ");

    if (className) {
      node.setAttribute("class", className);
    } else {
      node.removeAttribute("class");
    }

    for (node = node.firstElementChild; node; node = node.nextElementSibling) {
      this._cleanClasses(node);
    }
  },

  /**
   * Converts each <a> and <img> uri in the given element to an absolute URI,
   * ignoring #ref URIs.
   *
   * @param Element
   * @return void
   */
  _fixRelativeUris: function(articleContent) {
    var scheme = this._doc.location.protocol;
    // If the document location is a b64 data scheme, we can't fix
    // relative URIs.
    if (scheme == "data:") {
      return;
    }
    var baseURI = this._doc.baseURI;
    var documentURI = this._doc.documentURI;

    /**
     * Remove the path portion of the URI
     *
     * @param  string uri the URI to modify
     * @return string the URI with the path removed
     */
    function toBaseURI(uri) {
      try {
        // If this is a data URI, ignore it.
        if (Readability.prototype.REGEXPS.b64DataUrl.test(uri)) {
          return uri;
        }
        // If this is already an absolute URI, ignore it.
        if (/^[a-zA-Z][a-zA-Z0-9\+\-\.]*:/.test(uri)) {
          return uri;
        }
        // Otherwise, resolve it against the document base URI.
        return (new URL(uri, baseURI)).href;
      } catch (ex) {
        // Something went wrong, just return the original URI.
        // This is likely to be an invalid URI.
      }
      return uri;
    }

    var links = this._getAllNodesWithTag(articleContent, ["a"]);
    this._forEachNode(links, function(link) {
      var href = link.getAttribute("href");
      if (href) {
        // Remove links with javascript: URIs, since
        // they won't work after scripts have been removed from the page.
        if (href.indexOf("javascript:") === 0) {
          // if the link only contains simple text content, it can be converted to a span
          if (link.childNodes.length === 1 && link.childNodes[0].nodeType === Node.TEXT_NODE) {
            var span = this._doc.createElement("span");
            span.textContent = link.textContent;
            link.parentNode.replaceChild(span, link);
          } else {
            // if the link has multiple children, they should all be preserved
            var container = this._doc.createElement("span");
            while (link.firstChild) {
              container.appendChild(link.firstChild);
            }
            link.parentNode.replaceChild(container, link);
          }
        } else {
          if (!this.REGEXPS.hashUrl.test(href)) {
            link.setAttribute("href", toBaseURI(href));
          }
        }
      }
    });

    var medias = this._getAllNodesWithTag(articleContent, [
      "img", "picture", "figure", "video", "audio", "source"
    ]);

    this._forEachNode(medias, function(media) {
      var src = media.getAttribute("src");
      var poster = media.getAttribute("poster");
      var srcset = media.getAttribute("srcset");

      if (src && !this.REGEXPS.hashUrl.test(src)) {
        media.setAttribute("src", toBaseURI(src));
      }

      if (poster && !this.REGEXPS.hashUrl.test(poster)) {
        media.setAttribute("poster", toBaseURI(poster));
      }

      if (srcset) {
        var newSrcset = srcset.replace(this.REGEXPS.srcsetUrl, function(_, p1, p2, p3) {
          return toBaseURI(p1) + (p2 || "") + p3;
        });

        media.setAttribute("srcset", newSrcset);
      }
    });
  },

  _simplifyNestedElements: function (articleContent) {
    var node = articleContent;

    while (node) {
      if (node.parentNode && ["DIV", "SECTION"].includes(node.tagName) && !(node.id && node.id.startsWith("readability"))) {
        if (this._isElementWithoutContent(node)) {
          node = this._removeAndGetNext(node);
          continue;
        } else if (this._hasSingleTagInsideElement(node, "DIV") || this._hasSingleTagInsideElement(node, "SECTION")) {
          var child = node.children[0];
          for (var i = 0; i < node.attributes.length; i++) {
            child.setAttribute(node.attributes[i].name, node.attributes[i].value);
          }
          node.parentNode.replaceChild(child, node);
          node = child;
          continue;
        }
      }

      node = this._getNextNode(node);
    }
  },

  /**
   * Get the article title as a string.
   * @return string
   */
  _getArticleTitle: function() {
    var doc = this._doc;
    var curTitle = "";
    var origTitle = "";

    try {
      curTitle = origTitle = doc.title.trim();

      // If they had an element with id "title" in their HTML
      if (typeof curTitle !== "string") {
        curTitle = origTitle = this._getInnerText(doc.getElementsByTagName("title")[0]);
      }
    } catch (e) { /* ignore exceptions setting the title. */ }

    var titleHadHierarchicalSeparators = false;
    function wordCount(str) {
      return str.split(/\s+/).length;
    }

    // If there's a separator in the title, first check whether the fraction to the right
    // of the separator is either 1) the site name or 2) the domain name.
    if (curTitle.match(/ [\|\-\\\/>»] /)) {
      titleHadHierarchicalSeparators = / [\\\/>»] /.test(curTitle);
      curTitle = origTitle.replace(/(.*)[\|\-\\\/>»] .*/gi, "$1");

      // If the resulting title is too short (3 words or fewer), remove
      // the first part instead:
      if (wordCount(curTitle) < 3) {
        curTitle = origTitle.replace(/[^\|\-\\\/>»]*[\|\-\\\/>»](.*)/gi, "$1");
      }
    } else if (curTitle.indexOf(": ") !== -1) {
      // Check if we have an heading containing this exact string, so we
      // could assume it's the full title.
      var headings = this._concatNodeLists(
        doc.getElementsByTagName("h1"),
        doc.getElementsByTagName("h2")
      );
      var trimmedTitle = curTitle.trim();
      var match = this._someNode(headings, function(heading) {
        return heading.textContent.trim() === trimmedTitle;
      });

      // If we don't, let's extract the title out of the original title string.
      if (!match) {
        curTitle = origTitle.substring(origTitle.lastIndexOf(": ") + 2);

        // If the title is too short or too long, clean up the colon part:
        if (wordCount(curTitle) < 3 || wordCount(curTitle) > 9) {
          curTitle = origTitle.substring(origTitle.indexOf(": ") + 2);
        }
        // But if cleaning up the colon part results in a title that is too short,
        // and the original title had hierarchical separators,
        // use the original title without the hierarchical separator parts instead.
        // TODO: Try to combine this with the separator logic above.
        else if (titleHadHierarchicalSeparators && wordCount(curTitle) < 3) {
          curTitle = origTitle.replace(/(.*)[\|\-\\\/>»] .*/gi, "$1");
        }
      }
    } else if (curTitle.length > 150 || curTitle.length < 15) {
      var hOnes = doc.getElementsByTagName("h1");

      if (hOnes.length === 1) {
        curTitle = this._getInnerText(hOnes[0]);
      }
    }

    curTitle = curTitle.trim().replace(this.REGEXPS.normalize, " ");
    // If we now have 4 words or fewer as our title, and either the original title
    // had no separators, or the original title had hierarchical separators,
    // use the original title, but trimmed.
    if (wordCount(curTitle) <= 4 &&
        (!titleHadHierarchicalSeparators ||
         !/ [\|\-\\\/>»] /.test(origTitle))) {
      curTitle = origTitle.trim();
    }
    return curTitle;
  },

  /**
   * Prepare the HTML document for readability to scrape it.
   * This includes things like stripping javascript, CSS, and handling terrible markup.
   *
   * @return void
   **/
  _prepDocument: function() {
    var doc = this._doc;

    // Remove all style tags in head
    this._removeNodes(doc.getElementsByTagName("style"));

    if (doc.body) {
      this._replaceBrs(doc.body);
    }

    this._replaceNodeTags(doc.getElementsByTagName("font"), "SPAN");
  },

  /**
   * Finds the next element, starting from the given node, and ignoring
   * whitespace in between. If the given node is an element, the same node is
   * returned.
   */
  _nextElement: function (node) {
    var next = node;
    while (next &&
           (next.nodeType != Node.ELEMENT_NODE) &&
           this.REGEXPS.whitespace.test(next.textContent)) {
      next = next.nextSibling;
    }
    return next;
  },

  /**
   * Replaces 2 or more successive <br> elements with a single <p> element.
   * Whitespace between <br> elements are ignored. For example:
   *   <br>
   *   <br>
   * becomes <p></p>
   *
   *   <br>
   *   some text
   *   <br>
   * becomes <p>some text</p>
   */
  _replaceBrs: function (elem) {
    this._forEachNode(this._getAllNodesWithTag(elem, ["br"]), function(br) {
      var next = br.nextSibling;

      // Whether 2 or more <br> elements have been found and replaced with a
      // <p> block.
      var replacedByP = false;

      // If we find a <br> chain, remove the <br>s until we hit another node
      // or non-whitespace. This leaves behind the first <br> in the chain
      // (which will be replaced by a <p> later).
      while ((next = this._nextElement(next)) && (next.tagName == "BR")) {
        replacedByP = true;
        var brSibling = next.nextSibling;
        next.parentNode.removeChild(next);
        next = brSibling;
      }

      // If we removed 2 or more <br>s in a row, replace the first <br> in
      // the chain with a <p> block.
      if (replacedByP) {
        var p = this._doc.createElement("p");
        br.parentNode.replaceChild(p, br);
        next = p.nextSibling;

        // If the previous <br> chain was followed by text, move this text inside the p.
        // The text node is removed from its original position and attached to the p.
        while (next) {
          // If the next node is an element, we've hit a new block, so stop.
          if (next.nodeType == Node.ELEMENT_NODE) {
            break;
          }

          var nextSibling = next.nextSibling;
          // Skip text nodes that only contain whitespace.
          if (this.REGEXPS.hasContent.test(next.textContent)) {
            p.appendChild(next);
          }
          next = nextSibling;
        }
      }
    });
  },

  _setNodeTag: function (node, tag) {
    this.log("_setNodeTag", node, tag);
    var replacement = node.ownerDocument.createElement(tag);
    while (node.firstChild) {
      replacement.appendChild(node.firstChild);
    }
    node.parentNode.replaceChild(replacement, node);
    if (node.readability) {
      replacement.readability = node.readability;
    }

    for (var i = 0; i < node.attributes.length; i++) {
      try {
        replacement.setAttribute(node.attributes[i].name, node.attributes[i].value);
      } catch (e) {
        /* An error is thrown if the attribute name is not valid.
         * E.g. invalid characters in XML. We should ignore such errors.
         */
      }
    }
    return replacement;
  },

  /**
   * Prepare the article node for display. Clean out any inline styles,
   * iframes, forms, strip extraneous <p> tags, etc.
   *
   * @param Element
   * @return void
   */
  _prepArticle: function(articleContent) {
    this._cleanStyles(articleContent);

    // Check for data tables before we continue, to avoid removing items in
    // those tables, which will often be isolated even though they're visually
    // relevant.
    this._markDataTables(articleContent);

    this._fixLazyImages(articleContent);

    // Clean out junk from the article content
    this._cleanConditionally(articleContent, "form");
    this._cleanConditionally(articleContent, "fieldset");
    this._clean(articleContent, "object");
    this._clean(articleContent, "embed");
    this._clean(articleContent, "footer");
    this._clean(articleContent, "link");
    this._clean(articleContent, "aside");

    // Clean out elements with little content that have "share" in their id/class combinations from final top candidates,
    // which means we don't remove the top candidates themselves
    var shareElementThreshold = this.DEFAULT_CHAR_THRESHOLD;
    this._forEachNode(articleContent.children, function(topCandidate) {
      this._cleanMatchedNodes(topCandidate, function(node, matchString) {
        return this.REGEXPS.shareElements.test(matchString) && node.textContent.length < shareElementThreshold;
      });
    });


    this._clean(articleContent, "iframe");
    this._clean(articleContent, "input");
    this._clean(articleContent, "textarea");
    this._clean(articleContent, "select");
    this._clean(articleContent, "button");
    this._cleanHeaders(articleContent);

    // Do these last as the previous stuff may have removed junk
    // that will affect these
    this._cleanConditionally(articleContent, "table");
    this._cleanConditionally(articleContent, "ul");
    this._cleanConditionally(articleContent, "div");

    // Remove extra paragraphs
    this._removeNodes(this._getAllNodesWithTag(articleContent, ["p"]), function (paragraph) {
      var imgCount = paragraph.getElementsByTagName("img").length;
      var embedCount = paragraph.getElementsByTagName("embed").length;
      var objectCount = paragraph.getElementsByTagName("object").length;
      // At this point, nasty iframes have been removed, so iframes are generally okay.
      var iframeCount = paragraph.getElementsByTagName("iframe").length;
      var totalCount = imgCount + embedCount + objectCount + iframeCount;

      return totalCount === 0 && !this._getInnerText(paragraph, false);
    });

    this._forEachNode(this._getAllNodesWithTag(articleContent, ["br"]), function(br) {
      var next = this._nextElement(br.nextSibling);
      if (next && next.tagName == "P") {
        br.parentNode.removeChild(br);
      }
    });

    // Remove single-cell tables
    this._forEachNode(this._getAllNodesWithTag(articleContent, ["table"]), function(table) {
      var tbody = this._hasSingleTagInsideElement(table, "TBODY") ? table.firstElementChild : table;
      if (this._hasSingleTagInsideElement(tbody, "TR")) {
        var row = tbody.firstElementChild;
        if (this._hasSingleTagInsideElement(row, "TD")) {
          var cell = row.firstElementChild;
          var text = this._getInnerText(cell, false);
          // Lots of text in a single cell table is probably a data table, so leave it alone.
          if (text.length < 25) {
            this._setNodeTag(cell, this._everyNode(cell.childNodes, this._isPhrasingContent) ? "P" : "DIV");
            table.parentNode.replaceChild(cell, table);
          }
        }
      }
    });
  },

  /**
   * Initialize a node with the readability object. Also checks the
   * className/id for special names to add to its score.
   *
   * @param Element
   * @return void
   **/
  _initializeNode: function(node) {
    node.readability = {"contentScore": 0};

    switch (node.tagName) {
      case "DIV":
        node.readability.contentScore += 5;
        break;

      case "PRE":
      case "TABLE":
      case "UL":
        node.readability.contentScore += 3;
        break;

      case "ADDRESS":
      case "BLOCKQUOTE":
      case "OL":
      case "P":
      case "FIGURE":
        node.readability.contentScore += 1;
        break;
    }

    // Add a point for the id/class.
    node.readability.contentScore += this._getClassWeight(node);
  },

  _removeAndGetNext: function(node) {
    var nextNode = this._getNextNode(node, true);
    node.parentNode.removeChild(node);
    return nextNode;
  },

  /**
   * Traverse the DOM from node to node, starting at the node passed in.
   * Pass true for the second parameter to indicate this node itself
   * (and its kids) are going away, and we want the next node over.
   *
   * Calling this in a loop will traverse the DOM depth-first.
   */
  _getNextNode: function(node, ignoreSelfAndKids) {
    // First check for kids if those aren't being ignored
    if (!ignoreSelfAndKids && node.firstElementChild) {
      return node.firstElementChild;
    }
    // Then for siblings...
    if (node.nextElementSibling) {
      return node.nextElementSibling;
    }
    // And finally, move up the parent chain *and* find a sibling
    // (because this is depth-first traversal, we will have already
    // seen the parent nodes themselves).
    do {
      node = node.parentNode;
    } while (node && !node.nextElementSibling);
    return node && node.nextElementSibling;
  },

  // compares second text to first one
  // 1 = same text, 0 = completely different text
  // works the way that it splits both texts into words and finds words that are common in both texts
  // the result is the number of common words divided by the total number of words
  _textSimilarity: function(textA, textB) {
    var tokensA = textA.toLowerCase().split(this.REGEXPS.tokenize).filter(Boolean);
    var tokensB = textB.toLowerCase().split(this.REGEXPS.tokenize).filter(Boolean);
    if (!tokensA.length || !tokensB.length) {
      return 0;
    }
    var common = tokensA.filter(function(token) { return tokensB.includes(token); });
    return common.length / (Math.max(tokensA.length, tokensB.length) + (tokensA.length - common.length));
  },

  _checkByline: function(node, matchString) {
    if (this._articleByline) {
      return false;
    }

    if (node.getAttribute !== undefined) {
      var rel = node.getAttribute("rel");
      var itemprop = node.getAttribute("itemprop");
    }

    if ((rel === "author" || (itemprop && itemprop.indexOf("author") !== -1) || this.REGEXPS.byline.test(matchString)) &&
        this._isValidByline(node.textContent)) {
      this._articleByline = node.textContent.trim();
      return true;
    }

    return false;
  },

  _getNodeAncestors: function(node, maxDepth) {
    maxDepth = maxDepth || 0;
    var i = 0, ancestors = [];
    while (node.parentNode) {
      ancestors.push(node.parentNode);
      if (maxDepth && ++i === maxDepth) {
        break;
      }
      node = node.parentNode;
    }
    return ancestors;
  },

  /***
   * grabArticle - Using a variety of metrics (content score, classname, element types), find the content that is
   *         most likely to be the article candidate.
   *
   * @param Element page a document to process.
   * @return Element  An array of choices, like [[element, score], [element, score]]
   **/
  _grabArticle: function (page) {
    this.log("**** grabArticle ****");
    var doc = this._doc;
    var isPaging = (page !== null);
    page = page ? page : this._doc.body;

    // We can't grab an article if we don't have a page!
    if (!page) {
      this.log("No body found in document. Abort.");
      return null;
    }

    var pageCacheHtml = page.innerHTML;

    while (true) {
      this.log("Starting grabArticle loop");
      var stripUnlikelyCandidates = this._flagIsActive(this.FLAG_STRIP_UNLIKELYS);

      // First, find all candidates.
      var candidates = [];

      var node = page.firstElementChild;
      while (node) {
        // Remove unlikely candidates
        if (stripUnlikelyCandidates) {
          var unlikelyMatchString = this._getAllAttributes(node);
          if (this.REGEXPS.unlikelyCandidates.test(unlikelyMatchString) &&
              !this.REGEXPS.okMaybeItsACandidate.test(unlikelyMatchString) &&
              !this._hasAncestorTag(node, "table") &&
              !this._hasAncestorTag(node, "code") &&
              node.tagName !== "BODY" &&
              node.tagName !== "A") {
            this.log("Removing unlikely candidate - " + unlikelyMatchString);
            node = this._removeAndGetNext(node);
            continue;
          }
        }

        // Remove DIV, SECTION, and HEADER nodes without any content(e.g. text, image, video, or iframe).
        if ((node.tagName === "DIV" || node.tagName === "SECTION" || node.tagName === "HEADER" ||
             node.tagName === "H1" || node.tagName === "H2" || node.tagName === "H3" ||
             node.tagName === "H4" || node.tagName === "H5" || node.tagName === "H6") &&
            this._isElementWithoutContent(node)) {
          node = this._removeAndGetNext(node);
          continue;
        }

        if (this.DEFAULT_TAGS_TO_SCORE.indexOf(node.tagName) !== -1) {
          candidates.push(node);
        }

        node = this._getNextNode(node);
      }

      // Loop through all candidates, scoring them. Work backwards from the
      // bottom up to avoid issues with calling removeChild on a
      // node's parent directly.
      var candidatesList = [];
      for (var i = 0; i < candidates.length; i++) {
        var candidate = candidates[i];

        // Initialize readability data
        if (!candidate.readability) {
          this._initializeNode(candidate);
          candidatesList.push(candidate);
        }

        if (this._maxElemsToParse > 0 && candidatesList.length > this._maxElemsToParse) {
          this.log("Aborting parsing article; too many elements");
          // Due to how this is called recursively, this is actually expected
          // to be "too many elements in this page" which is a sign that we
          // are probably too far down the DOM tree.
          page.innerHTML = pageCacheHtml;
          return null;
        }
      }

      var topCandidates = [];
      for (i = candidatesList.length - 1; i >= 0; i--) {
        var candidate = candidatesList[i];

        // If this candidate has no score, remove it.
        if (!candidate.readability) {
          continue;
        }

        // Scale the final candidates score based on link density. Good content
        // should have a relatively small link density (5% or less) and be mostly
        // unaffected by this operation.
        var candidateScore = candidate.readability.contentScore * (1 - this._getLinkDensity(candidate));
        candidate.readability.contentScore = candidateScore;

        this.log("Candidate:", candidate, "with score " + candidateScore);

        // After we've calculated scores, loop through all of the possible
        // candidate nodes we found and find the one with the highest score.
        for (var c = 0; c < this._nbTopCandidates; c++) {
          var tc = topCandidates[c];

          if (!tc || candidateScore > tc.readability.contentScore) {
            topCandidates.splice(c, 0, candidate);
            if (topCandidates.length > this._nbTopCandidates) {
              topCandidates.pop();
            }
            break;
          }
        }
      }

      var bestCandidate = topCandidates[0];
      var alternativeCandidateAncestors = [];
      if (topCandidates.length > 1) {
        alternativeCandidateAncestors = topCandidates.slice(1).map(function(c) { return this._getNodeAncestors(c); }, this);
      }

      // If we still have no top candidate, return null.
      if (!bestCandidate) {
        page.innerHTML = pageCacheHtml;
        return null;
      }
      this.log("Best candidate:", bestCandidate);

      // Now that we have the top candidate, find the common parent node of
      // the top candidates.
      var commonAncestor = null;
      if (topCandidates.length > 1) {
        commonAncestor = this._getCommonAncestor(bestCandidate, topCandidates.slice(1));
        if (!commonAncestor) {
          this.log("No common ancestor found");
        } else {
          this.log("Common ancestor:", commonAncestor);
        }
      }

      // If we have a common ancestor, and it's not the body, we use it.
      // Otherwise we use the best candidate's parent.
      var parentOfBestCandidate = bestCandidate.parentNode;
      if (commonAncestor && commonAncestor !== doc.body) {
        parentOfBestCandidate = commonAncestor;
      }

      // If we don't have a common ancestor, or the common ancestor is the body,
      // and the best candidate is not the body itself, we try to find a better
      // common ancestor by looking at the alternative candidates.
      if ((!commonAncestor || commonAncestor === doc.body) && bestCandidate !== doc.body) {
        var bestScore = bestCandidate.readability.contentScore;
        var bestCandidateAncestors = this._getNodeAncestors(bestCandidate);
        for (i = 0; i < alternativeCandidateAncestors.length; i++) {
          var alternativeAncestors = alternativeCandidateAncestors[i];
