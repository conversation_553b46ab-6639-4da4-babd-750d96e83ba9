import React, { useState } from 'react';

interface CategoryInputProps {
  initialCategory: string;
  onCategoryChange: (category: string) => void;
  suggestedCategories: string[]; // Added for suggestions
}

const CategoryInput: React.FC<CategoryInputProps> = ({ initialCategory, onCategoryChange, suggestedCategories }) => {
  const [category, setCategory] = useState<string>(initialCategory);
  const [showSuggestions, setShowSuggestions] = useState(false);

  const handleCategoryInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newCategory = event.target.value;
    setCategory(newCategory);
    onCategoryChange(newCategory);
    setShowSuggestions(true); // Show suggestions when typing
  };

  const handleSuggestionClick = (suggestedCategory: string) => {
    setCategory(suggestedCategory);
    onCategoryChange(suggestedCategory);
    setShowSuggestions(false); // Hide suggestions after selection
  };

  return (
    <div data-testid="category-input">
      <h4>Category</h4>
      <input
        type="text"
        data-testid="category-name-input"
        value={category}
        onChange={handleCategoryInputChange}
        placeholder="Enter category or select suggestion"
      />
       {showSuggestions && suggestedCategories.length > 0 && (
        <div data-testid="category-suggestions" style={{ border: '1px solid #ccc', marginTop: '4px', padding: '4px' }}>
          <p>Suggestions:</p>
          {suggestedCategories.map((suggested, index) => (
            <div
key={index}
              data-testid={`category-suggestion-item-${index}`}
              style={{ cursor: 'pointer', padding: '2px' }}
              onClick={() => handleSuggestionClick(suggested)}
            >
              {suggested}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CategoryInput;