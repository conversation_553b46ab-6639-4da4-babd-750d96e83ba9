import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';

const FilterSortBar = ({
  availableTags,
  availableCategories,
  selectedTags,
  onSelectedTagsChange,
  selectedCategories,
  onSelectedCategoriesChange,
  selectedDateFilter,
  onDateFilterChange,
  selectedSourceFilter,
  onSourceFilterChange,
  selectedSortBy,
  onSortByChange,
  selectedSortOrder,
  onSortOrderChange,
}) => {
  const handleTagChange = (tag) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter((t) => t !== tag)
      : [...selectedTags, tag];
    onSelectedTagsChange(newTags);
  };

  const handleCategoryChange = (e) => {
    onSelectedCategoriesChange(e.target.value);
  };

  const handleDateFilterChange = (e) => {
    onDateFilterChange(e.target.value);
  };

  const handleSourceFilterChange = (e) => {
    onSourceFilterChange(e.target.value);
  };

  const handleSortByChange = (e) => {
    onSortByChange(e.target.value);
  };

  const handleSortOrderChange = (e) => {
    onSortOrderChange(e.target.value);
  };


  return (
    <div className="filter-sort-bar">
      <fieldset className="filter-group">
        <legend>Filter by Tag</legend>
        {availableTags.map((tag) => (
          <label key={tag} htmlFor={`tag-${tag}`}>
            <input
              type="checkbox"
              id={`tag-${tag}`}
              name={tag}
              value={tag}
              checked={selectedTags.includes(tag)}
              onChange={() => handleTagChange(tag)}
            />
            {tag}
          </label>
        ))}
      </fieldset>

      <fieldset className="filter-group">
        <legend>Filter by Category</legend>
        <label htmlFor="category-filter">Filter by category</label>
        <select
          id="category-filter"
          name="category"
          value={selectedCategories}
          onChange={handleCategoryChange}
        >
          <option value="">All Categories</option>
          {availableCategories.map((category) => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
      </fieldset>

      <fieldset className="filter-group">
        <legend>Filter by Date</legend>
        <label htmlFor="date-filter">Filter by date</label>
        <select // Simple select for now, could be a date picker
          id="date-filter"
          name="date"
          value={selectedDateFilter}
          onChange={handleDateFilterChange}
        >
          <option value="">Any Date</option>
          <option value="today">Today</option>
          <option value="last7days">Last 7 Days</option>
          <option value="last30days">Last 30 Days</option>
        </select>
      </fieldset>

      <fieldset className="filter-group">
        <legend>Filter by Source</legend>
        <label htmlFor="source-filter">Filter by source</label>
        <input
          type="text"
          id="source-filter"
          name="source"
          value={selectedSourceFilter}
          onChange={handleSourceFilterChange}
          placeholder="Enter source..."
        />
      </fieldset>

      <fieldset className="sort-group">
        <legend>Sort By</legend>
        <label htmlFor="sort-by">Sort by criteria</label>
        <select id="sort-by" name="by" value={selectedSortBy} onChange={handleSortByChange}>
          <option value="date">Date</option>
          <option value="title">Title</option>
          <option value="relevance">Relevance</option>
        </select>
        <label htmlFor="sort-order">Sort order</label>
        <select id="sort-order" name="order" value={selectedSortOrder} onChange={handleSortOrderChange}>
          <option value="desc">Descending</option>
          <option value="asc">Ascending</option>
        </select>
      </fieldset>
    </div>
  );
};

FilterSortBar.propTypes = {
  availableTags: PropTypes.arrayOf(PropTypes.string),
  availableCategories: PropTypes.arrayOf(PropTypes.string),
  selectedTags: PropTypes.arrayOf(PropTypes.string).isRequired,
  onSelectedTagsChange: PropTypes.func.isRequired,
  selectedCategories: PropTypes.string.isRequired,
  onSelectedCategoriesChange: PropTypes.func.isRequired,
  selectedDateFilter: PropTypes.string.isRequired,
  onDateFilterChange: PropTypes.func.isRequired,
  selectedSourceFilter: PropTypes.string.isRequired,
  onSourceFilterChange: PropTypes.func.isRequired,
  selectedSortBy: PropTypes.oneOf(['date', 'title', 'relevance']).isRequired,
  onSortByChange: PropTypes.func.isRequired,
  selectedSortOrder: PropTypes.oneOf(['asc', 'desc']).isRequired,
  onSortOrderChange: PropTypes.func.isRequired,
};

FilterSortBar.defaultProps = {
  availableTags: [],
  availableCategories: [],
  selectedTags: [],
  selectedCategories: '',
  selectedDateFilter: '',
  selectedSourceFilter: '',
  selectedSortBy: 'date',
  selectedSortOrder: 'desc',
};

export default FilterSortBar;