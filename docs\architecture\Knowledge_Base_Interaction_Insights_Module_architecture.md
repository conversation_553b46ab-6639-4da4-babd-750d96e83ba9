# High-Level Architecture: Knowledge Base Interaction & Insights Module

## 1. Introduction

This document defines the high-level architecture for the Knowledge Base Interaction & Insights Module of the Personalized AI Knowledge Companion & PKM Web Clipper project. This architecture is designed to support the AI verifiable tasks outlined in the Master Project Plan ([`docs/Master_Project_Plan.md`](docs/Master_Project_Plan.md)) and enable the system to pass the high-level acceptance tests defined in the Master Acceptance Test Plan ([`docs/Master_Acceptance_Test_Plan.md`](docs/Master_Acceptance_Test_Plan.md)), specifically focusing on Test Cases 3 through 9.

The module's core responsibility is to provide users with the ability to interact with their captured knowledge base, including browsing, searching, querying, and deriving insights, while adhering to the project's local-first and privacy principles. This version incorporates findings from the AI Linking Strategies Implementation Research ([`research/ai_linking_strategies_implementation_research/`](research/ai_linking_strategies_implementation_research/)) to enhance conceptual linking capabilities.

## 2. Architectural Goals

*   Enable seamless browsing and retrieval of saved content.
*   Provide powerful search capabilities, including natural language semantic search.
*   Integrate AI-powered features for Q&A, summarization, and content transformation.
*   Suggest meaningful, AI-driven conceptual links between related knowledge items, with user control and interpretability.
*   Ensure core functionalities (browsing, basic search, core conceptual linking) are available offline.
*   Maintain user data privacy and control.
*   Support AI verifiable outcomes for testing and development.
*   Adopt a phased, iterative approach for advanced AI linking features, considering a hybrid (local/cloud) model where appropriate.

## 3. High-Level Component Breakdown

The Knowledge Base Interaction & Insights Module is composed of the following key components:

### 3.1. User Interface (UI) Layer

*   **Responsibility:** Handles all user interactions, displays the knowledge base content, search results, AI-generated insights (including conceptual links), and provides controls for various module functionalities. It acts as the presentation layer, interacting with the backend services to fetch and display data. Provides mechanisms for user feedback on suggested links and allows configuration of linking parameters.
*   **Supported Test Cases:** Directly supports the user-facing aspects of Test Cases 3, 4, 5, 6, 7, and 8.
*   **Interaction:** Communicates with the KBAL, Search Service, Query Understanding Engine, AI Services Gateway, and Conceptual Linking Engine.

:start_line:30
-------
### 3.2. Knowledge Base Access Layer (KBAL)

*   **Responsibility:** Provides an abstraction layer for accessing and managing the locally stored knowledge base content. It encapsulates the details of the underlying storage mechanism and offers a consistent API for retrieving, querying, and potentially updating content, including pre-processed content or metadata needed for linking. The current implementation utilizes `lowdb` for local-first JSON-based data persistence, ensuring data privacy and offline access. Secure path handling is implemented to mitigate Path Traversal vulnerabilities by using a secure base path and sanitizing filenames.
*   **Supported Test Cases:** Essential for all test cases requiring access to saved content (3, 4, 5, 6, 7, 8, 9). Enables the local-first principle.
*   **Interaction:** Interacted with by the UI Layer, Search Service, Conceptual Linking Engine, and Offline Access Handler.

### 3.3. Search Service

*   **Responsibility:** Implements the search functionality for the knowledge base. This includes both traditional keyword-based search and advanced natural language semantic search using techniques like vector embeddings. It interacts with the KBAL to retrieve content and an indexing service (potentially including an Approximate Nearest Neighbor - ANN index for embeddings) for efficient searching.
*   **Supported Test Cases:** Directly supports Test Case 4 (Natural Language Search) and contributes to Test Case 9 (Offline Access to Saved Content) for keyword search.
*   **Interaction:** Receives queries from the UI Layer (via Query Understanding Engine), interacts with the KBAL and local embedding/index stores.

### 3.4. Query Understanding Engine

*   **Responsibility:** Processes natural language input from the user to understand their intent. This involves parsing queries, identifying keywords, entities, and the type of request (e.g., search, Q&A, summarization). It then routes the processed request to the appropriate downstream component (Search Service or AI Services Gateway).
*   **Supported Test Cases:** Supports the natural language aspects of Test Cases 4 (Search) and 5 (AI Q&A).
*   **Interaction:** Receives input from the UI Layer, interacts with the Search Service and AI Services Gateway.

### 3.5. AI Services Gateway

*   **Responsibility:** Acts as a central point for AI-powered functionalities that may require external services or more computationally intensive local models. It routes requests for Q&A, summarization, content transformation, and *advanced/optional* conceptual link analysis (e.g., complex typed link prediction, multimodal linking if not handled locally) to the appropriate AI models. This gateway manages interactions with external AI services (like Gemini) and provides an interface for local AI models, aligning with the responsible AI integration principle and supporting a hybrid architecture. Core conceptual linking primarily relies on local processing via the Conceptual Linking Engine.
*   **Supported Test Cases:** Directly supports Test Cases 5 (AI Q&A), 6 (AI Summarization), 7 (AI Content Transformation), and contributes to advanced aspects of Test Case 8 (AI Suggested Conceptual Links).
*   **Interaction:** Receives requests from the UI Layer (via Query Understanding Engine for Q&A) or Conceptual Linking Engine (for advanced linking), interacts with external AI APIs or local AI models, and returns results.

### 3.6. Conceptual Linking Engine (CLE)

*   **Responsibility:** Orchestrates the generation, ranking, and presentation of conceptual links between knowledge items. It operates primarily on local data and models to ensure privacy and offline capability for core linking features.
    *   **Content Preprocessing:** Prepares content for linking (e.g., text extraction, cleaning, chunking).
    *   **Embedding Generation:** Creates vector embeddings for content items using local-first models (e.g., Sentence-Transformers).
    *   **Local Embedding Store & ANN Search:** Manages storage of embeddings and performs efficient similarity searches (e.g., using FAISS, HNSWLib).
    *   **Link Suggestion Strategies:** Implements various strategies for suggesting links:
        *   **Semantic Similarity:** Core local-first linking based on embedding similarity.
        *   **Typed Link Prediction (Phased):** Future enhancement to identify types of relationships (e.g., "supports," "contradicts"), potentially using local NLI models or, for more complex scenarios, the AI Services Gateway.
    *   **Link Ranking & Filtering:** Ranks suggested links based on relevance, novelty, and user context. Allows user-configurable filtering.
    *   **Local Knowledge Graph (Optional/Phased):** Future enhancement to build and utilize a lightweight local KG to enrich linking capabilities (e.g., entity/relationship extraction).
    *   **Highlighting Support:** Identifies and provides data for highlighting specific text segments that support suggested links.
*   **Supported Test Cases:** Directly supports Test Case 8 (AI Suggested Conceptual Links), focusing on local-first implementation.
*   **Interaction:**
    *   Interacts with KBAL to access and preprocess content.
    *   Manages local embedding generation and storage.
    *   May interact with the AI Services Gateway for *advanced or optional* linking strategies (e.g., complex GNNs, multimodal linking) as part of a hybrid approach in later phases.
    *   Provides suggested links, supporting text, and ranking information to the UI Layer.
    *   Receives user feedback and configuration from the UI Layer.

### 3.7. Offline Access Handler

*   **Responsibility:** Manages the system's behavior when an internet connection is not available. It ensures that functionalities relying solely on locally stored data (browsing, viewing saved content, basic keyword search via the Search Service, core conceptual linking via CLE) remain operational. It should gracefully handle requests for online-dependent features by providing appropriate user feedback.
*   **Supported Test Cases:** Crucial for meeting the requirements of Test Case 9 (Offline Access to Saved Content) and reinforcing the local-first principle for core features.
*   **Interaction:** Intercepts requests from the UI Layer and routes them appropriately based on network status and feature dependency, interacting with the KBAL, Search Service, and Conceptual Linking Engine for offline capabilities.

## 4. Component Interactions (Conceptual Flow - Updated for Linking)

```mermaid
graph TD
    A[User Interface (UI) Layer] --> B(Query Understanding Engine);
    A --> C(KBAL);
    A --> D(Search Service);
    A --> E(AI Services Gateway);
    A --> CLE(Conceptual Linking Engine);
    A --> G(Offline Access Handler);

    B --> D;
    B --> E;

    D --> C;
    D --> CLE_EmbStore((Local Embedding Store / ANN Index));

    E --> H(External AI Services);
    E --> I(Local AI Models - Advanced);

    CLE --> C;
    CLE --> CLE_EmbStore;
    CLE --> E; %% For optional advanced linking

    G --> C;
    G --> D;
    G --> CLE;

    C --> J(Local Storage - Raw Content);
    CLE_EmbStore --> J_Emb(Local Storage - Embeddings/Index);
```

*   **Browsing (Test Case 3):** UI Layer requests content list/details from KBAL.
*   **Natural Language Search (Test Case 4):** UI Layer sends query to Query Understanding Engine. Engine processes and sends to Search Service. Search Service queries KBAL (and potentially local embedding store via CLE for semantic aspects) and returns results to UI Layer.
*   **AI Q&A (Test Case 5):** UI Layer sends query and selected content reference to Query Understanding Engine. Engine identifies Q&A request and routes to AI Services Gateway with content. Gateway interacts with AI model and returns answer to UI Layer.
*   **AI Summarization/Transformation (Test Cases 6 & 7):** UI Layer sends request and selected content reference to AI Services Gateway. Gateway interacts with AI model and returns result to UI Layer.
*   **AI Suggested Conceptual Links (Test Case 8):**
    *   **Background Processing (or on-demand):** CLE accesses content via KBAL, generates/updates embeddings, stores them in Local Embedding Store.
    *   **Suggestion Request:** UI Layer requests links for an item. CLE uses Local Embedding Store (ANN search) for semantic similarity, applies ranking/filtering, and identifies supporting text.
    *   **Presentation:** CLE provides suggestions to UI Layer.
    *   **Advanced (Phased):** For typed links or more complex analysis, CLE might leverage local NLI models or consult AI Services Gateway.
*   **Offline Access (Test Case 9):** Offline Access Handler intercepts UI requests. If offline:
    *   Browsing/Basic Search: Routes to KBAL/Search Service.
    *   Core Conceptual Linking: Routes to CLE (using local embeddings).
    *   Online-dependent AI features: Provides feedback to UI.

## 5. Technology Considerations (High-Level - Updated for Linking)

*   **UI Layer:** Modern JavaScript framework (e.g., React, Vue). Libraries for graph visualization (e.g., vis.js, Cytoscape.js) for link display.
*   **KBAL & Storage:** Local storage solution (e.g., Electron's `app.getPath('userData')` for file system, IndexedDB, SQLite).
*   **Search Service:** Keyword search (standard indexing). Semantic search via Conceptual Linking Engine's embedding store.
*   **Query Understanding Engine:** NLP libraries or AI models via AI Services Gateway.
*   **AI Services Gateway:** Integration with external AI APIs (e.g., Gemini API), secure API key management.
*   **Conceptual Linking Engine:**
    *   **Embedding Models:** Lightweight Sentence-Transformers (e.g., `all-MiniLM-L6-v2`) for local execution.
    *   **Embedding Storage/Search:** Local databases with vector support (e.g., SQLite with extensions, TinyDB) combined with ANN libraries (e.g., FAISS, HNSWLib, `hnswlib-node`).
    *   **NLI Models (Phased):** Distilled models (e.g., `cross-encoder/nli-MiniLM-L6-H768`) for local typed link prediction.
    *   **Content Processing:** Libraries for text extraction from various formats (including PDFs).
*   **Offline Access Handler:** Network status detection and conditional routing logic.

## 6. Alignment with Master Project Plan and Acceptance Tests

This updated architecture directly supports the AI verifiable tasks and high-level acceptance tests by:

*   Providing distinct components responsible for each core function, with an enhanced Conceptual Linking Engine.
*   Defining clear interaction points, enabling data flow for each test case, especially Test Case 8.
*   Explicitly detailing local-first mechanisms for core conceptual linking (embedding generation, ANN search) within the CLE.
*   Incorporating recommendations from AI Linking Strategies Implementation Research, such as phased implementation for advanced linking features and considerations for a hybrid model.
*   Emphasizing user control and interpretability in the UI Layer for linking features.
*   Laying the groundwork for implementing AI verifiable completion criteria for Test Case 8, focusing on meaningful conceptual links and highlighted supporting text, initially via local semantic similarity.

## 7. Self-Reflection and Potential Issues

*   **Quality:** Modular design promotes maintainability. The phased approach for linking allows for iterative quality improvements.
*   **Security:** AI Services Gateway remains critical for external interactions. Local data handling by KBAL and CLE needs to be secure.
*   **Performance:**
    *   Local embedding generation and ANN search performance on diverse hardware for large knowledge bases is a key challenge.
    *   PDF content processing for linking can be resource-intensive.
    *   Optimization of local models and indexing strategies will be crucial.
*   **Maintainability:** Clear separation of concerns aids maintainability. The phased introduction of linking complexity helps manage development.
*   **Alignment:** Architecture aligns with MPP, acceptance tests, and incorporates key research findings for AI linking.

Potential issues include:
*   Complexity of managing local AI models and their dependencies.
*   Balancing the richness of conceptual links with performance and resource constraints.
*   Designing effective user feedback mechanisms for link quality.
*   Ensuring robust handling of diverse content types for linking.

## 8. Phased Implementation for Conceptual Linking

Reflecting the AI Linking Strategies Implementation Research, the Conceptual Linking Engine's capabilities will be developed iteratively:

1.  **Phase 1: Core Semantic Similarity Linking:**
    *   Local embedding generation for text content.
    *   Local ANN search for similarity-based link suggestions.
    *   Basic ranking and UI presentation with highlighted supporting text.
    *   Offline capability for these core links.
2.  **Phase 2: Enhanced Ranking & User Control:**
    *   Advanced ranking algorithms (novelty, user context).
    *   User configuration for linking parameters and filtering.
    *   Improved UI for link exploration and feedback.
3.  **Phase 3: Typed Links & Basic Local KG (Optional):**
    *   Integration of local NLI models for basic typed link prediction (e.g., "supports," "contradicts").
    *   Exploration of lightweight local Knowledge Graph construction and utilization.
4.  **Phase 4: Advanced & Hybrid Linking (Future):**
    *   Exploration of more complex linking (e.g., multimodal, advanced GNNs) potentially leveraging the AI Services Gateway in a hybrid model.

## 9. Next Steps

Based on this updated high-level architecture, the next steps in the SPARC framework would involve:

*   **Detailed Design:** Breaking down each component, especially the Conceptual Linking Engine's phases, into smaller units, defining specific data structures, APIs, and algorithms.
*   **Framework Scaffolding:** Setting up project structure and dependencies for local AI model integration and embedding management.
*   **Granular Test Specification & Generation:** Creating detailed tests for linking functionalities, starting with Phase 1.
*   **Feature Implementation (TDD):** Developing components following TDD, focusing initially on core semantic linking.