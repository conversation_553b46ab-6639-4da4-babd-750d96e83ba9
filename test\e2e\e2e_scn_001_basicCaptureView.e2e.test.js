// test/e2e/e2e_scn_001_basicCaptureView.e2e.test.js

// Mock WebContentCaptureModule
const mockCaptureWebContent = jest.fn();
const mockGetWebContentMetadata = jest.fn();
jest.mock('../../src/web-content-capture/index.js', () => ({
  captureWebContent: mockCaptureWebContent,
  getWebContentMetadata: mockGetWebContentMetadata,
}));

// Mock KBAL (storage service)
const mockSaveCapturedItem = jest.fn();
const mockGetCapturedItem = jest.fn(); // For simulating viewing
const mockStorage = {
  saveItem: mockSaveCapturedItem,
  getItem: mockGetCapturedItem,
};
jest.mock('../../src/knowledge-base-interaction/kbal/services/kbalService.js', () => mockStorage, { virtual: true });


// Helper function to simulate the workflow for E2E_SCN_001
async function simulateBasicCaptureAndViewWorkflow({
  captureUrl,
  captureMode, // e.g., "article"
  simulatedWebContent,
  simulatedMetadata, // { url, title, capturedDate }
  userSelectedFormat, // e.g., "Markdown"
}) {
  // 1. User navigates to a target webpage (implicit)
  // 2. User activates the browser extension and initiates content capture
  mockCaptureWebContent.mockResolvedValue(simulatedWebContent);
  mockGetWebContentMetadata.mockResolvedValue(simulatedMetadata);

  const capturedContent = await require('../../src/web-content-capture/index.js').captureWebContent(captureMode, captureUrl);
  const metadata = await require('../../src/web-content-capture/index.js').getWebContentMetadata(captureUrl);

  // 3. Browser extension processes and sends the content to the main application (implicit)
  // 4. Content is saved to the local knowledge base
  const itemToSave = {
    id: `item-${Date.now()}`, // Generate a unique ID
    ...metadata, // includes url, title, capturedDate
    content: capturedContent,
    format: userSelectedFormat,
  };
  await mockStorage.saveItem(itemToSave);

  // 5. User opens the main application UI (implicit)
  // 6. User navigates to the Knowledge Base view (implicit)
  // 7. User locates and selects the newly captured content item from the list (simulated by getItem)
  mockGetCapturedItem.mockResolvedValue(itemToSave); // Simulate fetching the saved item
  const viewedItem = await mockStorage.getItem(itemToSave.id);

  return { savedItem: itemToSave, viewedItem };
}

describe('E2E_SCN_001: Basic Content Capture and Viewing', () => {
  beforeEach(() => {
    mockCaptureWebContent.mockClear();
    mockGetWebContentMetadata.mockClear();
    mockSaveCapturedItem.mockClear();
    mockGetCapturedItem.mockClear();
  });

  test('should capture web content, store it, and allow viewing', async () => {
    const testParams = {
      captureUrl: 'http://example.com/article1',
      captureMode: 'article',
      simulatedWebContent: 'This is the main content of article 1.',
      simulatedMetadata: {
        url: 'http://example.com/article1',
        title: 'Test Article 1',
        capturedDate: new Date().toISOString(),
      },
      userSelectedFormat: 'Markdown',
    };

    const { savedItem, viewedItem } = await simulateBasicCaptureAndViewWorkflow(testParams);

    // Verifications for capture
    expect(mockCaptureWebContent).toHaveBeenCalledWith(testParams.captureMode, testParams.captureUrl);
    expect(mockGetWebContentMetadata).toHaveBeenCalledWith(testParams.captureUrl);

    // Verifications for storage (kbalService.saveItem)
    expect(mockSaveCapturedItem).toHaveBeenCalledTimes(1);
    const actualSavedItem = mockSaveCapturedItem.mock.calls[0][0];
    expect(actualSavedItem.content).toBe(testParams.simulatedWebContent);
    expect(actualSavedItem.format).toBe(testParams.userSelectedFormat);
    expect(actualSavedItem.url).toBe(testParams.simulatedMetadata.url);
    expect(actualSavedItem.title).toBe(testParams.simulatedMetadata.title);
    expect(actualSavedItem.capturedDate).toBe(testParams.simulatedMetadata.capturedDate);
    expect(actualSavedItem).toHaveProperty('id');

    // Verifications for viewing (kbalService.getItem)
    expect(mockGetCapturedItem).toHaveBeenCalledWith(actualSavedItem.id);
    expect(viewedItem).toEqual(actualSavedItem);
  });
});