import { defineConfig, devices } from '@playwright/test';
import path from 'path';

// Define the extension path relative to this config file
const EXTENSION_PATH = path.join(__dirname, 'apps', 'chrome-extension', 'dist');
console.log(`Playwright Config: EXTENSION_PATH resolved to: ${EXTENSION_PATH}`);

export default defineConfig({
  testDir: './apps/chrome-extension/tests/e2e',
  testMatch: '**/*.spec.ts',
  timeout: 60000,
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',

  use: {
    trace: 'on-first-retry',
    // Configure the browser to load the extension
    // This will be handled in the beforeEach hook of each test file
    // by launching a new context with the extension loaded.
  },

  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        // The extension loading arguments will be passed directly to browser.newContext
        // in the beforeEach hook of the test files.
        // Ensure the extension is loaded correctly
        launchOptions: {
          args: [
            `--disable-extensions-except=${EXTENSION_PATH}`,
            `--load-extension=${EXTENSION_PATH}`,
          ],
        },
      },
    },
  ],
});
