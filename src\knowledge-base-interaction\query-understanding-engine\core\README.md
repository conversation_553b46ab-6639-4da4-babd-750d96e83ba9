# Query Understanding Engine - Core Components

This directory contains the core logic for the Query Understanding Engine (QUE).

## Components:

-   **[`queryUnderstandingEngine.js`](queryUnderstandingEngine.js:1)**: The main orchestrator for the query understanding process. It integrates the functionalities of parsing, intent recognition, entity extraction, and request routing.
-   **[`queryParser.js`](queryParser.js:1)**: Responsible for the initial processing of the raw natural language query. This includes tasks like tokenization, and potentially basic linguistic analysis, to transform the input string into a more structured format suitable for subsequent processing stages.

## AI Verifiability:

-   Existence of this `README.md` file.
-   Existence of `queryUnderstandingEngine.js` and `queryParser.js`.

---
*AI-VERIFIABLE: README.md for QUE core components created.*