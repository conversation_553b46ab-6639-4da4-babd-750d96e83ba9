// __tests__/NavigationFiltersPane.test.js
import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import NavigationFiltersPane from '../renderer/components/NavigationFiltersPane';
import useStore from '../renderer/store/useStore';

// Mock the API client module - no longer needed here as component doesn't call it directly
// jest.mock('../renderer/api/client');

// Mock the Zustand store
const mockSetSearchTerm = jest.fn();
const mockPerformSearch = jest.fn();
const mockSetSelectedTags = jest.fn();
const mockSetDateRangeFilter = jest.fn();
const mockSetSelectedContentTypes = jest.fn();
const mockClearAdvancedFilters = jest.fn();
const mockSetSelectedCategories = jest.fn();
const mockSetSourceFilter = jest.fn();
const mockSetTagFilterLogic = jest.fn();

// Initial store state for tests
let mockStoreState = {
  searchTerm: '',
  searchLoading: false,
  searchError: null,
  allTags: ['tech', 'ai', 'news'],
  selectedTags: [],
  dateRangeFilter: { startDate: null, endDate: null },
  allContentTypes: ['article', 'pdf', 'bookmark'],
  selectedContentTypes: [],
  allCategories: ['work', 'personal', 'project'],
  selectedCategories: [],
  sourceFilter: '',
  tagFilterLogic: 'AND',
};

jest.mock('../renderer/store/useStore', () => {
  return {
    __esModule: true,
    default: jest.fn((selector) => selector(mockStoreState)), // Allow selector to access mockStoreState
  };
});


// Helper to update mock store state for specific tests
const setMockStoreState = (newState) => {
  mockStoreState = { ...mockStoreState, ...newState };
};

// Helper to reset all mocks and mock store state
const resetAllMocks = () => {
  mockSetSearchTerm.mockClear();
  mockPerformSearch.mockClear();
  mockSetSelectedTags.mockClear();
  mockSetDateRangeFilter.mockClear();
  mockSetSelectedContentTypes.mockClear();
  mockClearAdvancedFilters.mockClear();
  mockSetSelectedCategories.mockClear();
  mockSetSourceFilter.mockClear();
  mockSetTagFilterLogic.mockClear();

  // Reset store state to initial
  mockStoreState = {
    searchTerm: '',
    searchLoading: false,
    searchError: null,
    allTags: ['tech', 'ai', 'news'],
    selectedTags: [],
    dateRangeFilter: { startDate: null, endDate: null },
    allContentTypes: ['article', 'pdf', 'bookmark'],
    selectedContentTypes: [],
    allCategories: ['work', 'personal', 'project'],
    selectedCategories: [],
    sourceFilter: '',
    tagFilterLogic: 'AND',
    // Ensure all actions are available in the mock state for the component to pick up
    setSearchTerm: mockSetSearchTerm,
    performSearch: mockPerformSearch,
    setSelectedTags: mockSetSelectedTags,
    setDateRangeFilter: mockSetDateRangeFilter,
    setSelectedContentTypes: mockSetSelectedContentTypes,
    clearAdvancedFilters: mockClearAdvancedFilters,
    setSelectedCategories: mockSetSelectedCategories,
    setSourceFilter: mockSetSourceFilter,
    setTagFilterLogic: mockSetTagFilterLogic,
  };
  // Re-apply the mock implementation with the reset state
   useStore.mockImplementation((selector) => selector ? selector(mockStoreState) : mockStoreState);
};


describe('NavigationFiltersPane Component', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  test('renders the main heading "Navigation & Filters"', () => {
    render(<NavigationFiltersPane className="test-pane" />);
    expect(screen.getByRole('heading', { name: /navigation & filters/i, level: 3 })).toBeInTheDocument();
  });

  describe('Search Section', () => {
    test('renders the "Search" subheading, input field, and search button', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByRole('heading', { name: /search/i, level: 4 })).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/search knowledge.../i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
    });

    test('updates search term in store on input change and triggers performSearch via useEffect', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      const searchInput = screen.getByPlaceholderText(/search knowledge.../i);

      act(() => {
        fireEvent.change(searchInput, { target: { value: 'test query' } });
      });

      expect(mockSetSearchTerm).toHaveBeenCalledWith('test query');
      // performSearch is called by useEffect due to searchTerm change
      // The number of calls depends on initial render + change.
      // We expect it to be called after the change.
      expect(mockPerformSearch).toHaveBeenCalled();
    });

    test('calls performSearch from store on form submit', () => {
      setMockStoreState({ searchTerm: 'submit query' });
      render(<NavigationFiltersPane className="test-pane" />);

      act(() => {
        // Find the form by its container instead of role
        const searchForm = screen.getByPlaceholderText(/search knowledge.../i).closest('form');
        fireEvent.submit(searchForm);
      });
      expect(mockPerformSearch).toHaveBeenCalledWith('submit query');
    });

    test('displays loading state on search button when searchLoading is true', () => {
      setMockStoreState({ searchLoading: true });
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByRole('button', { name: /searching.../i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /searching.../i })).toBeDisabled();
    });

    test('displays error message when searchError is present', () => {
      const errorMessage = "Network Error";
      setMockStoreState({ searchError: errorMessage });
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByText(`Search Error: ${errorMessage}`)).toBeInTheDocument();
    });
  });

  describe('Advanced Filters UI and Interactions', () => {
    test('renders "Filter by Tags (AND)" section with checkboxes and default AND logic', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByRole('heading', { name: /filter by tags \(AND\)/i, level: 4 })).toBeInTheDocument();
      expect(screen.getByLabelText('AND')).toBeChecked();
      expect(screen.getByLabelText('OR')).not.toBeChecked();
      mockStoreState.allTags.forEach(tag => {
        expect(screen.getByLabelText(tag)).toBeInTheDocument();
      });
    });

    test('clicking a tag checkbox calls setSelectedTags and triggers performSearch', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      const tagCheckbox = screen.getByLabelText('tech');

      act(() => {
        fireEvent.click(tagCheckbox);
      });

      expect(mockSetSelectedTags).toHaveBeenCalledWith(['tech']);
      expect(mockPerformSearch).toHaveBeenCalled(); // Called by useEffect
    });

    test('tag checkbox reflects selectedTags state', () => {
        setMockStoreState({ selectedTags: ['ai'] });
        render(<NavigationFiltersPane className="test-pane" />);
        expect(screen.getByLabelText('ai')).toBeChecked();
        expect(screen.getByLabelText('tech')).not.toBeChecked();
    });

    test('changing tag logic calls setTagFilterLogic and triggers performSearch', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      const orRadio = screen.getByLabelText('OR');

      act(() => {
        fireEvent.click(orRadio);
      });

      expect(mockSetTagFilterLogic).toHaveBeenCalledWith('OR');
      expect(mockPerformSearch).toHaveBeenCalled(); // Called by useEffect
      // Update mock state for the heading check
      setMockStoreState({ tagFilterLogic: 'OR' });
      // Re-render or check the heading text if it's dynamic based on state
      // For simplicity, we assume the component re-renders with new state from the store
      // If the heading text itself is dynamic and re-renders, this would be:
      // expect(screen.getByRole('heading', { name: /filter by tags \(or\)/i, level: 4 })).toBeInTheDocument();
    });


    test('renders "Filter by Categories" section with checkboxes', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByRole('heading', { name: /filter by categories/i, level: 4 })).toBeInTheDocument();
      mockStoreState.allCategories.forEach(category => {
        expect(screen.getByLabelText(category)).toBeInTheDocument();
      });
    });

    test('clicking a category checkbox calls setSelectedCategories and triggers performSearch', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      const categoryCheckbox = screen.getByLabelText('work');

      act(() => {
        fireEvent.click(categoryCheckbox);
      });

      expect(mockSetSelectedCategories).toHaveBeenCalledWith(['work']);
      expect(mockPerformSearch).toHaveBeenCalled(); // Called by useEffect
    });

    test('category checkbox reflects selectedCategories state', () => {
      setMockStoreState({ selectedCategories: ['personal'] });
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByLabelText('personal')).toBeChecked();
      expect(screen.getByLabelText('work')).not.toBeChecked();
    });

    test('renders "Filter by Source" section with text input', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByRole('heading', { name: /filter by source/i, level: 4 })).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/enter source \(e.g., example.com\)/i)).toBeInTheDocument();
    });

    test('typing in source input calls setSourceFilter and triggers performSearch', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      const sourceInput = screen.getByPlaceholderText(/enter source \(e.g., example.com\)/i);

      act(() => {
        fireEvent.change(sourceInput, { target: { value: 'example.com' } });
      });

      expect(mockSetSourceFilter).toHaveBeenCalledWith('example.com');
      expect(mockPerformSearch).toHaveBeenCalled(); // Called by useEffect
    });

    test('source input reflects sourceFilter state', () => {
      setMockStoreState({ sourceFilter: 'testsite.org' });
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByPlaceholderText(/enter source \(e.g., example.com\)/i)).toHaveValue('testsite.org');
    });

    test('renders "Filter by Creation Date" section with date inputs', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByRole('heading', { name: /filter by creation date/i, level: 4 })).toBeInTheDocument();
      expect(screen.getByLabelText(/from:/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/to:/i)).toBeInTheDocument();
    });

    test('changing a date input calls setDateRangeFilter and triggers performSearch', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      const startDateInput = screen.getByLabelText(/from:/i);

      act(() => {
        fireEvent.change(startDateInput, { target: { name: 'startDate', value: '2023-01-01' } });
      });

      expect(mockSetDateRangeFilter).toHaveBeenCalledWith({ startDate: '2023-01-01', endDate: null });
      expect(mockPerformSearch).toHaveBeenCalled(); // Called by useEffect
    });

    test('date inputs reflect dateRangeFilter state', () => {
        setMockStoreState({ dateRangeFilter: { startDate: '2023-05-01', endDate: '2023-05-15'} });
        render(<NavigationFiltersPane className="test-pane" />);
        expect(screen.getByLabelText(/from:/i)).toHaveValue('2023-05-01');
        expect(screen.getByLabelText(/to:/i)).toHaveValue('2023-05-15');
    });

    test('renders "Filter by Content Type" section with checkboxes', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      expect(screen.getByRole('heading', { name: /filter by content type/i, level: 4 })).toBeInTheDocument();
      mockStoreState.allContentTypes.forEach(type => {
        expect(screen.getByLabelText(type)).toBeInTheDocument();
      });
    });

    test('clicking a content type checkbox calls setSelectedContentTypes and triggers performSearch', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      const contentTypeCheckbox = screen.getByLabelText('pdf');

      act(() => {
        fireEvent.click(contentTypeCheckbox);
      });

      expect(mockSetSelectedContentTypes).toHaveBeenCalledWith(['pdf']);
      expect(mockPerformSearch).toHaveBeenCalled(); // Called by useEffect
    });

    test('content type checkbox reflects selectedContentTypes state', () => {
        setMockStoreState({ selectedContentTypes: ['article'] });
        render(<NavigationFiltersPane className="test-pane" />);
        expect(screen.getByLabelText('article')).toBeChecked();
        expect(screen.getByLabelText('pdf')).not.toBeChecked();
    });


    test('renders "Clear All Filters" button and calls clearAdvancedFilters and performSearch on click', () => {
      render(<NavigationFiltersPane className="test-pane" />);
      const clearButton = screen.getByRole('button', { name: /clear all filters/i });
      expect(clearButton).toBeInTheDocument();

      act(() => {
        fireEvent.click(clearButton);
      });

      expect(mockClearAdvancedFilters).toHaveBeenCalled();
      expect(mockPerformSearch).toHaveBeenCalled(); // Called by useEffect after filters are cleared
    });
  });

  // Obsolete test suites for Tags (old fetching logic) and Categories have been removed.
  // The "Main Sections" was also removed from the component.
});
