import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

const AIInteractionPanel = ({
  aiPanelArgs,
  onSubmit,
  onClose,
  qaQuestion,
  qaAnswer,
  qaLoading,
  qaError,
  summary,
  summaryLoading,
  summaryError,
  summaryGenerated,
  transformedContent,
  transformationLoading,
  transformationError,
  selectedTransformationType,
  onSaveTransformedContent,
  savingNewItemLoading,
  savingNewItemError,
  onClearSaveNewItemStatus,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [transformationTypeInput, setTransformationTypeInput] = useState('');

  useEffect(() => {
    // Reset input when panel type changes
    setInputValue('');
    setTransformationTypeInput('');
  }, [aiPanelArgs?.type]);

  const handleInputChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleTransformationTypeChange = (event) => {
    setTransformationTypeInput(event.target.value);
  };

  const handleSubmit = () => {
    if (aiPanelArgs.type === 'qa') {
      if (inputValue.trim() !== '') {
        onSubmit(inputValue);
        setInputValue('');
      }
    } else if (aiPanelArgs.type === 'summarize') {
      onSubmit(); // No input needed for summarize, just trigger
    } else if (aiPanelArgs.type === 'transform') {
      if (transformationTypeInput.trim() !== '') {
        onSubmit(transformationTypeInput); // Submit transformation type
      }
    }
  };

  const handleSaveTransformedContent = () => {
    if (transformedContent) {
      onSaveTransformedContent(transformedContent);
    }
  };

  const renderContent = () => {
    switch (aiPanelArgs?.type) {
      case 'qa':
        return (
          <>
            <div className="ai-panel-input">
              <textarea
                value={inputValue}
                onChange={handleInputChange}
                placeholder="Ask a question about the selected item..."
                aria-label="AI QA input area"
                rows="4"
              />
              <button onClick={handleSubmit} disabled={qaLoading} aria-label="Submit QA query">
                {qaLoading ? 'Loading...' : 'Ask'}
              </button>
            </div>
            <div className="ai-panel-output" data-testid="qa-output-area">
              {qaLoading && <p>Thinking...</p>}
              {qaError && <p className="error">Error: {qaError}</p>}
              {qaAnswer && (
                <>
                  <h4>Question:</h4>
                  <p>{qaQuestion}</p>
                  <h4>Answer:</h4>
                  <p>{qaAnswer}</p>
                </>
              )}
              {!qaLoading && !qaAnswer && !qaError && <p>Enter your question above.</p>}
            </div>
          </>
        );
      case 'summarize':
        return (
          <>
            <div className="ai-panel-input">
              <button onClick={handleSubmit} disabled={summaryLoading} aria-label="Generate summary">
                {summaryLoading ? 'Generating...' : 'Generate Summary'}
              </button>
            </div>
            <div className="ai-panel-output" data-testid="summary-output-area">
              {summaryLoading && <p>Generating summary...</p>}
              {summaryError && <p className="error">Error: {summaryError}</p>}
              {summaryGenerated && (
                <>
                  <h4>Summary:</h4>
                  <p>{summary}</p>
                </>
              )}
              {!summaryLoading && !summaryGenerated && !summaryError && <p>Click "Generate Summary" to get an AI summary of the item.</p>}
            </div>
          </>
        );
      case 'transform':
        return (
          <>
            <div className="ai-panel-input">
              <input
                type="text"
                value={transformationTypeInput}
                onChange={handleTransformationTypeChange}
                placeholder="e.g., markdown, plain text, bullet points"
                aria-label="Transformation type input"
              />
              <button onClick={handleSubmit} disabled={transformationLoading} aria-label="Transform content">
                {transformationLoading ? 'Transforming...' : 'Transform'}
              </button>
            </div>
            <div className="ai-panel-output" data-testid="transform-output-area">
              {transformationLoading && <p>Transforming content...</p>}
              {transformationError && <p className="error">Error: {transformationError}</p>}
              {transformedContent && (
                <>
                  <h4>Transformed Content ({selectedTransformationType}):</h4>
                  <textarea value={transformedContent} readOnly rows="10" />
                  <button
                    onClick={handleSaveTransformedContent}
                    disabled={savingNewItemLoading}
                    aria-label="Save transformed content as new item"
                  >
                    {savingNewItemLoading ? 'Saving...' : 'Save as New Item'}
                  </button>
                  {savingNewItemError && <p className="error">Save Error: {savingNewItemError}</p>}
                  {!savingNewItemLoading && !savingNewItemError && transformedContent && (
                    <p className="success">Content saved successfully!</p>
                  )}
                  {savingNewItemError && (
                    <button onClick={onClearSaveNewItemStatus} className="clear-status-button">
                      Clear Status
                    </button>
                  )}
                </>
              )}
              {!transformationLoading && !transformedContent && !transformationError && <p>Enter a transformation type and click "Transform".</p>}
            </div>
          </>
        );
      default:
        return <p>Select an AI interaction type from the action bar.</p>;
    }
  };

  return (
    <div className="ai-interaction-panel">
      <div className="ai-panel-header">
        <h3>AI Interaction</h3>
        <button onClick={onClose} className="close-button" aria-label="Close AI panel">
          &times;
        </button>
      </div>
      <div className="ai-panel-content">
        {renderContent()}
      </div>
    </div>
  );
};

AIInteractionPanel.propTypes = {
  aiPanelArgs: PropTypes.shape({
    type: PropTypes.string.isRequired, // 'qa', 'summarize', 'transform'
    itemId: PropTypes.string,
  }),
  onSubmit: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  qaQuestion: PropTypes.string,
  qaAnswer: PropTypes.string,
  qaLoading: PropTypes.bool,
  qaError: PropTypes.string,
  summary: PropTypes.string,
  summaryLoading: PropTypes.bool,
  summaryError: PropTypes.string,
  summaryGenerated: PropTypes.bool,
  transformedContent: PropTypes.string,
  transformationLoading: PropTypes.bool,
  transformationError: PropTypes.string,
  selectedTransformationType: PropTypes.string,
  onSaveTransformedContent: PropTypes.func.isRequired,
  savingNewItemLoading: PropTypes.bool,
  savingNewItemError: PropTypes.string,
  onClearSaveNewItemStatus: PropTypes.func.isRequired,
};

AIInteractionPanel.defaultProps = {
  aiPanelArgs: null,
  qaQuestion: '',
  qaAnswer: '',
  qaLoading: false,
  qaError: '',
  summary: '',
  summaryLoading: false,
  summaryError: '',
  summaryGenerated: false,
  transformedContent: '',
  transformationLoading: false,
  transformationError: '',
  selectedTransformationType: '',
  savingNewItemLoading: null, // Use null to indicate no save attempt yet
  savingNewItemError: '',
};

export default AIInteractionPanel;