import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoryManagement from '../renderer/components/CategoryManagement';
import useStore from '../renderer/store/useStore';

// Mock the Zustand store
jest.mock('../renderer/store/useStore');

const mockFetchCategories = jest.fn();
const mockCreateCategory = jest.fn();
const mockUpdateCategory = jest.fn();
const mockDeleteCategory = jest.fn();

const initialCategories = [
  { id: 'c1', name: 'Work' },
  { id: 'c2', name: 'Personal' },
];

describe('CategoryManagement Component', () => {
  beforeEach(() => {
    // Reset mocks and store state before each test
    mockFetchCategories.mockClear();
    mockCreateCategory.mockClear();
    mockUpdateCategory.mockClear();
    mockDeleteCategory.mockClear();

    useStore.mockImplementation((selector) => {
      const state = {
        allCategories: initialCategories,
        fetchCategories: mockFetchCategories,
        createCategory: mockCreateCategory,
        updateCategory: mockUpdateCategory,
        deleteCategory: mockDeleteCategory,
        categoryOperationLoading: false,
        categoryOperationError: null,
      };
      return selector ? selector(state) : state;
    });
  });

  test('renders component, fetches and displays categories', async () => {
    useStore.mockImplementation((selector) => {
      const state = {
        allCategories: initialCategories,
        fetchCategories: mockFetchCategories,
        createCategory: mockCreateCategory,
        updateCategory: mockUpdateCategory,
        deleteCategory: mockDeleteCategory,
        categoryOperationLoading: false,
        categoryOperationError: null,
      };
      return selector ? selector(state) : state;
    });
    render(<CategoryManagement />);
    expect(screen.getByText('Manage Categories')).toBeInTheDocument();
    expect(mockFetchCategories).toHaveBeenCalledTimes(1);
    await waitFor(() => {
      expect(screen.getByText('Work')).toBeInTheDocument();
      expect(screen.getByText('Personal')).toBeInTheDocument();
    });
  });

  test('allows creating a new category', async () => {
    mockCreateCategory.mockResolvedValueOnce({ id: 'c3', name: 'New Category' });
    render(<CategoryManagement />);

    fireEvent.change(screen.getByPlaceholderText('Enter new category name'), { target: { value: 'New Category' } });
    fireEvent.click(screen.getByRole('button', { name: 'Add Category' }));

    await waitFor(() => {
      expect(mockCreateCategory).toHaveBeenCalledWith('New Category');
    });
  });

  test('allows editing a category', async () => {
    mockUpdateCategory.mockResolvedValueOnce({ id: 'c1', name: 'Work Updated' });
    render(<CategoryManagement />);

    const workCategoryItem = screen.getByText('Work').closest('li');
    const editButton = workCategoryItem.querySelector('button:not(.delete-button)');
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText('Edit Category')).toBeInTheDocument();
    });

    fireEvent.change(screen.getByPlaceholderText('Enter updated category name'), { target: { value: 'Work Updated' } });
    fireEvent.click(screen.getByRole('button', { name: 'Save Changes' }));

    await waitFor(() => {
      expect(mockUpdateCategory).toHaveBeenCalledWith('c1', 'Work Updated');
    });
  });

  test('allows deleting a category with confirmation', async () => {
    window.confirm = jest.fn(() => true); // Mock window.confirm
    mockDeleteCategory.mockResolvedValueOnce();
    render(<CategoryManagement />);

    const workCategoryItem = screen.getByText('Work').closest('li');
    const deleteButton = workCategoryItem.querySelector('button.delete-button');
    fireEvent.click(deleteButton);

    expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete this category? This action cannot be undone.');
    await waitFor(() => {
      expect(mockDeleteCategory).toHaveBeenCalledWith('c1');
    });
  });

  test('displays error message if category operation fails', () => {
    useStore.mockImplementation((selector) => {
      const state = {
        allCategories: [],
        fetchCategories: mockFetchCategories,
        createCategory: mockCreateCategory,
        updateCategory: mockUpdateCategory,
        deleteCategory: mockDeleteCategory,
        categoryOperationLoading: false,
        categoryOperationError: 'Failed to fetch categories',
      };
      return selector ? selector(state) : state;
    });
    render(<CategoryManagement />);
    expect(screen.getByText('Error: Failed to fetch categories')).toBeInTheDocument();
  });

  test('shows loading indicator when categories are being fetched', () => {
    useStore.mockImplementation((selector) => {
      const state = {
        allCategories: [],
        fetchCategories: jest.fn(),
        createCategory: mockCreateCategory,
        updateCategory: mockUpdateCategory,
        deleteCategory: mockDeleteCategory,
        categoryOperationLoading: true,
        categoryOperationError: null,
      };
      return selector ? selector(state) : state;
    });
    render(<CategoryManagement />);
    expect(screen.getByText('Loading categories...')).toBeInTheDocument();
  });
});
