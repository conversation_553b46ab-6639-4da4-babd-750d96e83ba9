import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import DOMPurify from 'dompurify';
import { FixedSizeList as List } from 'react-window';

const ITEM_HEIGHT = 120; // Approximate height for each item, adjust as needed

const ContentList = ({ items = [], onSelectItem, selectedItemId }) => {
  if (!items || items.length === 0) {
    return <div className="content-list-empty">No items to display.</div>;
  }

  const Row = ({ index, style }) => {
    const item = items[index];

    const memoizedSanitizedItem = useMemo(() => {
      const sanitizedTitle = DOMPurify.sanitize(item.title || '');
      const sanitizedSnippet = item.snippet ? DOMPurify.sanitize(item.snippet) : '';
      const sanitizedTags = item.tags && item.tags.length > 0
        ? item.tags.map(tag => DOMPurify.sanitize(tag || '')).join(', ')
        : '';
      const displayTimestamp = item.timestamp
        ? new Date(item.timestamp).toLocaleDateString()
        : '';
      const sanitizedSource = item.source ? DOMPurify.sanitize(item.source) : '';
      return {
        sanitizedTitle,
        sanitizedSnippet,
        sanitizedTags,
        displayTimestamp,
        sanitizedSource,
      };
    }, [item]);

    const {
      sanitizedTitle,
      sanitizedSnippet,
      sanitizedTags,
      displayTimestamp,
      sanitizedSource,
    } = memoizedSanitizedItem;

    const handleItemClick = () => {
      if (onSelectItem) {
        onSelectItem(item); // Pass the full item object
      }
    };

    const handleItemKeyPress = (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        if (onSelectItem) {
          onSelectItem(item); // Pass the full item object
        }
      }
    };

    const isSelected = item.id === selectedItemId;

    return (
      <div style={style}>
        <li
          className="content-list-item"
          onClick={handleItemClick}
          onKeyPress={handleItemKeyPress}
          tabIndex="0"
          // role="button" // Removed: li should be listitem, interactivity is fine
          aria-label={`View details for ${sanitizedTitle}`}
        >
          <h3 className="content-list-item-title">{sanitizedTitle}</h3>
          {sanitizedSnippet && <p className="content-list-item-snippet">{sanitizedSnippet}</p>}
          {sanitizedTags && (
            <p className="content-list-item-tags">Tags: {sanitizedTags}</p>
          )}
          {item.timestamp && (
            <p className="content-list-item-timestamp">
              {displayTimestamp}
            </p>
          )}
          {sanitizedSource && <p className="content-list-item-source">Source: {sanitizedSource}</p>}
        </li>
      </div>
    );
  };

  Row.propTypes = {
    index: PropTypes.number.isRequired,
    style: PropTypes.object.isRequired,
  };


  // Diagnostic log
  console.log('[ContentList] Rendering FixedSizeList. Props will be logged by the mock if active, or by react-window itself.');
  console.log('[ContentList] Intended outerElementType: ul');

  return (
    <List
      outerElementType="ul" // Ensures the list has role="list"
      className="content-list" // Apply to the List wrapper if needed, or style directly
      height={600} // Example height, make this dynamic or configurable
      itemCount={items.length}
      itemSize={ITEM_HEIGHT}
      width="100%" // Example width, make this dynamic or configurable
      aria-label="Knowledge base items" // Good for the UL itself
    >
      {Row}
    </List>
  );
};

ContentList.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      snippet: PropTypes.string,
      tags: PropTypes.arrayOf(PropTypes.string),
      timestamp: PropTypes.string,
      source: PropTypes.string,
    })
  ),
  onSelectItem: PropTypes.func.isRequired,
};

// Removed defaultProps, handled in function signature

export default ContentList;