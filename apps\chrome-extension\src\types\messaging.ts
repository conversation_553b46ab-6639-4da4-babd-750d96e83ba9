// Define types for messaging and data structures used in the extension

/**
 * Represents a captured bookmark.
 */
export interface Bookmark {
  url: string;
  title: string;
  timestamp: string; // ISO string
  // Add other relevant metadata fields as needed later (e.g., faviconUrl)
}

/**
 * Represents data for updating a Knowledge Base entry.
 */
export interface UpdateEntryData {
  title?: string;
  url?: string;
  content?: string;
  tags?: string[];
  type?: string;
  // Add other fields that can be updated
}

/**
 * Represents data for creating a new Knowledge Base entry.
 */
export interface CreateEntryData {
  title: string;
  url?: string;
  content?: string;
  tags?: string[];
  type: string;
  // Add other fields required for creation
}

// Define other message types here as the project grows
// export interface CaptureRequest { ... }
// export interface CaptureResponse { ... }