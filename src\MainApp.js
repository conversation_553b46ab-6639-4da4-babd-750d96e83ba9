import React from 'react';
import styles from './styles/MainApp.module.css';
import IntelligentCaptureUI from './components/IntelligentCaptureUI'; // Added import

function MainApp() {
  // Mock handlers and data for IntelligentCaptureUI
  const handleTagAccept = (tag) => console.log('Accepted tag:', tag);
  const handleTagReject = (tag) => console.log('Rejected tag:', tag);
  const handleCategoryAccept = (category) => console.log('Accepted category:', category);
  const handleCategoryReject = (category) => console.log('Rejected category:', category);
  const handleTagAdd = (tag) => console.log('Added tag:', tag);
  const handleCategoryAdd = (category) => console.log('Added category:', category);
  const handleFeedback = (feedback) => console.log('Feedback:', feedback);

  const suggestedTags = ['ExampleTag1', 'ExampleTag2', 'AI Generated'];
  const suggestedCategories = ['Work', 'Personal', 'Research'];

  return (
    <div className={styles.mainAppContainer}>
      <h1>Main Application UI</h1>
      <p>This is the main application user interface.</p>
      
      <hr />
      <h2>Intelligent Capture Suggestions</h2>
      <IntelligentCaptureUI
        suggestedTags={suggestedTags}
        suggestedCategories={suggestedCategories}
        onTagAccept={handleTagAccept}
        onTagReject={handleTagReject}
        onCategoryAccept={handleCategoryAccept}
        onCategoryReject={handleCategoryReject}
        onTagAdd={handleTagAdd}
        onCategoryAdd={handleCategoryAdd}
        onFeedback={handleFeedback}
      />
    </div>
  );
}

export default MainApp;