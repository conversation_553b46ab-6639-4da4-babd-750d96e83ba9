# Query Understanding Engine - Request Routing

This directory is responsible for the logic that determines the appropriate downstream service or component to handle the processed user query.

## Components:

-   **[`requestRouter.js`](requestRouter.js:1)**: Contains the implementation for routing decisions. Based on the recognized intent, extracted entities, and potentially other contextual information, this component selects the most suitable service (e.g., Search Service, AI Services Gateway, or a specific Q&A module) to fulfill the user's request.

## AI Verifiability:

-   Existence of this `README.md` file.
-   Existence of `requestRouter.js`.

---
*AI-VERIFIABLE: README.md for QUE request routing component created.*