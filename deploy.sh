#!/bin/bash

echo "Starting deployment..."

# Pull the latest code
git pull origin main

# Install dependencies
npm install

# Build the project
npm run build

# Package the Chrome extension
echo "Packaging the Chrome extension..."
npm install -g crx
crx pack src/browser-extension-ui --private-key="$CHROME_PRIVATE_KEY" --outfile=extension.crx

# Upload the Chrome extension to the Chrome Web Store
echo "Uploading the Chrome extension to the Chrome Web Store..."
npm install -g chrome-webstore-upload
chrome-webstore-upload upload --source extension.crx --client_id="$CHROME_CLIENT_ID" --client_secret="$CHROME_CLIENT_SECRET" --refresh_token="$CHROME_REFRESH_TOKEN" --extension_id="$CHROME_EXTENSION_ID"

# Deploy the project
# TODO: Configure deployment to your hosting provider (e.g., Netlify, Vercel, or a custom server).
# Example for Netlify:
# npm install -g netlify-cli
# netlify deploy --prod

echo "Deployment complete!"