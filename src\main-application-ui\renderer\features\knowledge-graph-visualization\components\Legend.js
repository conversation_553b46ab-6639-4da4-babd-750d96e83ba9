import React from 'react';

const Legend = ({ visualEncodings, nodeTypeVisibility }) => {
  if (!visualEncodings) {
    return (
      <div data-testid="legend-actual">
        <h3>Legend</h3>
        <p>No visual encoding information available.</p>
      </div>
    );
  }

  const visibleNodeTypesCount = nodeTypeVisibility 
    ? Object.values(nodeTypeVisibility).filter(nt => nt.visible).length
    : (visualEncodings.nodeTypes ? Object.keys(visualEncodings.nodeTypes).length : 0);

  return (
    <div data-testid="legend-actual">
      <h3>Legend</h3>
      {/* This part is for the test mock:
      <p>{visibleNodeTypesCount} Node Types in Legend</p> 
      */}
      
      <h4>Node Types</h4>
      <ul>
        {visualEncodings.nodeTypes && Object.entries(visualEncodings.nodeTypes).map(([typeId, encoding]) => {
          const isVisible = nodeTypeVisibility && nodeTypeVisibility[typeId] ? nodeTypeVisibility[typeId].visible : true;
          if (!isVisible && nodeTypeVisibility) return null; // Don't show hidden types if visibility info is present

          return (
            <li key={typeId} style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '5px',
                opacity: isVisible || !nodeTypeVisibility ? 1 : 0.5,
                '--legend-color': encoding.color || '#666' // Set CSS custom property
              }}
            >
              <span style={{
                height: '15px',
                width: '15px',
                backgroundColor: encoding.color || '#666',
                border: '1px solid #333',
                marginRight: '8px',
                display: 'inline-block',
                borderRadius: encoding.shape === 'ellipse' || encoding.shape === 'circle' ? '50%' : (encoding.shape === 'rectangle' || encoding.shape === 'square' ? '0%' : '2px'), // Basic shapes
              }}></span>
              {encoding.label || typeId}
            </li>
          );
        })}
      </ul>

      <h4>Edge Types</h4>
      <ul>
        {visualEncodings.edgeTypes && Object.entries(visualEncodings.edgeTypes).map(([typeId, encoding]) => (
          <li key={typeId} style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '5px',
              '--legend-color': encoding.color || '#ccc' // Set CSS custom property
            }}
          >
            <span style={{
              height: encoding.style === 'dashed' ? '2px' : '3px', // Make dashed thinner
              width: '20px',
              backgroundColor: encoding.style !== 'dashed' ? (encoding.color || '#ccc') : 'transparent',
              borderTop: encoding.style === 'dashed' ? `2px dashed ${encoding.color || '#ccc'}` : 'none',
              marginRight: '8px',
              display: 'inline-block',
            }}></span>
            {encoding.label || typeId}
          </li>
        ))}
      </ul>
      {!visualEncodings.nodeTypes && !visualEncodings.edgeTypes && <p>No specific encodings defined.</p>}
    </div>
  );
};

export default Legend;