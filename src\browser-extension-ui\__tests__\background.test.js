// Mock browser APIs before importing background.js
global.chrome = {
    runtime: {
        onMessage: {
            addListener: jest.fn(),
            removeListener: jest.fn(),
            hasListener: jest.fn()
        },
        sendMessage: jest.fn((message) => { // Mock for sendMessageToPopup
            return Promise.resolve({ success: true });
        }),
        lastError: null
    },
    tabs: {
        query: jest.fn(),
        sendMessage: jest.fn()
    },
    action: { // For Manifest V3
        onClicked: {
            addListener: jest.fn()
        }
    },
    browserAction: { // For Manifest V2
        onClicked: {
            addListener: jest.fn()
        }
    }
};
global.browser = global.chrome; // For Firefox compatibility layer in tests

// Helper to wait for the next tick of the event loop, ensuring microtasks are processed.
const nextTick = () => new Promise(resolve => process.nextTick(resolve));

// Define mock functions *before* jest.mock is called.
// These need to be accessible by the factory function provided to jest.mock.
// Spies and module will be defined here
let onMessageCallback;
let backgroundModule; // To store the imported module
let getSettingsSpy, processSpy, saveSpy; // To store spies

describe('Web Content Capture Background Script Logic', () => {
    beforeEach(() => {
        jest.resetModules(); // Crucial to get a fresh module with fresh services for spying
        jest.clearAllMocks(); // Clear any mocks on chrome APIs etc.
        chrome.runtime.lastError = null;

        // Import the actual background module
        backgroundModule = require('../background');

        // Spy on the methods of the *actual* service objects exported by background.js
        // These services are named 'mock*' in background.js but are its actual, non-Jest-mocked services.
        getSettingsSpy = jest.spyOn(backgroundModule.mockConfigurationService, 'getSettings');
        processSpy = jest.spyOn(backgroundModule.mockContentProcessingService, 'process');
        saveSpy = jest.spyOn(backgroundModule.mockStorageInterface, 'save');

        // Set default implementations for the spies
        getSettingsSpy.mockResolvedValue({ defaultCaptureMode: 'article', defaultSaveFormat: 'markdown' });
        processSpy.mockResolvedValue({
            success: true,
            data: { content: 'Default mock processed content', metadata: {}, contentPreview: 'Default preview' }
        });
        saveSpy.mockResolvedValue({ success: true, savedId: 'default-mock-saved-id' });

        // Default mock for chrome.tabs.query (remains the same)
        chrome.tabs.query.mockImplementation((queryInfo, callback) => {
            const result = [{ id: 1, url: 'https://current-tab.com', title: 'Current Tab Title' }];
            if (callback) { callback(result); return Promise.resolve(undefined); }
            return Promise.resolve(result);
        });

        // Default mock for chrome.tabs.sendMessage (remains the same)
        chrome.tabs.sendMessage.mockImplementation((tabId, message, options, callback) => {
            let responseData = { success: false, error: 'Unhandled message in tabs.sendMessage mock' };
            if (message.type === 'GET_SELECTED_CONTENT') {
                responseData = { success: true, data: 'Mock selected text' };
            } else if (message.type === 'GET_FULL_PAGE_HTML') {
                responseData = { success: true, data: '<html><body>Mock HTML</body></html>' };
            } else if (message.type === 'ENABLE_SELECTION_MODE' || message.type === 'DISABLE_SELECTION_MODE') {
                responseData = { success: true };
            }
            if (callback) { callback(responseData); return Promise.resolve(undefined); }
            return Promise.resolve(responseData);
        });
        
        // Extract the onMessageCallback from the newly imported module's listener
        // This needs to happen *after* backgroundModule is required and chrome.runtime.onMessage.addListener is called by it.
        if (chrome.runtime.onMessage.addListener.mock.calls.length > 0) {
            onMessageCallback = chrome.runtime.onMessage.addListener.mock.calls[chrome.runtime.onMessage.addListener.mock.calls.length - 1][0];
        } else {
            // This might happen if background.js doesn't add listener immediately upon import.
            // For this project, it does. If not, this logic needs adjustment.
            throw new Error("chrome.runtime.onMessage.addListener was not called by background.js. Test setup error.");
        }
    });

    describe('handlePopupInit', () => {
        test('should query active tab, get settings, and respond with tab info, PDF status, and settings', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => {
                sendResponseResolve(response);
            });

            const mockSenderTab = { id: 1, url: 'https://example.com/page.html', title: 'Example Page' };
            const sender = { tab: mockSenderTab };

            chrome.tabs.query.mockResolvedValueOnce([{ id: mockSenderTab.id, url: mockSenderTab.url, title: mockSenderTab.title }]);
            // Use the top-level mock function for setting one-time behavior
            getSettingsSpy.mockResolvedValueOnce({ defaultMode: 'article' });

            const request = { type: 'POPUP_INIT' };
            const handlerReturnValue = onMessageCallback(request, sender, sendResponse);
            
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }

            expect(chrome.tabs.query).toHaveBeenCalledWith({ active: true, currentWindow: true });
            expect(getSettingsSpy).toHaveBeenCalled(); // Check the top-level mock
            expect(sendResponse).toHaveBeenCalledWith({
                success: true,
                tabInfo: { id: mockSenderTab.id, url: mockSenderTab.url, title: mockSenderTab.title },
                isPdf: false,
                settings: { defaultMode: 'article' }
            });
        });

        test('should correctly identify a PDF URL', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });

            const mockSenderTab = { id: 2, url: 'https://example.com/document.pdf', title: 'PDF Document' };
            const sender = { tab: mockSenderTab };
            chrome.tabs.query.mockResolvedValueOnce([{ id: mockSenderTab.id, url: mockSenderTab.url, title: mockSenderTab.title }]);
            getSettingsSpy.mockResolvedValueOnce({}); // Use the top-level mock

            const request = { type: 'POPUP_INIT' };
            const handlerReturnValue = onMessageCallback(request, sender, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }
            
            expect(sendResponse).toHaveBeenCalledWith(expect.objectContaining({
                isPdf: true
            }));
        });

         test('should handle errors during POPUP_INIT when tabs.query fails', async () => {
             let sendResponseResolve;
             const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
             const sendResponse = jest.fn(response => { sendResponseResolve(response); });
            
             const mockSenderTab = { id: 1 }; // Minimal sender info
             const sender = { tab: mockSenderTab };
             chrome.tabs.query.mockRejectedValueOnce(new Error("Tabs query failed")); // Simulate error
 
             const request = { type: 'POPUP_INIT' };
             const handlerReturnValue = onMessageCallback(request, sender, sendResponse);
             if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                 await sendResponseCalledPromise;
                 await nextTick();
             }
 
             expect(sendResponse).toHaveBeenCalledWith({
                 success: false,
                error: "Tabs query failed" // Ensure the error message is propagated
            });
        });
    });

    describe('handleInitiateCapture', () => {
        const mockPayloadBase = { // Renamed to avoid conflict
            mode: 'article',
            tabInfo: { id: 1, url: 'https://example.com', title: 'Test Article' }
        };
        const mockSender = { tab: { id: 1 } }; // Sender context for the message

        test('should call ContentProcessingService and respond with processed data', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });

            const mockProcessedData = {
                content: 'Processed article',
                metadata: { title: 'Test Article' },
                contentPreview: 'Preview'
            };
            processSpy.mockResolvedValueOnce({ success: true, data: mockProcessedData }); // Use top-level

            const request = { type: 'INITIATE_CAPTURE', payload: mockPayloadBase };
            const handlerReturnValue = onMessageCallback(request, mockSender, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }

            expect(processSpy).toHaveBeenCalledWith(mockPayloadBase); // Check top-level
            expect(sendResponse).toHaveBeenCalledWith({ success: true, data: mockProcessedData });
            // Check if status update was sent to popup
            expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(expect.objectContaining({ type: 'CAPTURE_STATUS_UPDATE' }));
        });

        test('should fetch selection from content script for "selection" mode', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });

            const selectionPayload = { ...mockPayloadBase, mode: 'selection' };
            chrome.tabs.sendMessage.mockResolvedValueOnce({ success: true, data: 'Mock selected text from content script' });
            processSpy.mockResolvedValueOnce({ success: true, data: { content: 'Processed selection' } }); // Use top-level


            const request = { type: 'INITIATE_CAPTURE', payload: selectionPayload };
            const handlerReturnValue = onMessageCallback(request, mockSender, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }

            expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(selectionPayload.tabInfo.id, { type: 'GET_SELECTED_CONTENT' });
            expect(processSpy).toHaveBeenCalledWith(expect.objectContaining({ // Check top-level
                selectedText: 'Mock selected text from content script'
            }));
            expect(sendResponse).toHaveBeenCalledWith(expect.objectContaining({ success: true }));
        });

        test('should fetch full HTML from content script for "fullPage" mode', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });

            const fullPagePayload = { ...mockPayloadBase, mode: 'fullPage' };
            chrome.tabs.sendMessage.mockResolvedValueOnce({ success: true, data: '<html><body>Mock Full HTML</body></html>' });
            processSpy.mockResolvedValueOnce({ success: true, data: { content: 'Processed full page' } }); // Use top-level


            const request = { type: 'INITIATE_CAPTURE', payload: fullPagePayload };
            const handlerReturnValue = onMessageCallback(request, mockSender, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }

            expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(fullPagePayload.tabInfo.id, { type: 'GET_FULL_PAGE_HTML' });
            expect(processSpy).toHaveBeenCalledWith(expect.objectContaining({ // Check top-level
                fullHtml: '<html><body>Mock Full HTML</body></html>'
            }));
            expect(sendResponse).toHaveBeenCalledWith(expect.objectContaining({ success: true }));
        });

        test('should handle failure from ContentProcessingService', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });
            
            processSpy.mockImplementationOnce(() => // Use top-level
                Promise.resolve({ success: false, error: 'CPS Processing Error From Test' })
            );

            const request = { type: 'INITIATE_CAPTURE', payload: mockPayloadBase };
            const handlerReturnValue = onMessageCallback(request, mockSender, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }

            expect(sendResponse).toHaveBeenCalledWith({ success: false, error: 'CPS Processing Error From Test' });
        });

        test('should handle error during capture process (e.g., CPS rejects)', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });

            processSpy.mockImplementationOnce(() => // Use top-level
                Promise.reject(new Error('CPS Rejected Big Fail From Test'))
            );

            const request = { type: 'INITIATE_CAPTURE', payload: mockPayloadBase };
            const handlerReturnValue = onMessageCallback(request, mockSender, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }
            expect(sendResponse).toHaveBeenCalledWith({ success: false, error: 'CPS Rejected Big Fail From Test' });
        });

        test('should correctly handle "pdf" mode in initiateCapture', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });

            const pdfPayload = { ...mockPayloadBase, mode: 'pdf', tabInfo: { id: 3, url: 'https://example.com/document.pdf', title: 'Test PDF Document' } };
            const mockPdfProcessedData = {
                content: 'path/to/mock/Test_PDF_Document.pdf', // Expected format from mock CPS
                metadata: expect.objectContaining({ originalTitle: 'Test PDF Document', author: "PDF Author" }),
                contentPreview: 'PDF: Test PDF Document'
            };
            // Ensure the mock CPS is configured to return this for PDF mode
            processSpy.mockImplementationOnce(async (data) => { // Use top-level
                if (data.mode === 'pdf') {
                    return { success: true, data: mockPdfProcessedData };
                }
                return { success: false, error: 'CPS mock not configured for this mode in PDF test' };
            });


            const request = { type: 'INITIATE_CAPTURE', payload: pdfPayload };
            const handlerReturnValue = onMessageCallback(request, mockSender, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }

            expect(processSpy).toHaveBeenCalledWith(pdfPayload); // Check top-level
            expect(sendResponse).toHaveBeenCalledWith({ success: true, data: mockPdfProcessedData });
            expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(expect.objectContaining({ type: 'CAPTURE_STATUS_UPDATE' }));
        });
test('should handle failure from content script when fetching selection', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });

            const selectionPayload = { 
                mode: 'selection', 
                tabInfo: { id: 1, url: 'https://example.com', title: 'Test Article' }
            };
            const mockSender = { tab: { id: 1 } };

            // Simulate failure from chrome.tabs.sendMessage by returning a rejected promise
            const simulatedError = { success: false, error: 'Content script GET_SELECTED_CONTENT failed' };
            chrome.tabs.sendMessage.mockImplementationOnce((tabId, message) => {
                if (tabId === selectionPayload.tabInfo.id && message.type === 'GET_SELECTED_CONTENT') {
                    return Promise.resolve(simulatedError); // Simulate the content script responding with an error object
                }
                // Fallback for unexpected calls during this specific mock
                return Promise.reject(new Error(`Unexpected chrome.tabs.sendMessage call in test: ${tabId}, ${message.type}`));
            });
            
            // processSpy should not be called if tabs.sendMessage results in an error path in background.js
            // Default mock for processSpy is already set in beforeEach.

            const request = { type: 'INITIATE_CAPTURE', payload: selectionPayload };
            const handlerReturnValue = onMessageCallback(request, mockSender, sendResponse);
            
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                // Add another nextTick to ensure any subsequent promises in the error handling path of handleInitiateCapture resolve
                await nextTick();
                await nextTick();
            }

            expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(
                selectionPayload.tabInfo.id,
                { type: 'GET_SELECTED_CONTENT' }
                // No callback or options passed by the actual code
            );
            expect(processSpy).not.toHaveBeenCalled();
            expect(sendResponse).toHaveBeenCalledWith({ 
                success: false, 
                error: 'Content script GET_SELECTED_CONTENT failed'
            });
            // Check that an error status update was sent to the popup
            expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(
                expect.objectContaining({
                    type: 'CAPTURE_STATUS_UPDATE',
                    payload: expect.objectContaining({ 
                        message: expect.stringContaining('Content script GET_SELECTED_CONTENT failed'), 
                        statusType: 'error' 
                    })
                })
            );
        });
    });

    describe('handleSaveCapture', () => {
        const mockSavePayload = {
            content: 'Final content to save',
            metadata: { title: 'Saved Item Title' },
            format: 'markdown',
            sourceUrl: 'https://example.com/save-source'
        };

        test('should call StorageInterface and respond with save result', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });
            
            saveSpy.mockResolvedValueOnce({ success: true, savedId: 'xyz789-saved' }); // Use top-level
            chrome.tabs.query.mockResolvedValueOnce([{ id: 1 }]);

            const request = { type: 'SAVE_CAPTURE', payload: mockSavePayload };
            const handlerReturnValue = onMessageCallback(request, {}, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }

            expect(saveSpy).toHaveBeenCalledWith(mockSavePayload); // Check top-level
            expect(sendResponse).toHaveBeenCalledWith({ success: true, data: { success: true, savedId: 'xyz789-saved' } });
            expect(chrome.runtime.sendMessage).toHaveBeenCalledWith(expect.objectContaining({ type: 'CAPTURE_STATUS_UPDATE' }));
        });

        test('should handle failure from StorageInterface', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });

            saveSpy.mockImplementationOnce(() => // Use top-level
                Promise.resolve({ success: false, error: 'Storage Interface Error From Test' })
            );
            chrome.tabs.query.mockResolvedValueOnce([{ id: 1 }]);

            const request = { type: 'SAVE_CAPTURE', payload: mockSavePayload };
            const handlerReturnValue = onMessageCallback(request, {}, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }
            expect(sendResponse).toHaveBeenCalledWith({ success: false, error: 'Storage Interface Error From Test' });
        });

        test('should handle error during save process (e.g., StorageInterface rejects)', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => { sendResponseResolve(response); });

            saveSpy.mockImplementationOnce(() => // Use top-level
                Promise.reject(new Error('Storage Save Crash From Test'))
            );
            chrome.tabs.query.mockResolvedValueOnce([{ id: 1 }]);

            const request = { type: 'SAVE_CAPTURE', payload: mockSavePayload };
            const handlerReturnValue = onMessageCallback(request, {}, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }
            expect(sendResponse).toHaveBeenCalledWith({ success: false, error: 'Storage Save Crash From Test' });
        });
    });

    describe('Selection Mode Activation/Deactivation', () => {
        test('ACTIVATE_SELECTION_MODE should forward message to content script and respond', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => sendResponseResolve(response));
            
            const request = { type: 'ACTIVATE_SELECTION_MODE', tabId: 123 };
            chrome.tabs.sendMessage.mockResolvedValueOnce({ success: true });

            const handlerReturnValue = onMessageCallback(request, {}, sendResponse);
            
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }

            expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(123, { type: 'ENABLE_SELECTION_MODE' });
            expect(sendResponse).toHaveBeenCalledWith({ success: true });
        });

        test('DEACTIVATE_SELECTION_MODE should forward message to content script and respond', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => sendResponseResolve(response));

            const request = { type: 'DEACTIVATE_SELECTION_MODE', tabId: 456 };
            chrome.tabs.sendMessage.mockResolvedValueOnce({ success: true });

            const handlerReturnValue = onMessageCallback(request, {}, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                await nextTick();
            }

            expect(chrome.tabs.sendMessage).toHaveBeenCalledWith(456, { type: 'DISABLE_SELECTION_MODE' });
            expect(sendResponse).toHaveBeenCalledWith({ success: true });
        });

        test('ACTIVATE_SELECTION_MODE should handle error if no tabId and respond', async () => {
            let sendResponseResolve;
            const sendResponseCalledPromise = new Promise(resolve => { sendResponseResolve = resolve; });
            const sendResponse = jest.fn(response => sendResponseResolve(response));
            
            const request = { type: 'ACTIVATE_SELECTION_MODE' }; // No tabId

            const handlerReturnValue = onMessageCallback(request, {}, sendResponse);
            if (handlerReturnValue === true || handlerReturnValue instanceof Promise) {
                await sendResponseCalledPromise;
                 await nextTick();
            }

            expect(chrome.tabs.sendMessage).not.toHaveBeenCalled();
            expect(sendResponse).toHaveBeenCalledWith({ success: false, error: "No tabId provided for ACTIVATE_SELECTION_MODE" });
        });
    });

});