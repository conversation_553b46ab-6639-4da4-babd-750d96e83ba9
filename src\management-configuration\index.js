// management-configuration/index.js
//import fsPromises as fs from 'node:fs/promises'
const fs = require('node:fs/promises'); // Use promises API
const path = require('path');

// Define paths to JSON data files
const DATA_DIR = path.join(__dirname, 'data');
const SETTINGS_FILE_PATH = path.join(DATA_DIR, 'settings.json');
const TEMPLATES_FILE_PATH = path.join(DATA_DIR, 'templates.json');
const TAGS_FILE_PATH = path.join(DATA_DIR, 'tags.json');
const CATEGORIES_FILE_PATH = path.join(DATA_DIR, 'categories.json');

// Ensure data directory exists - can be synchronous at setup
const ensureDataDirExists = async () => {
    try {
        await fs.access(DATA_DIR);
    } catch (error) {
        if (error.code === 'ENOENT') {
            await fs.mkdir(DATA_DIR, { recursive: true });
        } else {
            throw error; // Re-throw other errors
        }
    }
};

// Helper function to read JSON files
const readJsonFile = async (filePath, defaultValue = {}) => {
    try {
        await ensureDataDirExists(); // Ensure directory exists before reading
        await fs.access(filePath); // Check if file exists
        const fileContent = await fs.readFile(filePath, 'utf-8');
        return fileContent ? JSON.parse(fileContent) : defaultValue;
    } catch (error) {
        if (error.code === 'ENOENT') { // File doesn't exist
            return defaultValue;
        }
        console.error(`Error reading or parsing JSON file ${filePath}:`, error);
        return defaultValue; // Return default value in case of other errors
    }
};

// Helper function to write JSON files
const writeJsonFile = async (filePath, data) => {
    try {
        await ensureDataDirExists(); // Ensure directory exists before writing
        await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
    } catch (error) {
        console.error(`Error writing JSON file ${filePath}:`, error);
        // Potentially throw error or handle as per application requirements
        throw error; // Re-throw to allow caller to handle
    }
};

// Default settings
const DEFAULT_SETTINGS = {
    defaultMode: 'Full Page',
    defaultFormat: 'Markdown',
};

// --- Settings Management ---
const getSettings = async () => {
    const settings = await readJsonFile(SETTINGS_FILE_PATH, DEFAULT_SETTINGS);
    return { ...DEFAULT_SETTINGS, ...settings };
};

const saveSettings = async (newSettings) => {
    await writeJsonFile(SETTINGS_FILE_PATH, newSettings);
};

// --- Template Management ---
const getTemplates = async () => {
    return await readJsonFile(TEMPLATES_FILE_PATH, []);
};

const saveTemplate = async (templateToSave) => {
    const templates = await getTemplates();
    const existingIndex = templates.findIndex(t => t.id === templateToSave.id);
    if (existingIndex > -1) {
        templates[existingIndex] = templateToSave;
    } else {
        // Simple ID generation for new templates if not provided
        if (!templateToSave.id) {
            templateToSave.id = `tpl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        templates.push(templateToSave);
    }
    await writeJsonFile(TEMPLATES_FILE_PATH, templates);
};

const deleteTemplate = async (templateId) => {
    let templates = await getTemplates();
    templates = templates.filter(t => t.id !== templateId);
    await writeJsonFile(TEMPLATES_FILE_PATH, templates);
};

// --- Tag Management ---
const getTags = async () => {
    return await readJsonFile(TAGS_FILE_PATH, []);
};

const saveTag = async (tagToSave) => {
    const tags = await getTags();
    const existingIndex = tags.findIndex(t => t.id === tagToSave.id);
    if (existingIndex > -1) {
        tags[existingIndex] = tagToSave;
    } else {
        if (!tagToSave.id) {
            tagToSave.id = `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        tags.push(tagToSave);
    }
    await writeJsonFile(TAGS_FILE_PATH, tags);
};

const deleteTag = async (tagId) => {
    let tags = await getTags();
    tags = tags.filter(t => t.id !== tagId);
    await writeJsonFile(TAGS_FILE_PATH, tags);
};

const mergeTags = async (sourceTagIds, targetTag) => {
    // Get all existing tags
    let tags = await getTags();

    // Find if target tag already exists
    let finalTargetTag = tags.find(t => t.id === targetTag.id);

    // If target tag doesn't exist, create it
    if (!finalTargetTag) {
        // Generate ID if not provided
        if (!targetTag.id) {
            targetTag.id = `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        // Add to tags collection
        tags.push(targetTag);
        finalTargetTag = targetTag;
    } else {
        // Update existing target tag with any new properties
        finalTargetTag = { ...finalTargetTag, ...targetTag };
        tags = tags.map(t => t.id === finalTargetTag.id ? finalTargetTag : t);
    }

    // Remove source tags (but keep the target tag)
    tags = tags.filter(t => !sourceTagIds.includes(t.id) || t.id === finalTargetTag.id);

    // Save updated tags
    await writeJsonFile(TAGS_FILE_PATH, tags);

    // Return the final target tag
    return finalTargetTag;
};


// --- Category Management ---
const getCategories = async () => {
    return await readJsonFile(CATEGORIES_FILE_PATH, []);
};

const saveCategory = async (categoryToSave) => {
    const categories = await getCategories();
    const existingIndex = categories.findIndex(c => c.id === categoryToSave.id);
    if (existingIndex > -1) {
        categories[existingIndex] = categoryToSave;
    } else {
        if (!categoryToSave.id) {
            categoryToSave.id = `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        categories.push(categoryToSave);
    }
    await writeJsonFile(CATEGORIES_FILE_PATH, categories);
};

const deleteCategory = async (categoryId) => {
    let categories = await getCategories();
    categories = categories.filter(c => c.id !== categoryId);
    await writeJsonFile(CATEGORIES_FILE_PATH, categories);
};

// Initialize data directory on module load (can be done once)
ensureDataDirExists().catch(err => console.error("Failed to ensure data directory:", err));

module.exports = {
    getSettings,
    saveSettings,
    getTemplates,
    saveTemplate,
    deleteTemplate,
    getTags,
    saveTag,
    deleteTag,
    mergeTags,
    getCategories,
    saveCategory,
    deleteCategory,
};