import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import SettingsView from '../renderer/components/SettingsView';
import useStore from '../renderer/store/useStore';

// Mock child components and Zustand store
jest.mock('../renderer/components/TagManagement', () => () => <div data-testid="tag-management">Tag Management</div>);
jest.mock('../renderer/components/CategoryManagement', () => () => <div data-testid="category-management">Category Management</div>);

// Mock the parts of the store that are used by the child components (even if indirectly through SettingsView)
// For SettingsView itself, it doesn't directly use store selectors/actions, but its children do.
// So, we provide a minimal mock for useStore.
const mockFetchTags = jest.fn();
const mockFetchCategories = jest.fn();

jest.mock('../renderer/store/useStore', () => jest.fn());

describe('SettingsView Component', () => {
  const mockOnBack = jest.fn();

  beforeEach(() => {
    // Reset mocks before each test
    mockOnBack.mockClear();
    mockFetchTags.mockClear();
    mockFetchCategories.mockClear();

    // Provide a fresh mock implementation for useStore for each test
    useStore.mockImplementation((selector) => {
      const state = {
        allTags: [],
        tagOperationLoading: false,
        tagOperationError: null,
        fetchTags: mockFetchTags,
        createTag: jest.fn(),
        updateTag: jest.fn(),
        deleteTag: jest.fn(),

        allCategories: [],
        categoryOperationLoading: false,
        categoryOperationError: null,
        fetchCategories: mockFetchCategories,
        createCategory: jest.fn(),
        updateCategory: jest.fn(),
        deleteCategory: jest.fn(),
      };
      return selector ? selector(state) : state;
    });
  });

  test('renders the settings header and title', () => {
    render(<SettingsView onBack={mockOnBack} />);
    expect(screen.getByText('Application Settings')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Back to App/i })).toBeInTheDocument();
  });

  test('renders tag and category management sections', () => {
    render(<SettingsView onBack={mockOnBack} />);

    // Click on the Tags tab to show tag management
    fireEvent.click(screen.getByRole('button', { name: /Tags/i }));
    expect(screen.getByText('Tag Management')).toBeInTheDocument();

    // Click on the Categories tab to show category management
    fireEvent.click(screen.getByRole('button', { name: /Categories/i }));
    expect(screen.getByText('Category Management')).toBeInTheDocument();
  });

  test('calls onBack prop when back button is clicked', () => {
    render(<SettingsView onBack={mockOnBack} />);
    fireEvent.click(screen.getByRole('button', { name: /Back to App/i }));
    expect(mockOnBack).toHaveBeenCalledTimes(1);
  });
});
