# Project Documentation: Personalized AI Knowledge Companion & PKM Web Clipper

Welcome to the central documentation hub for the Personalized AI Knowledge Companion & PKM Web Clipper project. This directory contains all relevant documents pertaining to the project's planning, requirements, design, testing, and operational status.

The goal of this documentation is to be clear, understandable, and useful for human programmers and project stakeholders who need to comprehend the system, track changes, or identify potential issues.

## Key Documentation Sections

*   **[`Master_Project_Plan.md`](docs/Master_Project_Plan.md:1):** The overarching plan for the project, outlining scope, goals, modules, current status, and next steps. This is a living document that reflects the project's evolution.
*   **[`PRD.md`](docs/PRD.md:1) (Product Requirements Document):** Defines the product's purpose, features, functional and non-functional requirements, user stories, and success metrics.

## Subdirectories

The documentation is further organized into the following subdirectories:

*   **[`architecture/`](docs/architecture/):** Contains high-level architectural design documents for the system and its various modules. See [`architecture/README.md`](docs/architecture/README.md:1) for an overview of its contents.
*   **[`specs/`](docs/specs/):** Houses detailed feature overview specifications for each primary module and UI component. See [`specs/README.md`](docs/specs/README.md:1) for more details.
*   **[`testplans/`](docs/testplans/):** Includes all test plan documents, covering module-specific testing, UI testing, and end-to-end scenarios. Refer to [`testplans/README.md`](docs/testplans/README.md:1) for an index.
*   **[`reports/`](docs/reports/):** A collection of various project reports, including integration status, optimization efforts, scaffolding activities, and test execution summaries. The [`reports/README.md`](docs/reports/README.md:1) provides a guide to its subcategories.
*   **[`comprehension/`](docs/comprehension/):** Contains documents related to code comprehension, analysis of specific functionalities, and technical debt assessments.
*   **[`debugging/`](docs/debugging/):** Includes diagnostic reports and analyses of specific bugs or issues encountered during development and testing.

This structured approach aims to make information accessible and maintainable. For any new documentation, please ensure it is placed in the appropriate directory and, if necessary, update the relevant `README.md` files to include references to the new content.