// __tests__/useStore.test.js
import useStore from '../renderer/store/useStore';
import { act } from '@testing-library/react';

jest.mock('../renderer/store/useStore', () => {
  const mockStore = {
    items: [],
    selectedItemId: null,
    searchTerm: '',
    searchResults: [],
    searchLoading: false,
    searchError: null,
    filterTags: [],
    selectedTags: [],
    dateRangeFilter: { startDate: null, endDate: null },
    allContentTypes: [],
    selectedContentTypes: [],
    filterCategories: [],
    selectedCategories: [],
    sourceFilter: '',
    tagFilterLogic: 'AND',
    qaQuestion: '',
    qaAnswer: null,
    qaLoading: false,
    qaError: null,
    summary: null,
    summaryLoading: false,
    summaryError: null,
    summaryGenerated: false,
    conceptualLinks: [],
    conceptualLinksLoading: false,
    conceptualLinksError: null,
    selectedTransformationType: null,
    transformedContent: null,
    transformationLoading: false,
    transformationError: null,
    savingNewItemLoading: false,
    savingNewItemError: null,
    manualLinks: [],
    manualLinksLoading: false,
    manualLinksError: null,
    manualLinkCreating: false,
    manualLinkCreateError: null,
    manualLinkDeleting: false,
    manualLinkDeleteError: null,
    allTags: [],
    tagOperationLoading: false,
    tagOperationError: null,
    allCategories: [],
    categoryOperationLoading: false,
    categoryOperationError: null,
    captureSettings: {
      defaultFormat: 'markdown',
      extractTitle: true,
      extractUrl: true,
      extractPublicationDate: true,
      enableAiAutoTagging: false,
    },
    captureSettingsLoading: false,
    captureSettingsError: null,
    captureSettingsSaving: false,
    captureSettingsSaveError: null,
    clippingTemplates: [],
    clippingTemplatesLoading: false,
    clippingTemplatesError: null,
    clippingTemplateCreating: false,
    clippingTemplateCreateError: null,
    clippingTemplateUpdating: false,
    clippingTemplateUpdateError: null,
    clippingTemplateDeleting: false,
    clippingTemplateDeleteError: null,
    clippingTemplateSettingDefault: false,
    clippingTemplateSetDefaultError: null,
    exportInProgress: false,
    exportError: null,
    importInProgress: false,
    importError: null,
    addItem: jest.fn(),
    removeItem: jest.fn(),
    selectItem: jest.fn(),
    clearSelectedItem: jest.fn(),
    setSearchTerm: jest.fn(),
    performSearch: jest.fn(),
    setFilterTags: jest.fn(),
    setSelectedTags: jest.fn(),
    setDateRangeFilter: jest.fn(),
    setAllContentTypes: jest.fn(),
    setSelectedContentTypes: jest.fn(),
    clearAdvancedFilters: jest.fn(),
    setFilterCategories: jest.fn(),
    setSelectedCategories: jest.fn(),
    setSourceFilter: jest.fn(),
    setTagFilterLogic: jest.fn(),
    setQaQuestion: jest.fn(),
    submitQaQuestion: jest.fn(),
    clearQa: jest.fn(),
    fetchSummary: jest.fn(),
    clearSummary: jest.fn(),
    fetchConceptualLinks: jest.fn(),
    clearConceptualLinks: jest.fn(),
    setSelectedTransformationType: jest.fn(),
    transformItemContent: jest.fn(),
    clearTransformationState: jest.fn(),
    saveTransformedContentAsNewItem: jest.fn(),
    clearSaveNewItemStatus: jest.fn(),
    fetchManualLinks: jest.fn(),
    createManualLink: jest.fn(),
    deleteManualLink: jest.fn(),
    clearManualLinks: jest.fn(),
    clearManualLinkStatus: jest.fn(),
    fetchTags: jest.fn(),
    createTag: jest.fn(),
    updateTag: jest.fn(),
    deleteTag: jest.fn(),
    fetchCategories: jest.fn(),
    createCategory: jest.fn(),
    updateCategory: jest.fn(),
    deleteCategory: jest.fn(),
    fetchCaptureSettings: jest.fn(),
    updateCaptureSetting: jest.fn(),
    saveCaptureSettings: jest.fn(),
    clearCaptureSettingsStatus: jest.fn(),
    fetchClippingTemplates: jest.fn(),
    createClippingTemplate: jest.fn(),
    updateClippingTemplate: jest.fn(),
    deleteClippingTemplate: jest.fn(),
    setDefaultClippingTemplate: jest.fn(),
    clearClippingTemplateStatus: jest.fn(),
    exportData: jest.fn(),
    importData: jest.fn(),
    clearDataManagementStatus: jest.fn(),
  };

  const useStoreMock = (selector) => {
    return selector ? selector(mockStore) : mockStore;
  };

  useStoreMock.setState = (newState) => {
    Object.assign(mockStore, newState);
  };

  return useStoreMock;
});

it('should allow access to the store', () => {
  const store = useStore();
  expect(store).toBeDefined();
});
