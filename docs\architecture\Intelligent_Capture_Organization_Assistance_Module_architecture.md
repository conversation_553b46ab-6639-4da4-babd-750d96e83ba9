# High-Level Architecture: Intelligent Capture & Organization Assistance Module

**Version:** 1.0
**Date:** May 12, 2025

## 1. Overview

This document outlines the high-level architecture for the "Intelligent Capture & Organization Assistance Module." This module integrates with the "Web Content Capture Module" to provide AI-driven assistance during the content capture process. Its primary functions are to suggest tags, categories, and summaries for captured content, allow user annotations (notes, highlights), and collect user feedback on AI suggestions, all while prioritizing user control and privacy.

This architecture is derived from the Product Requirements Document ([`docs/PRD.md`](docs/PRD.md)) and the Feature Overview Specification ([`docs/specs/Intelligent_Capture_Organization_Assistance_Module_overview.md`](docs/specs/Intelligent_Capture_Organization_Assistance_Module_overview.md)).

## 2. Key Architectural Principles

*   **Modularity:** Components are designed with clear responsibilities and interfaces.
*   **User-Centricity:** User control and feedback are paramount. AI suggestions are aids, not mandates.
*   **Privacy-First:** Local processing is prioritized for suggestions where feasible. External AI calls require user awareness and consent.
*   **Extensibility:** While focusing on v1 scope, the architecture allows for future enhancements in AI capabilities and personalization.

## 3. Components

The module consists of the following key components:

![Component Diagram Sketch (Conceptual) - A central Orchestration Service connects to: Web Content Capture Module (external, for UI and initial content), Content Processor, AI Suggestion Service (with sub-modules for Summarization, Tagging, Categorization), User Interaction Handler, Annotation Manager, Feedback Handler, and Core Knowledge Base (external, for data storage and retrieval of existing structure). AI Suggestion Service's Summarization sub-module connects to External Gemini API. Tagging/Categorization sub-modules may use local models or (optionally, with consent) external AI.]

*   **3.1. Orchestration Service:**
    *   **Responsibilities:**
        *   Acts as the central coordinator for all operations within the module.
        *   Receives captured content from the Web Content Capture Module.
        *   Manages the flow of data between other components.
        *   Initiates AI suggestion generation.
        *   Communicates with the Web Content Capture Module to display suggestions and receive user input.
        *   Packages the final enriched content and metadata for storage in the Core Knowledge Base.
    *   **Interfaces:** Web Content Capture Module, Content Processor, AI Suggestion Service, User Interaction Handler, Annotation Manager, Feedback Handler, Core Knowledge Base.

*   **3.2. Content Processor:**
    *   **Responsibilities:**
        *   Receives raw captured content (text, metadata) from the Orchestration Service.
        *   Prepares content for analysis by AI services (e.g., text cleaning, normalization, truncation if necessary based on token limits for external APIs).
    *   **Interfaces:** Orchestration Service, AI Suggestion Service.

*   **3.3. AI Suggestion Service:**
    *   A composite service responsible for generating various AI-driven suggestions.
    *   **3.3.1. Summarization Sub-module:**
        *   **Responsibilities:** Generates concise summaries of the captured content.
        *   **Technology:** Utilizes the external Gemini API (FR 5.2.3).
        *   **Interfaces:** Orchestration Service, Content Processor (for prepared content), External Gemini API.
        *   **Privacy:** Adheres to NFR 6.1.2 and NFR 6.1.4 regarding user consent and data handling for external calls.
    *   **3.3.2. Tagging Sub-module:**
        *   **Responsibilities:** Suggests relevant tags for the captured content.
        *   **Technology (v1 Priority: Local):**
            *   Local processing: Keyword extraction, TF-IDF analysis against existing user tags, pattern matching.
            *   Future: Option for external AI model integration with user consent.
        *   **Interfaces:** Orchestration Service, Content Processor, Core Knowledge Base (for existing tags).
        *   **Privacy:** Local processing by default enhances privacy and offline capability.
    *   **3.3.3. Categorization Sub-module:**
        *   **Responsibilities:** Suggests organizational categories/folders/projects.
        *   **Technology (v1 Priority: Local):**
            *   Local processing: Analyzes content and compares against the user's existing organizational structure from the Core Knowledge Base (FR 5.2.2). Heuristics based on content keywords and structural analysis.
            *   Future: Option for external AI model integration with user consent.
        *   **Interfaces:** Orchestration Service, Content Processor, Core Knowledge Base (for existing structure).
        *   **Privacy:** Local processing by default enhances privacy and offline capability.

*   **3.4. User Interaction Handler:**
    *   **Responsibilities:**
        *   Manages user modifications to AI-suggested tags (add, edit, remove - FR 5.2.4).
        *   Handles user selection or creation of organizational categories/projects, allowing overrides of AI suggestions (FR 5.2.5).
        *   Captures user-entered personal notes or comments (FR 5.2.6).
    *   **Interfaces:** Orchestration Service, Web Content Capture Module (for UI elements).

*   **3.5. Annotation Manager:**
    *   **Responsibilities:**
        *   Manages user highlighting of text within the content preview (FR 5.2.7).
        *   Stores highlight information (e.g., selection offsets, text).
    *   **Interfaces:** Orchestration Service, Web Content Capture Module (for UI elements).

*   **3.6. Feedback Handler:**
    *   **Responsibilities:**
        *   Collects explicit user feedback (e.g., thumbs up/down, ratings) on the quality and relevance of AI-generated suggestions (tags, categories - FR 5.2.8).
        *   Prepares feedback data for storage.
    *   **Interfaces:** Orchestration Service, Web Content Capture Module (for UI elements).

*   **3.7. Configuration Manager (Interface/Proxy):**
    *   **Responsibilities:**
        *   Provides access to user-specific configurations related to the Intelligent Capture & Organization Assistance Module (e.g., enable/disable certain AI features, preferred number of tag suggestions).
        *   This might be a proxy to a global settings manager.
    *   **Interfaces:** Orchestration Service, AI Suggestion Service.

## 4. Interactions with Other Modules/Systems

*   **4.1. Web Content Capture Module:**
    *   **Input from Web Content Capture Module:** Provides captured content (text, metadata like URL, title) to the Orchestration Service.
    *   **Output to Web Content Capture Module:** The Orchestration Service sends AI-generated suggestions (summary, tags, categories), and instructions for UI elements (for notes, highlights, feedback) to be rendered by the Web Content Capture Module's interface.
    *   **Input from Web Content Capture Module (User Actions):** Receives user's final choices for tags, category, notes, highlights, and feedback via the Orchestration Service.

*   **4.2. Core Knowledge Base:**
    *   **Input from Core Knowledge Base:** The AI Suggestion Service (specifically Tagging and Categorization sub-modules) queries the Knowledge Base for existing user tags and organizational structure to inform suggestions.
    *   **Output to Core Knowledge Base:** The Orchestration Service sends the fully processed item (original content, AI summary, user-confirmed/edited tags, chosen category, user notes, highlights, and associated metadata) for persistent storage. Feedback data collected by the Feedback Handler is also sent for storage.

*   **4.3. External AI Services (e.g., Gemini):**
    *   The AI Suggestion Service (Summarization Sub-module) interacts with the Gemini API to generate summaries.
    *   All interactions must comply with NFR 6.1.2 (user awareness and control over data sent) and NFR 6.1.4 (safeguards against training public models with private data).

## 5. Data Flow

**High-Level Data Flow for Suggestion Generation and User Interaction:**

1.  **Capture Trigger:** User initiates content capture via the Web Content Capture Module.
2.  **Content Delivery:** Web Content Capture Module sends captured content (e.g., article text, URL, title) to the Orchestration Service of this module.
3.  **Content Preparation:** Orchestration Service passes content to the Content Processor for cleaning and formatting.
4.  **Parallel AI Processing (Initiated by Orchestration Service):**
    *   **Summarization:** Content Processor -> Summarization Sub-module -> Gemini API -> Summary returned.
    *   **Tagging:** Content Processor -> Tagging Sub-module (utilizing Core KB for existing tags if local) -> Tag suggestions returned.
    *   **Categorization:** Content Processor -> Categorization Sub-module (utilizing Core KB for existing structure) -> Category suggestions returned.
5.  **Display Suggestions:** Orchestration Service sends the generated summary, tag suggestions, and category suggestions to the Web Content Capture Module to be displayed in its UI.
6.  **User Interaction & Refinement:**
    *   User views suggestions.
    *   User interacts with UI elements (provided by this module but rendered by Web Content Capture Module) to:
        *   Modify/accept/reject tags (User Interaction Handler).
        *   Select/change/create category (User Interaction Handler).
        *   Add personal notes (User Interaction Handler).
        *   Highlight content (Annotation Manager).
        *   Provide feedback on AI suggestions (Feedback Handler).
7.  **Data Aggregation:** The User Interaction Handler, Annotation Manager, and Feedback Handler relay the user's final inputs and feedback to the Orchestration Service.
8.  **Finalization & Storage:** Orchestration Service compiles the original content, all approved/added metadata (tags, category, summary), annotations (notes, highlights), and feedback. This complete package is then sent to the Core Knowledge Base for storage.

## 6. Key Design Decisions & Rationale

*   **Prioritization of Local Processing for Tags/Categories:**
    *   **Rationale:** Aligns with paramount privacy requirements (NFR 6.1.1, NFR 6.1.4), enhances responsiveness for these suggestions (NFR 6.3.4), and enables basic suggestion functionality offline (NFR 6.4.1). External AI for these can be a future enhancement with explicit user consent.
    *   **Impact:** Requires development of local suggestion algorithms (e.g., keyword-based, TF-IDF, structure analysis).

*   **Explicit Use of Gemini for Summaries:**
    *   **Rationale:** Leverages a powerful external model for high-quality summarization as specified (FR 5.2.3).
    *   **Impact:** Requires careful management of API calls, user consent for data transfer (NFR 6.1.2), and UI handling for potential latency.

*   **Centralized Orchestration Service:**
    *   **Rationale:** Simplifies the interaction logic between various components and external modules, making the system easier to manage and extend.
    *   **Impact:** The Orchestration Service becomes a critical component; its reliability and performance are key.

*   **Decoupled Feedback Handling:**
    *   **Rationale:** Allows for systematic collection of user feedback on AI quality, which is crucial for future (v2+) personalization and model improvement, even if v1 doesn't implement adaptive learning.
    *   **Impact:** Requires schema for storing feedback data.

*   **Integration via Web Content Capture Module's UI:**
    *   **Rationale:** Provides a seamless user experience by embedding assistance features directly into the existing capture workflow.
    *   **Impact:** Requires clear API contracts and coordination between this module and the Web Content Capture Module for UI rendering and event handling.

## 7. Non-Functional Requirements Considerations

*   **Responsiveness (NFR 6.3.4):** Achieved by local processing for frequent suggestions (tags, categories) and asynchronous handling of external calls (summary). The UI must use loading indicators for slower operations.
*   **User Control (NFR 6.5.1):** The architecture ensures all AI outputs are suggestions. User Interaction Handler provides mechanisms for users to override, edit, or ignore them.
*   **Privacy (NFR 6.1.1, 6.1.2, 6.1.4):** Addressed by prioritizing local processing, requiring explicit user consent for external data transfer (e.g., for summaries via Gemini), and ensuring data sent externally is not used for training public models.
*   **Offline Access (NFR 6.4.1):** Local suggestion capabilities for tags and categories contribute to offline usability. Summarization (Gemini) will require connectivity. Access to already saved data is handled by the Core Knowledge Base.

## 8. Future Considerations (Beyond v1 Scope)

*   **Advanced Personalization:** Using collected feedback (via Feedback Handler) to train/fine-tune local suggestion models.
*   **Proactive Suggestions:** Expanding AI capabilities to offer more context-aware suggestions.
*   **Support for More Content Types:** Adapting suggestion logic if new content types are introduced.
*   **Alternative/Additional AI Models:** Integrating other AI services for tagging or categorization if local models prove insufficient for desired quality and user consents to external use.

## 9. Open Questions from Spec

*   **Specific AI models/services for tag and category suggestions (beyond Gemini for summaries):**
    *   **Architecture Decision (v1):** Prioritize custom-developed local models (keyword extraction, TF-IDF, structural analysis based on existing KB). External models are a future consideration.
*   **Detailed interaction flow for creating a new category/project directly from the capture interface:**
    *   **Architecture Note:** The User Interaction Handler will manage this. It will likely involve providing a text input for the new category name and potentially selecting a parent category if a hierarchical structure is supported by the Core Knowledge Base. The Web Content Capture Module's UI will need to render these elements.
*   **Exact format and storage of highlight information:**
    *   **Architecture Suggestion:** Store as an array of objects, each object containing `startOffset`, `endOffset` (within the cleaned text content), and `selectedText`. This would be part of the metadata associated with the captured item in the Core Knowledge Base.
*   **Specifics of how user feedback on AI suggestions will be stored and potentially used for future personalization:**
    *   **Architecture Suggestion (Storage):** Store feedback linked to the specific suggestion and captured item. E.g., `{itemId: "xyz", suggestionType: "tag", suggestedValue: "AI", feedback: "positive"}`.
    *   **Architecture Note (Use):** For v1, this data is collected. Future versions can use this dataset to evaluate and retrain/fine-tune suggestion models.