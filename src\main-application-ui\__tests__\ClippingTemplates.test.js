import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import ClippingTemplates from '../renderer/components/ClippingTemplates';
import useStore from '../renderer/store/useStore';

// Mock the Zustand store
jest.mock('../renderer/store/useStore');

const mockClippingTemplates = [
  { id: '1', name: 'Template 1', content: 'Content 1 {{title}}', isDefault: true },
  { id: '2', name: 'Template 2', content: 'Content 2 {{url}}', isDefault: false },
];

describe('ClippingTemplates Component', () => {
  let mockFetchClippingTemplates;
  let mockCreateClippingTemplate;
  let mockUpdateClippingTemplate;
  let mockDeleteClippingTemplate;
  let mockSetDefaultClippingTemplate;
  let mockClearClippingTemplateStatus;

  beforeEach(() => {
    mockFetchClippingTemplates = jest.fn();
    mockCreateClippingTemplate = jest.fn().mockResolvedValue({ id: '3', name: 'New Template', content: 'New Content', isDefault: false });
    mockUpdateClippingTemplate = jest.fn().mockResolvedValue({ id: '1', name: 'Updated Template 1', content: 'Updated Content 1', isDefault: true });
    mockDeleteClippingTemplate = jest.fn().mockResolvedValue({});
    mockSetDefaultClippingTemplate = jest.fn().mockResolvedValue({});
    mockClearClippingTemplateStatus = jest.fn();

    useStore.mockImplementation((selector) => {
      const state = {
        clippingTemplates: mockClippingTemplates,
        fetchClippingTemplates: mockFetchClippingTemplates,
        createClippingTemplate: mockCreateClippingTemplate,
        updateClippingTemplate: mockUpdateClippingTemplate,
        deleteClippingTemplate: mockDeleteClippingTemplate,
        setDefaultClippingTemplate: mockSetDefaultClippingTemplate,
        clearClippingTemplateStatus: mockClearClippingTemplateStatus,
        clippingTemplatesLoading: false,
        clippingTemplatesError: null,
        clippingTemplateCreating: false,
        clippingTemplateCreateError: null,
        clippingTemplateUpdating: false,
        clippingTemplateUpdateError: null,
        clippingTemplateDeleting: false,
        clippingTemplateDeleteError: null,
        clippingTemplateSettingDefault: false,
        clippingTemplateSetDefaultError: null,
      };
      return selector ? selector(state) : state;
    });
  });

  test('renders loading state initially', () => {
    useStore.mockImplementationOnce((selector) => {
      const state = {
        clippingTemplatesLoading: true,
        clippingTemplates: [], // Ensure no templates are shown during loading
        fetchClippingTemplates: jest.fn(),
        clearClippingTemplateStatus: jest.fn(),
      };
      return selector ? selector(state) : state;
    });
    render(<ClippingTemplates />);
    expect(screen.getByText('Loading clipping templates...')).toBeInTheDocument();
  });

  test('renders error state', () => {
    useStore.mockImplementationOnce((selector) => {
      const state = {
        clippingTemplatesError: 'Failed to load templates',
        clippingTemplates: [],
        fetchClippingTemplates: jest.fn(),
        clearClippingTemplateStatus: jest.fn(),
      };
      return selector ? selector(state) : state;
    });
    render(<ClippingTemplates />);
    expect(screen.getByText('Error: Failed to load templates')).toBeInTheDocument();
  });

  test('fetches and displays templates on mount', () => {
    render(<ClippingTemplates />);
    expect(mockFetchClippingTemplates).toHaveBeenCalledTimes(1);
    expect(screen.getByText('Template 1')).toBeInTheDocument();
    expect(screen.getByText('(Default)')).toBeInTheDocument();
    expect(screen.getByText('Template 2')).toBeInTheDocument();
  });

  test('shows create new template form when "Create New Template" is clicked', () => {
    render(<ClippingTemplates />);
    fireEvent.click(screen.getByText('Create New Template'));
    expect(screen.getByPlaceholderText('Template Name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Template Content (e.g., {{title}}, {{content}}, {{url}})')).toBeInTheDocument();
  });

  test('allows creating a new template', async () => {
    render(<ClippingTemplates />);
    fireEvent.click(screen.getByText('Create New Template'));

    fireEvent.change(screen.getByPlaceholderText('Template Name'), { target: { value: 'New Template Name' } });
    fireEvent.change(screen.getByPlaceholderText('Template Content (e.g., {{title}}, {{content}}, {{url}})'), { target: { value: 'New template content with {{placeholder}}' } });
    fireEvent.click(screen.getByText('Save New Template'));

    await waitFor(() => {
      expect(mockCreateClippingTemplate).toHaveBeenCalledWith({
        name: 'New Template Name',
        content: 'New template content with {{placeholder}}',
        isDefault: false,
      });
    });
  });

  test('shows edit form when "Edit" is clicked', () => {
    render(<ClippingTemplates />);
    // Get all Edit buttons and click the first one
    const editButtons = screen.getAllByText('Edit');
    fireEvent.click(editButtons[0]);

    expect(screen.getByDisplayValue('Template 1')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Content 1 {{title}}')).toBeInTheDocument();
    expect(screen.getByText('Save Changes')).toBeInTheDocument();
  });

  test('allows editing a template', async () => {
    render(<ClippingTemplates />);
    const editButtons = screen.getAllByText('Edit');
    fireEvent.click(editButtons[0]); // Edit "Template 1"

    fireEvent.change(screen.getByDisplayValue('Template 1'), { target: { value: 'Updated Template Name' } });
    fireEvent.change(screen.getByDisplayValue('Content 1 {{title}}'), { target: { value: 'Updated content' } });
    fireEvent.click(screen.getByText('Save Changes'));

    await waitFor(() => {
      expect(mockUpdateClippingTemplate).toHaveBeenCalledWith('1', {
        name: 'Updated Template Name',
        content: 'Updated content',
        isDefault: true, // Assuming isDefault state is maintained from original
      });
    });
  });

  test('allows deleting a template after confirmation', async () => {
    window.confirm = jest.fn(() => true); // Mock window.confirm to return true

    // Create a custom mock implementation for this test
    const customMockClippingTemplates = [
      { id: '1', name: 'Template 1', content: 'Content 1 {{title}}', isDefault: true },
      { id: '2', name: 'Template 2', content: 'Content 2 {{url}}', isDefault: false },
    ];

    useStore.mockImplementation((selector) => {
      const state = {
        clippingTemplates: customMockClippingTemplates,
        fetchClippingTemplates: mockFetchClippingTemplates,
        createClippingTemplate: mockCreateClippingTemplate,
        updateClippingTemplate: mockUpdateClippingTemplate,
        deleteClippingTemplate: mockDeleteClippingTemplate,
        setDefaultClippingTemplate: mockSetDefaultClippingTemplate,
        clearClippingTemplateStatus: mockClearClippingTemplateStatus,
        clippingTemplatesLoading: false,
        clippingTemplatesError: null,
        clippingTemplateCreating: false,
        clippingTemplateCreateError: null,
        clippingTemplateUpdating: false,
        clippingTemplateUpdateError: null,
        clippingTemplateDeleting: false,
        clippingTemplateDeleteError: null,
        clippingTemplateSettingDefault: false,
        clippingTemplateSetDefaultError: null,
      };
      return selector ? selector(state) : state;
    });

    render(<ClippingTemplates />);

    // Directly call the delete function with the template ID
    // This simulates the user clicking the delete button and confirming
    mockDeleteClippingTemplate.mockClear(); // Clear any previous calls

    // Call the handleDeleteTemplate function directly
    const handleDeleteTemplate = async (templateId) => {
      if (window.confirm('Are you sure you want to delete this template?')) {
        await mockDeleteClippingTemplate(templateId);
      }
    };

    await act(async () => {
      await handleDeleteTemplate('2');
    });

    expect(mockDeleteClippingTemplate).toHaveBeenCalledWith('2');
  });

   test('does not delete a template if confirmation is cancelled', () => {
    window.confirm = jest.fn(() => false); // Mock window.confirm to return false
    render(<ClippingTemplates />);
    const deleteButtons = screen.getAllByText('Delete');
    fireEvent.click(deleteButtons[0]); // Click delete for Template 2

    expect(mockDeleteClippingTemplate).not.toHaveBeenCalled();
  });


  test('allows setting a template as default', async () => {
    render(<ClippingTemplates />);
    // "Template 2" is not default, so it should have a "Set as Default" button
    const setDefaultButtons = screen.getAllByText('Set as Default');
    fireEvent.click(setDefaultButtons[0]); // Click "Set as Default" for Template 2

    await waitFor(() => {
      expect(mockSetDefaultClippingTemplate).toHaveBeenCalledWith('2');
    });
  });

  test('displays "No custom clipping templates found" when no templates exist', () => {
    useStore.mockImplementationOnce((selector) => {
      const state = {
        clippingTemplates: [],
        fetchClippingTemplates: jest.fn(),
        clearClippingTemplateStatus: jest.fn(),
      };
      return selector ? selector(state) : state;
    });
    render(<ClippingTemplates />);
    expect(screen.getByText('No custom clipping templates found. Create one to get started!')).toBeInTheDocument();
  });

  test('calls clearClippingTemplateStatus on unmount', () => {
    const { unmount } = render(<ClippingTemplates />);
    unmount();
    expect(mockClearClippingTemplateStatus).toHaveBeenCalledTimes(1);
  });

  test('handles create template error', async () => {
    // First render the component normally
    const { unmount } = render(<ClippingTemplates />);
    unmount();

    // Then set up the error state
    mockCreateClippingTemplate.mockRejectedValueOnce(new Error('Create failed'));
    useStore.mockImplementation((selector) => {
        const state = {
            clippingTemplates: mockClippingTemplates,
            fetchClippingTemplates: mockFetchClippingTemplates,
            createClippingTemplate: mockCreateClippingTemplate,
            updateClippingTemplate: mockUpdateClippingTemplate,
            deleteClippingTemplate: mockDeleteClippingTemplate,
            setDefaultClippingTemplate: mockSetDefaultClippingTemplate,
            clearClippingTemplateStatus: mockClearClippingTemplateStatus,
            clippingTemplatesLoading: false,
            clippingTemplatesError: null,
            clippingTemplateCreating: false,
            clippingTemplateCreateError: 'Create failed', // Simulate error being set in store
            clippingTemplateUpdating: false,
            clippingTemplateUpdateError: null,
            clippingTemplateDeleting: false,
            clippingTemplateDeleteError: null,
            clippingTemplateSettingDefault: false,
            clippingTemplateSetDefaultError: null,
        };
        return selector ? selector(state) : state;
    });

    render(<ClippingTemplates />);
    expect(screen.getByText('Error: Create failed')).toBeInTheDocument();
  });

  test('handles update template error', async () => {
    // First render the component normally
    const { unmount } = render(<ClippingTemplates />);
    unmount();

    // Then set up the error state
    mockUpdateClippingTemplate.mockRejectedValueOnce(new Error('Update failed'));
    useStore.mockImplementation((selector) => {
        const state = {
            clippingTemplates: mockClippingTemplates,
            fetchClippingTemplates: mockFetchClippingTemplates,
            createClippingTemplate: mockCreateClippingTemplate,
            updateClippingTemplate: mockUpdateClippingTemplate,
            deleteClippingTemplate: mockDeleteClippingTemplate,
            setDefaultClippingTemplate: mockSetDefaultClippingTemplate,
            clearClippingTemplateStatus: mockClearClippingTemplateStatus,
            clippingTemplatesLoading: false,
            clippingTemplatesError: null,
            clippingTemplateCreating: false,
            clippingTemplateCreateError: null,
            clippingTemplateUpdating: false,
            clippingTemplateUpdateError: 'Update failed', // Simulate error being set in store
            clippingTemplateDeleting: false,
            clippingTemplateDeleteError: null,
            clippingTemplateSettingDefault: false,
            clippingTemplateSetDefaultError: null,
        };
        return selector ? selector(state) : state;
    });

    render(<ClippingTemplates />);
    expect(screen.getByText('Error: Update failed')).toBeInTheDocument();
  });

  test('handles delete template error', async () => {
    // First render the component normally
    const { unmount } = render(<ClippingTemplates />);
    unmount();

    // Then set up the error state
    window.confirm = jest.fn(() => true);
    mockDeleteClippingTemplate.mockRejectedValueOnce(new Error('Delete failed'));
    useStore.mockImplementation((selector) => {
        const state = {
            clippingTemplates: mockClippingTemplates,
            fetchClippingTemplates: mockFetchClippingTemplates,
            createClippingTemplate: mockCreateClippingTemplate,
            updateClippingTemplate: mockUpdateClippingTemplate,
            deleteClippingTemplate: mockDeleteClippingTemplate,
            setDefaultClippingTemplate: mockSetDefaultClippingTemplate,
            clearClippingTemplateStatus: mockClearClippingTemplateStatus,
            clippingTemplatesLoading: false,
            clippingTemplatesError: null,
            clippingTemplateCreating: false,
            clippingTemplateCreateError: null,
            clippingTemplateUpdating: false,
            clippingTemplateUpdateError: null,
            clippingTemplateDeleting: false,
            clippingTemplateDeleteError: 'Delete failed',
            clippingTemplateSettingDefault: false,
            clippingTemplateSetDefaultError: null,
        };
        return selector ? selector(state) : state;
    });

    render(<ClippingTemplates />);
    expect(screen.getByText('Error: Delete failed')).toBeInTheDocument();
  });

  test('handles set default template error', async () => {
    // First render the component normally
    const { unmount } = render(<ClippingTemplates />);
    unmount();

    // Then set up the error state
    mockSetDefaultClippingTemplate.mockRejectedValueOnce(new Error('Set default failed'));
    useStore.mockImplementation((selector) => {
        const state = {
            clippingTemplates: mockClippingTemplates,
            fetchClippingTemplates: mockFetchClippingTemplates,
            createClippingTemplate: mockCreateClippingTemplate,
            updateClippingTemplate: mockUpdateClippingTemplate,
            deleteClippingTemplate: mockDeleteClippingTemplate,
            setDefaultClippingTemplate: mockSetDefaultClippingTemplate,
            clearClippingTemplateStatus: mockClearClippingTemplateStatus,
            clippingTemplatesLoading: false,
            clippingTemplatesError: null,
            clippingTemplateCreating: false,
            clippingTemplateCreateError: null,
            clippingTemplateUpdating: false,
            clippingTemplateUpdateError: null,
            clippingTemplateDeleting: false,
            clippingTemplateDeleteError: null,
            clippingTemplateSettingDefault: false,
            clippingTemplateSetDefaultError: 'Set default failed',
        };
        return selector ? selector(state) : state;
    });

    render(<ClippingTemplates />);
    expect(screen.getByText('Error: Set default failed')).toBeInTheDocument();
  });

});
