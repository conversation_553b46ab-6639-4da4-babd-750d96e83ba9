// Mock AI Service for generating suggestions

interface AISuggestions {
  tags: string[];
  categories: string[];
  summary: string;
}

export const fetchMockSuggestions = async (content: string): Promise<AISuggestions> => {
  console.log("Fetching mock AI suggestions for content:", content.substring(0, 100) + '...'); // Log snippet of content

  // Simulate an asynchronous API call delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // Return predefined mock suggestions
  return {
    tags: ['Mock Tag 1', 'Mock Tag 2', 'Mock Tag 3'],
    categories: ['Mock Category A', 'Mock Category B'],
    summary: 'This is a mock summary generated by the AI service for the provided content.',
  };
};