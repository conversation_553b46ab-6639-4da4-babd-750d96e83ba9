{"manifest_version": 3, "name": "Web Content Capture Extension", "version": "0.1.0", "description": "Captures web content like bookmarks.", "permissions": ["tabs", "storage", "activeTab", "offscreen"], "background": {"service_worker": "background.js", "type": "module"}, "action": {"default_popup": "popup.html", "default_title": "Capture Content"}, "options_ui": {"page": "options.html", "open_in_tab": true}, "icons": {"16": "icon16.png", "48": "icon48.png", "128": "icon128.png"}}