// src/knowledge-base-interaction/features/content-summarization/utils/logger.js

const LOG_LEVEL = process.env.LOG_LEVEL || 'info'; // e.g., 'debug', 'info', 'warn', 'error'

/**
 * Generic log function.
 * @param {string} level - The log level (e.g., 'INFO', 'ERROR').
 * @param {string} message - The log message.
 * @param {object} [details] - Optional details object.
 */
function log(level, message, details) {
  const timestamp = new Date().toISOString();
  let logEntry = `${timestamp} [${level}] ${message}`;
  if (details && Object.keys(details).length > 0) {
    try {
      logEntry += ` | Details: ${JSON.stringify(details)}`;
    } catch (e) {
      logEntry += ` | Details: (Error serializing details)`;
    }
  }
  console.log(logEntry);
}

/**
 * Logs an informational message.
 * @param {string} message - The message to log.
 * @param {object} [details] - Optional details object.
 */
export function logInfo(message, details) {
  if (LOG_LEVEL === 'info' || LOG_LEVEL === 'debug') {
    log('INFO', message, details);
  }
}

/**
 * Logs a warning message.
 * @param {string} message - The message to log.
 * @param {object} [details] - Optional details object.
 */
export function logWarn(message, details) {
  if (LOG_LEVEL === 'warn' || LOG_LEVEL === 'info' || LOG_LEVEL === 'debug') {
    log('WARN', message, details);
  }
}

/**
 * Logs an error message.
 * @param {string} message - The message to log.
 * @param {Error|object} [errorOrDetails] - The error object or details object.
 */
export function logError(message, errorOrDetails) {
  if (LOG_LEVEL === 'error' || LOG_LEVEL === 'warn' || LOG_LEVEL === 'info' || LOG_LEVEL === 'debug') {
    if (errorOrDetails instanceof Error) {
      log('ERROR', `${message} | Error: ${errorOrDetails.message}`, { stack: errorOrDetails.stack });
    } else {
      log('ERROR', message, errorOrDetails);
    }
  }
}

/**
 * Logs a debug message.
 * @param {string} message - The message to log.
 * @param {object} [details] - Optional details object.
 */
export function logDebug(message, details) {
  if (LOG_LEVEL === 'debug') {
    log('DEBUG', message, details);
  }
}