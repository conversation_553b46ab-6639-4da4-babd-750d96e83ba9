# Primary Findings: Local-First Databases and Semantic Search

Local-first database solutions enable browser extensions to store and manage data directly on the user's device while supporting synchronization and offline functionality. When combined with semantic search technologies, they empower rich, context-aware querying of local knowledge bases.

### Local-First Database Solutions for Browser Extensions

#### 1. RxDB
RxDB is a NoSQL database optimized for local-first architectures, offering native support for **IndexedDB** and experimental integration with the **Origin Private File System (OPFS)**. Key features:
*   **Structured metadata storage**: Schema-based document modeling with TypeScript support, ideal for organizing metadata.
*   **Unstructured content handling**: Attachments API for storing blobs (e.g., images, text files) alongside structured data.
*   **Sync capabilities**: Real-time replication with backend systems via WebSocket or REST, critical for cross-device consistency.
*   **OPFS integration**: Enables near-native file I/O speeds in Chrome extensions via Web Workers, bypassing IndexedDB's asynchronous limitations.

Example OPFS setup:
```javascript
import { Storage } from 'rxdb/plugins/opfs';
const database = await createRxDatabase({
  name: 'metadata_db',
  storage: Storage
});
```

#### 2. PouchDB
A lightweight IndexedDB wrapper designed for sync-first workflows:
*   **CouchDB compatibility**: Built-in bidirectional sync with CouchDB servers.
*   **Conflict resolution**: Automatic handling of write conflicts in offline scenarios.
*   **Limitations**: No native support for OPFS, relying solely on IndexedDB's async API.

#### 3. IndexedDB with OPFS
For low-level control:
*   **Direct file storage**: Use OPFS to persist large unstructured files (e.g., PDFs, videos) as `FileSystemFileHandle` objects.
*   **Metadata indexing**: Store structured metadata in IndexedDB with searchable indexes.

Chrome extension manifest requirement for OPFS:
```json
"permissions": ["fileSystem"]
```

### Semantic Search Implementation

#### 1. Text Embedding Models
Local execution in browsers via:
*   **WebAssembly/WebGPU**: Run quantized models like **all-MiniLM-L6-v2** (384-dim embeddings) using ONNX Runtime Web.
*   **Transformer.js**: Browser-optimized inference for Sentence-BERT models.

Example embedding generation:
```javascript
import { pipeline } from '@xenova/transformers';
const extractor = await pipeline('feature-extraction', 'Xenova/all-MiniLM-L6-v2');
const embedding = await extractor('search query', { pooling: 'mean' });
```

#### 2. Vector Databases
*   **In-memory ANN**: Use libraries like **Faiss.js** (via WASM) for approximate nearest neighbor searches.
*   **Hybrid storage**: Store vectors in OPFS binary files and metadata in RxDB/PouchDB:

| Component       | Storage Solution      | Rationale                          |
|-----------------|-----------------------|------------------------------------|
| Vector Index    | OPFS (mmap-able)      | Fast SIMD-driven similarity search |
| Metadata        | RxDB                 | Rich query capabilities            |
| Raw Content     | OPFS Blobs           | Efficient large-file storage       |

#### 3. Implementation Pipeline
1.  **Content ingestion**:
    *   Extract text from PDFs/docs using `pdf.js`
    *   Generate embeddings via web workers
2.  **Indexing**:
    *   Store vectors in OPFS-backed Float32Array buffers
    *   Link embeddings to metadata via UUIDs in RxDB
3.  **Querying**:
    *   Convert search phrase to embedding
    *   Perform ANN search using Faiss.js
    *   Join results with metadata from RxDB

### Challenges and Considerations
*   **Memory limits**: Browser extensions cap memory usage (~4GB for Chrome), necessitating efficient vector quantization.
*   **Model size**: ONNX-quantized models under 40MB perform best for WebAssembly deployments.
*   **Security**: OPFS isolation prevents extensions from accessing system files directly.

RxDB's OPFS integration coupled with WASM-accelerated embedding models currently offers the most viable path for implementing local-first semantic search in browser extensions, balancing performance with Web platform constraints.