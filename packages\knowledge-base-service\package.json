{"name": "@pkm-ai/knowledge-base-service", "version": "0.1.0", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.build.json", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --verbose --coverage", "lint": "eslint . --ext .ts,.tsx"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.15.19", "cross-env": "^7.0.3", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.4.5"}, "dependencies": {"@types/uuid": "^10.0.0", "async-mutex": "^0.5.0", "lowdb": "^7.0.1", "uuid": "^11.1.0"}}