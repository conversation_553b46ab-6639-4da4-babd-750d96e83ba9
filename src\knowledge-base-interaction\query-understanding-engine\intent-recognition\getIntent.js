// src/knowledge-base-interaction/query-understanding-engine/intent-recognition/getIntent.js

/**
 * Enum for recognized intents.
 * @readonly
 * @enum {string}
 */
export const Intent = {
  SUMMARIZATION: 'SUMMARIZATION',
  QUESTION_ANSWERING: 'QUESTION_ANSWERING',
  SEMANTIC_SEARCH: 'SEMANTIC_SEARCH',
  CONTENT_TRANSFORMATION: 'CONTENT_TRANSFORMATION',
  CONCEPTUAL_LINKING: 'CONCEPTUAL_LINKING',
  UNKNOWN: 'UNKNOWN',
};

/**
 * Analyzes a query to determine the user's intent.
 * This is a placeholder implementation using keyword matching.
 * In a real application, this would involve more sophisticated NLP techniques
 * or integration with an AI model.
 *
 * @param {string} query - The user's natural language query.
 * @returns {Promise<Intent>} A promise that resolves with the recognized Intent.
 */
export async function getIntent(query) {
  const lowerQuery = query.toLowerCase();

  if (lowerQuery.includes('summarize') || lowerQuery.includes('summary')) {
    return Intent.SUMMARIZATION;
  }
  if (lowerQuery.includes('question') || lowerQuery.includes('answer') || lowerQuery.includes('what is') || lowerQuery.includes('how to')) {
    return Intent.QUESTION_ANSWERING;
  }
  if (lowerQuery.includes('search') || lowerQuery.includes('find') || lowerQuery.includes('look for')) {
    return Intent.SEMANTIC_SEARCH;
  }
  if (lowerQuery.includes('transform') || lowerQuery.includes('convert') || lowerQuery.includes('extract facts') || lowerQuery.includes('bullet points')) {
    return Intent.CONTENT_TRANSFORMATION;
  }
  if (lowerQuery.includes('link') || lowerQuery.includes('connect') || lowerQuery.includes('related to')) {
    return Intent.CONCEPTUAL_LINKING;
  }

  return Intent.UNKNOWN;
}