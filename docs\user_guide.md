# User Guide

Welcome to the project! This guide will help you understand and use the application and its features.

## Core Features

The application provides powerful tools for managing your knowledge:

### Web Content Capture
Capture web content in various formats directly from your browser using the browser extension. This includes articles, images, and selected text.

### Intelligent Organization
Organize your captured content intelligently:
- **Tagging:** Assign relevant tags to easily categorize and find content later.
- **Categorization:** Group content into custom categories.
- **Summarization:** Automatically generate summaries of captured articles or text.

### Knowledge Base Interaction
Interact with your organized knowledge base in multiple ways:
- **Browsing:** Navigate through your content by tags, categories, or recency.
- **Search:** Perform full-text searches across all your captured content.
- **Q&A:** Ask questions about your content and get answers based on the information stored.
- **Summarization:** Generate summaries of specific documents or collections.
- **Transformation:** Transform content into different formats or extract key information.
- **Linking:** Create conceptual links between related pieces of content to build a knowledge graph.

## Local-First Principle and Data Privacy

The application is built on a local-first principle. This means your data is primarily stored on your local machine, giving you full control and ensuring privacy. Data is not sent to external servers unless you explicitly configure and use features that require it (e.g., integration with external AI services).

## Main Application UI

The main application provides a comprehensive interface for managing your knowledge base. Key areas include:
- **Navigation Pane:** Browse content by categories, tags, or other filters.
- **Item List Pane:** View a list of captured content items based on your navigation or search.
- **Detail View Pane:** See the full content and metadata of a selected item.
- **Search Bar:** Quickly search your knowledge base.
- **Settings:** Configure application settings, including integrations and data management options.

## Browser Extension

The browser extension is your primary tool for capturing web content. Once installed, you can use its interface to capture the current page, select specific elements, and apply initial tags or categories before saving to your knowledge base.

## User-Centric Verification

The system has been built and verified against high-level acceptance tests that represent complete user flows. You can review these tests to understand the core functionalities from a user perspective: [`docs/Master_Acceptance_Test_Plan.md`](docs/Master_Acceptance_Test_Plan.md)