import React from 'react';

const SearchFilterBar = ({
  currentSearchTerm,
  onSearchTermChange,
  onFilterApply,
  quickFilterOptions = []
}) => {
  
  const handleSearch = () => {
    if (onFilterApply) {
      onFilterApply({ searchTerm: currentSearchTerm });
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div data-testid="search-filter-bar-actual">
      <h3>Search & Filter</h3>
      <input
        type="text"
        placeholder="Search or filter graph..."
        value={currentSearchTerm}
        onChange={(e) => onSearchTermChange(e.target.value)}
        onKeyDown={handleKeyPress} // Changed from onKeyPress to onKeyDown
        data-testid="search-input" // Keep this for the test
        style={{ marginRight: '10px' }}
      />
      <button onClick={handleSearch}>Search</button>
      <div style={{ marginTop: '10px' }}>
        {quickFilterOptions.map(filter => (
          <button
            key={filter.id}
            onClick={() => onFilterApply({ quickFilterId: filter.id })}
            style={{ marginRight: '5px' }}
          >
            {filter.label}
          </button>
        ))}
      </div>
      {/* Add more advanced filter controls here later */}
    </div>
  );
};

export default SearchFilterBar;