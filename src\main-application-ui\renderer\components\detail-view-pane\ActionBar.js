import React from 'react';
import PropTypes from 'prop-types';

const ActionBar = ({
  onInitiateAIQA = null,
  onInitiateContentTransformation = null,
  onViewConceptualLinks = null,
  onEditMetadata = null,
  isAIQAEnabled = true,
  isContentTransformationEnabled = true,
  isConceptualLinksEnabled = true,
  isEditMetadataEnabled = false,
}) => {
  const showActionBar = 
    (isAIQAEnabled && onInitiateAIQA) ||
    (isContentTransformationEnabled && onInitiateContentTransformation) ||
    (isConceptualLinksEnabled && onViewConceptualLinks) ||
    (isEditMetadataEnabled && onEditMetadata);

  if (!showActionBar) {
    return null;
  }

  return (
    <div className="action-bar">
      {isAIQAEnabled && onInitiateAIQA && (
        <button
          type="button"
          className="action-bar-button ai-qa-button"
          onClick={onInitiateAIQA}
          aria-label="Initiate AI Q&A"
        >
          Ask AI (Q&A)
        </button>
      )}
      {isContentTransformationEnabled && onInitiateContentTransformation && (
        <button
          type="button"
          className="action-bar-button content-transform-button"
          onClick={onInitiateContentTransformation}
          aria-label="Initiate AI Content Transformation"
        >
          Transform Content
        </button>
      )}
      {isConceptualLinksEnabled && onViewConceptualLinks && (
        <button
          type="button"
          className="action-bar-button conceptual-links-button"
          onClick={onViewConceptualLinks}
          aria-label="View Suggested Conceptual Links"
        >
          View Links
        </button>
      )}
      {isEditMetadataEnabled && onEditMetadata && (
        <button
          type="button"
          className="action-bar-button edit-metadata-button"
          onClick={onEditMetadata}
          aria-label="Edit Metadata"
        >
          Edit Metadata
        </button>
      )}
    </div>
  );
};

ActionBar.propTypes = {
  onInitiateAIQA: PropTypes.func,
  onInitiateContentTransformation: PropTypes.func,
  onViewConceptualLinks: PropTypes.func,
  onEditMetadata: PropTypes.func,
  isAIQAEnabled: PropTypes.bool,
  isContentTransformationEnabled: PropTypes.bool,
  isConceptualLinksEnabled: PropTypes.bool,
  isEditMetadataEnabled: PropTypes.bool,
};

export default ActionBar;