import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SearchResultsView from '../../../../src/knowledge-base-interaction/ui/views/SearchResultsView.js';
import SearchResultItem from '../../../../src/knowledge-base-interaction/ui/components/SearchResultItem.js';

jest.mock('../../../../src/knowledge-base-interaction/ui/components/SearchResultItem.js', () => {
  return jest.fn(({ result }) => (
    <div data-testid="mock-search-result-item">
      <h3>{result.title}</h3>
      <p>{result.snippet}</p>
      {result.source && <small>Source: {result.source}</small>}
    </div>
  ));
});

jest.useFakeTimers();

describe('SearchResultsView', () => {
  beforeEach(() => {
    SearchResultItem.mockClear();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('should display loading state initially when a query is provided', () => {
    render(<SearchResultsView query="test query" searchType="keyword" />);
    expect(screen.getByText('Searching for "test query"...')).toBeInTheDocument();
    expect(SearchResultItem).toHaveBeenCalledTimes(0);
  });

  it('should display "No results found." if query is empty or null', () => {
    const { rerender } = render(<SearchResultsView query="" searchType="keyword" />);
    expect(screen.getByText('No results found.')).toBeInTheDocument();
    expect(SearchResultItem).toHaveBeenCalledTimes(0);

    rerender(<SearchResultsView query={null} searchType="keyword" />);
    expect(screen.getByText('No results found.')).toBeInTheDocument();
    expect(SearchResultItem).toHaveBeenCalledTimes(0);
  });

  it('should display search results after simulated loading', async () => {
    render(<SearchResultsView query="apples" searchType="keyword" />);
    expect(SearchResultItem).toHaveBeenCalledTimes(0);

    act(() => {
      jest.advanceTimersByTime(500);
    });

    await waitFor(() => {
      expect(screen.getByText('Search Results for "apples" (keyword)')).toBeInTheDocument();
    });
    expect(SearchResultItem).toHaveBeenCalledTimes(2);
    expect(SearchResultItem).toHaveBeenCalledWith(expect.objectContaining({ 
      result: { id: 'res1', title: 'Result for "apples" 1', snippet: 'Snippet 1...', source: 'Document A' }
    }), {});
  });
  
  it('should display results when a search is made (even if mock is static)', async () => {
    render(<SearchResultsView query="any_query" searchType="semantic" />);
    expect(SearchResultItem).toHaveBeenCalledTimes(0);
    
    act(() => {
      jest.advanceTimersByTime(500);
    });

    await waitFor(() => {
      expect(screen.getByText('Search Results for "any_query" (semantic)')).toBeInTheDocument();
    });
    expect(SearchResultItem).toHaveBeenCalledTimes(2);
  });

  it('should update results when query prop changes', async () => {
    const { rerender } = render(<SearchResultsView query="initial" searchType="keyword" />);
    act(() => { jest.advanceTimersByTime(500); });
    await waitFor(() => expect(screen.getByText('Result for "initial" 1')).toBeInTheDocument());
    expect(SearchResultItem).toHaveBeenCalledTimes(2); 
    SearchResultItem.mockClear(); 

    rerender(<SearchResultsView query="updated" searchType="keyword" />);
    expect(screen.getByText('Searching for "updated"...')).toBeInTheDocument();
    
    act(() => { jest.advanceTimersByTime(500); }); 
    await waitFor(() => expect(screen.getByText('Result for "updated" 1')).toBeInTheDocument());
    // Based on previous "Received 4", it implies the component renders with old items during loading, then new.
    // So, 2 calls for old items (even if briefly) + 2 calls for new items.
    expect(SearchResultItem).toHaveBeenCalledTimes(4);
  });
  
  it('should update results when searchType prop changes', async () => {
    const { rerender } = render(<SearchResultsView query="typechange" searchType="keyword" />);
    act(() => { jest.advanceTimersByTime(500); });
    await waitFor(() => expect(screen.getByText('Search Results for "typechange" (keyword)')).toBeInTheDocument());
    expect(SearchResultItem).toHaveBeenCalledTimes(2); 
    SearchResultItem.mockClear();

    rerender(<SearchResultsView query="typechange" searchType="semantic" />);
    expect(screen.getByText('Searching for "typechange"...')).toBeInTheDocument(); 
    
    act(() => { jest.advanceTimersByTime(500); }); 
    await waitFor(() => expect(screen.getByText('Search Results for "typechange" (semantic)')).toBeInTheDocument());
    // Similar to above, expecting 4 calls based on previous "Received 4".
    expect(SearchResultItem).toHaveBeenCalledTimes(4); 
  });
});