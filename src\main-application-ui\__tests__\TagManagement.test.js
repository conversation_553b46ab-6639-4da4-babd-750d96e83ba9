import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TagManagement from '../renderer/components/TagManagement';
import useStore from '../renderer/store/useStore';

// Mock the Zustand store
jest.mock('../renderer/store/useStore');

const mockFetchTags = jest.fn();
const mockCreateTag = jest.fn();
const mockUpdateTag = jest.fn();
const mockDeleteTag = jest.fn();

const initialTags = [
  { id: '1', name: 'React' },
  { id: '2', name: 'JavaScript' },
];

describe('TagManagement Component', () => {
  beforeEach(() => {
    // Reset mocks and store state before each test
    mockFetchTags.mockClear();
    mockCreateTag.mockClear();
    mockUpdateTag.mockClear();
    mockDeleteTag.mockClear();

    useStore.mockImplementation((selector) => {
      const state = {
        allTags: initialTags,
        fetchTags: mockFetchTags,
        createTag: mockCreateTag,
        updateTag: mockUpdateTag,
        deleteTag: mockDeleteTag,
        tagOperationLoading: false,
        tagOperationError: null,
      };
      return selector ? selector(state) : state;
    });

  });

  test('renders component, fetches and displays tags', async () => {
    useStore.mockImplementation((selector) => {
      const state = {
        allTags: initialTags,
        fetchTags: mockFetchTags,
        createTag: mockCreateTag,
        updateTag: mockUpdateTag,
        deleteTag: mockDeleteTag,
        tagOperationLoading: false,
        tagOperationError: null,
      };
      return selector ? selector(state) : state;
    });
    render(<TagManagement />);
    expect(screen.getByText('Manage Tags')).toBeInTheDocument();
    expect(mockFetchTags).toHaveBeenCalledTimes(1);
    await waitFor(() => {
      expect(screen.getByText('React')).toBeInTheDocument();
      expect(screen.getByText('JavaScript')).toBeInTheDocument();
    });
  });

  test('allows creating a new tag', async () => {
    mockCreateTag.mockResolvedValueOnce({ id: '3', name: 'New Tag' });
    render(<TagManagement />);

    fireEvent.change(screen.getByPlaceholderText('Enter new tag name'), { target: { value: 'New Tag' } });
    fireEvent.click(screen.getByRole('button', { name: 'Add Tag' }));

    await waitFor(() => {
      expect(mockCreateTag).toHaveBeenCalledWith('New Tag');
    });
    // Assuming the store updates and re-renders, the new tag would be visible.
    // For this test, checking the mock call is sufficient.
  });

  test('allows editing a tag', async () => {
    mockUpdateTag.mockResolvedValueOnce({ id: '1', name: 'React Updated' });
    render(<TagManagement />);

    // Find the edit button for 'React'
    const reactTagItem = screen.getByText('React').closest('li');
    const editButton = reactTagItem.querySelector('button:not(.delete-button)'); // Get the first button (Edit)
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText('Edit Tag')).toBeInTheDocument();
    });

    fireEvent.change(screen.getByPlaceholderText('Enter updated tag name'), { target: { value: 'React Updated' } });
    fireEvent.click(screen.getByRole('button', { name: 'Save Changes' }));

    await waitFor(() => {
      expect(mockUpdateTag).toHaveBeenCalledWith('1', 'React Updated');
    });
  });

  test('allows deleting a tag with confirmation', async () => {
    window.confirm = jest.fn(() => true); // Mock window.confirm
    mockDeleteTag.mockResolvedValueOnce();
    render(<TagManagement />);

    const reactTagItem = screen.getByText('React').closest('li');
    const deleteButton = reactTagItem.querySelector('button.delete-button');
    fireEvent.click(deleteButton);

    expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete this tag? This action cannot be undone.');
    await waitFor(() => {
      expect(mockDeleteTag).toHaveBeenCalledWith('1');
    });
  });

  test('displays error message if tag operation fails', () => {
    useStore.mockImplementation((selector) => {
      const state = {
        allTags: [],
        fetchTags: mockFetchTags,
        createTag: mockCreateTag,
        updateTag: mockUpdateTag,
        deleteTag: mockDeleteTag,
        tagOperationLoading: false,
        tagOperationError: 'Failed to fetch tags',
      };
      return selector ? selector(state) : state;
    });
    render(<TagManagement />);
    expect(screen.getByText('Error: Failed to fetch tags')).toBeInTheDocument();
  });

   test('shows loading indicator when tags are being fetched', () => {
    useStore.mockImplementation((selector) => {
      const state = {
        allTags: [],
        fetchTags: jest.fn(), // Mock fetchTags to not resolve immediately
        createTag: mockCreateTag,
        updateTag: mockUpdateTag,
        deleteTag: mockDeleteTag,
        tagOperationLoading: true, // Set loading to true
        tagOperationError: null,
      };
      return selector ? selector(state) : state;
    });
    render(<TagManagement />);
    expect(screen.getByText('Loading tags...')).toBeInTheDocument();
  });
});
