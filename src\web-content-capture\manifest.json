{"manifest_version": 3, "name": "Web Content Capture", "version": "1.0", "description": "Captures the full HTML content of a web page and saves it to a local file.", "permissions": ["activeTab", "scripting", "storage", "downloads"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["lib/Readability.js", "content_script.js"]}], "action": {"default_popup": "popup.html", "default_icon": {"16": "/assets/icon16.png", "48": "/assets/icon48.png", "128": "/assets/icon128.png"}}, "icons": {"16": "/assets/icon16.png", "48": "/assets/icon48.png", "128": "/assets/icon128.png"}}