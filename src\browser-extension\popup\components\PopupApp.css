.popup-app {
  font-family: Arial, sans-serif;
  padding: 15px;
  width: 300px; /* Adjust as needed */
  text-align: center;
  background-color: #f9f9f9;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.popup-app h1 {
  font-size: 1.5em;
  margin-bottom: 15px;
  color: #333;
}

.popup-app button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 1em;
  margin-top: 15px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.popup-app button:hover {
  background-color: #0056b3;
}

.popup-app button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.status-success {
  color: green;
  margin-top: 10px;
  font-weight: bold;
}

.status-error {
  color: red;
  margin-top: 10px;
  font-weight: bold;
}

/* Placeholder for CaptureTypeSelector styles */
.capture-type-selector {
  margin-bottom: 15px;
}

.capture-type-selector label {
  margin-right: 10px;
}

/* Placeholder for CaptureControls styles */
.capture-controls {
  margin-top: 15px;
}