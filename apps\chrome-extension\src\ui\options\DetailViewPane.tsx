import React, { useState, useEffect, useCallback } from 'react';
import { KnowledgeBaseEntry as KnowledgeBaseItem } from '@pkm-ai/knowledge-base-service';
import { UpdateEntryData } from '../../types/messaging';

interface DetailViewPaneProps {
  item: KnowledgeBaseItem | null;
  onUpdate: (id: string, data: UpdateEntryData) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  onRefresh?: () => void; // Optional: to refresh the list after certain actions
}

const DetailViewPane: React.FC<DetailViewPaneProps> = ({ item, onUpdate, onDelete, onRefresh }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editFormData, setEditFormData] = useState<UpdateEntryData>({});

  useEffect(() => {
    if (item) {
      setEditFormData({
        title: item.title,
        url: item.url,
        content: item.content,
        tags: item.tags || [],
        type: item.type,
      });
      setIsEditing(false); // Reset editing state when item changes
    }
  }, [item]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditFormData((prev: UpdateEntryData) => ({ ...prev, [name]: value }));
  };

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setEditFormData((prev: UpdateEntryData) => ({ ...prev, tags: value.split(',').map(tag => tag.trim()).filter(tag => tag) }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (item) {
      await onUpdate(item.id, editFormData);
      setIsEditing(false);
      if (onRefresh) onRefresh();
    }
  };

  const handleDelete = async () => {
    if (item && window.confirm(`Are you sure you want to delete "${item.title}"?`)) {
      await onDelete(item.id);
      // Item will be cleared by parent component
    }
  };

  if (!item) {
    return <div data-testid="select-item-message" className="p-6 text-gray-500 bg-white rounded-lg shadow">Select an item to view its details.</div>;
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow h-full overflow-y-auto" data-testid="detail-view-pane">
      {isEditing ? (
        <form onSubmit={handleSubmit} className="space-y-4" data-testid="edit-form">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700">Title</label>
            <input
              data-testid="edit-title-input"
              type="text"
              name="title"
              id="title"
              value={editFormData.title || ''}
              onChange={handleInputChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            />
          </div>
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-700">URL</label>
            <input
              data-testid="edit-url-input"
              type="url"
              name="url"
              id="url"
              value={editFormData.url || ''}
              onChange={handleInputChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700">Content</label>
            <textarea
              data-testid="edit-content-textarea"
              name="content"
              id="content"
              rows={6}
              value={editFormData.content || ''}
              onChange={handleInputChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700">Tags (comma-separated)</label>
            <input
              data-testid="edit-tags-input"
              type="text"
              name="tags"
              id="tags"
              value={(editFormData.tags || []).join(', ')}
              onChange={handleTagsChange}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
           <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700">Type</label>
            <select
                data-testid="edit-type-select"
                name="type"
                id="type"
                value={editFormData.type || 'note'}
                onChange={handleInputChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
                <option value="note">Note</option>
                <option value="bookmark">Bookmark</option>
                <option value="article">Article</option>
                {/* Add other types as needed */}
            </select>
          </div>
          <div className="flex space-x-3">
            <button data-testid="save-changes-button" type="submit" className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Save Changes</button>
            <button data-testid="cancel-edit-button" type="button" onClick={() => setIsEditing(false)} className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">Cancel</button>
          </div>
        </form>
      ) : (
        <>
          <div className="flex justify-between items-start mb-3">
            <h2 data-testid="view-title" className="text-2xl font-semibold text-gray-800 break-all">{item.title}</h2>
            <div className="flex-shrink-0 space-x-2">
                <button data-testid="edit-entry-button" onClick={() => setIsEditing(true)} className="px-3 py-1.5 text-sm bg-yellow-500 text-white rounded-md hover:bg-yellow-600">Edit</button>
                <button data-testid="delete-entry-button" onClick={handleDelete} className="px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700">Delete</button>
            </div>
          </div>

          {item.url && (
            <a
              data-testid="view-url-link"
              href={item.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline text-sm mb-4 block break-all"
            >
              {item.url}
            </a>
          )}
          {/* For item.content, ensure sanitization if it can contain HTML. Here, it's rendered as plain text within <pre> for formatting. */}
          <h4 className="font-semibold text-md text-gray-700 mt-4 mb-1">Content:</h4>
          <pre data-testid="view-content" className="prose max-w-none text-gray-700 mb-4 p-3 bg-gray-50 rounded-md whitespace-pre-wrap break-all">{item.content || 'No content.'}</pre>
          
          {item.tags && item.tags.length > 0 && (
            <div className="mb-4">
              <h4 className="font-semibold text-sm text-gray-600">Tags:</h4>
              <div className="flex flex-wrap gap-2 mt-1">
                {item.tags.map((tag: string) => (
                  <span
                    data-testid="view-tag" // Generic for now, can be made more specific if needed e.g. view-tag-${tag}
                    key={tag}
                    className="px-2.5 py-1 bg-gray-200 text-gray-800 text-xs rounded-full font-medium"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}
           <p data-testid="view-type" className="text-sm text-gray-500 mb-2"><strong>Type:</strong> {item.type || 'N/A'}</p>
          {item.createdAt && (
            <p className="text-xs text-gray-400">
              Created: {new Date(item.createdAt).toLocaleString()}
            </p>
          )}
          {item.updatedAt && (
            <p className="text-xs text-gray-400 mt-0.5">
              Updated: {new Date(item.updatedAt).toLocaleString()}
            </p>
          )}
        </>
      )}
    </div>
  );
};

export default DetailViewPane;