# Diagnosis Report: Test Case TC_WCC_SAVE_MD_001 Failure

**Test Case ID:** `TC_WCC_SAVE_MD_001`
**File:** [`src/web-content-capture/__tests__/webContentCapture.test.js`](src/web-content-capture/__tests__/webContentCapture.test.js)
**Feature:** Web Content Capture - Save as Markdown (Micro-task 1.8: Implement configurable saving formats)

## 1. Observed Behavior

The test case `TC_WCC_SAVE_MD_001` consistently fails due to a string comparison mismatch. The Jest diff output reportedly shows the expected and received strings as visually identical, leading to suspicion about Jest matchers or subtle string differences (e.g., whitespace, line endings).

## 2. Analysis

The investigation involved analyzing the test setup in [`src/web-content-capture/__tests__/webContentCapture.test.js`](src/web-content-capture/__tests__/webContentCapture.test.js) and the corresponding implementation in [`src/web-content-capture/index.js`](src/web-content-capture/index.js), specifically the `saveProcessedData` and `convertToMarkdown` functions.

### 2.1. Code Logic for Markdown Saving

-   The `saveProcessedData` function (in [`src/web-content-capture/index.js`](src/web-content-capture/index.js:386)) handles the logic for saving content in various formats.
-   When the format is 'markdown', it calls `this.convertToMarkdown(contentToConvert, { includeFrontmatter: true, metadata: processedData.metadata })` (line 400).
-   The `convertToMarkdown` function (in [`src/web-content-capture/index.js`](src/web-content-capture/index.js:289)) performs the following steps when a `turndownService` instance is available:
    1.  It converts the input HTML string to a basic Markdown string using `turndownService.turndown(htmlContent || "")` (line 334).
    2.  It trims the output from `turndownService.turndown()` (line 336).
    3.  If the `includeFrontmatter` option is true (which it is by default when called from `saveProcessedData`), it constructs a YAML frontmatter block from the provided metadata and prepends it to the Markdown string, ensuring two newlines separate the frontmatter from the content (lines 338-350: `frontmatter += '---\n\n'; markdownOutput = frontmatter + markdownOutput;`).
    4.  Finally, it trims the entire resulting string (line 352).

### 2.2. Test Case Mock Implementation

-   The test case `TC_WCC_SAVE_MD_001` mocks the `turndownServiceInstance.turndown` method (around line 1252 in [`src/web-content-capture/__tests__/webContentCapture.test.js`](src/web-content-capture/__tests__/webContentCapture.test.js:1252)).
-   The mock is implemented as:
    ```javascript
    mockTurndownServiceInstance.turndown.mockImplementation(htmlInput => {
      if (htmlInput === mockProcessedDataHTML.content) {
        return specificExpectedMarkdown; // Problematic line
      }
      return `unexpected_html_for_markdown: ${htmlInput}`;
    });
    ```
-   The `specificExpectedMarkdown` variable (defined on line 1250) is:
    ```javascript
    const specificExpectedMarkdown = "---\ntitle: \"Test Page for Save\"\nurl: \"http://example.com/save\"\n---\n\n# Title\n\nSome **bold** text.";
    ```
    This string *already includes the YAML frontmatter*.

## 3. Root Cause Identification

The root cause of the test failure is an **incorrect mock implementation for `turndownServiceInstance.turndown` within the `TC_WCC_SAVE_MD_001` test case.**

Because the mock returns a string that *already contains frontmatter*, the `convertToMarkdown` function subsequently prepends frontmatter *again*. This results in the `finalContent` (which is sent to the background script for saving and asserted in the test) having duplicated frontmatter.

**Sequence of Events Leading to Failure:**

1.  `saveProcessedData` calls `convertToMarkdown`.
2.  `convertToMarkdown` calls the mocked `turndownServiceInstance.turndown`.
3.  The mock returns `specificExpectedMarkdown` (which includes frontmatter).
4.  `convertToMarkdown` takes this string and, because `includeFrontmatter` is true, prepends *another* block of frontmatter.
5.  The resulting string with duplicated frontmatter is then compared against `expectedDataNormalized` (which is `specificExpectedMarkdown` with only a single frontmatter block), causing the assertion `expect(receivedDataNormalized).toBe(expectedDataNormalized)` to fail.

The issue is not with the Jest matcher, the test environment, or a functional bug in the application code itself. It's a flaw in the test's mock setup.

## 4. Proposed Solution

The mock for `turndownServiceInstance.turndown` in `TC_WCC_SAVE_MD_001` needs to be corrected to return only the raw Markdown conversion of the HTML input, without any frontmatter. The `convertToMarkdown` function is responsible for adding the frontmatter.

**Specific Change in [`src/web-content-capture/__tests__/webContentCapture.test.js`](src/web-content-capture/__tests__/webContentCapture.test.js):**

Modify the mock implementation around line 1252:

```diff
--- a/src/web-content-capture/__tests__/webContentCapture.test.js
+++ b/src/web-content-capture/__tests__/webContentCapture.test.js
@@ -1249,10 +1249,11 @@
     test('TC_WCC_SAVE_MD_001: should save content as Markdown', async () => {
       const dataToSave = { ...mockProcessedDataHTML, format: 'markdown' };
       // Updated to include frontmatter as saveProcessedData adds it by default, with correct newlines
-      const specificExpectedMarkdown = "---\ntitle: \"Test Page for Save\"\nurl: \"http://example.com/save\"\n---\n\n# Title\n\nSome **bold** text.";
-      
+      const expectedMarkdownAfterTurndown = "# Title\n\nSome **bold** text."; // Turndown should only return this part
+      const expectedFinalMarkdownWithFrontmatter = "---\ntitle: \"Test Page for Save\"\nurl: \"http://example.com/save\"\n---\n\n" + expectedMarkdownAfterTurndown;
+
       mockTurndownServiceInstance.turndown.mockImplementation(htmlInput => {
         if (htmlInput === mockProcessedDataHTML.content) {
-          return specificExpectedMarkdown;
+          return expectedMarkdownAfterTurndown; // Mock returns only the content part, without frontmatter
         }
         return `unexpected_html_for_markdown: ${htmlInput}`;
       });
@@ -1265,7 +1266,7 @@
       expect(messageSent.action).toBe('saveContent');
       // Normalize newlines in received data for comparison
       const receivedDataNormalized = messageSent.data.replace(/\r\n/g, "\n");
-      const expectedDataNormalized = specificExpectedMarkdown.replace(/\r\n/g, "\n");
+      const expectedDataNormalized = expectedFinalMarkdownWithFrontmatter.replace(/\r\n/g, "\n");
       
       if (receivedDataNormalized !== expectedDataNormalized) {
         console.log("Mismatch detected in TC_WCC_SAVE_MD_001. Character by character comparison:");

```

## 5. Conclusion

The failure of `TC_WCC_SAVE_MD_001` is due to a misconfiguration of the `turndownServiceInstance.turndown` mock within the test. Correcting the mock to align with the responsibilities of the `convertToMarkdown` function (i.e., letting `convertToMarkdown` handle frontmatter addition) will resolve the test failure.