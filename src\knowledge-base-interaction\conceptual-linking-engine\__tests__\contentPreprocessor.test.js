import ContentPreprocessor from '../contentPreprocessor';

describe('ContentPreprocessor', () => {
    let preprocessor;

    beforeEach(() => {
        preprocessor = new ContentPreprocessor();
    });

    it('should be instantiable', () => {
        expect(preprocessor).toBeInstanceOf(ContentPreprocessor);
    });

    it('should remove leading and trailing whitespace', () => {
        const text = "  hello world  ";
        expect(preprocessor.preprocess(text)).toBe("hello world");
    });

    it('should replace multiple spaces with a single space', () => {
        const text = "hello   world";
        expect(preprocessor.preprocess(text)).toBe("hello world");
    });

    it('should replace tabs with a single space', () => {
        const text = "hello\tworld";
        expect(preprocessor.preprocess(text)).toBe("hello world");
    });

    it('should replace newlines with a single space', () => {
        const text = "hello\nworld";
        expect(preprocessor.preprocess(text)).toBe("hello world");
    });

    it('should handle mixed whitespace characters', () => {
        const text = "  hello \t world \n another \r\n line  ";
        expect(preprocessor.preprocess(text)).toBe("hello world another line");
    });

    it('should return an empty string if input is empty', () => {
        const text = "";
        expect(preprocessor.preprocess(text)).toBe("");
    });

    it('should return an empty string if input is only whitespace', () => {
        const text = "   \t  \n  ";
        expect(preprocessor.preprocess(text)).toBe("");
    });

    it('should not alter a string that is already clean', () => {
        const text = "this is a clean string.";
        expect(preprocessor.preprocess(text)).toBe("this is a clean string.");
    });

    it('should throw a TypeError if input is not a string', () => {
        expect(() => preprocessor.preprocess(null)).toThrow(TypeError);
        expect(() => preprocessor.preprocess(undefined)).toThrow(TypeError);
        expect(() => preprocessor.preprocess(123)).toThrow(TypeError);
        expect(() => preprocessor.preprocess({})).toThrow(TypeError);
        expect(() => preprocessor.preprocess([])).toThrow(TypeError);
    });
});