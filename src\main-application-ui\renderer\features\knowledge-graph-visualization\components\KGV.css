.kgv-container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* Or a specific height */
  border: 1px solid blue;
}

.kgv-main-area {
  display: flex;
  flex-grow: 1;
  border: 1px solid green;
}

.kgv-side-panel {
  width: 250px; /* Adjust as needed */
  padding: 10px;
  border-left: 1px solid #ccc;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Give ControlPanel, GraphRenderingArea specific flex properties if needed */
/* For example, GraphRenderingArea should take up most space */
/* ControlPanel might be a fixed width sidebar or a top bar */

/* Styling for GraphRenderingArea's actual div is in its own component for now */
/* Styling for other components can be added here or in their own CSS files */