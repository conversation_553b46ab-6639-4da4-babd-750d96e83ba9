import React, { useState, useEffect } from 'react';
import useStore from '../store/useStore';
import './ClippingTemplates.css';

const ClippingTemplates = () => {
  const {
    clippingTemplates,
    fetchClippingTemplates,
    createClippingTemplate,
    updateClippingTemplate,
    deleteClippingTemplate,
    setDefaultClippingTemplate,
    clippingTemplatesLoading,
    clippingTemplatesError,
    clippingTemplateCreating,
    clippingTemplateCreateError,
    clippingTemplateUpdating,
    clippingTemplateUpdateError,
    clippingTemplateDeleting,
    clippingTemplateDeleteError,
    clippingTemplateSettingDefault,
    clippingTemplateSetDefaultError,
    clearClippingTemplateStatus,
  } = useStore();

  const [newTemplateName, setNewTemplateName] = useState('');
  const [newTemplateContent, setNewTemplateContent] = useState('');
  const [editingTemplate, setEditingTemplate] = useState(null); // { id, name, content }
  const [showCreateForm, setShowCreateForm] = useState(false);

  useEffect(() => {
    fetchClippingTemplates();
    return () => {
      clearClippingTemplateStatus(); // Clear any lingering statuses when component unmounts
    };
  }, [fetchClippingTemplates, clearClippingTemplateStatus]);

  const handleCreateNew = () => {
    setEditingTemplate(null);
    setNewTemplateName('');
    setNewTemplateContent('');
    setShowCreateForm(true);
  };

  const handleSaveNewTemplate = async () => {
    if (!newTemplateName.trim() || !newTemplateContent.trim()) {
      // Basic validation, consider more robust validation
      alert('Template name and content cannot be empty.');
      return;
    }
    try {
      await createClippingTemplate({ name: newTemplateName, content: newTemplateContent, isDefault: false });
      setNewTemplateName('');
      setNewTemplateContent('');
      setShowCreateForm(false);
    } catch (error) {
      // Error is handled by the store, but you could add specific UI feedback here
      console.error("Failed to create template:", error);
    }
  };

  const handleEditTemplate = (template) => {
    setEditingTemplate({ ...template });
    setNewTemplateName(template.name); // Pre-fill for consistency if create form is reused
    setNewTemplateContent(template.content);
    setShowCreateForm(false); // Hide create form if it was open
  };

  const handleSaveEditedTemplate = async () => {
    if (!editingTemplate || !editingTemplate.name.trim() || !editingTemplate.content.trim()) {
      alert('Template name and content cannot be empty.');
      return;
    }
    try {
      await updateClippingTemplate(editingTemplate.id, { name: editingTemplate.name, content: editingTemplate.content, isDefault: editingTemplate.isDefault });
      setEditingTemplate(null);
    } catch (error) {
      console.error("Failed to update template:", error);
    }
  };

  const handleDeleteTemplate = async (templateId) => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      try {
        await deleteClippingTemplate(templateId);
      } catch (error) {
        console.error("Failed to delete template:", error);
      }
    }
  };

  const handleSetDefault = async (templateId) => {
    try {
      await setDefaultClippingTemplate(templateId);
    } catch (error) {
      console.error("Failed to set default template:", error);
    }
  };

  const cancelEdit = () => {
    setEditingTemplate(null);
  };

  const cancelCreate = () => {
    setShowCreateForm(false);
    setNewTemplateName('');
    setNewTemplateContent('');
  };

  if (clippingTemplatesLoading) {
    return <div>Loading clipping templates...</div>;
  }

  // Consolidate error display
  const anyError = clippingTemplatesError ||
                   clippingTemplateCreateError ||
                   clippingTemplateUpdateError ||
                   clippingTemplateDeleteError ||
                   clippingTemplateSetDefaultError;

  if (anyError) {
    return <div className="error-message">Error: {anyError}</div>;
  }

  return (
    <div className="clipping-templates-container">
      <h3>Manage Clipping Templates</h3>

      {!editingTemplate && !showCreateForm && (
        <button onClick={handleCreateNew} className="btn btn-primary">
          Create New Template
        </button>
      )}

      {(showCreateForm && !editingTemplate) && (
        <div className="template-form create-template-form">
          <h4>Create New Template</h4>
          <input
            type="text"
            placeholder="Template Name"
            value={newTemplateName}
            onChange={(e) => setNewTemplateName(e.target.value)}
            disabled={clippingTemplateCreating}
          />
          <textarea
            placeholder="Template Content (e.g., {{title}}, {{content}}, {{url}})"
            value={newTemplateContent}
            onChange={(e) => setNewTemplateContent(e.target.value)}
            rows={6}
            disabled={clippingTemplateCreating}
          />
          <div className="form-actions">
            <button onClick={handleSaveNewTemplate} disabled={clippingTemplateCreating} className="btn btn-success">
              {clippingTemplateCreating ? 'Saving...' : 'Save New Template'}
            </button>
            <button onClick={cancelCreate} disabled={clippingTemplateCreating} className="btn btn-secondary">
              Cancel
            </button>
          </div>
          {clippingTemplateCreateError && <p className="error-message">{clippingTemplateCreateError}</p>}
        </div>
      )}

      {editingTemplate && (
        <div className="template-form edit-template-form">
          <h4>Edit Template: {editingTemplate.name}</h4>
          <input
            type="text"
            placeholder="Template Name"
            value={editingTemplate.name}
            onChange={(e) => setEditingTemplate({ ...editingTemplate, name: e.target.value })}
            disabled={clippingTemplateUpdating}
          />
          <textarea
            placeholder="Template Content"
            value={editingTemplate.content}
            onChange={(e) => setEditingTemplate({ ...editingTemplate, content: e.target.value })}
            rows={6}
            disabled={clippingTemplateUpdating}
          />
          <div className="form-actions">
            <button onClick={handleSaveEditedTemplate} disabled={clippingTemplateUpdating} className="btn btn-success">
              {clippingTemplateUpdating ? 'Saving...' : 'Save Changes'}
            </button>
            <button onClick={cancelEdit} disabled={clippingTemplateUpdating} className="btn btn-secondary">
              Cancel
            </button>
          </div>
          {clippingTemplateUpdateError && <p className="error-message">{clippingTemplateUpdateError}</p>}
        </div>
      )}

      {clippingTemplates.length === 0 && !clippingTemplatesLoading && !showCreateForm && !editingTemplate && (
        <p>No custom clipping templates found. Create one to get started!</p>
      )}

      {clippingTemplates.length > 0 && !editingTemplate && !showCreateForm && (
        <ul className="templates-list">
          {clippingTemplates.map((template) => (
            <li key={template.id} className={`template-item ${template.isDefault ? 'default-template' : ''}`}>
              <div className="template-info">
                <strong>{template.name}</strong> {template.isDefault && <span className="default-badge">(Default)</span>}
                <pre className="template-content-preview">{template.content.substring(0, 100)}{template.content.length > 100 ? '...' : ''}</pre>
              </div>
              <div className="template-actions">
                <button onClick={() => handleEditTemplate(template)} className="btn btn-sm btn-info" disabled={clippingTemplateDeleting || clippingTemplateSettingDefault}>Edit</button>
                <button onClick={() => handleDeleteTemplate(template.id)} className="btn btn-sm btn-danger" disabled={clippingTemplateDeleting || clippingTemplateSettingDefault || template.isDefault}>
                  {clippingTemplateDeleting ? 'Deleting...' : 'Delete'}
                </button>
                {!template.isDefault && (
                  <button
                    onClick={() => handleSetDefault(template.id)}
                    className="btn btn-sm btn-warning"
                    disabled={clippingTemplateDeleting || clippingTemplateSettingDefault}
                  >
                    {clippingTemplateSettingDefault ? 'Setting...' : 'Set as Default'}
                  </button>
                )}
              </div>
            </li>
          ))}
        </ul>
      )}
      {clippingTemplateDeleteError && <p className="error-message">{clippingTemplateDeleteError}</p>}
      {clippingTemplateSetDefaultError && <p className="error-message">{clippingTemplateSetDefaultError}</p>}
    </div>
  );
};

export default ClippingTemplates;