import React, { useEffect, useRef, memo, useMemo } from 'react';
import cytoscape from 'cytoscape';
import DOMPurify from 'dompurify';
// import fcose from 'cytoscape-fcose'; // For a potentially better force-directed layout
// cytoscape.use(fcose); // Register extension

// Helper to map visual encodings to Cytoscape style
const mapEncodingsToStyle = (visualEncodings) => {
  const style = [];
  if (!visualEncodings) return style;

  if (visualEncodings.nodeTypes) {
    Object.entries(visualEncodings.nodeTypes).forEach(([typeId, encoding]) => {
      style.push({
        selector: `node[type = "${typeId}"]`,
        style: {
          'background-color': encoding.color || '#666',
          'label': 'data(label)',
          'shape': encoding.shape || 'ellipse',
          'width': encoding.size || '30px',
          'height': encoding.size || '30px',
          'font-size': '10px',
          'color': '#fff', // Label color
          'text-outline-color': '#333', // Label outline
          'text-outline-width': '1px',
        }
      });
    });
  } else { // Default node style if no types defined
    style.push({
        selector: 'node',
        style: { 'background-color': '#666', 'label': 'data(label)', 'font-size': '10px', 'color': '#fff', 'text-outline-color': '#333', 'text-outline-width': '1px' }
    });
  }

  if (visualEncodings.edgeTypes) {
    Object.entries(visualEncodings.edgeTypes).forEach(([typeId, encoding]) => {
      style.push({
        selector: `edge[type = "${typeId}"]`,
        style: {
          'width': encoding.thickness || 2,
          'line-color': encoding.color || '#ccc',
          'target-arrow-color': encoding.color || '#ccc',
          'target-arrow-shape': 'triangle',
          'curve-style': 'bezier', // Or 'straight', 'haystack'
          'label': 'data(label)', // If edges have labels
          'font-size': '8px',
          'color': '#555',
          'text-rotation': 'autorotate',
        }
      });
    });
  } else { // Default edge style
     style.push({
        selector: 'edge',
        style: { 'width': 2, 'line-color': '#ccc', 'target-arrow-color': '#ccc', 'target-arrow-shape': 'triangle', 'curve-style': 'bezier' }
    });
  }
  return style;
};


const GraphRenderingArea = ({
  graphData,
  layout, // layout name string e.g. 'grid', 'circle', 'force-directed' (or fcose)
  onNodeSelect,
  onEdgeSelect,
  onCanvasInteraction, // { type: 'zoom'/'pan', details: ... }
  visualEncodings
}) => {
  const cyRef = useRef(null); // For the div container
  const cyInstanceRef = useRef(null); // For the Cytoscape instance

  const elements = useMemo(() => {
    return {
      nodes: graphData.nodes.map(node => ({
        data: {
          ...node,
          label: node.label ? DOMPurify.sanitize(node.label, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : node.label,
        }
      })),
      edges: graphData.edges.map(edge => ({
        data: {
          ...edge,
          label: edge.label ? DOMPurify.sanitize(edge.label, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : edge.label,
        }
      })),
    };
  }, [graphData]);

  const cyStyle = useMemo(() => {
    return mapEncodingsToStyle(visualEncodings);
  }, [visualEncodings]);

  // Effect for Cytoscape initialization and cleanup
  useEffect(() => {
    if (!cyRef.current) return;

    console.log('GraphRenderingArea: Initializing Cytoscape');
    const cy = cytoscape({
      container: cyRef.current,
      elements: elements, // Use memoized elements for initial setup
      style: cyStyle,     // Use memoized style for initial setup
      layout: { name: layout || 'grid' }, // Initial layout
      zoom: 1,
      minZoom: 0.1,
      maxZoom: 3,
      zoomingEnabled: true,
      userZoomingEnabled: true,
      panningEnabled: true,
      userPanningEnabled: true,
      boxSelectionEnabled: true,
    });
    cyInstanceRef.current = cy;

    // Event listeners
    cy.on('tap', 'node', (event) => {
      const nodeId = event.target.id();
      if (onNodeSelect) onNodeSelect([nodeId]);
    });
    cy.on('tap', 'edge', (event) => {
      const edgeId = event.target.id();
      if (onEdgeSelect) onEdgeSelect(edgeId);
    });
    cy.on('zoom pan', (event) => {
      if (onCanvasInteraction) {
        onCanvasInteraction({ type: event.type, level: cy.zoom(), pan: cy.pan() });
      }
    });

    return () => {
      if (cyInstanceRef.current) {
        console.log('GraphRenderingArea: Destroying Cytoscape instance');
        cyInstanceRef.current.destroy();
        cyInstanceRef.current = null;
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array: runs once on mount and cleanup on unmount

  // Effect for updating elements
  useEffect(() => {
    if (cyInstanceRef.current) {
      console.log('GraphRenderingArea: Updating Cytoscape elements');
      cyInstanceRef.current.json({ elements });
    }
  }, [elements]);

  // Effect for updating style
  useEffect(() => {
    if (cyInstanceRef.current) {
      console.log('GraphRenderingArea: Updating Cytoscape style');
      cyInstanceRef.current.style(cyStyle);
    }
  }, [cyStyle]);

  // Effect for layout changes specifically
  useEffect(() => {
    if (cyInstanceRef.current && layout) {
      console.log(`GraphRenderingArea: Applying layout: ${layout}`);
      cyInstanceRef.current.layout({ name: layout }).run();
    }
  }, [layout]);


  return (
    <div data-testid="graph-rendering-area-actual" ref={cyRef} style={{ width: '100%', height: 'calc(100vh - 200px)', border: '1px solid #ddd', background: '#f0f0f0' }}>
      {/* Cytoscape graph will be rendered here */}
      {/* The mock test's span and button elements are not part of the actual component */}
    </div>
  );
};

// Memoize to prevent re-renders if props haven't changed, especially graphData
export default memo(GraphRenderingArea);