import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import DetailViewPane from '../DetailViewPane';
import { KnowledgeBaseEntry } from '@pkm-ai/knowledge-base-service'; // Use the actual type from the service
import { UpdateEntryData } from '../../../types/messaging';

const mockItem: KnowledgeBaseEntry = {
  id: '1',
  title: 'Detailed Item Title',
  content: 'This is the full content of the item. It can be quite long and include various details.',
  url: 'http://example.com/detailed-item',
  tags: ['detail', 'testing', 'react'],
  type: 'article',
  createdAt: new Date('2023-01-15T10:00:00Z'),
  updatedAt: new Date('2023-01-16T12:30:00Z'),
};

const mockItemNoOptionalFields: KnowledgeBaseEntry = {
  id: '2',
  title: 'Minimal Item Title',
  content: 'Minimal content.',
  type: 'note',
  createdAt: new Date('2023-02-20T08:00:00Z'),
  updatedAt: new Date('2023-02-20T08:00:00Z'),
};

describe('DetailViewPane', () => {
  const mockOnUpdate = jest.fn();
  const mockOnDelete = jest.fn();
  const mockOnRefresh = jest.fn();

  beforeEach(() => {
    // Ensure mocks return promises as per their expected prop types
    mockOnUpdate.mockClear().mockResolvedValue(undefined as void | Promise<void>);
    mockOnDelete.mockClear().mockResolvedValue(undefined as void | Promise<void>); // mockResolvedValue for Promise<void>
    mockOnRefresh.mockClear();
    // Mock window.confirm for delete operations
    window.confirm = jest.fn(() => true);
  });

  test('renders "Select an item to view its details." when no item is provided', () => {
    render(<DetailViewPane item={null} onUpdate={mockOnUpdate} onDelete={mockOnDelete} onRefresh={mockOnRefresh} />);
    expect(screen.getByText('Select an item to view its details.')).toBeInTheDocument();
  });

  test('renders item details correctly when an item is provided', () => {
    render(<DetailViewPane item={mockItem} onUpdate={mockOnUpdate} onDelete={mockOnDelete} onRefresh={mockOnRefresh} />);
    expect(screen.getByRole('heading', { name: 'Detailed Item Title', level: 2 })).toBeInTheDocument();
    // Content is in a <pre> tag
    expect(screen.getByText('This is the full content of the item. It can be quite long and include various details.')).toBeInTheDocument();
    
    const linkElement = screen.getByRole('link', { name: 'http://example.com/detailed-item' });
    expect(linkElement).toBeInTheDocument();
    expect(linkElement).toHaveAttribute('href', 'http://example.com/detailed-item');
    
    expect(screen.getByText('Tags:')).toBeInTheDocument();
    expect(screen.getByText('detail')).toBeInTheDocument();
    expect(screen.getByText('testing')).toBeInTheDocument();
    expect(screen.getByText('react')).toBeInTheDocument();
    expect(screen.getByText('Type:')).toBeInTheDocument();
    expect(screen.getByText('article')).toBeInTheDocument();


    // Using toLocaleString for date checks as in the component
    const createdDateString = mockItem.createdAt.toLocaleString();
    const updatedDateString = mockItem.updatedAt.toLocaleString();

    expect(screen.getByText(`Created: ${createdDateString}`)).toBeInTheDocument();
    expect(screen.getByText(`Updated: ${updatedDateString}`)).toBeInTheDocument();
  });

  test('renders correctly when optional fields (url, tags) are missing', () => {
    render(<DetailViewPane item={mockItemNoOptionalFields} onUpdate={mockOnUpdate} onDelete={mockOnDelete} onRefresh={mockOnRefresh} />);
    expect(screen.getByRole('heading', { name: 'Minimal Item Title', level: 2 })).toBeInTheDocument();
    expect(screen.getByText('Minimal content.')).toBeInTheDocument();
    expect(screen.getByText('Type:')).toBeInTheDocument();
    expect(screen.getByText('note')).toBeInTheDocument();
    
    expect(screen.queryByRole('link')).not.toBeInTheDocument();
    expect(screen.queryByText('Tags:')).not.toBeInTheDocument();

    expect(screen.getByText((content) => content.startsWith('Created:'))).toBeInTheDocument();
    expect(screen.getByText((content) => content.startsWith('Updated:'))).toBeInTheDocument();
  });

  test('switches to edit mode and populates form', async () => {
    render(<DetailViewPane item={mockItem} onUpdate={mockOnUpdate} onDelete={mockOnDelete} onRefresh={mockOnRefresh} />);
    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);

    expect(await screen.findByLabelText(/title/i)).toHaveValue(mockItem.title);
    expect(screen.getByLabelText(/url/i)).toHaveValue(mockItem.url);
    expect(screen.getByLabelText(/content/i)).toHaveValue(mockItem.content);
    expect(screen.getByLabelText(/tags/i)).toHaveValue(mockItem.tags?.join(', '));
    expect(screen.getByLabelText(/type/i)).toHaveValue(mockItem.type);
    expect(screen.getByRole('button', { name: /save changes/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  });

  test('calls onUpdate with form data when saved', async () => {
    render(<DetailViewPane item={mockItem} onUpdate={mockOnUpdate} onDelete={mockOnDelete} onRefresh={mockOnRefresh} />);
    fireEvent.click(screen.getByRole('button', { name: /edit/i }));

    const titleInput = await screen.findByLabelText(/title/i);
    await userEvent.clear(titleInput);
    await userEvent.type(titleInput, 'Updated Title by Test');

    const contentInput = screen.getByLabelText(/content/i);
    await userEvent.clear(contentInput);
    await userEvent.type(contentInput, 'Updated content by test.');
    
    const tagsInput = screen.getByLabelText(/tags/i);
    // Use fireEvent.change to set the value at once, avoiding issues with controlled component + userEvent.type for comma-separated values
    fireEvent.change(tagsInput, { target: { value: 'newtag1, newtag2' } });

    // Wrap state-updating event in act
    // The handleSubmit is async, and it calls onUpdate (async) then setIsEditing (sync) and onRefresh (sync).
    // The act wrapper is important here.
    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: /save changes/i }));
      // We need to ensure promises within handleSubmit resolve before assertions.
      // mockOnUpdate already returns a resolved promise.
    });
    
    expect(mockOnUpdate).toHaveBeenCalledTimes(1);
    const expectedUpdateData: UpdateEntryData = {
      title: 'Updated Title by Test',
      url: mockItem.url, // Unchanged
      content: 'Updated content by test.',
      tags: ['newtag1', 'newtag2'],
      type: mockItem.type, // Unchanged
    };
    expect(mockOnUpdate).toHaveBeenCalledWith(mockItem.id, expectedUpdateData);
    expect(mockOnRefresh).toHaveBeenCalledTimes(1); // onRefresh should be called after update
  });

  test('calls onDelete when delete button is clicked and confirmed', async () => {
    render(<DetailViewPane item={mockItem} onUpdate={mockOnUpdate} onDelete={mockOnDelete} onRefresh={mockOnRefresh} />);
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(deleteButton);

    expect(window.confirm).toHaveBeenCalledWith(`Are you sure you want to delete "${mockItem.title}"?`);
    expect(mockOnDelete).toHaveBeenCalledTimes(1);
    expect(mockOnDelete).toHaveBeenCalledWith(mockItem.id);
  });
  
  test('does not call onDelete if confirmation is cancelled', async () => {
    (window.confirm as jest.Mock).mockReturnValueOnce(false); // Simulate user clicking "Cancel"
    render(<DetailViewPane item={mockItem} onUpdate={mockOnUpdate} onDelete={mockOnDelete} onRefresh={mockOnRefresh} />);
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(deleteButton);

    expect(window.confirm).toHaveBeenCalledTimes(1);
    expect(mockOnDelete).not.toHaveBeenCalled();
  });

  test('returns to view mode when cancel button is clicked in edit mode', async () => {
    render(<DetailViewPane item={mockItem} onUpdate={mockOnUpdate} onDelete={mockOnDelete} onRefresh={mockOnRefresh} />);
    fireEvent.click(screen.getByRole('button', { name: /edit/i })); // Enter edit mode

    expect(await screen.findByLabelText(/title/i)).toBeInTheDocument(); // Verify edit mode

    fireEvent.click(screen.getByRole('button', { name: /cancel/i })); // Click cancel

    // Verify back in view mode
    expect(screen.getByRole('heading', { name: mockItem.title, level: 2 })).toBeInTheDocument();
    expect(screen.queryByLabelText(/title/i)).not.toBeInTheDocument(); // Form field should be gone
  });

});