chrome.runtime.onMessage.addListener(
  function(request, sender, sendResponse) {
    if (request.action === "capture" || request.action === "saveContent") { // Modified to handle generic saveContent
      // Determine content and filename based on the original action or new data
      let contentToSave;
      let contentType;
      let filename;

      if (request.action === "capture") { // Legacy full page HTML capture
        chrome.scripting.executeScript({
          target: { tabId: sender.tab.id },
          function: () => {
            return document.documentElement.outerHTML;
          }
        },
        (results) => {
          if (results && results.length > 0) {
            contentToSave = results[0].result;
            contentType = "text/html";
            filename = "captured_page.html";
            saveToFile(contentToSave, contentType, filename, sendResponse);
          } else {
            sendResponse({ success: false, error: "Failed to capture HTML." });
          }
        });
        return true; // Async for executeScript
      } else if (request.action === "saveContent") {
        contentToSave = request.data;
        const metadata = request.metadata || {};
        const fileExtension = metadata.fileExtension || (request.isHTML ? 'html' : 'txt');
        
        // Determine contentType
        if (fileExtension === 'json') {
          contentType = 'application/json';
        } else if (request.isHTML) {
          contentType = 'text/html';
        } else {
          contentType = 'text/plain';
        }
        
        const safeTitle = metadata.title ? metadata.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() : 'captured_content';
        filename = `${safeTitle}.${fileExtension}`;
        
        saveToFile(contentToSave, contentType, filename, sendResponse);
        return true; // Async for download
      }
    }
  }
);

function saveToFile(content, contentType, filename, sendResponse) {
  try {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);

    chrome.downloads.download({
      url: url,
      filename: filename,
      saveAs: true // Prompts the user for save location
    }, (downloadId) => {
      // It's good practice to revoke the object URL after the download is initiated or failed.
      // Doing it here ensures it's cleaned up regardless of download success.
      URL.revokeObjectURL(url);

      if (chrome.runtime.lastError) {
        console.error("Download failed:", chrome.runtime.lastError.message);
        sendResponse({ success: false, error: `Download failed: ${chrome.runtime.lastError.message}` });
      } else if (downloadId === undefined) {
        console.warn("Download did not start or was cancelled by user immediately.");
        sendResponse({ success: false, error: "Download did not start or was cancelled." });
      } else {
        sendResponse({ success: true, downloadId: downloadId });
      }
    });
  } catch (e) {
    console.error("Error preparing content for download:", e);
    sendResponse({ success: false, error: `Error preparing download: ${e.message}` });
  }
}