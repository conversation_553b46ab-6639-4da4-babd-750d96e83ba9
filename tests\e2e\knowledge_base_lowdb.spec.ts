import { test, expect, chromium, type BrowserContext, type Page } from '@playwright/test';
import path from 'path';
import type { KnowledgeBaseEntry } from '../../packages/knowledge-base-service/src/types'; // Adjust path as needed

const pathToExtension = path.join(__dirname, '..', '..', 'apps', 'chrome-extension', 'dist');
let extensionId: string;

// Helper function to get the service worker
async function getServiceWorker(context: BrowserContext): Promise<any> {
  let sw;
  let attempts = 0;
  const maxAttempts = 15; // Increased attempts
  console.log('Attempting to find service worker...');
  while (attempts < maxAttempts) {
    attempts++;
    const serviceWorkers = context.serviceWorkers();
    if (serviceWorkers.length > 0) {
      console.log(`Attempt ${attempts}: Found ${serviceWorkers.length} service worker(s). URLs: ${serviceWorkers.map(s => s.url()).join(', ')}`);
      sw = serviceWorkers.find(s => s.url().startsWith('chrome-extension://') && s.url().endsWith('background/index.js')); // More specific
    } else {
      console.log(`Attempt ${attempts}: No service workers found.`);
    }
    
    if (sw) {
      console.log(`Service worker found: ${sw.url()}`);
      extensionId = sw.url().split('/')[2];
      console.log(`Extension ID determined: ${extensionId}`);
      return sw;
    }
    if (attempts < maxAttempts) {
      console.log(`Retrying in 1500ms... (Attempt ${attempts}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, 1500));
    }
  }
  throw new Error('Extension service worker not found after multiple attempts.');
}


test.describe('Knowledge Base LowDB E2E Tests', () => {
  let browserContext: BrowserContext;
  let serviceWorker: any;
  let page: Page; // Main page for tests

  test.beforeAll(async () => {
    browserContext = await chromium.launchPersistentContext('', {
      headless: false, // Set to true for CI
      args: [
        `--disable-extensions-except=${pathToExtension}`,
        `--load-extension=${pathToExtension}`,
      ],
    });
    serviceWorker = await getServiceWorker(browserContext);
    if (!serviceWorker) {
        throw new Error("Service worker could not be initialized for tests.");
    }
  });

  test.afterAll(async () => {
    await browserContext.close();
  });

  test.beforeEach(async () => {
    page = await browserContext.newPage();
    // Clear the LowDB database before each test via the service worker
    if (serviceWorker) {
      try {
        await serviceWorker.evaluate(async () => {
          // @ts-ignore
          if (self.kbService && typeof self.kbService.clearDatabase === 'function') {
            // @ts-ignore
            await self.kbService.clearDatabase();
            console.log('Knowledge base (lowdb) cleared via service worker.');
          } else {
            console.error('kbService or clearDatabase method not available on service worker global scope.');
          }
        });
      } catch (e) {
        console.error('Error clearing knowledge base via service worker:', e);
        // Optionally, throw error if clearing is critical for test integrity
        // throw new Error('Failed to clear knowledge base: ' + (e as Error).message);
      }
    } else {
        throw new Error("Service worker not available in beforeEach to clear database.");
    }
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('should capture a bookmark and verify its persistence in LowDB', async () => {
    const testUrl = 'https://playwright.dev/';
    const testTitle = 'Fast and reliable end-to-end testing for modern web apps | Playwright';
    
    await page.goto(testUrl);
    await page.waitForLoadState('domcontentloaded');
    expect(await page.title()).toBe(testTitle);
    expect(page.url()).toBe(testUrl);

    // Open the popup
    const popupPage = await browserContext.newPage();
    if (!extensionId) throw new Error("Extension ID not found for popup.");
    await popupPage.goto(`chrome-extension://${extensionId}/popup.html`);
    await popupPage.waitForLoadState('domcontentloaded');

    const displayedTitleLocator = popupPage.locator('#current-tab-title');
    const displayedUrlLocator = popupPage.locator('#current-tab-url');
    await expect(displayedTitleLocator).toHaveText(testTitle, { timeout: 10000 }); // Increased timeout
    await expect(displayedUrlLocator).toHaveText(testUrl, { timeout: 10000 });

    const captureButtonLocator = popupPage.locator('#capture-bookmark-button');
    await captureButtonLocator.click();

    // Wait for success message or a timeout
    const messageLocator = popupPage.locator('p:has-text("Bookmark captured successfully!")');
    await expect(messageLocator).toBeVisible({ timeout: 10000 });

    await popupPage.waitForTimeout(1000); // Give a bit more time for DB write

    // Retrieve all entries from LowDB via the service worker
    let allEntries: KnowledgeBaseEntry[] = [];
    if (serviceWorker) {
      allEntries = await serviceWorker.evaluate(async () => {
        // @ts-ignore
        if (self.kbService && typeof self.kbService.getAllEntries === 'function') {
          // @ts-ignore
          return await self.kbService.getAllEntries();
        }
        return [];
      });
    } else {
      throw new Error("Service worker not available to retrieve entries.");
    }
    
    expect(allEntries.length).toBe(1);
    const capturedEntry = allEntries[0];
    expect(capturedEntry).toBeDefined();
    expect(capturedEntry.title).toBe(testTitle);
    expect(capturedEntry.url).toBe(testUrl);
    expect(capturedEntry.type).toBe('bookmark');
    // Tags might be empty or based on suggestions, adjust as needed
    // For now, let's assume it might include suggested tags if any were visible and passed
    // If popup passes tags, this check should be more specific.
    // Default tags from background/index.ts addBookmark are an empty array if not provided.
    expect(Array.isArray(capturedEntry.tags)).toBe(true); 

    await popupPage.close();
  });

  // Test 2: Verify Display, Search, and Filter in Options Page (KnowledgeBaseView)
  test('should display entries, allow search/filter in options page', async () => {
    // Setup: Add some initial entries to LowDB
    const initialEntriesData = [
      { title: 'Playwright Test Page', url: 'https://playwright.dev/', content: 'Content about Playwright.', tags: ['testing', 'e2e'], type: 'bookmark' },
      { title: 'Another Example Site', url: 'https://example.org/', content: 'Some details about example.org.', tags: ['example', 'web'], type: 'bookmark' },
      { title: 'PKM AI Project Notes', content: 'Notes about the PKM project structure and lowdb.', tags: ['pkm', 'lowdb', 'notes'], type: 'note' },
    ];

    if (serviceWorker) {
      for (const entryData of initialEntriesData) {
        await serviceWorker.evaluate(async (data: typeof entryData) => {
          // @ts-ignore
          if (self.kbService && typeof self.kbService.createEntry === 'function') {
            // @ts-ignore
            await self.kbService.createEntry(data);
          }
        }, entryData);
      }
      console.log(`${initialEntriesData.length} initial entries added to LowDB.`);
    } else {
      throw new Error("Service worker not available to add initial entries.");
    }
    
    // Navigate to options page
    const optionsPage = await browserContext.newPage();
    if (!extensionId) throw new Error("Extension ID not found for options page.");
    await optionsPage.goto(`chrome-extension://${extensionId}/options.html`);
    await optionsPage.waitForLoadState('domcontentloaded');
    await optionsPage.waitForSelector('h1:has-text("Knowledge Base")'); // Wait for KBView to likely be ready

    // Verify initial display of all entries
    // Assuming ContentList items have a specific data-testid or class
    // For simplicity, let's count list items within the ContentList area
    // This requires ContentList.tsx to render items in a way that can be counted, e.g., <li> or <div role="listitem">
    // Let's assume each item rendered by ContentList has a class 'kb-list-item'
    // Wait for items to be potentially loaded asynchronously
    await optionsPage.waitForSelector('.kb-list-item', { timeout: 10000 });
    const displayedItems = await optionsPage.locator('.kb-list-item').count();
    expect(displayedItems).toBe(initialEntriesData.length);

    // Test search functionality
    const searchInput = optionsPage.locator('input[placeholder="Search by title or content..."]');
    
    // Search for "Playwright"
    await searchInput.fill('Playwright');
    await optionsPage.waitForTimeout(500); // Debounce/async update time
    await optionsPage.waitForSelector('.kb-list-item', { timeout: 5000 }); // wait for re-render
    let filteredItems = await optionsPage.locator('.kb-list-item').count();
    expect(filteredItems).toBe(1);
    await expect(optionsPage.locator('.kb-list-item').first()).toContainText('Playwright Test Page');

    // Search for "lowdb" (should match one note and one bookmark via tags/content)
    await searchInput.fill('lowdb');
    await optionsPage.waitForTimeout(500);
    await optionsPage.waitForSelector('.kb-list-item', { timeout: 5000 });
    filteredItems = await optionsPage.locator('.kb-list-item').count();
    expect(filteredItems).toBe(1); // PKM AI Project Notes (content or tag)
    await expect(optionsPage.locator('.kb-list-item').first()).toContainText('PKM AI Project Notes');


    // Search for a term that matches nothing
    await searchInput.fill('nonexistentsearchterm12345');
    await optionsPage.waitForTimeout(500);
    // Expect "No items match your search" message
    await expect(optionsPage.locator('p:has-text("No items match your search.")')).toBeVisible({timeout: 5000});
    filteredItems = await optionsPage.locator('.kb-list-item').count();
    expect(filteredItems).toBe(0);

    // Clear search to show all items again
    await searchInput.fill('');
    await optionsPage.waitForTimeout(500);
    await optionsPage.waitForSelector('.kb-list-item', { timeout: 5000 });
    filteredItems = await optionsPage.locator('.kb-list-item').count();
    expect(filteredItems).toBe(initialEntriesData.length);

    // Test selecting an item and viewing details
    const firstItemInList = optionsPage.locator('.kb-list-item').first();
    await firstItemInList.click();
    
    // DetailViewPane should update. Verify some details.
    // Assuming DetailViewPane has elements with identifiable text or locators
    const detailViewTitle = optionsPage.locator('.w-2/3 h2'); // A bit generic, make more specific if possible
    const detailViewContent = optionsPage.locator('.w-2/3 .prose p');

    await expect(detailViewTitle).toContainText(initialEntriesData[0].title, {timeout: 5000});
    await expect(detailViewContent).toContainText(initialEntriesData[0].content, {timeout: 5000});
    
    await optionsPage.close();
  });

});