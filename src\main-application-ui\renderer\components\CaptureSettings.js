import React, { useEffect } from 'react';
import useStore from '../store/useStore';
import './CaptureSettings.css';

const CaptureSettings = () => {
  const {
    captureSettings,
    fetchCaptureSettings,
    updateCaptureSetting,
    saveCaptureSettings,
    captureSettingsLoading,
    captureSettingsError,
    captureSettingsSaving,
    captureSettingsSaveError,
    clearCaptureSettingsStatus,
  } = useStore((state) => ({
    captureSettings: state.captureSettings,
    fetchCaptureSettings: state.fetchCaptureSettings,
    updateCaptureSetting: state.updateCaptureSetting,
    saveCaptureSettings: state.saveCaptureSettings,
    captureSettingsLoading: state.captureSettingsLoading,
    captureSettingsError: state.captureSettingsError,
    captureSettingsSaving: state.captureSettingsSaving,
    captureSettingsSaveError: state.captureSettingsSaveError,
    clearCaptureSettingsStatus: state.clearCaptureSettingsStatus,
  }));

  useEffect(() => {
    fetchCaptureSettings();
    return () => {
      // Clear any lingering error messages when the component unmounts
      clearCaptureSettingsStatus();
    };
  }, [fetchCaptureSettings, clearCaptureSettingsStatus]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    updateCaptureSetting(name, type === 'checkbox' ? checked : value);
  };

  const handleSave = () => {
    saveCaptureSettings(captureSettings);
  };

  if (captureSettingsLoading) {
    return <div className="loading-message">Loading capture settings...</div>;
  }

  if (captureSettingsError) {
    return <div className="error-message">Error loading capture settings: {captureSettingsError}</div>;
  }

  return (
    <div className="capture-settings-container section-container">
      <h2>Capture Settings</h2>
      <div className="setting-item">
        <label htmlFor="defaultFormat">Default Capture Format:</label>
        <select
          id="defaultFormat"
          name="defaultFormat"
          value={captureSettings.defaultFormat}
          onChange={handleChange}
        >
          <option value="markdown">Markdown</option>
          <option value="html">HTML</option>
          <option value="text">Plain Text</option>
        </select>
      </div>

      <div className="setting-item">
        <p>Metadata Extraction:</p>
        <label>
          <input
            type="checkbox"
            name="extractTitle"
            checked={captureSettings.extractTitle}
            onChange={handleChange}
          />
          Auto-extract Title
        </label>
        <label>
          <input
            type="checkbox"
            name="extractUrl"
            checked={captureSettings.extractUrl}
            onChange={handleChange}
          />
          Auto-extract URL
        </label>
        <label>
          <input
            type="checkbox"
            name="extractPublicationDate"
            checked={captureSettings.extractPublicationDate}
            onChange={handleChange}
          />
          Auto-extract Publication Date
        </label>
      </div>

      <div className="setting-item">
        <p>AI Assistance:</p>
        <label>
          <input
            type="checkbox"
            name="enableAiAutoTagging"
            checked={captureSettings.enableAiAutoTagging}
            onChange={handleChange}
          />
          Enable AI Auto-Tagging/Categorization
        </label>
      </div>

      <button onClick={handleSave} disabled={captureSettingsSaving} className="save-button">
        {captureSettingsSaving ? 'Saving...' : 'Save Capture Settings'}
      </button>
      {captureSettingsSaveError && (
        <div className="error-message saving-error">Error saving settings: {captureSettingsSaveError}</div>
      )}
      {!captureSettingsSaving && !captureSettingsSaveError && captureSettings.lastSaveSuccess && (
         <div className="success-message">Settings saved successfully!</div>
      )}
    </div>
  );
};

export default CaptureSettings;