# Detailed Design: Search Service (Knowledge Base Interaction & Insights Module)

## 1. Component Overview

The Search Service is a critical component within the Knowledge Base Interaction & Insights Module. Its primary function is to enable users to efficiently find relevant information stored within the knowledge base. It supports both traditional keyword-based search for quick lookups and advanced natural language semantic search for more nuanced and context-aware queries. The service interacts with the Knowledge Base Abstraction Layer (KBAL) to retrieve content and relies on an internal indexing mechanism to ensure fast and accurate search results.

## 2. Key Responsibilities and Features

The Search Service has the following key responsibilities and features:

*   **Keyword Search:**
    *   Allows users to search for exact words or phrases within the knowledge base.
    *   Supports basic search operators (e.g., AND, OR, NOT - to be confirmed based on implementation complexity).
    *   Provides fast results for simple queries.
    *   Crucial for offline access to saved content (supports Test Case 9).
*   **Semantic Search:**
    *   Enables users to search using natural language questions or descriptive phrases.
    *   Understands the intent and context behind user queries, going beyond literal keyword matching.
    *   Utilizes vector embeddings to find semantically similar content.
    *   Directly supports Test Case 4 (Natural Language Search).
*   **Indexing:**
    *   Maintains an up-to-date index of the knowledge base content to facilitate fast search operations.
    *   Supports indexing for both keyword and semantic search (e.g., inverted index for keywords, vector index for semantic).
    *   Handles incremental updates to the index as new content is added or existing content is modified/deleted.

## 3. API Definition

The Search Service will expose the following primary API functions. (Note: This is a conceptual API; actual implementation might vary based on the chosen framework/language and communication protocols, e.g., REST, gRPC, or direct function calls within a monolithic application).

### Functions:

*   `search(query: SearchQuery): Promise<SearchResults>`
    *   **Description:** Performs a search based on the provided query object. The type of search (keyword or semantic) will be determined by the `SearchQuery` object or an internal decision logic based on query characteristics.
    *   **Parameters:**
        *   `query`: `SearchQuery` - An object containing details of the search request.
    *   **Returns:** `Promise<SearchResults>` - A promise that resolves to an object containing the search results.
    *   **AI Verifiable Outcome:** The function returns results that are demonstrably relevant to the input query, verifiable through automated tests comparing query-result pairs.

*   `updateIndex(contentId: string, content: string, operation: 'add' | 'update' | 'delete'): Promise<void>`
    *   **Description:** Adds, updates, or removes content from the search index. This would typically be called by the KBAL when content changes.
    *   **Parameters:**
        *   `contentId`: `string` - Unique identifier for the content.
        *   `content`: `string` (optional for 'delete') - The text content to be indexed or updated.
        *   `operation`: `'add' | 'update' | 'delete'` - The operation to perform.
    *   **Returns:** `Promise<void>` - A promise that resolves when the indexing operation is complete.
    *   **AI Verifiable Outcome:** Subsequent searches reflect the changes made by this function. For 'add'/'update', the content becomes searchable. For 'delete', it is no longer found.

*   `rebuildIndex(): Promise<void>`
    *   **Description:** Triggers a full rebuild of the search index. This might be necessary for maintenance or after significant data changes.
    *   **Returns:** `Promise<void>` - A promise that resolves when the index rebuild is complete.
    *   **AI Verifiable Outcome:** The index accurately reflects the current state of all content in the KBAL after the rebuild.

### Result Formatting:

*   Search results will include:
    *   A list of relevant content items.
    *   For each item:
        *   Content ID
        *   Title or snippet
        *   Relevance score (especially for semantic search)
        *   Link or reference to the full content in the KBAL
        *   Optionally, highlighted keywords or relevant excerpts.

## 4. Data Structures

Key data structures used by the Search Service:

*   **`SearchQuery`**:
    ```typescript
    interface SearchQuery {
      queryText: string;          // The raw search string from the user
      searchType?: 'keyword' | 'semantic' | 'auto'; // Optional: specify search type, or 'auto' to let the service decide
      filters?: Record<string, any>; // e.g., { category: 'work', tags: ['urgent'] }
      pagination?: {
        page: number;
        pageSize: number;
      };
      userId?: string; // For personalization or access control if needed in the future
    }
    ```

*   **`SearchResultItem`**:
    ```typescript
    interface SearchResultItem {
      id: string;                 // Unique ID of the knowledge base item
      title: string;              // Title of the item
      snippet: string;            // A short relevant excerpt from the item
      relevanceScore: number;     // A score indicating how relevant the item is to the query (0.0 to 1.0)
      sourceLink: string;         // A link or identifier to retrieve the full item from KBAL
      metadata?: Record<string, any>; // e.g., creation date, tags
    }
    ```

*   **`SearchResults`**:
    ```typescript
    interface SearchResults {
      items: SearchResultItem[];
      totalHits: number;
      currentPage: number;
      totalPages: number;
      queryInterpretation?: string; // Optional: How the system interpreted a natural language query
    }
    ```

*   **Index Data (Conceptual):**
    *   **Keyword Index (e.g., Inverted Index):**
        *   `Map<string, { docId: string, frequency: number, positions: number[] }[]>`
        *   Maps terms to a list of documents containing the term, along with frequency and positional information.
    *   **Semantic Index (e.g., Vector Index):**
        *   `Map<string, number[]>` (Document ID to Vector mapping)
        *   A collection of vector embeddings, where each vector represents a piece of content (e.g., a document, a paragraph, or a sentence).
        *   The actual storage might be a specialized vector database or a file format suitable for libraries like FAISS or Annoy.

## 5. Interaction with other components

*   **UI Layer (via Query Understanding Engine - QUE):**
    *   The Search Service receives processed search queries from the QUE.
    *   The QUE might perform initial parsing, intent recognition, or entity extraction before forwarding the query.
    *   The Search Service returns formatted search results to the QUE, which then passes them to the UI Layer for display.
*   **Query Understanding Engine (QUE):**
    *   The QUE acts as an intermediary. It might enrich the raw user query with additional context or structure it into the `SearchQuery` format expected by the Search Service.
    *   For natural language queries, QUE might identify if a semantic search is more appropriate.
*   **Knowledge Base Abstraction Layer (KBAL):**
    *   The Search Service interacts with the KBAL to retrieve the actual content of documents identified as relevant by the search algorithms (based on their IDs).
    *   The KBAL notifies the Search Service (likely via its `updateIndex` API) when content is created, updated, or deleted, so the search index can be kept consistent.

## 6. Search Algorithms

*   **Keyword Search:**
    *   **Approach:** Standard inverted index lookup.
    *   **Process:**
        1.  Tokenize the query text.
        2.  Remove stop words (common words like "the", "is", "in").
        3.  Perform stemming or lemmatization to reduce words to their root form.
        4.  Look up processed terms in the inverted index to find matching document IDs.
        5.  Rank results based on relevance (e.g., TF-IDF, BM25).
    *   **Technology:** Can be implemented using libraries like Lunr.js (for client-side/Node.js) or custom logic for simpler cases.
*   **Semantic Search:**
    *   **Approach:** Vector similarity search.
    *   **Process:**
        1.  **Query Embedding:** Convert the natural language user query into a vector embedding using a pre-trained language model (e.g., Sentence-BERT, Universal Sentence Encoder, or a model accessible via an API like OpenAI).
        2.  **Similarity Search:** Compare this query vector against the pre-computed vectors of all indexed content items using a similarity metric (e.g., cosine similarity).
        3.  **Ranking:** Rank results based on their similarity scores.
    *   **Technology:** Requires a library for generating embeddings (e.g., `transformers.js`, `sentence-transformers` if a Python backend is used, or API calls) and a library for efficient nearest neighbor search in vector space (e.g., FAISS, Annoy, or built-in capabilities of vector databases). For local models, resource management (memory, CPU/GPU) is a key consideration.

## 7. Indexing Strategy

*   **Types of Indexes:**
    *   **Keyword Index:** An inverted index will be maintained for fast keyword lookups.
    *   **Semantic Index:** A vector index will store embeddings of content chunks (e.g., paragraphs or documents).
*   **Index Granularity:**
    *   For keyword search, indexing at the document level is standard.
    *   For semantic search, indexing smaller chunks (e.g., paragraphs or even sentences) might yield more precise results, but increases index size and complexity. A balance needs to be struck. Initially, document-level embeddings will be considered, with potential for finer granularity later.
*   **Index Updates:**
    *   **Real-time/Near Real-time:** When content is added, updated, or deleted in the KBAL, the KBAL will trigger an update to the Search Service's indexes.
    *   For keyword index, updates are generally fast.
    *   For semantic index, re-calculating embeddings and updating the vector index can be more resource-intensive. A queueing mechanism might be used for batching updates if performance becomes an issue.
*   **Index Storage:**
    *   Keyword index can be stored in memory (for smaller datasets) or on disk (e.g., as JSON files or using a lightweight embedded database).
    *   Vector index might be stored using specialized libraries (FAISS saves to a file, vector databases manage their own storage).
*   **Offline Considerations:**
    *   The keyword index and the necessary logic for keyword search must be available offline to support Test Case 9. This implies local storage of the index.
    *   Semantic search, especially if relying on large models or external APIs for embeddings, might be limited or unavailable offline. If a local model is used, its size and resource requirements will determine offline feasibility. The initial design will assume semantic search requires online connectivity or a sufficiently powerful local device with the model downloaded.

## 8. Error Handling and Edge Cases

*   **No Results Found:**
    *   Return an empty `SearchResults` object with `totalHits: 0`.
    *   Provide a user-friendly message (handled by UI, but Search Service should signal this state).
    *   Potentially offer suggestions for query modification (e.g., "Try different keywords," "Rephrase your question").
*   **Invalid Queries:**
    *   Malformed `SearchQuery` object (e.g., missing `queryText`).
    *   Unsupported characters or operators in keyword search.
    *   The service should return an appropriate error response (e.g., HTTP 400 Bad Request if it's an HTTP API) with a descriptive error message.
*   **Indexing Failures:**
    *   Failure to generate embeddings (e.g., model unavailable, API error).
    *   Failure to write to the index (e.g., disk full, permission issues).
    *   Log errors comprehensively. Implement retry mechanisms for transient failures. For persistent failures, a notification system might be needed for administrative attention.
*   **Large Result Sets:**
    *   Implement pagination (`SearchQuery.pagination`) to handle large numbers of results efficiently, preventing overwhelming the client or the service.
*   **Resource Limitations (Semantic Search):**
    *   If using local models for embeddings: handle potential out-of-memory errors or slow performance on low-spec devices. Provide graceful degradation (e.g., fall back to keyword search or notify the user).
*   **KBAL Unavailability:**
    *   If the KBAL is temporarily unavailable, the Search Service might not be able to retrieve full content for search results or update its index. It should handle this gracefully, possibly returning cached results if available or indicating an error.

## 9. AI Verifiable Outcomes

The design of the Search Service supports AI verifiable outcomes in several ways:

*   **Testable API:** The clearly defined API functions (`search`, `updateIndex`) can be invoked by automated test scripts.
*   **Deterministic Keyword Search:** For a given keyword query and index state, the results should be deterministic and predictable, allowing for exact match assertions in tests.
*   **Semantic Relevance Testing:** While exact matches are harder for semantic search, test cases can be designed with query-result pairs where human evaluators (or a "golden" model) have established relevance. Automated tests can then check if the semantic search returns these expected relevant documents within the top N results and with a relevance score above a certain threshold. (Supports Test Case 4).
*   **Index Consistency:** After `updateIndex` or `rebuildIndex` operations, tests can verify that:
    *   Newly added content is findable.
    *   Updated content reflects changes in search results.
    *   Deleted content is no longer findable.
*   **Offline Functionality (Keyword Search):** Tests can simulate offline conditions and verify that keyword search still functions correctly using the local index. (Supports Test Case 9).
*   **Performance Metrics:** Automated tests can measure query response times and index update times to ensure they meet performance targets.

## 10. Self-Reflection

*   **Quality:**
    *   The design emphasizes a separation of concerns (keyword vs. semantic, search vs. indexing).
    *   The API is designed to be clear and extensible.
    *   The inclusion of both keyword and semantic search provides a robust and versatile user experience.
    *   Modularity allows for different implementations of indexing or embedding generation to be swapped in/out.
*   **Security:**
    *   **Input Sanitization:** Queries received from the QUE must be sanitized to prevent injection attacks if any part of the query is used to construct database queries or file paths (less likely with inverted/vector indexes but good practice).
    *   **Access Control:** If the knowledge base has access-controlled content, the Search Service must integrate with the KBAL or an authorization service to ensure users only see results they are permitted to access. The `userId` in `SearchQuery` can facilitate this.
    *   **Data at Rest:** If sensitive information is indexed, the indexes themselves (especially if stored on disk) should be encrypted.
    *   **Dependency Security:** Libraries used for embeddings, vector search, or keyword indexing should be vetted for security vulnerabilities.
*   **Performance:**
    *   **Keyword Search:** Expected to be fast (sub-second response times for typical datasets) due to efficient inverted index structures. Index size will grow linearly with the number of unique terms and documents.
    *   **Semantic Search:**
        *   **Query Embedding:** Time taken depends on the model size and hardware. Local models on CPU can take hundreds of milliseconds to a few seconds per query. API calls add network latency.
        *   **Vector Search:** Efficient with libraries like FAISS/Annoy, even for millions of vectors (typically tens to hundreds of milliseconds).
        *   **Overall Response Time (Semantic):** Could range from ~200ms (optimized local, small model, GPU) to several seconds (large model, CPU, large dataset). Target < 1-2 seconds for a good user experience.
        *   **Indexing Time (Semantic):** Generating embeddings for the entire dataset can be time-consuming initially. Incremental updates for new/changed documents are more manageable.
        *   **Index Size (Semantic):** Vector embeddings can be large. E.g., a 768-dimension float32 vector is 3KB. 10,000 documents would be ~30MB, 1,000,000 documents ~3GB, just for the vectors.
    *   **Mitigation:** Caching popular queries/results, optimizing embedding models (quantization), using efficient vector index parameters.
*   **Maintainability:**
    *   The component is well-defined with clear responsibilities.
    *   Separation of keyword and semantic search logic allows for independent development and maintenance.
    *   The `updateIndex` API provides a clear contract for keeping the index synchronized.
    *   Comprehensive logging for errors and operations will be crucial.
    *   Configuration for models, index paths, etc., should be externalized.
*   **Alignment with Acceptance Tests:**
    *   **Test Case 4 (Natural Language Search):** Directly addressed by the Semantic Search feature, including query embedding and vector similarity search. The AI verifiable outcomes section details how this can be tested.
    *   **Test Case 9 (Offline Access to Saved Content):** Directly addressed by the Keyword Search feature and the requirement for its index to be available offline. The indexing strategy and AI verifiable outcomes reflect this.
    *   The design provides the necessary mechanisms (API, indexing, search algorithms) to support these test cases.

This detailed design provides a solid foundation for implementing the Search Service. Further refinements will occur during the implementation phase, especially concerning the choice of specific libraries and performance tuning.