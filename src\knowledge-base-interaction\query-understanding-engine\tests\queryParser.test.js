// AI-VERIFIABLE: Placeholder test file for QueryParser.
// These tests will verify the functionality of the query parsing logic.

import QueryParser from '../core/queryParser';

describe('QueryParser - Unit Tests', () => {
    let parser;

    beforeEach(() => {
        parser = new QueryParser();
    });

    test('AI-VERIFIABLE: should parse a simple query, remove stopwords, and extract keywords', () => {
        const rawQuery = "This is a test query about knowledge bases.";
        const expectedCleanedQuery = "this is a test query about knowledge bases";
        const expectedTokens = ["this", "is", "a", "test", "query", "about", "knowledge", "bases"];
        const expectedKeywords = ["test", "query", "knowledge", "bases"]; // "this", "is", "a", "about" are stopwords

        const result = parser.parse(rawQuery);

        expect(result.original).toBe(rawQuery);
        expect(result.cleanedQuery).toBe(expectedCleanedQuery);
        expect(result.tokens).toEqual(expectedTokens);
        expect(result.keywords).toEqual(expectedKeywords);
    });

    test('AI-VERIFIABLE: should handle queries with mixed case and punctuation', () => {
        const rawQuery = "Search For Documents, e.g. PDFs or DOCs!";
        const expectedCleanedQuery = "search for documents e g pdfs or docs";
        const expectedTokens = ["search", "for", "documents", "e", "g", "pdfs", "or", "docs"];
        // "for", "or" are stopwords. "e", "g" are not stopwords and are single letters.
        const expectedKeywords = ["search", "documents", "e", "g", "pdfs", "docs"];


        const result = parser.parse(rawQuery);
        expect(result.cleanedQuery).toBe(expectedCleanedQuery);
        expect(result.tokens).toEqual(expectedTokens);
        expect(result.keywords).toEqual(expectedKeywords);
    });

    test('AI-VERIFIABLE: should handle queries with extra whitespace and leading/trailing punctuation', () => {
        const rawQuery = "  ...leading and trailing spaces!!  ";
        const expectedCleanedQuery = "leading and trailing spaces";
        const expectedTokens = ["leading", "and", "trailing", "spaces"];
        const expectedKeywords = ["leading", "trailing", "spaces"]; // "and" is a stopword

        const result = parser.parse(rawQuery);
        expect(result.cleanedQuery).toBe(expectedCleanedQuery);
        expect(result.tokens).toEqual(expectedTokens);
        expect(result.keywords).toEqual(expectedKeywords);
    });

    test('AI-VERIFIABLE: should throw an error for an empty or whitespace-only query', () => {
        expect(() => parser.parse('')).toThrow('Raw query must be a non-empty string for parsing.');
        expect(() => parser.parse('   ')).toThrow('Raw query must be a non-empty string for parsing.');
        expect(() => parser.parse('  \t\n ')).toThrow('Raw query must be a non-empty string for parsing.');
    });

    test('AI-VERIFIABLE: should return an empty array for keywords if all tokens are stopwords or query is only punctuation', () => {
        const rawQuery = "Is it of the?";
        const result = parser.parse(rawQuery);
        expect(result.keywords).toEqual([]);
        expect(result.tokens).toEqual(["is", "it", "of", "the"]);

        const rawQueryPunct = "!!! ??? ...";
        const resultPunct = parser.parse(rawQueryPunct);
        expect(resultPunct.keywords).toEqual([]);
        expect(resultPunct.tokens).toEqual([]);
        expect(resultPunct.cleanedQuery).toEqual("");
    });

    test('AI-VERIFIABLE: should handle queries with numbers', () => {
        const rawQuery = "Find 10 documents from 2023";
        const expectedCleanedQuery = "find 10 documents from 2023";
        const expectedTokens = ["find", "10", "documents", "from", "2023"];
        const expectedKeywords = ["find", "10", "documents", "2023"]; // "from" is a stopword

        const result = parser.parse(rawQuery);
        expect(result.cleanedQuery).toBe(expectedCleanedQuery);
        expect(result.tokens).toEqual(expectedTokens);
        expect(result.keywords).toEqual(expectedKeywords);
    });

    test('AI-VERIFIABLE: should handle hyphenated words as single tokens', () => {
        // Current implementation replaces hyphens with spaces.
        // If hyphenated words should be single tokens, the regex in parse() needs adjustment.
        // For now, testing current behavior:
        const rawQuery = "State-of-the-art technology";
        // Current regex: cleanedQuery = cleanedQuery.replace(/[.,?!;:()[\]{}]/g, ' ');
        // Hyphens are NOT in this set, so they are preserved.
        // Then, cleanedQuery = cleanedQuery.replace(/['"`]/g, '');
        // Then, split by space.
        const expectedCleanedQuery = "state-of-the-art technology";
        const expectedTokens = ["state-of-the-art", "technology"];
        const expectedKeywords = ["state-of-the-art", "technology"];

        const result = parser.parse(rawQuery);
        expect(result.cleanedQuery).toBe(expectedCleanedQuery);
        expect(result.tokens).toEqual(expectedTokens);
        expect(result.keywords).toEqual(expectedKeywords);
    });

     test('AI-VERIFIABLE: should handle queries that become empty after cleaning', () => {
        const rawQuery = "... --- ???";
        const result = parser.parse(rawQuery);
        expect(result.cleanedQuery).toBe("---"); // Hyphens are not removed by current punctuation regex
        expect(result.tokens).toEqual(["---"]); // Tokenizer splits by space
        expect(result.keywords).toEqual(["---"]); // "---" is not a stopword
    });

    test('AI-VERIFIABLE: should correctly identify keywords when no stopwords are present', () => {
        const rawQuery = "complex algorithm analysis";
        const expectedCleanedQuery = "complex algorithm analysis";
        const expectedTokens = ["complex", "algorithm", "analysis"];
        const expectedKeywords = ["complex", "algorithm", "analysis"];

        const result = parser.parse(rawQuery);
        expect(result.cleanedQuery).toBe(expectedCleanedQuery);
        expect(result.tokens).toEqual(expectedTokens);
        expect(result.keywords).toEqual(expectedKeywords);
    });

    // Add more tests for different query structures, punctuation,
    // special characters, and edge cases as the parser evolves.
    // Consider tests for different languages if that becomes a requirement.
});

// AI-VERIFIABLE: End of queryParser.test.js