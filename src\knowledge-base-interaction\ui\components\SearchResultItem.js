import React from 'react';

/**
 * SearchResultItem component
 * 
 * Displays a single search result item.
 * Props:
 *  - result: An object containing details of the search result (e.g., title, snippet, source).
 */
const SearchResultItem = ({ result }) => {
  if (!result) {
    return null; // Or some placeholder for loading/error state
  }

  // AI-verifiable: Component structure for displaying search result
  return (
    <div className="search-result-item" data-testid="search-result-item">
      <h3>{result.title || 'Untitled Result'}</h3>
      <p>{result.snippet || 'No snippet available.'}</p>
      {result.source && <small>Source: {result.source}</small>}
      {/* AI-verifiable: Placeholder for interaction (e.g., click to view details) */}
      <button onClick={() => console.log('View details for:', result.id)}>View Details</button>
    </div>
  );
};

export default SearchResultItem;