# Query Understanding Engine

## Overview

The Query Understanding Engine (QUE) is a core component of the Knowledge Base Interaction & Insights Module. Its primary responsibility is to process natural language user input to discern intent, extract relevant information, and prepare the query for further processing by other services.

This engine is designed to handle various types of user requests, including but not limited to:
-   Keyword-based searches
-   Natural language questions (Q&A)
-   Requests for content transformation
-   Queries aimed at establishing conceptual links between information entities

The QUE does **not** handle query understanding aspects specifically tied to Content Summarization, as that is managed by a dedicated component within the Content Summarization feature.

## Architecture

The engine is composed of several key sub-modules:

-   **Core Engine (`core/`)**: Contains the main orchestration logic for query processing.
    -   `queryUnderstandingEngine.js`: The central class that coordinates the query understanding process.
    -   `queryParser.js`: Responsible for initial parsing of the raw query string.
-   **Intent Recognition (`intent-recognition/`)**: Identifies the user's goal or the type of action they wish to perform (e.g., search, ask a question, link concepts).
    -   `intentRecognizer.js`: Implements the logic for intent classification.
-   **Entity Extraction (`entity-extraction/`)**: Extracts key pieces of information from the query, such as keywords, named entities (people, places, organizations), dates, and other relevant parameters.
    -   `entityExtractor.js`: Implements the logic for identifying and extracting entities.
-   **Request Routing (`request-routing/`)**: Determines the appropriate downstream service (e.g., Search Service, AI Services Gateway) to handle the processed query based on the recognized intent and extracted entities.
    -   `requestRouter.js`: Implements the logic for routing decisions.

## AI Verifiability

This directory and its initial files serve as the foundational boilerplate for the Query Understanding Engine. AI verification can be performed by checking:
-   The existence of this `README.md` file.
-   The presence of the core subdirectories: `core/`, `intent-recognition/`, `entity-extraction/`, `request-routing/`, and `tests/`.
-   The existence of placeholder JavaScript files within these directories as outlined above.

## Development

Development of this engine should follow Test-Driven Development (TDD) principles. Placeholder test files are provided in the `tests/` directory.

---
*AI-VERIFIABLE: Main README.md for Query Understanding Engine created.*