// Augmenting existing @types/chrome definitions

declare namespace chrome {
  namespace runtime {
    type ContextType =
      | 'TAB'
      | 'POPUP'
      | 'BACKGROUND'
      | 'OFFSCREEN_DOCUMENT'
      | 'SIDE_PANEL';

    interface ContextFilter {
      contextTypes?: ContextType[];
      documentIds?: string[];
      documentOrigins?: string[];
      documentUrls?: string[];
      tabIds?: number[];
      windowIds?: number[];
    }

    interface ExtensionContext {
      contextId?: string;
      contextType: ContextType;
      documentId?: string;
      documentOrigin?: string;
      documentUrl?: string;
      frameId?: number;
      incognito: boolean;
      tabId?: number;
      windowId?: number;
    }

    function getContexts(
      filter: ContextFilter,
      callback?: (contexts: chrome.runtime.ExtensionContext[]) => void
    ): Promise<chrome.runtime.ExtensionContext[]>;
  }

  namespace offscreen {
    // Using string literal union type for Reason due to isolatedModules
    type Reason =
      | "AUDIO_PLAYBACK"
      | "IFRAME_SCRIPTING"
      | "DOM_PARSER"
      | "BLOOM_FILTER"
      | "USER_MEDIA"
      | "DISPLAY_MEDIA"
      | "WEB_RTC"
      | "CLIPBOARD"
      | "DOM_SCRAPING"
      | "LOCAL_STORAGE"
      | "WORKERS"
      | "TESTING";
  }
}