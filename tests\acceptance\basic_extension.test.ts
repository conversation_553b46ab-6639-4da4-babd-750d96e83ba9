import { test, expect, <PERSON><PERSON><PERSON>, <PERSON>, chromium, BrowserContext } from '@playwright/test';
import path from 'path';

test('basic extension content script check', async () => {
  // Launch a persistent browser context with the extension loaded.
  // Use a temporary directory for user data.
  const pathToExtension = path.join(__dirname, '..', '..', 'dist'); // Adjust path as needed
  const context: BrowserContext = await chromium.launchPersistentContext('', {
    args: [
      `--disable-extensions-except=${pathToExtension}`,
      `--load-extension=${pathToExtension}`,
    ],
  });

  // Create a new page (tab)
  const page = await context.newPage();

  // Navigate to a simple page where the content script should run
  await page.goto('https://example.com/');

  // Check for a sign that the content script has run.
  // Assuming the content script adds a class 'extension-loaded' to the body.
  // This selector and expected class might need adjustment based on the actual boilerplate.
  const bodyClass = await page.locator('body').getAttribute('class');
  expect(bodyClass).toContain('extension-loaded'); // Verify the class is present

  console.log(`Body class after navigation: ${bodyClass}`);

  // Close the context after the test
  await context.close();
});