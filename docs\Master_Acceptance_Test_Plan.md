# Master Acceptance Test Plan

**Version:** 1.1
**Date:** May 19, 2025 (GitHub Template Integration Update)
**Related Documents:**
*   Master Project Plan: [`docs/Master_Project_Plan.md`](docs/Master_Project_Plan.md)
*   GitHub Template Research Report: [`docs/research/github_template_research_report.md`](docs/research/github_template_research_report.md)
*   Template Integration Guide: [`docs/template_integration_guide.md`](docs/template_integration_guide.md)

## 1. Introduction

This document outlines the master acceptance test plan for the Personalized AI Knowledge Companion & PKM Web Clipper project. It has been updated to reflect the integration of Jonghakseo's `chrome-extension-react-ts-boilerplate` GitHub template and the project's adoption of Playwright for E2E testing.

## 2. Scope

The acceptance tests will cover web content capture, intelligent organization, knowledge base interaction, and management/configuration functionalities as defined in the Master Project Plan. Testing will focus on end-to-end user flows within the browser extension environment.

## 3. Testing Approach

The high-level acceptance tests will primarily use **Playwright** for end-to-end testing in a real browser environment. This aligns with the capabilities of the chosen GitHub template and the project's need to accurately simulate browser API interactions (e.g., `chrome.runtime.lastError`, `chrome.storage.local`) and user workflows.

*   **E2E Testing (Playwright):** All critical user flows and high-level acceptance criteria will be validated using Playwright tests. These tests will interact with the browser extension's UI (popup, options pages, content scripts) and verify backend logic through observable UI changes or data persistence.
*   **Unit/Integration Tests (Jest/RTL):** While Playwright covers E2E, Jest and React Testing Library (RTL) will continue to be used for unit and integration testing of individual components and services, particularly within the `packages/` directory of the monorepo or for UI components in `apps/chrome-extension`.

All tests will be designed to be automatically executed and their results verifiable by an AI system (e.g., checking for successful test suite execution and specific output patterns).

## 4. High-Level Test Cases

These test cases will be implemented as Playwright tests.

### 4.1. Web Content Capture (Adapting to Template Structure)

*   **Test Case 1.1: Capture and Store Web Content (via Popup & `lowdb`)**
    *   **Description:** Verify that the system can capture web content (e.g., URL, title, selection) via the extension popup and store it locally using the `lowdb` service.
    *   **Completion Criterion:** The captured content is successfully written to the `lowdb` database (`db.json`). The stored data accurately reflects the captured information and user selections made in the popup UI. A Playwright test simulates user interaction with the popup, triggers capture, and verifies data persistence by checking `lowdb` (or a mock interface if direct file access is complex in test).

*   **Test Case 1.2: Popup Initialization Error Handling (`chrome.runtime.lastError`)**
    *   **Description:** Verify that the browser extension popup correctly handles `chrome.runtime.lastError` during its initialization phase when communicating with the background script.
    *   **Completion Criterion:** A Playwright test simulates a scenario where `chrome.runtime.lastError` is set (e.g., by mocking a failed `chrome.runtime.sendMessage` from the background script during popup load). The popup UI displays an appropriate error message or enters a graceful fallback state. No unhandled exceptions occur.

### 4.2. Intelligent Organization (Integrated with Capture Flow)

*   **Test Case 2.1: Automatic Tagging and Categorization during Capture**
    *   **Description:** Verify that the system can automatically suggest tags and categories (mocked or via AI service) during the content capture process and save them with the content.
    *   **Completion Criterion:** During a Playwright-driven capture flow, AI-suggested tags/categories are displayed in the UI. User can accept/modify them. The chosen tags/categories are saved correctly with the content item in `lowdb`.

### 4.3. Knowledge Base Interaction & Insights (UI within Template)

*   **Test Case 3.1: Browse and View Saved Content (from `lowdb`)**
    *   **Description:** Verify that the user can browse and view all saved content (stored in `lowdb`) in a unified interface (e.g., an options page or dedicated view within the extension).
    *   **Completion Criterion:** A Playwright test navigates to the knowledge base view. The system displays a list of content items fetched from `lowdb`. User can select an item, and its full content is displayed. `react-window` virtualization for lists is functional.

*   **Test Case 4.1: Natural Language Search (on `lowdb` Content)**
    *   **Description:** Verify the system can perform semantic search (or keyword search initially) using natural language queries on content stored in `lowdb`.
    *   **Completion Criterion:** User enters a query via a Playwright test. System returns relevant items from `lowdb`.

*   **Test Case 5.1: AI Q&A on Selected Content (from `lowdb`)**
    *   **Description:** Verify the system can answer questions based *only* on the content of selected items from `lowdb`.
    *   **Completion Criterion:** Playwright test selects item(s), asks a question. System provides an answer synthesized from selected `lowdb` content.

*   **Test Case 6.1: AI Summarization of Selected Content (from `lowdb`)**
    *   **Description:** Verify the system can generate summaries of selected items from `lowdb`.
    *   **Completion Criterion:** Playwright test selects item(s), requests summary. System provides a summary of selected `lowdb` content.

*   **Test Case 7.1: AI Content Transformation (on `lowdb` Content)**
    *   **Description:** Verify the system can transform content of selected items from `lowdb`.
    *   **Completion Criterion:** Playwright test selects item, requests transformation. System provides transformed content from `lowdb`.

*   **Test Case 8.1: AI Suggested Conceptual Links (between `lowdb` Items)**
    *   **Description:** Verify the system suggests meaningful conceptual links between items stored in `lowdb`.
    *   **Completion Criterion:** System suggests links between `lowdb` items. Playwright test verifies display of links.

*   **Test Case 9.1: Offline Access to Saved Content (from `lowdb`)**
    *   **Description:** Verify that browsing and searching content already saved in `lowdb` works without an internet connection.
    *   **Completion Criterion:** Playwright test simulates offline mode. User can still browse and search `lowdb` content.

### 4.4. Management & Configuration (Using Template's Storage/UI)

*   **Test Case 10.1: Configure Capture Settings (via `chrome.storage.local` or `lowdb`)**
    *   **Description:** Verify users can configure capture settings (e.g., default format, auto-tagging preferences) via an options page, and these settings persist.
    *   **Completion Criterion:** Playwright test navigates to options page, changes settings. Settings are saved (e.g., in `chrome.storage.local` or `lowdb` as per implementation) and correctly applied during subsequent captures.

### 4.5. Detailed End-to-End Scenarios

For a more granular implementation and detailed scenarios covering comprehensive user workflows across multiple modules, refer to the E2E Test Scenario Overview:
*   [`docs/testplans/E2E_Test_Scenario_Overview.md`](docs/testplans/E2E_Test_Scenario_Overview.md) (To be reviewed and updated for Playwright and template structure)

This document provides specific test cases that build upon and verify the high-level acceptance criteria outlined above through concrete end-to-end user journeys, now to be implemented with Playwright.

## 5. AI Verifiability

Each Playwright test case will be designed with clear success/failure criteria that can be automatically checked:
*   Successful completion of the Playwright test script without errors.
*   Presence or absence of specific UI elements.
*   Correctness of data displayed in the UI.
*   Verification of data persisted in `lowdb` or `chrome.storage.local` (either by direct inspection if feasible in the test environment, or by observing subsequent UI behavior that depends on that data).

## 6. Test Environment

*   **Framework:** Playwright
*   **Browsers:** Latest stable Chrome (or as specified by the template compatibility)
*   **Extension Build:** Production-like build of the extension, loaded into the browser by Playwright.
*   **Data:** Test-specific datasets for `lowdb`, or a clean state for each test run.

## 7. Test Deliverables

*   This Master Acceptance Test Plan document.
*   Playwright test scripts located in a designated test directory (e.g., `apps/chrome-extension/tests/e2e/` or `tests/acceptance/` as per project structure).
*   Test execution reports generated by Playwright.

## 8. Conclusion

This Master Acceptance Test Plan, updated for Playwright and template integration, provides a framework for verifying the quality and functionality of the Personalized AI Knowledge Companion & PKM Web Clipper project through robust end-to-end testing.