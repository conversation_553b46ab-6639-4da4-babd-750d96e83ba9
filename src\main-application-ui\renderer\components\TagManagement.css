.tag-management-section {
  /* Inherits styles from .settings-section */
}

.tag-form {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.tag-form input[type="text"] {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
}

.tag-form button {
  padding: 10px 15px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.2s ease;
}

.tag-form button:hover {
  background-color: #218838;
}

.tag-form button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.tag-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.tag-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 10px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.tag-list-item:last-child {
  border-bottom: none;
}

.tag-name {
  font-size: 1.05em;
  color: #333;
}

.tag-actions button {
  margin-left: 10px;
  padding: 6px 12px;
  font-size: 0.9em;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid transparent;
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.tag-actions button:hover {
  opacity: 0.8;
}

.tag-actions button.delete-button {
  background-color: #dc3545;
  color: white;
}

.tag-actions button.delete-button:hover {
  background-color: #c82333;
}

.tag-actions button:disabled {
  background-color: #e9ecef;
  color: #6c757d;
  border-color: #ced4da;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

/* Edit Tag Modal Styles */
.edit-tag-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* Ensure it's above other content */
}

.edit-tag-modal {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 400px;
}

.edit-tag-modal h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.5em;
  color: #333;
}

.edit-tag-modal input[type="text"] {
  width: calc(100% - 22px); /* Account for padding and border */
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
}

.edit-tag-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.edit-tag-actions button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
}

.edit-tag-actions button[type="submit"] {
  background-color: #007bff;
  color: white;
}
.edit-tag-actions button[type="submit"]:hover {
  background-color: #0056b3;
}

.edit-tag-actions button[type="button"] {
  background-color: #6c757d;
  color: white;
}
.edit-tag-actions button[type="button"]:hover {
  background-color: #5a6268;
}