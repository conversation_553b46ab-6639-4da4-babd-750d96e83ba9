/* src/main-application-ui/renderer/components/CaptureSettings.css */

.capture-settings-container {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px; /* Add margin to separate from other settings sections */
}

.capture-settings-container h2 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.setting-item {
  margin-bottom: 20px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: bold;
}

.setting-item select,
.setting-item input[type="checkbox"] {
  margin-right: 8px;
}

.setting-item select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box; /* Ensures padding doesn't affect overall width */
  font-size: 1em;
}

.setting-item p { /* For grouping checkboxes under a title */
  font-weight: bold;
  color: #555;
  margin-bottom: 10px;
}

.setting-item label input[type="checkbox"] {
  margin-right: 5px;
  vertical-align: middle;
}

.setting-item label { /* For checkbox labels to be inline and have spacing */
  display: inline-block; /* Changed from block to inline-block */
  margin-right: 15px; /* Add some space between checkbox options */
  font-weight: normal; /* Override bold for checkbox labels if needed */
}


.save-button {
  background-color: #007bff;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  transition: background-color 0.2s ease-in-out;
}

.save-button:hover {
  background-color: #0056b3;
}

.save-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading-message,
.error-message,
.success-message {
  padding: 10px;
  margin-top: 10px;
  border-radius: 4px;
  text-align: center;
}

.loading-message {
  background-color: #e9f5ff;
  color: #007bff;
  border: 1px solid #b8dfff;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

.saving-error {
  margin-top: 10px; /* Specific margin for saving error */
}

.success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
  margin-top: 10px;
}

/* General section container styling - can be moved to a more global CSS if used elsewhere */
.section-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.section-container h2 {
    margin-top: 0;
    color: #333;
    font-size: 1.6em; /* Slightly larger for section titles */
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff; /* Accent color for the border */
    margin-bottom: 20px;
}