.knowledgeBaseView {
  padding: 20px;
  margin-bottom: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .knowledgeBaseView {
    padding: 15px;
    margin-bottom: 20px;
  }
}

.knowledgeBaseView h2 {
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.searchBar {
  margin-bottom: 20px;
}

.searchBar input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
}

.searchBar input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

.knowledgeBaseList {
  list-style: none;
  padding: 0;
}

.knowledgeBaseList li {
  padding: 10px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.knowledgeBaseList li:last-child {
  border-bottom: none;
}

.knowledgeBaseList li button {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.knowledgeBaseList li button:hover {
  background-color: #0056b3;
}
.knowledgeBaseListItem {
  padding: 10px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box; /* Important for react-window */
}

.knowledgeBaseListItem:last-child {
  border-bottom: none;
}

.knowledgeBaseListItem button {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  margin-left: 5px; /* Add some spacing between buttons and title */
}

.knowledgeBaseListItem button:hover {
  background-color: #0056b3;
}