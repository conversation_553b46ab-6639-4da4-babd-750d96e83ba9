import React, { useState, useCallback, memo } from 'react';

const ControlPanelComponent = ({ // Renamed to avoid conflict with memo export
  onLayoutChange,
  onFilterChange,
  onNodeTypeToggle,
  onEdgeTypeToggle,
  nodeTypes = [],
  edgeTypes = [],
  layoutOptions = [{ value: 'force-directed', label: 'Force Directed' }, { value: 'circle', label: 'Circle' }],
  filterAttributes = [], // e.g., [{ id: 'attr1', name: 'Attribute 1', type: 'string' }]
  currentLayout = 'force-directed' // Assuming this might be passed or managed
}) => {
  // State for filter inputs if they are managed internally before applying
  const [attributeFilterValues, setAttributeFilterValues] = useState({});

  const handleAttributeFilterChange = useCallback((attributeId, value) => {
    setAttributeFilterValues(prev => ({ ...prev, [attributeId]: value }));
  }, []); // setAttributeFilterValues is stable

  const handleApplyFilters = useCallback(() => {
    // Pass a structured filter object based on attributeFilterValues
    // This needs to align with how the container expects to receive and process filters
    onFilterChange(attributeFilterValues);
  }, [onFilterChange, attributeFilterValues]);

  return (
    <div data-testid="control-panel-actual">
      <h3>Control Panel</h3>
      
      <div>
        <h4>Layout Options</h4>
        <label htmlFor="layout-select">Select Layout:</label>
        <select
          id="layout-select"
          aria-label="Select Layout"
          value={currentLayout}
          onChange={(e) => onLayoutChange(e.target.value)}
        >
          {layoutOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
        </select>
        {/* Placeholder for layout parameter adjustments */}
      </div>

      <div>
        <h4>Define Attribute Filters</h4>
        {filterAttributes.map(attr => (
          <div key={attr.id}>
            <label htmlFor={`filter-${attr.id}`}>Filter by {attr.name}:</label>
            <input
              type={attr.type === 'number' ? 'number' : 'text'}
              id={`filter-${attr.id}`}
              aria-label={`Filter by ${attr.name}`} // Match test: "Filter by Attribute 1"
              value={attributeFilterValues[attr.id] || ''}
              onChange={(e) => handleAttributeFilterChange(attr.id, e.target.value)}
            />
          </div>
        ))}
        <button onClick={handleApplyFilters} aria-label="Apply Defined Filters Button">Apply Defined Filters</button>
        {/* The mock test for container used a simple button:
        <button onClick={() => onFilterChange({ type: 'node', criteria: 'test' })}>Apply Test Filter</button>
        The standalone test expects more structure. This will need reconciliation or conditional rendering.
        For now, focusing on standalone test structure.
        */}
      </div>

      <div>
        <h4>Node Types</h4>
        {nodeTypes.map(nodeType => (
          <div key={nodeType.id}>
            <label>
              <input
                type="checkbox"
                checked={nodeType.visible}
                onChange={(e) => onNodeTypeToggle(nodeType.id, e.target.checked)}
                aria-label={nodeType.label} // For test: "Type A"
              />
              {nodeType.label || nodeType.id}
            </label>
          </div>
        ))}
      </div>

      <div>
        <h4>Edge Types</h4>
        {edgeTypes.map(edgeType => (
          <div key={edgeType.id}>
            <label>
              <input
                type="checkbox"
                checked={edgeType.visible}
                onChange={(e) => onEdgeTypeToggle(edgeType.id, e.target.checked)}
                aria-label={edgeType.label} // For test: "Relation X"
              />
              {edgeType.label || edgeType.id}
            </label>
          </div>
        ))}
      </div>
      
      {/* <p>Layout options, zoom controls, etc.</p> */}
    </div>
  );
};

const ControlPanel = memo(ControlPanelComponent); // Wrapped with memo

export default ControlPanel;