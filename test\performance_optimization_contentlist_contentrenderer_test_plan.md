# Test Plan: Performance Optimization for ContentList and ContentRenderer

## 1. Introduction

This document outlines the test plan for verifying the performance optimizations implemented in the [`ContentList.js`](src/main-application-ui/renderer/components/knowledge-base-view/ContentList.js:0) and [`ContentRenderer.js`](src/main-application-ui/renderer/components/detail-view-pane/ContentRenderer.js:0) components. The primary goals are to ensure significant performance improvements as defined in the feature specification, confirm that existing functionality remains intact (no regressions), and verify that security measures (e.g., XSS protection) are not compromised.

This plan adheres to London School of TDD principles, emphasizing interaction-based testing and the mocking of collaborators to verify observable outcomes. It also defines a recursive (frequent regression) testing strategy to ensure ongoing stability.

**Reference Specification:** [`docs/specs/performance_optimization_contentlist_contentrenderer.md`](docs/specs/performance_optimization_contentlist_contentrenderer.md)

## 2. Scope of Testing

The scope of this test plan is to comprehensively test the performance optimizations and functional integrity of [`ContentList.js`](src/main-application-ui/renderer/components/knowledge-base-view/ContentList.js:0) and [`ContentRenderer.js`](src/main-application-ui/renderer/components/detail-view-pane/ContentRenderer.js:0). Testing will focus on verifying the achievement of the Acceptance Criteria (ACs) outlined in the feature specification, which serve as the primary AI Verifiable End Results for this optimization effort.

**Key Areas of Focus (mapped to Acceptance Criteria):**

*   **AC1 (ContentList - Initial Render Performance):** Verify significant reduction in initial render time for large lists.
*   **AC2 (ContentList - Scrolling Performance):** Ensure smooth scrolling with high frame rates for large lists.
*   **AC3 (ContentList - Sanitization Overhead Reduction):** Confirm reduced CPU time spent on sanitization within `ContentList.js`.
*   **AC4 (ContentRenderer - HTML Display Performance):** Verify faster rendering of large/complex HTML content.
*   **AC5 (Security Integrity):** Ensure XSS protection remains effective and `DOMPurify` usage is correct.
*   **AC6 (Functional Parity):** Confirm all existing functionalities of both components operate as expected.
*   **AC7 (Memory Usage Reduction):** Verify reduced memory footprint for `ContentList.js` with large lists.
*   **AC8 (Test Coverage):** Ensure new logic is covered by tests and overall coverage is maintained/improved.

## 3. Testing Strategy

### 3.1. Overall Approach: London School of TDD

The testing strategy will follow the **London School of TDD ("Outside-In")** principles:

*   **Interaction-Based Testing:** Tests will focus on the observable behavior of the components (`ContentList.js`, `ContentRenderer.js`) by verifying their interactions with collaborators.
*   **Mocking Collaborators:** Dependencies and collaborators (e.g., data providers, `react-window` API, `DOMPurify` if its direct interaction is tested, event handlers passed as props) will be mocked or stubbed. This allows tests to be isolated and focused on the unit's behavior in response to specific inputs and interactions.
*   **Verifying Observable Outcomes:** Tests will assert on the outcomes that are observable from the outside of the component, such as:
    *   Rendered output (what is passed to the virtualization library or the DOM).
    *   Calls made to mocked collaborators (e.g., `onSelectItem` prop, `DOMPurify.sanitize`).
    *   Performance metrics (render times, FPS) measured externally.

Internal state will not be directly tested; rather, its correctness will be inferred from the component's observable behavior and interactions.

### 3.2. Performance Testing

*   **Methodology:**
    *   Use browser developer tools (e.g., Chrome DevTools Performance tab, Firefox Profiler) to measure rendering times, CPU usage, frame rates, and memory allocation.
    *   Utilize React Profiler for component-specific render times and commit phases.
    *   Automated performance tests will be developed where feasible (e.g., using libraries like `jest-performance-testing` or custom scripts to measure render times under controlled conditions).
*   **Metrics:**
    *   Initial render time (Time to Interactive, First Contentful Paint).
    *   Scrolling smoothness (Frames Per Second - FPS, jank detection).
    *   CPU time spent in specific functions (especially sanitization).
    *   Memory usage (heap snapshots).

### 3.3. Functional Testing

*   **Methodology:**
    *   Unit tests (e.g., using Jest and React Testing Library) will verify individual component logic, focusing on interactions.
    *   Integration tests will verify the components' behavior within a simulated application context, ensuring correct interaction with parent/child components and data flow.
*   **Focus:**
    *   Correct rendering of items in `ContentList.js` (with and without virtualization).
    *   Correct item selection and invocation of `onSelectItem` in `ContentList.js`.
    *   Correct display of various content types (HTML, Markdown, plain text) in `ContentRenderer.js`.
    *   Preservation of all existing UI behaviors and states.

### 3.4. Security Testing (Regression)

*   **Methodology:**
    *   Review and execute existing XSS vulnerability checks.
    *   Craft specific test cases with known malicious payloads to ensure `DOMPurify` sanitization (whether pre-sanitized or memoized) remains effective.
    *   Verify that `dangerouslySetInnerHTML` is used only with reliably sanitized content.
*   **Focus:**
    *   Ensuring no new XSS vulnerabilities are introduced.
    *   Confirming that `DOMPurify`'s configuration and usage patterns correctly mitigate risks.

### 3.5. Recursive (Regression) Testing Strategy

A comprehensive recursive testing strategy is crucial to catch regressions early and ensure ongoing stability as the system evolves.

*   **Triggers for Re-running Tests:**
    1.  **Post-Commit Hook (Local):** After every commit to a feature branch involving changes in or dependencies of [`ContentList.js`](src/main-application-ui/renderer/components/knowledge-base-view/ContentList.js:0) or [`ContentRenderer.js`](src/main-application-ui/renderer/components/detail-view-pane/ContentRenderer.js:0). Run a targeted subset of tests.
    2.  **Pull Request (CI):** Before merging a feature branch into `main`/`develop`. Run a comprehensive suite of tests.
    3.  **Nightly Build (CI):** Daily execution of the full test suite, including more extensive performance benchmarks.
    4.  **Pre-Release:** Full test suite execution, including manual exploratory testing around performance and UX.
    5.  **Dependency Updates:** After updating key libraries (e.g., React, `react-window`, `DOMPurify`).

*   **Test Prioritization and Tagging:**
    Tests will be tagged to allow for selective execution:
    *   `@critical`: Tests covering core functionality, key performance metrics (AC1, AC2, AC4), and critical security checks (AC5). Failure is a blocker.
    *   `@performance`: All performance-related tests (AC1, AC2, AC3, AC4, AC7).
    *   `@functional`: All functional regression tests (AC6).
    *   `@security`: All security-related tests (AC5).
    *   `@ContentList`: Tests specific to `ContentList.js`.
    *   `@ContentRenderer`: Tests specific to `ContentRenderer.js`.
    *   `@virtualization`: Tests specific to list virtualization logic.
    *   `@sanitization`: Tests specific to sanitization logic.

*   **Test Subset Selection for Regression Triggers:**
    *   **Local Post-Commit:**
        *   If `ContentList.js` changed: Run `@critical @ContentList`, `@functional @ContentList`, relevant `@performance @ContentList`, relevant `@security @ContentList`.
        *   If `ContentRenderer.js` changed: Run `@critical @ContentRenderer`, `@functional @ContentRenderer`, relevant `@performance @ContentRenderer`, relevant `@security @ContentRenderer`.
    *   **Pull Request (CI):**
        *   Run all `@critical` tests.
        *   Run all `@functional` tests for affected components.
        *   Run a focused set of `@performance` tests (e.g., benchmarks with smaller datasets).
        *   Run all `@security` tests.
    *   **Nightly Build (CI):**
        *   Run ALL tests (`@critical`, `@performance` with large datasets, `@functional`, `@security`).
    *   **Pre-Release:**
        *   Run ALL tests.

*   **Layered Testing Strategy:**
    *   **Unit Tests:** Form the base, focusing on individual component logic and interactions with mocked collaborators. These are fast and run frequently.
    *   **Integration Tests:** Verify interactions between `ContentList.js`, `ContentRenderer.js`, and their immediate parent/child components or data services (mocked).
    *   **Performance Benchmark Tests:** A dedicated suite for measuring performance against defined ACs. These might be run less frequently on CI due to resource intensity but are critical for nightly and pre-release builds.
    *   **Manual/Exploratory Testing:** For UX aspects of scrolling smoothness and perceived responsiveness, especially on different devices/browsers if applicable.

This recursive strategy ensures that feedback on regressions is rapid, allowing developers to address issues quickly and maintain a high level of quality throughout the development lifecycle.

## 4. Test Cases

Each test case will target one or more AI Verifiable End Results (Acceptance Criteria).

### 4.1. Test Cases for [`ContentList.js`](src/main-application-ui/renderer/components/knowledge-base-view/ContentList.js:0)

#### 4.1.1. Performance: Initial Render Time (Virtualization)
*   **Test Case ID:** CL-PERF-001
*   **Description:** Measure initial render time of `ContentList.js` with a large number of items (100, 500, 1000, 5000).
*   **AI Verifiable End Result Targeted:** AC1
*   **Interactions to Test:** Component mounting and initial rendering.
*   **Collaborators to Mock:**
    *   Data provider: To supply a large list of item data.
    *   `react-window` (or chosen virtualization library): Interactions will be primarily through props passed to it. The test will verify that `ContentList.js` correctly configures and uses the virtualization library.
*   **Observable Outcome:**
    *   Initial render time measured using browser performance tools or React Profiler is below the target (< 500ms for 1000 items) and shows a >= 70% improvement.
    *   Only a small subset of items (visible + buffer) are rendered to the DOM.
*   **Recursive Testing Scope:** `@critical`, `@performance`, `@ContentList`, `@virtualization`

#### 4.1.2. Performance: Scrolling Smoothness (Virtualization)
*   **Test Case ID:** CL-PERF-002
*   **Description:** Measure scrolling performance (FPS, jank) when scrolling through a large list.
*   **AI Verifiable End Result Targeted:** AC2
*   **Interactions to Test:** User scrolling interaction.
*   **Collaborators to Mock:** Data provider.
*   **Observable Outcome:**
    *   Average FPS is >= 45 FPS during scrolling, measured using browser performance tools.
    *   No perceivable jank or lag.
*   **Recursive Testing Scope:** `@critical`, `@performance`, `@ContentList`, `@virtualization`

#### 4.1.3. Performance: Sanitization Overhead
*   **Test Case ID:** CL-PERF-003
*   **Description:** Profile CPU usage to measure time spent in sanitization functions called by `ContentList.js` (or its data preparation stage if pre-sanitization is used).
*   **AI Verifiable End Result Targeted:** AC3
*   **Interactions to Test:** Rendering of list items involving fields that require sanitization.
*   **Collaborators to Mock:** Data provider (supplying items with fields needing sanitization).
*   **Observable Outcome:**
    *   CPU profiling shows >80% reduction in time spent on `DOMPurify.sanitize` calls attributable to `ContentList.js` item rendering compared to baseline.
    *   If memoization is used, verify `DOMPurify.sanitize` is not called for unchanged items/fields on re-render.
*   **Recursive Testing Scope:** `@performance`, `@ContentList`, `@sanitization`

#### 4.1.4. Performance: Memory Usage
*   **Test Case ID:** CL-PERF-004
*   **Description:** Measure memory footprint when `ContentList.js` renders a large list.
*   **AI Verifiable End Result Targeted:** AC7
*   **Interactions to Test:** Component mounting and rendering a large list.
*   **Collaborators to Mock:** Data provider.
*   **Observable Outcome:**
    *   Memory usage (heap snapshot) reduced by at least 50% compared to baseline for 1000 items.
*   **Recursive Testing Scope:** `@performance`, `@ContentList`, `@virtualization`

#### 4.1.5. Functional: Correct Item Rendering (Virtualization)
*   **Test Case ID:** CL-FUNC-001
*   **Description:** Verify that visible items in the virtualized list display all required data fields correctly (title, snippet, tags, source, timestamp).
*   **AI Verifiable End Result Targeted:** AC6, FR2, FR3
*   **Interactions to Test:** Rendering of individual items within the virtualized list.
*   **Collaborators to Mock:**
    *   Data provider: To supply item data with various field values.
    *   `onSelectItem` prop (mock function).
*   **Observable Outcome:**
    *   Rendered output for visible items matches expected sanitized data.
    *   All specified fields are present and correctly formatted.
*   **Recursive Testing Scope:** `@critical`, `@functional`, `@ContentList`, `@virtualization`

#### 4.1.6. Functional: Item Selection (Virtualization)
*   **Test Case ID:** CL-FUNC-002
*   **Description:** Verify that clicking on an item in the virtualized list correctly triggers the `onSelectItem` callback with the correct item data.
*   **AI Verifiable End Result Targeted:** AC6, FR7
*   **Interactions to Test:** User click interaction on a list item.
*   **Collaborators to Mock:**
    *   Data provider.
    *   `onSelectItem` prop (mock function to capture calls).
*   **Observable Outcome:**
    *   `onSelectItem` mock is called once with the correct item's ID or data when an item is clicked.
*   **Recursive Testing Scope:** `@critical`, `@functional`, `@ContentList`, `@virtualization`

#### 4.1.7. Functional: Keyboard Navigation (Accessibility)
*   **Test Case ID:** CL-FUNC-003
*   **Description:** Verify keyboard navigation (up/down arrows, Enter key for selection) works correctly within the virtualized list.
*   **AI Verifiable End Result Targeted:** AC6, NFR6
*   **Interactions to Test:** Keyboard events.
*   **Collaborators to Mock:** Data provider, `onSelectItem` prop.
*   **Observable Outcome:**
    *   Focus moves correctly between items.
    *   `onSelectItem` is called with the correct item when Enter is pressed on a focused item.
    *   Correct ARIA attributes are present.
*   **Recursive Testing Scope:** `@functional`, `@ContentList`, `@virtualization`

#### 4.1.8. Security: Sanitization of List Item Fields
*   **Test Case ID:** CL-SEC-001
*   **Description:** Verify that all displayable text fields in list items (title, snippet, tags, source) are properly sanitized.
*   **AI Verifiable End Result Targeted:** AC5, FR3
*   **Interactions to Test:** Rendering list items with potentially malicious content in their fields.
*   **Collaborators to Mock:** Data provider (supplying items with XSS payloads in text fields).
*   **Observable Outcome:**
    *   Rendered output for list items does not execute any injected scripts.
    *   Malicious HTML tags are stripped or rendered as inert text.
    *   If pre-sanitization is used, verify the data passed to `ContentList.js` is already sanitized. If memoized sanitization within `ContentList.js`, verify `DOMPurify.sanitize` is called appropriately.
*   **Recursive Testing Scope:** `@critical`, `@security`, `@ContentList`, `@sanitization`

### 4.2. Test Cases for [`ContentRenderer.js`](src/main-application-ui/renderer/components/detail-view-pane/ContentRenderer.js:0)

#### 4.2.1. Performance: HTML Content Display Time
*   **Test Case ID:** CR-PERF-001
*   **Description:** Measure the time taken for `ContentRenderer.js` to display complex/large HTML content after props change.
*   **AI Verifiable End Result Targeted:** AC4
*   **Interactions to Test:** Component receiving new HTML content prop and re-rendering.
*   **Collaborators to Mock:** None directly, but test will control `content` and `contentType` props.
*   **Observable Outcome:**
    *   Time from prop change to render (measured via React Profiler or custom timers) is below target (< 200ms for ~500KB HTML) and shows >= 70% improvement.
    *   If memoization is used, verify `DOMPurify.sanitize` is not called if the raw HTML content prop has not changed.
*   **Recursive Testing Scope:** `@critical`, `@performance`, `@ContentRenderer`, `@sanitization`

#### 4.2.2. Functional: Correct Rendering of HTML Content
*   **Test Case ID:** CR-FUNC-001
*   **Description:** Verify that HTML content is correctly rendered (structure, styles if applicable) after sanitization.
*   **AI Verifiable End Result Targeted:** AC6, FR5
*   **Interactions to Test:** Rendering HTML content.
*   **Collaborators to Mock:** None directly.
*   **Observable Outcome:**
    *   Rendered HTML output matches the expected sanitized version of the input HTML.
    *   Content is displayed as intended, preserving safe HTML tags and attributes.
*   **Recursive Testing Scope:** `@critical`, `@functional`, `@ContentRenderer`

#### 4.2.3. Functional: Correct Rendering of Markdown Content
*   **Test Case ID:** CR-FUNC-002
*   **Description:** Verify that Markdown content is correctly rendered as preformatted text (as per current functionality).
*   **AI Verifiable End Result Targeted:** AC6, FR8
*   **Interactions to Test:** Rendering Markdown content.
*   **Collaborators to Mock:** None directly.
*   **Observable Outcome:**
    *   Markdown content is displayed within `<pre>` tags or equivalent, without interpretation of Markdown syntax.
*   **Recursive Testing Scope:** `@functional`, `@ContentRenderer`

#### 4.2.4. Functional: Correct Rendering of Plain Text Content
*   **Test Case ID:** CR-FUNC-003
*   **Description:** Verify that plain text content is rendered correctly.
*   **AI Verifiable End Result Targeted:** AC6, FR8
*   **Interactions to Test:** Rendering plain text content.
*   **Collaborators to Mock:** None directly.
*   **Observable Outcome:**
    *   Plain text content is displayed as is, preserving line breaks and spacing.
*   **Recursive Testing Scope:** `@functional`, `@ContentRenderer`

#### 4.2.5. Security: HTML Content Sanitization
*   **Test Case ID:** CR-SEC-001
*   **Description:** Verify that HTML content passed to `ContentRenderer.js` is effectively sanitized to prevent XSS.
*   **AI Verifiable End Result Targeted:** AC5, FR6
*   **Interactions to Test:** Rendering HTML content containing XSS payloads.
*   **Collaborators to Mock:** None directly, test provides malicious HTML as `content` prop.
*   **Observable Outcome:**
    *   Rendered HTML output does not execute injected scripts.
    *   Malicious tags/attributes are stripped or neutralized.
    *   If pre-sanitization is used, verify the data passed to `ContentRenderer.js` is already sanitized. If memoized sanitization, verify `DOMPurify.sanitize` is called appropriately.
*   **Recursive Testing Scope:** `@critical`, `@security`, `@ContentRenderer`, `@sanitization`

### 4.3. General Test Cases
#### 4.3.1. Test Coverage
*   **Test Case ID:** GEN-TEST-001
*   **Description:** Ensure that new logic for virtualization and optimized sanitization is adequately covered by unit/integration tests.
*   **AI Verifiable End Result Targeted:** AC8
*   **Observable Outcome:** Code coverage reports (e.g., from Jest) show maintained or improved coverage for the modified components and related utility functions.
*   **Recursive Testing Scope:** Run as part of CI builds (`@critical`, `@functional`, `@performance`, `@security`).

## 5. Test Data Requirements

*   **For `ContentList.js`:**
    *   **Small Dataset:** ~50 items with varying field lengths and simple content.
    *   **Medium Dataset:** ~500 items.
    *   **Large Dataset:** 1,000 items with diverse content, including some items with longer titles/snippets.
    *   **Very Large Dataset:** 5,000+ items for stress testing scrolling and memory.
    *   **Items with XSS Payloads:** List items where title, snippet, tags, source fields contain various XSS attack vectors (e.g., `<script>alert(1)</script>`, `<img> onerror=alert(1) src=x>`).
    *   Items with special characters, different languages, and empty/null fields.

*   **For `ContentRenderer.js`:**
    *   **Simple HTML Document:** Basic HTML structure, few tags (~1KB).
    *   **Complex HTML Document:** ~500KB HTML document with nested tags, inline styles, various elements (tables, lists, images - ensure image `src` attributes are handled safely).
    *   **HTML with XSS Payloads:** HTML content containing various XSS attack vectors.
    *   **Markdown Document:** Sample Markdown text of varying lengths.
    *   **Plain Text Document:** Simple text, text with multiple lines, special characters.

*   **Data Generation:** Scripts or utilities may be needed to generate large, consistent datasets for performance testing.

## 6. Test Environment and Tools

*   **Browsers:**
    *   Latest stable version of Google Chrome (primary for performance profiling).
    *   Latest stable version of Mozilla Firefox.
    *   (Optional) Latest stable version of Safari / Edge if target audience requires.
*   **Testing Frameworks/Libraries:**
    *   Jest (for unit and integration tests).
    *   React Testing Library (for interacting with components).
    *   `react-window` (or chosen virtualization library) - tests will interact with its API as used by `ContentList.js`.
    *   `DOMPurify` - for verifying sanitization.
*   **Profiling Tools:**
    *   Browser Developer Tools (Performance tab, Memory tab, Console).
    *   React Developer Tools (Profiler).
*   **CI/CD System:** Jenkins, GitHub Actions, or similar for automated test execution.
*   **Hardware:** Testing should ideally be performed on hardware representative of typical user systems. Performance benchmarks should note the hardware used.

## 7. Pass/Fail Criteria

*   **General:**
    *   All `@critical` tagged tests must pass for a build/PR to be considered successful.
    *   No new regressions in existing functionality (AC6).
    *   No new security vulnerabilities introduced (AC5).
*   **Performance:**
    *   **AC1:** `ContentList.js` initial render time for 1,000 items < 500ms AND >= 70% improvement.
    *   **AC2:** `ContentList.js` scrolling FPS for 1,000 items >= 45 FPS.
    *   **AC3:** CPU time for `ContentList.js` sanitization reduced by >80%.
    *   **AC4:** `ContentRenderer.js` HTML display time for ~500KB content < 200ms AND >= 70% improvement.
    *   **AC7:** `ContentList.js` memory usage for 1,000 items reduced by >= 50%.
*   **Functional:** All functional test cases must pass.
*   **Security:** All security test cases must pass; no XSS exploits should succeed.
*   **Test Coverage (AC8):** Code coverage targets (e.g., >80% for new/modified logic) must be met or overall project coverage maintained/improved.

Failure to meet these criteria will require investigation and remediation before the feature is considered complete and accepted.