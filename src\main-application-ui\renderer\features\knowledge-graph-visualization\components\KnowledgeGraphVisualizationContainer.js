import React, { useState, useEffect, useCallback, useMemo } from 'react';
import GraphRenderingArea from './GraphRenderingArea';
import ControlPanel from './ControlPanel';
import InformationDisplayPanel from './InformationDisplayPanel';
import <PERSON>FilterBar from './SearchFilterBar';
import Legend from './Legend';
import './KGV.css'; // Assuming a CSS file for styling

const KnowledgeGraphVisualizationContainer = ({ initialGraphData, visualEncodings }) => {
  const [graphData, setGraphData] = useState(initialGraphData || { nodes: [], edges: [] });
  const [displayedGraphData, setDisplayedGraphData] = useState(initialGraphData || { nodes: [], edges: [] });
  const [layout, setLayout] = useState('cose'); // Changed default layout
  const [selectedItem, setSelectedItem] = useState(null);
  const [selectedItemType, setSelectedItemType] = useState(null); // 'node' or 'edge'
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState({}); // Store as object: { attr1: 'value1', nodeType_typeA: true }
  const [nodeTypeVisibility, setNodeTypeVisibility] = useState(() => {
    const initialVisibility = {};
    if (visualEncodings && visualEncodings.nodeTypes) {
      Object.keys(visualEncodings.nodeTypes).forEach(typeId => {
        initialVisibility[typeId] = { id: typeId, visible: true, label: visualEncodings.nodeTypes[typeId].label };
      });
    }
    return initialVisibility;
  });
  const [edgeTypeVisibility, setEdgeTypeVisibility] = useState(() => {
    const initialVisibility = {};
    if (visualEncodings && visualEncodings.edgeTypes) {
      Object.keys(visualEncodings.edgeTypes).forEach(typeId => {
        initialVisibility[typeId] = { id: typeId, visible: true, label: visualEncodings.edgeTypes[typeId].label };
      });
    }
    return initialVisibility;
  });


  const applyFiltersAndSearch = useCallback(() => {
    let newNodes = graphData.nodes || [];
    let newEdges = graphData.edges || [];

    // Apply node type visibility
    newNodes = newNodes.filter(node => nodeTypeVisibility[node.type] && nodeTypeVisibility[node.type].visible);

    // Apply search term (simple search on label for now)
    if (searchTerm) {
      newNodes = newNodes.filter(node => node.label && node.label.toLowerCase().includes(searchTerm.toLowerCase()));
    }

    // Apply active attribute filters
    Object.entries(activeFilters).forEach(([filterKey, filterValue]) => {
      // Example: filterKey could be 'attr1' (from filterAttributes) or a specific criteria like 'testNodeLabel'
      // This logic needs to be robust based on how filterAttributes are defined and how ControlPanel sends them.
      if (graphData.filterAttributes && graphData.filterAttributes.find(attr => attr.id === filterKey) && filterValue) {
         newNodes = newNodes.filter(node =>
            node.attributes &&
            String(node.attributes[filterKey]).toLowerCase().includes(String(filterValue).toLowerCase())
        );
      } // This closing brace was missing in the previous diff attempt, it belongs to the if statement.
    });
    
    // Filter edges: based on visible nodes AND edge type visibility
    const visibleNodeIds = new Set(newNodes.map(n => n.id));
    newEdges = newEdges.filter(edge =>
        visibleNodeIds.has(edge.source) &&
        visibleNodeIds.has(edge.target) &&
        edgeTypeVisibility[edge.type] && edgeTypeVisibility[edge.type].visible
    );

    setDisplayedGraphData({ nodes: newNodes, edges: newEdges });
  }, [graphData, searchTerm, activeFilters, nodeTypeVisibility, edgeTypeVisibility]);

  useEffect(() => {
    applyFiltersAndSearch();
  }, [applyFiltersAndSearch]);
  
  useEffect(() => {
    // Update graphData if initialGraphData prop changes
    setGraphData(initialGraphData || { nodes: [], edges: [] });
    // Reset node type visibility if visualEncodings change
    if (visualEncodings && visualEncodings.nodeTypes) {
      const initialVisibility = {};
      Object.keys(visualEncodings.nodeTypes).forEach(typeId => {
        initialVisibility[typeId] = { id: typeId, visible: true, label: visualEncodings.nodeTypes[typeId].label };
      });
      setNodeTypeVisibility(initialVisibility);
    }
    if (visualEncodings && visualEncodings.edgeTypes) {
      const initialEdgeVisibility = {};
      Object.keys(visualEncodings.edgeTypes).forEach(typeId => {
        initialEdgeVisibility[typeId] = { id: typeId, visible: true, label: visualEncodings.edgeTypes[typeId].label };
      });
      setEdgeTypeVisibility(initialEdgeVisibility);
    }
  }, [initialGraphData, visualEncodings]);


  const handleLayoutChange = useCallback((newLayout) => {
    console.log('Layout changed to:', newLayout);
    setLayout(newLayout);
  }, []);

  const handleNodeSelect = useCallback((nodeIds) => {
    // For simplicity, select the first node if multiple are passed by mock
    const nodeId = nodeIds && nodeIds.length > 0 ? nodeIds[0] : null;
    const node = nodeId ? graphData.nodes.find(n => n.id === nodeId) : null;
    setSelectedItem(node);
    setSelectedItemType(node ? 'node' : null);
    console.log('Node selected:', node);
  }, [graphData.nodes]);

  const handleEdgeSelect = useCallback((edgeId) => {
    const edge = graphData.edges.find(e => e.id === edgeId);
    setSelectedItem(edge);
    setSelectedItemType(edge ? 'edge' : null);
    console.log('Edge selected:', edge);
  }, [graphData.edges]);

  const handleCanvasInteraction = useCallback((interaction) => {
    // Placeholder for handling zoom, pan, etc.
    console.log('Canvas interaction:', interaction);
  }, []);

  const handleSearchTermChange = useCallback((newSearchTerm) => {
    setSearchTerm(newSearchTerm);
  }, []);

  const handleFilterApply = useCallback((filterOptions) => {
    // For TC_KGV_CNT_004, filterOptions is { searchTerm: props.currentSearchTerm }
    // The search term is already handled by its own state and useEffect.
    // This handler could be used for more complex filter objects from SearchFilterBar if needed.
    console.log('Search filter apply:', filterOptions);
    // applyFiltersAndSearch will be triggered by searchTerm change
  }, []);
  
  const handleControlPanelFilterChange = useCallback((newFilters) => {
    console.log('ControlPanel filter change:', newFilters);
    // newFilters is an object like { attr1: 'value', attr2: 'value' } from ControlPanel
    // Or for the mock test: { type: 'node', criteria: 'test' }
    // The newFilters object is expected to be a flat object of key-value pairs
    // e.g., { attributeId: 'valueToFilterBy', anotherAttributeId: 'anotherValue' }
    setActiveFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const handleNodeTypeToggle = useCallback((typeId, isVisible) => {
    console.log('Toggling node type:', typeId, 'Visible:', isVisible);
    setNodeTypeVisibility(prev => ({
      ...prev,
      [typeId]: { ...prev[typeId], visible: isVisible }
    }));
  }, []);

  // Prepare node types for ControlPanel based on visibility state
  const controlPanelNodeTypes = useMemo(() => {
    return Object.values(nodeTypeVisibility);
  }, [nodeTypeVisibility]);

  const controlPanelEdgeTypes = useMemo(() => {
    return Object.values(edgeTypeVisibility);
  }, [edgeTypeVisibility]);

  const handleEdgeTypeToggle = useCallback((typeId, isVisible) => {
    console.log('Toggling edge type:', typeId, 'Visible:', isVisible);
    setEdgeTypeVisibility(prev => ({
      ...prev,
      [typeId]: { ...prev[typeId], visible: isVisible }
    }));
  }, []);


  // Mock data for ControlPanel props, to be replaced by actual data or config
  const layoutOptions = [
    { value: 'cose', label: 'Cose (Force-Directed)' },
    { value: 'grid', label: 'Grid' },
    { value: 'circle', label: 'Circle' },
    { value: 'breadthfirst', label: 'Breadthfirst' },
    // { value: 'fcose', label: 'FCose (Animated)' }, // If using extension
  ];
  const filterAttributes = initialGraphData?.filterAttributes || [ // Example, should come from data or config
    { id: 'detail', name: 'Detail', type: 'string' },
    // { id: 'type', name: 'Type', type: 'string' } // 'type' is special, handled by visibility toggles
  ];


  return (
    <div className="kgv-container">
      <SearchFilterBar
        currentSearchTerm={searchTerm}
        onSearchTermChange={handleSearchTermChange}
        onFilterApply={handleFilterApply}
        // quickFilterOptions={...} // If SearchFilterBar uses them directly
      />
      <div className="kgv-main-area">
        <ControlPanel
          currentLayout={layout}
          layoutOptions={layoutOptions}
          filterAttributes={filterAttributes}
          nodeTypes={controlPanelNodeTypes}
          edgeTypes={controlPanelEdgeTypes}
          onLayoutChange={handleLayoutChange}
          onFilterChange={handleControlPanelFilterChange}
          onNodeTypeToggle={handleNodeTypeToggle}
          onEdgeTypeToggle={handleEdgeTypeToggle}
        />
        <GraphRenderingArea
          graphData={displayedGraphData}
          layout={layout}
          onNodeSelect={handleNodeSelect}
          onEdgeSelect={handleEdgeSelect} // Added for completeness
          onCanvasInteraction={handleCanvasInteraction}
          visualEncodings={visualEncodings} // Pass visual encodings
        />
        <div className="kgv-side-panel">
          <InformationDisplayPanel
            selectedItem={selectedItem}
            itemType={selectedItemType}
            visualEncodings={visualEncodings}
          />
          <Legend
            visualEncodings={visualEncodings}
            nodeTypeVisibility={nodeTypeVisibility}
            edgeTypeVisibility={edgeTypeVisibility}
          />
        </div>
      </div>
    </div>
  );
};

export default KnowledgeGraphVisualizationContainer;