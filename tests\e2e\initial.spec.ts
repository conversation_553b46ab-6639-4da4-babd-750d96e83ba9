import { test, expect, chromium, type BrowserContext } from '@playwright/test';
import path from 'path';

const pathToExtension = path.join(__dirname, '..', '..', 'src', 'browser-extension-ui');
const extensionId = ''; // Will be populated once the extension is loaded

test.describe('Chrome Extension Initial Load', () => {
  let browserContext: BrowserContext;

  test.beforeAll(async () => {
    browserContext = await chromium.launchPersistentContext('', {
      headless: false, // Set to true for CI, false for local debugging
      args: [
        `--disable-extensions-except=${pathToExtension}`,
        `--load-extension=${pathToExtension}`,
      ],
    });
    // Add a small delay to allow the extension to fully load
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  test.afterAll(async () => {
    await browserContext.close();
  });

  test('should load the extension popup and verify title', async () => {
    // Find the extension's service worker to get its ID, with retries and logging
    let serviceWorker;
    let attempts = 0;
    const maxAttempts = 5;
    
    while (!serviceWorker && attempts < maxAttempts) {
      attempts++;
      const allServiceWorkers = browserContext.serviceWorkers();
      if (allServiceWorkers.length > 0) {
        console.log(`Attempt ${attempts}: Found ${allServiceWorkers.length} service worker(s):`);
        allServiceWorkers.forEach((sw, index) => {
          console.log(`  SW ${index}: ${sw.url()}`);
        });
      } else {
        console.log(`Attempt ${attempts}: No service workers found.`);
      }
      
      serviceWorker = allServiceWorkers.find(sw => sw.url().startsWith('chrome-extension://'));
      if (serviceWorker) {
        console.log(`Target service worker found: ${serviceWorker.url()}`);
        break;
      }
      if (attempts < maxAttempts) {
        console.log(`Retrying in 500ms... (Attempt ${attempts}/${maxAttempts})`);
        await new Promise(resolve => setTimeout(resolve, 500)); // Wait 500ms before retrying
      }
    }

    if (!serviceWorker) {
      const finalAllServiceWorkers = browserContext.serviceWorkers();
      if (finalAllServiceWorkers.length > 0) {
         console.error(`Final check: Found ${finalAllServiceWorkers.length} service worker(s) but none matched 'chrome-extension://':`);
         finalAllServiceWorkers.forEach((sw, index) => {
           console.error(`  SW ${index}: ${sw.url()}`);
         });
      } else {
         console.error("Final check: Still no service workers found.");
      }
      throw new Error("Extension service worker not found after multiple attempts. Ensure the extension loaded correctly and background.js is functional.");
    }
    const extensionId = serviceWorker.url().split('/')[2];

    // Navigate to the popup page
    const popupPage = await browserContext.newPage();
    await popupPage.goto(`chrome-extension://${extensionId}/popup.html`);

    // Verify the title of the popup page
    await expect(popupPage).toHaveTitle('Web Content Capture');
    
    // Add a small delay to visually confirm if running headful
    if (!test.info().project.use.headless) {
      await popupPage.waitForTimeout(2000);
    }
    await popupPage.close();
  });
});