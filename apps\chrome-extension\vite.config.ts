import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { nodePolyfills } from 'vite-plugin-node-polyfills';

const rootDir = resolve(__dirname);
const srcDir = resolve(rootDir, 'src');
const publicDir = resolve(rootDir, 'public');
const outDir = resolve(rootDir, 'dist');

export default defineConfig({
  resolve: {
    alias: {
      '@': resolve(srcDir),
      // The alias for knowledge-base-service might still be needed if it's a local workspace package
      // and not correctly resolved otherwise. Let's keep it for now.
      '@pkm-ai/knowledge-base-service': resolve(__dirname, '../../packages/knowledge-base-service/dist'),
      // Manual aliases for node built-ins are removed as the plugin should handle them.
    },
  },
  // define: { // Polyfill global for browser environment if needed by dependencies
  //   'global': 'globalThis', // Or 'window' or 'self' depending on target context
  // }, // This might be handled by the polyfill plugin or might not be needed.
  plugins: [
    react({ jsxRuntime: 'classic' }),
    nodePolyfills({
      // Options (if needed):
      // To exclude specific polyfills, add them to `exclude`:
      // exclude: ['fs'], // Exclude 'fs' if you don't want to polyfill it
      // To specify whether to polyfill `global`, `Buffer`, and `process`
      globals: {
        Buffer: true, // Default true
        global: true, // Default true
        process: true, // Default true
      },
      // To control whether paths like `node:fs` are resolved to polyfills:
      protocolImports: true, // Default true
    }),
  ],
  publicDir: publicDir,
  build: {
    outDir: outDir,
    sourcemap: process.env.NODE_ENV === 'development',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        // Assuming popup.html and options.html are moved to rootDir (e.g., apps/chrome-extension/)
        // and are no longer in publicDir if they are to be processed as main entry points.
        // publicDir should only contain assets that are copied as-is without processing script/link tags.
        popup: resolve(rootDir, 'popup.html'),
        options: resolve(rootDir, 'options.html'),
        background: resolve(srcDir, 'background', 'index.ts'),
        // keepalive.html, if it's just static, can remain in publicDir and be referenced from manifest
      },
      external: [
        // Attempt to prevent Vite from bundling lowdb's Node.js specific parts
        // This relies on the KnowledgeBaseService using dynamic imports for these.
        'lowdb/node',
        'node:fs', // Also mark node builtins explicitly if needed
        'node:path',
        'node:url'
      ],
      output: {
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'background') {
            return 'background.js'; // e.g., dist/background.js
          }
          // For JS entry points generated from HTML files (e.g., popup, options)
          return 'assets/js/[name].entry.[hash].js'; // e.g., dist/assets/js/popup.entry.xxxx.js
        },
        chunkFileNames: 'assets/js/[name].chunk.[hash].js', // For shared chunks
        assetFileNames: (assetInfo) => {
          const { name } = assetInfo;
          if (name && name.endsWith('.css')) {
            return 'assets/css/[name]-[hash][extname]';
          }
          // For other assets (images, fonts, etc., but not entry HTMLs as Vite handles them separately)
          // Note: HTML files listed as input in rollupOptions are handled by Vite's HTML processing
          // and will be placed in the root of outDir by default, with their asset links updated.
          // This pattern is for non-CSS, non-JS, non-HTML-entry assets.
          if (name && !name.endsWith('.js') && !name.endsWith('.html')) { // Avoid affecting JS chunks or already handled HTML
             return 'assets/misc/[name]-[hash][extname]';
          }
          // Let Vite's default naming apply for anything else (e.g. if an HTML file is somehow treated as a generic asset)
          // or rely on entryFileNames and chunkFileNames for JS.
          // A more robust default catch-all if not CSS or explicitly handled JS:
          return 'assets/[name]-[hash][extname]';
        },
      },
    },
  },
});