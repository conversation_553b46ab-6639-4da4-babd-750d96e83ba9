import React, { useState } from 'react';

interface TagInputProps {
  initialTags: string[];
  onTagsChange: (tags: string[]) => void;
}

const TagInput: React.FC<TagInputProps> = ({ initialTags, onTagsChange }) => {
  const [tags, setTags] = useState<string[]>(initialTags);
  const [newTag, setNewTag] = useState('');

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      const updatedTags = [...tags, newTag.trim()];
      setTags(updatedTags);
      onTagsChange(updatedTags);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = tags.filter(tag => tag !== tagToRemove);
    setTags(updatedTags);
    onTagsChange(updatedTags);
  };

  const handleNewTagInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewTag(event.target.value);
  };

  const handleNewTagInputKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleAddTag();
    }
  };

  return (
    <div data-testid="tag-input">
      <h4>Tags</h4>
      <div>
        {tags.map((tag, index) => (
          <span key={index} data-testid={`tag-item-${index}`} style={{ marginRight: '8px', backgroundColor: '#e0e0e0', padding: '4px', borderRadius: '4px' }}>
            {tag}
            <button
              data-testid={`remove-tag-button-${index}`}
              onClick={() => handleRemoveTag(tag)}
              style={{ marginLeft: '4px', cursor: 'pointer', border: 'none', background: 'none' }}
            >
              x
            </button>
          </span>
        ))}
      </div>
      <div style={{ marginTop: '8px' }}>
        <input
          type="text"
          data-testid="new-tag-input"
          value={newTag}
          onChange={handleNewTagInputChange}
          onKeyPress={handleNewTagInputKeyPress}
          placeholder="Add new tag"
        />
        <button data-testid="add-tag-button" onClick={handleAddTag} style={{ marginLeft: '4px' }}>Add</button>
      </div>
    </div>
  );
};

export default TagInput;