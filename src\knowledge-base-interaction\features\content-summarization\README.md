# Content Summarization Feature

This directory contains the source code for the content summarization feature,
part of the Knowledge Base Interaction & Insights Module.

## Components

*   **[ui-layer](./ui-layer/README.md):** Handles user interaction for submitting content to be summarized and displaying the results.
*   **[query-understanding-engine](./query-understanding-engine/README.md):** Interprets user requests, identifies the intent for summarization, and routes requests to the AI Services Gateway.
*   **[ai-services-gateway](./ai-services-gateway/README.md):** Interacts with external AI services (e.g., Gemini) to perform the content summarization.
*   **[utils](./utils/README.md):** Shared utility functions for logging, error handling, etc.

## Design Document

For detailed design specifications, refer to [`docs/design/Content_Summarization_Detailed_Design.md`](docs/design/Content_Summarization_Detailed_Design.md).