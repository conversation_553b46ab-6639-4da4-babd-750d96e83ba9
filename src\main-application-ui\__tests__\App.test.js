// __tests__/App.test.js
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import App from '../renderer/App';

// Mock the API client module - still needed if useStore relies on it for initial fetches
jest.mock('../renderer/api/client', () => ({
  getTags: jest.fn().mockResolvedValue([{ id: 't1', name: 'Tech' }, { id: 't2', name: 'Science' }]),
  getCategories: jest.fn().mockResolvedValue([{ id: 'c1', name: 'Work' }, { id: 'c2', name: 'Personal' }]),
  getItems: jest.fn().mockResolvedValue([]),
  getItemById: jest.fn().mockResolvedValue({}),
  createItem: jest.fn().mockResolvedValue({}),
  updateItem: jest.fn().mockResolvedValue({}),
  deleteItem: jest.fn().mockResolvedValue({}),
  searchItems: jest.fn().mockResolvedValue([]),
  fetchTags: jest.fn().mockResolvedValue(['Tech', 'Science']),
  fetchCategories: jest.fn().mockResolvedValue(['Work', 'Personal']),
}));

// Keep a flexible mock for useStore
let mockStoreState;
const setMockStoreState = (newState) => {
  mockStoreState = {
    items: [],
    selectedItemId: null,
    searchTerm: '',
    searchResults: [],
    searchLoading: false,
    searchError: null,
    filterTags: [{id: 't1', name: 'Tech'}, {id: 't2', name: 'Science'}], // Renamed from allTags
    selectedTags: [],
    filterCategories: [{id: 'c1', name: 'Work'}, {id: 'c2', name: 'Personal'}], // Renamed from allCategories
    selectedCategories: [],
    // ... other state properties from your actual store if needed by App directly
    setSelectedTags: jest.fn((tags) => { mockStoreState.selectedTags = tags; }),
    setSelectedCategories: jest.fn((categories) => { mockStoreState.selectedCategories = categories; }),
    performSearch: jest.fn(),
    setSearchTerm: jest.fn((term) => { mockStoreState.searchTerm = term; }),
    selectItem: jest.fn((itemId) => { mockStoreState.selectedItemId = itemId; }),
    clearSelectedItem: jest.fn(() => { mockStoreState.selectedItemId = null; }),
    fetchTags: jest.fn(), // Keep these if App or its direct children call them
    fetchCategories: jest.fn(),
    ...newState, // Allow overriding for specific tests
  };
};

jest.mock('../renderer/store/useStore', () => {
  return jest.fn((selector) => {
    if (typeof selector === 'function') {
      return selector(mockStoreState);
    }
    return mockStoreState; // Return the whole state if no selector
  });
});


// Mock child components of App.js as needed for focused testing of App itself
// KnowledgeBaseView is a major component, mock it to test App's interaction with it.
jest.mock('../renderer/components/KnowledgeBaseView', () => ({
  __esModule: true,
  default: jest.fn(({ onSelectItem, currentSearchTerm }) => (
    <div data-testid="knowledge-base-view">
      <p>Mock KnowledgeBaseView</p>
      <p>Search Term: {currentSearchTerm}</p>
      <button onClick={() => onSelectItem({ id: 'kbv-item-1', title: 'KBV Item 1' })}>Select KBV Item</button>
    </div>
  )),
}));

// DetailViewPane is also a direct child.
jest.mock('../renderer/components/DetailViewPane', () => ({
  __esModule: true,
  default: jest.fn(({ item, onOpenAiPanel, onShowConceptualLinks }) => (
    <div data-testid="detail-view-pane">
      {item ? `Selected: ${item.title}` : 'No item selected'}
      <button data-testid="open-ai-panel-button" onClick={() => onOpenAiPanel({type: 'qa', itemId: item?.id})}>Open AI</button>
      <button data-testid="show-links-button" onClick={onShowConceptualLinks}>Show Links</button>
    </div>
  )),
}));

jest.mock('../renderer/components/AIInteractionPanel', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="ai-interaction-panel">Mock AIInteractionPanel</div>),
}));

jest.mock('../renderer/components/ConceptualLinksDisplay', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="conceptual-links-display">Mock ConceptualLinksDisplay</div>),
}));

jest.mock('../renderer/components/OfflineStatusIndicator', () => ({
  __esModule: true,
  default: jest.fn(({ isOnline }) => (
    <div data-testid="offline-status-indicator">Status: {isOnline ? 'Online' : 'Offline'}</div>
  )),
}));

jest.mock('../renderer/components/SettingsView', () => ({
    __esModule: true,
    default: jest.fn(({onBack}) => <div data-testid="settings-view">Mock Settings <button onClick={onBack}>Back</button></div>),
}));


describe('App Component', () => {
  beforeEach(() => {
    // Reset mock store state for each test
    setMockStoreState({});
    // Clear all mock function calls
    jest.clearAllMocks();
  });

  test('renders App component with its main children', () => {
    render(<App />);
    expect(screen.getByTestId('knowledge-base-view')).toBeInTheDocument();
    expect(screen.getByTestId('detail-view-pane')).toBeInTheDocument();
    expect(screen.getByTestId('offline-status-indicator')).toBeInTheDocument();
    expect(screen.getByText('Status: Online')).toBeInTheDocument(); // From mock OfflineStatusIndicator
    expect(screen.getByRole('button', { name: 'Settings' })).toBeInTheDocument();
  });

  test('initially shows "No item selected" in DetailViewPane', () => {
    render(<App />);
    expect(screen.getByTestId('detail-view-pane')).toHaveTextContent('No item selected');
  });

  test('selecting an item in KnowledgeBaseView updates DetailViewPane', () => {
    // Initial render
    const { rerender } = render(<App />);
    
    // Simulate item selection from the mocked KnowledgeBaseView
    const selectItemButton = screen.getByRole('button', { name: 'Select KBV Item' });
    fireEvent.click(selectItemButton);
    
    // Check if useStore's selectItem was called (which updates mockStoreState.selectedItemId)
    expect(mockStoreState.selectItem).toHaveBeenCalledWith('kbv-item-1');
    
    // Update the mock store state to reflect the selection
    // The actual App component would re-render due to this state change in a real scenario.
    // Here, we manually update the mock and then re-render the App component.
    setMockStoreState({
      ...mockStoreState, // Preserve other parts of the state
      selectedItemId: 'kbv-item-1',
      items: [...mockStoreState.items, {id: 'kbv-item-1', title: 'KBV Item 1'}] // Ensure item exists
    });
    
    // Re-render the App component with the updated store state
    rerender(<App />);
    
    // Now, query for the DetailViewPane and check its content
    // There should only be one instance of detail-view-pane in the current render.
    expect(screen.getByTestId('detail-view-pane')).toHaveTextContent('Selected: KBV Item 1');
  });

  test('toggles SettingsView', () => {
    render(<App />);
    const settingsButton = screen.getByRole('button', { name: 'Settings' });
    fireEvent.click(settingsButton);
    expect(screen.getByTestId('settings-view')).toBeInTheDocument();
    const backButton = screen.getByRole('button', { name: 'Back' });
    fireEvent.click(backButton);
    expect(screen.queryByTestId('settings-view')).not.toBeInTheDocument();
  });

  test('shows AIInteractionPanel when an item is selected and AI action is triggered', () => {
    setMockStoreState({ selectedItemId: 'item1', items: [{id: 'item1', title: 'Test AI Item'}] });
    render(<App />);
    // DetailViewPane mock has a button to trigger onOpenAiPanel
    const openAiButton = screen.getByTestId('open-ai-panel-button');
    fireEvent.click(openAiButton);
    expect(screen.getByTestId('ai-interaction-panel')).toBeInTheDocument();
  });

  test('shows ConceptualLinksDisplay when an item is selected and links action is triggered', () => {
    setMockStoreState({ selectedItemId: 'item1', items: [{id: 'item1', title: 'Test Links Item'}] });
    render(<App />);
    // DetailViewPane mock has a button to trigger onShowConceptualLinks
    const showLinksButton = screen.getByTestId('show-links-button');
    fireEvent.click(showLinksButton);
    expect(screen.getByTestId('conceptual-links-display')).toBeInTheDocument();
  });
  
  // The detailed filter tests are now more appropriate for KnowledgeBaseView.test.js
  // App.test.js should verify that KnowledgeBaseView receives the necessary filter-related props from the store.
  test('KnowledgeBaseView receives filter-related props from store', () => {
    setMockStoreState({
      filterTags: [{id: 't1', name: 'StoreTag1'}],
      selectedTags: ['StoreTag1'],
      filterCategories: [{id: 'c1', name: 'StoreCat1'}],
      selectedCategories: ['StoreCat1'],
      searchTerm: 'store search'
    });
    render(<App />);
    
    // Check that the mocked KnowledgeBaseView (which displays searchTerm) shows the term from store
    expect(screen.getByText('Search Term: store search')).toBeInTheDocument();

    // Verify that KnowledgeBaseView was called with the correct props from the store
    // This requires checking the arguments of the mocked KnowledgeBaseView component
    const KnowledgeBaseViewMock = require('../renderer/components/KnowledgeBaseView').default;
    expect(KnowledgeBaseViewMock).toHaveBeenCalledWith(
      expect.objectContaining({
        availableTags: [{id: 't1', name: 'StoreTag1'}],
        selectedTags: ['StoreTag1'],
        availableCategories: [{id: 'c1', name: 'StoreCat1'}],
        selectedCategories: ['StoreCat1'],
        currentSearchTerm: 'store search',
        // items, searchResults, etc.
      }),
      {} // Second argument for React component context (usually empty object)
    );
  });

});
