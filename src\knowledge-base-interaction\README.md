# Knowledge Base Interaction & Insights Module

This module enables users to interact with their knowledge base. It supports browsing, search, Q&A, summarization, content transformation, and link suggestion based on the stored information.

## Core Functions

The module exports the following core functions:

*   **`browseItems(options)`**: Browses saved knowledge base items with sorting, filtering, and pagination.
    *   `options`: Object containing `sortBy`, `sortOrder`, `filterBy`, `page`, `limit`.
    *   Returns: `Promise<{data: Array<object>, pagination: object}>`

*   **`getItemDetails(itemId)`**: Fetches the full details for a specific knowledge base item.
    *   `itemId`: The ID of the item to fetch.
    *   Returns: `Promise<object|null>`

*   **`searchItems(query, options)`**: Searches the knowledge base using natural language queries or keywords, potentially leveraging semantic search via AI.
    *   `query`: The search query string.
    *   `options`: Search options (e.g., `{ semantic: true }`).
    *   Returns: `Promise<Array<object>>` (List of results with snippets/relevance).

*   **`searchAndSynthesize(query)`**: Performs a semantic search and then uses AI to synthesize an answer based on the results.
    *   `query`: The search query string.
    *   Returns: `Promise<{answer: string, sources: Array<string>} | null>`

*   **`askQuestion(question, itemIds, getItemDetailsFn?)`**: Answers questions based on the content of selected knowledge base item(s) using AI.
    *   `question`: The question to ask.
    *   `itemIds`: Array of item IDs to use as context.
    *   `getItemDetailsFn` (optional): A function to fetch item details. Defaults to the internal `getItemDetails`. This allows for dependency injection, primarily for testing.
    *   Returns: `Promise<{answer: string, sources: Array<string>}>`

*   **`summarizeItems(itemIds)`**: Generates an AI-powered summary of selected knowledge base item(s).
    *   `itemIds`: Array of item IDs to summarize.
    *   Returns: `Promise<string>` (Placeholder implementation).

*   **`transformContent(itemId, transformationType)`**: Transforms the content of an item using AI (e.g., extract facts, convert to bullet points).
    *   `itemId`: ID of the item to transform.
    *   `transformationType`: String indicating the desired transformation.
    *   Returns: `Promise<object|string>` (Placeholder implementation).

*   **`suggestLinks(itemId)`**: Suggests conceptual links between a selected item and others in the knowledge base, potentially using AI.
    *   `itemId`: ID of the item to find links for.
    *   Returns: `Promise<Array<{linkedItemId: string, justification: string}>>` (Placeholder implementation).
## Development Notes

### Technical Debt Addressed (May 2025)

Significant technical debt within this module was addressed, primarily focusing on implementing placeholder logic for initially deferred functionalities and ensuring basic test coverage. 
Key changes include updates to [`index.js`](src/knowledge-base-interaction/index.js:0) and the addition/modification of tests in [`__tests__/knowledgeBaseInteraction.test.js`](src/knowledge-base-interaction/__tests__/knowledgeBaseInteraction.test.js:0). 
A detailed analysis of the technical debt and the remediation steps can be found in the [Knowledge Base Interaction Tech Debt Analysis Report](docs/comprehension/knowledge_base_interaction_tech_debt_analysis.md:0).