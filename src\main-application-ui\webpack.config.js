const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
  mode: 'development', // Can be 'production' for builds
  entry: './renderer/index.js', // Entry point of your React app
  target: 'electron-renderer', // Important for Electron
  output: {
    path: path.resolve(__dirname, 'dist_renderer'), // Output directory
    filename: 'bundle.js', // Name of the bundled file
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/, // Transpile JS and JSX files
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
        },
      },
      {
        test: /\.css$/, // Process CSS files
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i, // Process image files
        type: 'asset/resource',
      },
    ],
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './index.html', // Use your existing index.html as a template
      filename: 'index.html', // Output HTML file name
    }),
  ],
  resolve: {
    extensions: ['.js', '.jsx'], // Automatically resolve these extensions
    modules: [path.resolve(__dirname, 'node_modules'), path.resolve(__dirname, '../../node_modules')],
  },
  devtool: 'inline-source-map', // For better debugging, remove for production
};
