import * as browser from 'webextension-polyfill'; // Use webextension-polyfill for cross-browser compatibility and types
import { Bookmark } from '../types/messaging'; // Assuming a types file exists or will be created
import { KnowledgeBaseService } from '@pkm-ai/knowledge-base-service';
import { KnowledgeBaseEntry } from '@pkm-ai/knowledge-base-service/dist/types'; // Assuming types are exported like this

// Instantiate the KnowledgeBaseService. In a larger app, this might be managed by a dependency injection system or a singleton pattern.
// For the extension, it will use the ChromeStorageLocalAdapter by default.
const kbService = new KnowledgeBaseService();

/**
 * Captures the current tab's URL and title as a bookmark.
 * @returns A Promise resolving with the Bookmark data.
 */
export async function captureBookmark(): Promise<Bookmark> {
  return new Promise((resolve, reject) => {
    browser.tabs.query({ active: true, currentWindow: true }).then((tabs: browser.Tabs.Tab[]) => {
      const activeTab = tabs[0];
      if (activeTab?.url && activeTab?.title) {
        resolve({
          url: activeTab.url,
          title: activeTab.title,
          timestamp: new Date().toISOString(),
        });
      } else {
        reject(new Error('Could not capture active tab URL or title.'));
      }
    }).catch(reject);
  });
}

/**
 * Mocks saving the captured bookmark data.
 * In a real implementation, this would interact with the lowdb service.
 * @param bookmark - The bookmark data to save.
 * @returns A Promise resolving when the mock save is complete.
 */
export async function saveCapturedContent(bookmark: Bookmark): Promise<KnowledgeBaseEntry> {
  console.log('Saving bookmark to Knowledge Base:', bookmark);
  // Integrate with the actual KnowledgeBaseService
  const newEntry: Omit<KnowledgeBaseEntry, 'id' | 'createdAt' | 'updatedAt'> = {
    type: 'bookmark', // Assuming 'bookmark' is a valid type in KnowledgeBaseEntry
    url: bookmark.url,
    title: bookmark.title,
    // Add other relevant fields from Bookmark or default values
    content: '', // Bookmarks typically don't have full content
    tags: [],
    // Add any other required fields from KnowledgeBaseEntry, potentially with default values
  };

  try {
    const createdEntry = await kbService.createEntry(newEntry);
    console.log('Bookmark saved successfully:', createdEntry);
    return createdEntry;
  } catch (error) {
    console.error('Failed to save bookmark to Knowledge Base:', error);
    throw error; // Re-throw the error to indicate failure
  }
}