{"compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["ES2022"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "declaration": true, "sourceMap": true, "composite": true, "declarationMap": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "__tests__"]}