# Primary Findings: Local-First Databases and Semantic Search (Part 3) - Impact of OPFS on Real-World Performance

OPFS and IndexedDB offer distinct approaches to browser storage, with performance characteristics and limitations that make them suitable for different scenarios.

## Performance Benchmarks
**OPFS** demonstrates superior throughput for sequential read/write operations when optimized. Autodesk's Viewer achieved significant performance gains by leveraging OPFS for GLB model caching, reducing latency through direct memory-mapped file access. However, naïve implementations storing each document as separate files can introduce overhead.

**IndexedDB** excels at structured data queries and transactional operations but suffers from higher latency for bulk operations. RxDB's benchmarks show OPFS-based storage plugins can outperform IndexedDB in write-heavy workloads when using single-file storage optimizations.

| Metric         | OPFS (Optimized) | IndexedDB      |
|----------------|------------------|----------------|
| Write Throughput | 50-100MB/s   | 5-20MB/s    |
| Read Latency    | <10ms         | 25-100ms    |
| Concurrent Ops  | Worker-limited   | Transaction-locked |

## Compatibility Issues
OPFS faces uneven browser support:
*   Chrome/Edge: Full support
*   Firefox: Partial (behind flag)
*   Safari: Requires iOS 16.4+

IndexedDB maintains near-universal support but has implementation quirks:
*   Safari's 1MB write transaction limit
*   Firefox's slower blob storage

## Limitations
**OPFS Constraints:**
*   No native indexing requires custom query layers
*   File handles become invalid after page reload
*   Complex synchronization between workers

**IndexedDB Drawbacks:**
*   60% storage quota utilization limit (~600MB)
*   Transactional locking blocks parallel writes
*   Index maintenance overhead during updates

## Optimization Strategies
RxDB's OPFS implementation shows several effective patterns:
1.  **Single-File Storage**: Storing all records in one file reduces I/O contention
2.  **Memory Mapping**: Combining OPFS with SharedArrayBuffer for zero-copy access
3.  **Hybrid Approaches**: Using LocalStorage for metadata alongside OPFS/IndexedDB

For extensions requiring large media caching (e.g., CAD viewers), OPFS provides better performance through direct file handle access. IndexedDB remains preferable for extensions needing complex query capabilities without custom indexing layers.