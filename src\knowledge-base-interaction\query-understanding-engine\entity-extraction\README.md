# Query Understanding Engine - Entity Extraction

This directory contains components dedicated to extracting key information (entities) from the user's query.

## Components:

-   **[`entityExtractor.js`](entityExtractor.js:1)**: Implements the logic for identifying and extracting various types of entities. This can include keywords, named entities (such as people, organizations, locations), dates, product names, or any other significant pieces of information relevant to the query and the domain. The methods employed might range from regular expressions and dictionary lookups to sophisticated Named Entity Recognition (NER) models.

## AI Verifiability:

-   Existence of this `README.md` file.
-   Existence of `entityExtractor.js`.

---
*AI-VERIFIABLE: README.md for QUE entity extraction component created.*