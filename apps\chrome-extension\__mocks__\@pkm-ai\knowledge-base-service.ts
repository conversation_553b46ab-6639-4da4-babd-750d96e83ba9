import { jest } from '@jest/globals';
// Use the aliased type from the actual package's dist output, similar to how components use it
import type { KnowledgeBaseEntry } from '@pkm-ai/knowledge-base-service/dist/types';

// Explicitly type the mock function
export const mockGetAllEntries: jest.MockedFunction<() => Promise<KnowledgeBaseEntry[]>> = jest.fn();

export class KnowledgeBaseService {
  constructor() {
    // Constructor can be a simple mock or do nothing
  }
  // Ensure the method signature matches the original class if needed for type checking
  async getAllEntries(): Promise<KnowledgeBaseEntry[]> {
    return mockGetAllEntries();
  }
  // Add other methods if they are called and need mocking
}

// To help with type inference for the mock itself if needed elsewhere
// This export might not be necessary if the default export of the class is what's used.
// export const MockedKnowledgeBaseService = KnowledgeBaseService as jest.MockedClass<typeof KnowledgeBaseService>;