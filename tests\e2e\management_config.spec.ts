import { test, expect, Page, BrowserContext } from '@playwright/test';
import { ExtensionHelper } from './helpers/extension-helper'; // Adjust path as needed

let context: BrowserContext;
let helper: ExtensionHelper;
let optionsPage: Page;

// Helper function to navigate to the options page and then to the settings tab
async function navigateToSettingsTab(page: Page) {
  // The page provided should already be the options page
  // Wait for the options page to load, specifically the navigation buttons
  await expect(page.getByRole('button', { name: 'Knowledge Base' })).toBeVisible({ timeout: 10000 }); // Increased timeout
  await expect(page.getByRole('button', { name: 'Settings' })).toBeVisible();

  // Click the "Settings" button to navigate to the SettingsPage
  await page.getByRole('button', { name: 'Settings' }).click();
  // Wait for the SettingsPage content to be visible
  await expect(page.getByRole('heading', { name: 'Capture Settings' })).toBeVisible();
}

test.beforeAll(async () => {
  helper = new ExtensionHelper();
  context = await helper.launchBrowser();
});

test.afterAll(async () => {
  await helper.closeBrowser();
});

test.beforeEach(async () => {
  optionsPage = await helper.openOptionsPage();
});

test.afterEach(async () => {
  if (optionsPage && !optionsPage.isClosed()) {
    await optionsPage.close();
  }
});


test.describe('Management & Configuration Module E2E Tests', () => {
  test('should navigate to the settings page from the options page', async () => {
    await navigateToSettingsTab(optionsPage);
    // The navigateToSettingsTab function already asserts visibility of the "Capture Settings" heading.
    // We can add another assertion specific to the settings page content if needed.
    await expect(optionsPage.getByText('Default Capture Mode:')).toBeVisible();
  });

  test('should display mock default settings correctly on the settings page', async () => {
    await navigateToSettingsTab(optionsPage);

    // Verify Default Capture Mode
    await expect(optionsPage.getByText('Default Capture Mode:')).toBeVisible();
    // Check the value associated with "Default Capture Mode:"
    // Assuming the label and value are in a structure where the value is a sibling or near the label.
    // A more robust locator might be needed if the DOM structure is complex.
    // For now, we'll check for the text "Full Page" within the same container or nearby.
    // Let's assume the structure is <div key="captureMode"><span>Label</span><span>Value</span></div>
    const captureModeSetting = optionsPage.locator('div:has-text("Default Capture Mode:")');
    await expect(captureModeSetting.getByText('Full Page')).toBeVisible();

    // Verify Preferred Content Format
    await expect(optionsPage.getByText('Preferred Content Format:')).toBeVisible();
    const contentFormatSetting = optionsPage.locator('div:has-text("Preferred Content Format:")');
    await expect(contentFormatSetting.getByText('Markdown')).toBeVisible();
  });
});