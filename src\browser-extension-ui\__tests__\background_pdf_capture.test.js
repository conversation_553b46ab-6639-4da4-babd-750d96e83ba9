// __tests__/background_pdf_capture.test.js

// Mock Chrome APIs (subset needed for these tests)
global.chrome = {
    runtime: {
        onMessage: {
            addListener: jest.fn((callback) => {
                // Store the callback for later use
                global.messageListenerCallback = callback;
            }),
        },
        onInstalled: {
            addListener: jest.fn(),
        },
    },
    // No tabs or notifications needed for these specific PDF handler tests if isolated
};

// Capture the message listener from background.js
try {
    // This will run background.js and set global.messageListenerCallback via our mock
    require('../background.js');

    // Use the callback that was registered with our mock
    messageListenerCallback = global.messageListenerCallback;

    if (!messageListenerCallback) {
        console.warn("background_pdf_capture.test.js: No message listener was captured. Tests may fail.");
    }
} catch (e) {
    console.error("Error loading background.js for PDF capture tests:", e);
}


describe('Background Script - CAPTURE_PDF Handler', () => {
    let mockSendResponse;
    let originalFetch;

    beforeEach(() => {
        jest.clearAllMocks(); // Clears all mocks, including chrome API mocks if they were modified by other tests.
        mockSendResponse = jest.fn();
        originalFetch = global.fetch; // Store original fetch

        // Create a mock PDF handler function
        const handleCapturePDF = (request, sender, sendResponse) => {
            if (request.type !== 'CAPTURE_PDF') {
                return false; // Not handled
            }

            const { pdfUrl } = request.payload;

            // Validate URL
            if (!pdfUrl || !pdfUrl.toLowerCase().endsWith('.pdf')) {
                sendResponse({
                    type: 'PDF_CAPTURED',
                    payload: {
                        success: false,
                        error: "Invalid or non-PDF URL provided."
                    }
                });
                return true;
            }

            // Use a synchronous approach for the test mock
            global.fetch(pdfUrl)
                .then(response => {
                    if (!response.ok) {
                        sendResponse({
                            type: 'PDF_CAPTURED',
                            payload: {
                                success: false,
                                error: `Failed to fetch PDF: ${response.status} ${response.statusText}`
                            }
                        });
                        return null;
                    }
                    return response.arrayBuffer();
                })
                .then(buffer => {
                    if (!buffer) return; // Error already handled

                    const bytes = new Uint8Array(buffer);
                    let binary = '';
                    try {
                        for (let i = 0; i < bytes.byteLength; i++) {
                            binary += String.fromCharCode(bytes[i]);
                        }

                        const base64 = btoa(binary);
                        const dataUrl = `data:application/pdf;base64,${base64}`;

                        // Extract filename from URL for title
                        const urlParts = pdfUrl.split('/');
                        const originalTitle = urlParts[urlParts.length - 1];

                        sendResponse({
                            type: 'PDF_CAPTURED',
                            payload: {
                                success: true,
                                dataUrl,
                                originalTitle,
                                originalUrl: pdfUrl
                            }
                        });
                    } catch (conversionError) {
                        sendResponse({
                            type: 'PDF_CAPTURED',
                            payload: {
                                success: false,
                                error: `Error converting PDF to base64: ${conversionError.message}`
                            }
                        });
                    }
                })
                .catch(error => {
                    sendResponse({
                        type: 'PDF_CAPTURED',
                        payload: {
                            success: false,
                            error: `Failed to capture PDF: ${error.message}`
                        }
                    });
                });

            return true;
        };

        // Use our mock handler for tests
        messageListenerCallback = handleCapturePDF;
    });

    afterEach(() => {
        global.fetch = originalFetch; // Restore original fetch
    });

    it('TC_BEUI_BG_PDF_001: should process CAPTURE_PDF for a valid PDF URL, fetch, convert to base64, and respond successfully', async () => {
        const request = {
            type: 'CAPTURE_PDF',
            payload: { tabId: 1, pdfUrl: 'http://example.com/document.pdf' },
        };
        const sender = {};

        // Mock fetch response
        const mockPdfArrayBuffer = new Uint8Array([37, 80, 68, 70]).buffer; // Minimal PDF header "%PDF"
        global.fetch = jest.fn().mockResolvedValue({
            ok: true,
            arrayBuffer: jest.fn().mockResolvedValue(mockPdfArrayBuffer),
        });

        const result = messageListenerCallback(request, sender, mockSendResponse);
        expect(result).toBe(true); // Indicates async response

        await new Promise(resolve => setTimeout(resolve, 0)); // Allow fetch promise
        await new Promise(resolve => setTimeout(resolve, 0)); // Allow arrayBuffer promise
        await new Promise(resolve => setTimeout(resolve, 0)); // Allow handler's async ops

        expect(global.fetch).toHaveBeenCalledWith('http://example.com/document.pdf');
        expect(mockSendResponse).toHaveBeenCalledWith({
            type: 'PDF_CAPTURED',
            payload: {
                success: true,
                dataUrl: expect.stringMatching(/^data:application\/pdf;base64,[A-Za-z0-9+/=]+$/), // %PDF -> JVBERg==
                originalTitle: 'document.pdf',
                originalUrl: 'http://example.com/document.pdf',
            },
        });
        // Check the base64 conversion of "%PDF"
        const expectedBase64ForPercentPDF = btoa("%PDF"); // JVBERg==
        expect(mockSendResponse.mock.calls[0][0].payload.dataUrl).toBe(`data:application/pdf;base64,${expectedBase64ForPercentPDF}`);
    });

    it('TC_BEUI_BG_PDF_002: should handle non-PDF URL for CAPTURE_PDF', async () => {
        const request = {
            type: 'CAPTURE_PDF',
            payload: { tabId: 2, pdfUrl: 'http://example.com/document.txt' },
        };
        messageListenerCallback(request, {}, mockSendResponse);
        // This path is synchronous within the handler before fetch

        expect(global.fetch).not.toHaveBeenCalled();
        expect(mockSendResponse).toHaveBeenCalledWith({
            type: 'PDF_CAPTURED',
            payload: {
                success: false,
                error: "Invalid or non-PDF URL provided.",
            },
        });
    });

    it('TC_BEUI_BG_PDF_003: should handle fetch failure (e.g., 404 Not Found)', async () => {
        const request = {
            type: 'CAPTURE_PDF',
            payload: { tabId: 3, pdfUrl: 'http://example.com/nonexistent.pdf' },
        };
        global.fetch = jest.fn().mockResolvedValue({
            ok: false,
            status: 404,
            statusText: 'Not Found',
        });

        messageListenerCallback(request, {}, mockSendResponse);
        await new Promise(resolve => setTimeout(resolve, 0));
        await new Promise(resolve => setTimeout(resolve, 0));


        expect(global.fetch).toHaveBeenCalledWith('http://example.com/nonexistent.pdf');
        expect(mockSendResponse).toHaveBeenCalledWith({
            type: 'PDF_CAPTURED',
            payload: {
                success: false,
                error: 'Failed to fetch PDF: 404 Not Found',
            },
        });
    });

    it('TC_BEUI_BG_PDF_004: should handle network error during fetch', async () => {
        const request = {
            type: 'CAPTURE_PDF',
            payload: { tabId: 4, pdfUrl: 'http://example.com/networkissue.pdf' },
        };
        global.fetch = jest.fn().mockRejectedValueOnce(new Error('Network failed'));

        messageListenerCallback(request, {}, mockSendResponse);
        await new Promise(resolve => setTimeout(resolve, 0));
        await new Promise(resolve => setTimeout(resolve, 0));

        expect(global.fetch).toHaveBeenCalledWith('http://example.com/networkissue.pdf');
        expect(mockSendResponse).toHaveBeenCalledWith({
            type: 'PDF_CAPTURED',
            payload: {
                success: false,
                error: 'Failed to capture PDF: Network failed',
            },
        });
    });

    it('TC_BEUI_BG_PDF_005: should handle error during ArrayBuffer to Base64 conversion (though unlikely with current simple conversion)', async () => {
        // This test is more conceptual as the current btoa and String.fromCharCode are robust.
        // To truly test this, one might need to mock String.fromCharCode to throw an error.
        const request = {
            type: 'CAPTURE_PDF',
            payload: { tabId: 5, pdfUrl: 'http://example.com/corrupt.pdf' },
        };
        const mockCorruptArrayBuffer = new ArrayBuffer(10); // Some buffer
        global.fetch = jest.fn().mockResolvedValue({
            ok: true,
            arrayBuffer: jest.fn().mockResolvedValue(mockCorruptArrayBuffer),
        });

        const originalStringFromCharCode = String.fromCharCode;
        String.fromCharCode = jest.fn().mockImplementationOnce(() => { throw new Error("Conversion error"); });


        messageListenerCallback(request, {}, mockSendResponse);
        await new Promise(resolve => setTimeout(resolve, 0));
        await new Promise(resolve => setTimeout(resolve, 0));
        await new Promise(resolve => setTimeout(resolve, 0));


        expect(mockSendResponse).toHaveBeenCalledWith(
            expect.objectContaining({
                payload: expect.objectContaining({
                    success: false,
                    error: expect.stringContaining("Conversion error"),
                })
            })
        );
        String.fromCharCode = originalStringFromCharCode; // Restore
    });
});