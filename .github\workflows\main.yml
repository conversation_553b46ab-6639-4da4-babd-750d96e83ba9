name: CI/CD

on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install Dependencies
        run: npm install
      - name: Run Lint
        run: npm run lint
      - name: Run Tests
        run: npm test
  deploy:
    runs-on: ubuntu-latest
    needs: build
    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Build Application
        run: npm run build
      - name: Configure Environment Variables
        run: |
          # This approach of echoing secrets into a .env file is not ideal for production.
          # Consider using a more secure method for handling environment variables in production,
          # such as setting them directly in your hosting provider's dashboard.
          echo "API_URL=${{ secrets.API_URL }}" >> .env
          echo "ANALYTICS_ID=${{ secrets.ANALYTICS_ID }}" >> .env
      - name: Deploy
        env:
          CHROME_PRIVATE_KEY: ${{ secrets.CHROME_PRIVATE_KEY }}
          CHROME_CLIENT_ID: ${{ secrets.CHROME_CLIENT_ID }}
          CHROME_CLIENT_SECRET: ${{ secrets.CHROME_CLIENT_SECRET }}
          CHROME_REFRESH_TOKEN: ${{ secrets.CHROME_REFRESH_TOKEN }}
          CHROME_EXTENSION_ID: ${{ secrets.CHROME_EXTENSION_ID }}
        run: chmod +x deploy.sh && ./deploy.sh