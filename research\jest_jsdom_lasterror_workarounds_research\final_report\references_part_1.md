# References - Part 1

This document lists the sources referenced throughout this research report on Jest/JSDOM `chrome.runtime.lastError` workarounds.

[1] Jest Documentation: Jest Environment. (n.d.). Retrieved from [https://jestjs.io/docs/configuration#testenvironment-string](https://jestjs.io/docs/configuration#testenvironment-string)
[2] jest-jsdom-browser-compatibility: Test cases for jsdom vs browser compatibility. (n.d.). GitHub. Retrieved from [https://github.com/mattphillips/jest-jsdom-browser-compatibility](https://github.com/mattphillips/jest-jsdom-browser-compatibility)
[3] How to add unit tests to a Chrome Extension with Jest. (n.d.). Retrieved from [https://medium.com/@janmolak/how-to-add-unit-tests-to-a-chrome-extension-with-jest-b1007216554a](https://medium.com/@janmolak/how-to-add-unit-tests-to-a-chrome-extension-with-jest-b1007216554a)
[4] Testing Chrome Extensions with Jest. (n.d.). Retrieved from [https://blog.logrocket.com/testing-chrome-extensions-jest/](https://blog.logrocket.com/testing-chrome-extensions-jest/)
[5] chrome.runtime.lastError - Mozilla | MDN. (n.d.). Retrieved from [https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/API/runtime/lastError](https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/API/runtime/lastError)
[Blueprint] diagnosis_reports/web_content_capture_ui_popup_init_lastError_diagnosis_v1.md (Provided by user)