# Primary Findings: Local-First Databases and Semantic Search (Part 2) - Scalability of Local Vector Databases

Local vector database solutions like Faiss.js and SQLite with extensions (e.g., `sqlite-vec`) exhibit distinct scalability tradeoffs for browser-based large knowledge bases, particularly in indexing strategies and data partitioning.

### Scalability of Faiss.js
1.  **Strengths**:
    *   **High-performance KNN queries**: Optimized for low-latency nearest-neighbor searches using GPU-accelerated indexing structures like IVF (Inverted File Index) and HNSW (Hierarchical Navigable Small World).
    *   **Batch processing**: Efficient for static datasets with infrequent updates, as Faiss indexes are built for "write once, query often" workflows.

2.  **Limitations**:
    *   **In-memory constraints**: Faiss.js requires entire datasets to reside in memory, making it unsuitable for knowledge bases exceeding browser memory limits (~4GB for most extensions).
    *   **Incremental insert bottlenecks**: Adding vectors incrementally triggers expensive index rebuilds, with insertion times scaling linearly with dataset size (e.g., 10k vectors take ~1s, 100k vectors ~10s).

### Scalability of SQLite with Extensions
1.  **Strengths**:
    *   **Disk-based storage**: `sqlite-vec` stores vectors in SQLite tables, enabling datasets larger than available memory by leveraging the browser's persistent storage API.
    *   **OLTP-friendly writes**: Appends occur in constant time (O(1)) since vectors are stored as BLOBs in row-oriented tables, avoiding full index rebuilds.
    *   **Partitioning support**: Native sharding via `ATTACH DATABASE` allows splitting knowledge bases across multiple .sqlite files while maintaining transactional consistency.

2.  **Limitations**:
    *   **Query latency tradeoff**: KNN searches in `sqlite-vec` are ~2–5x slower than Faiss for datasets under 100k vectors due to lack of GPU acceleration and advanced indexing.
    *   **Vector compression**: Unlike Faiss's PQ (Product Quantization), SQLite extensions often lack built-in compression, increasing storage overhead for high-dimensional embeddings.

### Indexing Strategies for Large Knowledge Bases
| Technique          | Faiss.js                          | SQLite + Extensions               |
|--------------------|-----------------------------------|------------------------------------|
| **Index Type**     | IVF, HNSW, PQ                 | Brute-force, IVF via extensions |
| **Memory Use**     | Entire dataset in RAM         | Disk-backed with LRU caching   |
| **Update Support** | Full rebuild required         | Incremental inserts           |

**Example**: A browser extension using SQLite-vec could handle user annotations that are added continuously, while Faiss.js could be used for periodic background indexing of those embeddings for faster search. However, Faiss's memory usage might limit the dataset size unless data is partitioned effectively.

### Data Partitioning Techniques
1.  **Dimension-based sharding**: Split vectors by feature space regions (e.g., using Faiss's `index_factory` to create partitioned IVF indices).
2.  **Time-series partitioning**: In SQLite, use `CHECK` constraints to segregate vectors into tables by timestamp, enabling efficient range queries.
3.  **Hybrid approaches**: Store raw vectors in SQLite for durability and incremental updates, while maintaining Faiss.js indexes for hot subsets in IndexedDB.

**Case Study**: A research assistant extension uses SQLite-vec to log daily user annotations (OLTP), then nightly exports embeddings to Faiss.js for fast semantic search (OLAP). Partitioning by date ensures Faiss indexes stay under memory limits.

### Performance Optimization
*   **Faiss.js**: Use `PQ8` quantization to reduce memory footprint by 4×, with minimal accuracy loss.
*   **SQLite**: Enable `PRAGMA mmap_size` to map vector BLOBs directly from disk, avoiding costly deserialization.

### Conclusion
For browser extensions prioritizing **write scalability** and **durability**, SQLite with extensions is superior, especially when paired with partitioning. Faiss.js excels in **read-heavy** scenarios but requires careful memory management. Hybrid architectures combining both tools (e.g., SQLite for storage + Faiss for caching) often provide the best balance for large knowledge bases.