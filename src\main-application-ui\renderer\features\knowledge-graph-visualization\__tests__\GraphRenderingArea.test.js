import React from 'react';
import { render, screen, act } from '@testing-library/react'; // Added act
import GraphRenderingArea from '../components/GraphRenderingArea';
// Import the actual helper to verify style mapping, if possible, or replicate logic
// For simplicity here, we'll check for key aspects of the style structure.

// --- Cytoscape Mock Setup ---
const mockCyInstanceRun = jest.fn();
const mockCyInstanceLayout = jest.fn().mockReturnValue({ run: mockCyInstanceRun });
const mockCyInstanceZoom = jest.fn(() => 1.0); // Default zoom level
const mockCyInstancePan = jest.fn(() => ({ x: 0, y: 0 })); // Default pan state
const mockCyInstanceJson = jest.fn();
const mockCyInstanceStyle = jest.fn();
const mockCyInstanceOn = jest.fn();
const mockCyInstanceDestroy = jest.fn();
const mockCyInstanceElements = jest.fn().mockReturnThis(); // For chaining if used
const mockCyInstanceFilter = jest.fn().mockReturnThis(); // For chaining if used

const mockCyInstance = {
  json: mockCyInstanceJson,
  style: mockCyInstanceStyle,
  layout: mockCyInstanceLayout,
  elements: mockCyInstanceElements,
  filter: mockCyInstanceFilter,
  on: mockCyInstanceOn,
  zoom: mockCyInstanceZoom,
  pan: mockCyInstancePan,
  destroy: mockCyInstanceDestroy,
  _eventHandlers: {}, // Internal store for registered event handlers
};

// Mock implementation for 'on' to store handlers
mockCyInstance.on.mockImplementation((eventTypes, selectorOrHandler, handlerFn) => {
  const events = eventTypes.split(' ');
  let selector, handler;
  if (typeof selectorOrHandler === 'string') {
    selector = selectorOrHandler;
    handler = handlerFn;
  } else {
    selector = undefined; // Event on core
    handler = selectorOrHandler;
  }
  events.forEach(event => {
    if (!mockCyInstance._eventHandlers[event]) {
      mockCyInstance._eventHandlers[event] = [];
    }
    mockCyInstance._eventHandlers[event].push({ selector, handler });
  });
});

jest.mock('cytoscape', () => jest.fn(() => mockCyInstance));

// Test utility to simulate Cytoscape events
// Test utility to simulate Cytoscape events
const simulateCyEvent = (eventType, arg2, arg3) => { // Renamed parameters for clarity
  if (!mockCyInstance._eventHandlers || !mockCyInstance._eventHandlers[eventType]) {
    // console.warn(`No handlers registered for event type: ${eventType}`);
    return;
  }

  let targetSelector;
  let eventData;

  if (typeof arg2 === 'string') { // arg2 is selector, arg3 is eventData
    targetSelector = arg2;
    eventData = arg3;
  } else { // arg2 is eventData (for core events), arg3 should be undefined or ignored
    targetSelector = undefined;
    eventData = arg2; // arg2 is the event object itself
  }

  mockCyInstance._eventHandlers[eventType].forEach(registeredEvent => {
    // Match selector if one is provided for the handler and for the simulation
    const selectorMatches = (!registeredEvent.selector && !targetSelector) || (registeredEvent.selector === targetSelector);
    if (selectorMatches) {
      registeredEvent.handler(eventData);
    }
  });
};

const resetCytoscapeMock = () => {
  const cytoscapeConstructor = require('cytoscape');
  cytoscapeConstructor.mockClear();
  cytoscapeConstructor.mockImplementation(() => mockCyInstance); // Ensure it always returns our mock instance

  mockCyInstanceJson.mockClear();
  mockCyInstanceStyle.mockClear();
  mockCyInstanceLayout.mockClear();
  mockCyInstanceRun.mockClear();
  mockCyInstanceElements.mockClear();
  mockCyInstanceFilter.mockClear();
  mockCyInstanceOn.mockClear(); // Clears call history of the mockImplementation itself
  mockCyInstanceZoom.mockClear();
  mockCyInstancePan.mockClear();
  mockCyInstanceDestroy.mockClear();

  // Reset default return values for methods that might be changed in tests
  mockCyInstanceZoom.mockReturnValue(1.0);
  mockCyInstancePan.mockReturnValue({ x: 0, y: 0 });

  mockCyInstance._eventHandlers = {}; // Crucially, clear stored handlers
};
// --- End Cytoscape Mock Setup ---

// Mock data for nodes and edges
const mockNodes = [
  { id: 'n1', label: 'Node 1', type: 'typeA', attributes: { detail: 'Detail A' } },
  { id: 'n2', label: 'Node 2', type: 'typeB', attributes: { detail: 'Detail B' } },
  { id: 'n3', label: 'Node 3', type: 'typeA', attributes: { detail: 'Detail C' } },
];

const mockEdges = [
  { id: 'e1', source: 'n1', target: 'n2', type: 'relX', attributes: { strength: 5 } },
  { id: 'e2', source: 'n2', target: 'n3', type: 'relY', attributes: { strength: 3 } },
];

const mockGraphData = {
  nodes: mockNodes,
  edges: mockEdges,
};

// Mock props
let mockProps;

// Helper to map visual encodings to expected Cytoscape style structure for assertions
// This is a simplified version of the component's internal mapEncodingsToStyle
const expectStyleFromEncodings = (visualEncodings) => {
  const expectedStyles = [];
  if (visualEncodings && visualEncodings.nodeTypes) {
    Object.entries(visualEncodings.nodeTypes).forEach(([typeId, encoding]) => {
      expectedStyles.push(
        expect.objectContaining({
          selector: `node[type = "${typeId}"]`,
          style: expect.objectContaining({
            'background-color': encoding.color || '#666',
            'shape': encoding.shape || 'ellipse',
          }),
        })
      );
    });
  } else {
     expectedStyles.push(expect.objectContaining({ selector: 'node' }));
  }
  if (visualEncodings && visualEncodings.edgeTypes) {
    Object.entries(visualEncodings.edgeTypes).forEach(([typeId, encoding]) => {
      expectedStyles.push(
        expect.objectContaining({
          selector: `edge[type = "${typeId}"]`,
          style: expect.objectContaining({
            'line-color': encoding.color || '#ccc',
            'width': encoding.thickness || 2,
          }),
        })
      );
    });
  } else {
    expectedStyles.push(expect.objectContaining({ selector: 'edge' }));
  }
  return expect.arrayContaining(expectedStyles);
};


describe('GraphRenderingArea Component', () => {
  beforeEach(() => {
    resetCytoscapeMock(); // Reset cytoscape mocks

    // Initialize mockProps before each test
    mockProps = {
      graphData: JSON.parse(JSON.stringify(mockGraphData)), // Deep copy to avoid test interference
      layout: 'force-directed',
      selectedNodeIds: [],
      selectedEdgeIds: [],
      onNodeSelect: jest.fn(),
      onEdgeSelect: jest.fn(),
      onCanvasInteraction: jest.fn(),
      visualEncodings: JSON.parse(JSON.stringify({ // Deep copy
        nodeTypes: {
          typeA: { color: 'blue', shape: 'circle', size: 10 },
          typeB: { color: 'green', shape: 'square', size: 12 },
        },
        edgeTypes: {
          relX: { color: 'gray', thickness: 2, style: 'solid' },
          relY: { color: 'red', thickness: 1, style: 'dashed' },
        },
      })),
    };
  });

  test('TC_KGV_GRA_001: should render without crashing and initialize Cytoscape', () => {
    render(<GraphRenderingArea {...mockProps} />);
    expect(screen.getByTestId('graph-rendering-area-actual')).toBeInTheDocument();
    expect(require('cytoscape')).toHaveBeenCalledTimes(1);
    expect(require('cytoscape')).toHaveBeenCalledWith(expect.objectContaining({
      container: expect.any(HTMLDivElement),
    }));
  });

  test('TC_KGV_GR_001: should pass nodes and edges to Cytoscape on initial render', () => {
    render(<GraphRenderingArea {...mockProps} />);
    const expectedElements = {
      nodes: mockGraphData.nodes.map(node => ({ data: { ...node } })),
      edges: mockGraphData.edges.map(edge => ({ data: { ...edge } })),
    };
    expect(require('cytoscape')).toHaveBeenCalledWith(expect.objectContaining({
      elements: expectedElements,
    }));
  });
  
  test('TC_KGV_GR_001: (Update) should update Cytoscape elements when graphData prop changes', () => {
    const { rerender } = render(<GraphRenderingArea {...mockProps} />);
    const newNodes = [{ id: 'n4', label: 'Node 4', type: 'typeC' }];
    const newGraphData = { nodes: newNodes, edges: [] };
    
    act(() => {
      rerender(<GraphRenderingArea {...mockProps} graphData={newGraphData} />);
    });

    const expectedElements = {
      nodes: newNodes.map(node => ({ data: { ...node } })),
      edges: [].map(edge => ({ data: { ...edge } })),
    };
    expect(mockCyInstance.json).toHaveBeenCalledWith({ elements: expectedElements });
  });


  test('TC_KGV_GR_004: should apply initial layout and re-apply on layout prop change', () => {
    // Initial layout
    const { rerender } = render(<GraphRenderingArea {...mockProps} layout="grid" />); // Initial render
    // Check that the initial layout was passed to the cytoscape constructor options
    expect(require('cytoscape')).toHaveBeenCalledWith(expect.objectContaining({
      layout: expect.objectContaining({ name: 'grid' }), // More specific check for layout object
    }));
    // At this point, the component's internal useEffect for layout might have also run.
    // We clear mocks to specifically test the prop update effect.
    mockCyInstanceLayout.mockClear();
    mockCyInstanceRun.mockClear();

    // Change layout prop using rerender from the first render
    act(() => {
      rerender(<GraphRenderingArea {...mockProps} layout="circle" />);
    });
    expect(mockCyInstanceLayout).toHaveBeenCalledWith(expect.objectContaining({ name: 'circle' }));
    expect(mockCyInstanceRun).toHaveBeenCalled();
    
    mockCyInstanceLayout.mockClear();
    mockCyInstanceRun.mockClear();

    act(() => {
      rerender(<GraphRenderingArea {...mockProps} layout="cose" />);
    });
    
    expect(mockCyInstanceLayout).toHaveBeenCalledWith({ name: 'cose' });
    expect(mockCyInstanceRun).toHaveBeenCalled();
  });

  test('TC_KGV_GR_007 & TC_KGV_GR_008: should apply visual encodings on initial load and update', () => {
    render(<GraphRenderingArea {...mockProps} />);
    // Check style on initialization
    expect(require('cytoscape')).toHaveBeenCalledWith(expect.objectContaining({
      style: expectStyleFromEncodings(mockProps.visualEncodings),
    }));

    // Check style update
    const { rerender } = render(<GraphRenderingArea {...mockProps} />); // Rerender to get instance
    const newVisualEncodings = {
      nodeTypes: { typeC: { color: 'purple', shape: 'diamond' } },
      edgeTypes: { relZ: { color: 'orange', thickness: 3 } },
    };
    mockCyInstanceStyle.mockClear(); // Clear previous calls from init

    act(() => {
      rerender(<GraphRenderingArea {...mockProps} visualEncodings={newVisualEncodings} />);
    });
    
    expect(mockCyInstance.style).toHaveBeenCalledWith(expectStyleFromEncodings(newVisualEncodings));
  });

  test('TC_KGV_CI_001 & TC_KGV_CI_002: should handle zoom interactions via Cytoscape events', () => {
    render(<GraphRenderingArea {...mockProps} />);
    
    mockCyInstanceZoom.mockReturnValue(1.5); // Simulate zoom level change
    mockCyInstancePan.mockReturnValue({ x: 10, y: 20 }); // Simulate pan state

    act(() => {
      simulateCyEvent('zoom', { type: 'zoom' }); // Pass event object as second arg for core events
    });

    expect(mockProps.onCanvasInteraction).toHaveBeenCalledWith({
      type: 'zoom',
      level: 1.5,
      pan: { x: 10, y: 20 },
    });
  });

  test('TC_KGV_CI_003: should handle pan interactions via Cytoscape events', () => {
    render(<GraphRenderingArea {...mockProps} />);

    mockCyInstanceZoom.mockReturnValue(1.2);
    mockCyInstancePan.mockReturnValue({ x: 50, y: 60 });

    act(() => {
      simulateCyEvent('pan', { type: 'pan' }); // Pass event object as second arg for core events
    });

    expect(mockProps.onCanvasInteraction).toHaveBeenCalledWith({
      type: 'pan',
      level: 1.2,
      pan: { x: 50, y: 60 },
    });
  });

  test('TC_KGV_CI_004: should handle single node selection via Cytoscape tap event', () => {
    render(<GraphRenderingArea {...mockProps} />);
    
    const mockNodeTapEvent = { target: { id: () => 'n1' } };
    act(() => {
      simulateCyEvent('tap', 'node', mockNodeTapEvent);
    });

    expect(mockProps.onNodeSelect).toHaveBeenCalledWith(['n1']);
  });

  test('should handle single edge selection via Cytoscape tap event', () => {
    render(<GraphRenderingArea {...mockProps} />);
    
    const mockEdgeTapEvent = { target: { id: () => 'e1' } };
     act(() => {
      simulateCyEvent('tap', 'edge', mockEdgeTapEvent);
    });

    expect(mockProps.onEdgeSelect).toHaveBeenCalledWith('e1');
  });
  
  test('TC_KGV_GR_009: should render disconnected components/isolated nodes correctly', () => {
    const disconnectedData = {
      nodes: [
        { id: 'iso1', label: 'Isolated 1' },
        { id: 'iso2', label: 'Isolated 2' },
      ],
      edges: [],
    };
    render(<GraphRenderingArea {...mockProps} graphData={disconnectedData} />);
    const expectedElements = {
      nodes: disconnectedData.nodes.map(node => ({ data: { ...node } })),
      edges: [],
    };
    expect(require('cytoscape')).toHaveBeenCalledWith(expect.objectContaining({
      elements: expectedElements,
    }));
  });

  test('should handle empty graphData gracefully', () => {
    const emptyData = { nodes: [], edges: [] };
    render(<GraphRenderingArea {...mockProps} graphData={emptyData} />);
    const expectedElements = { nodes: [], edges: [] };
    expect(require('cytoscape')).toHaveBeenCalledWith(expect.objectContaining({
      elements: expectedElements,
    }));
    // Ensure no errors are thrown and component renders
    expect(screen.getByTestId('graph-rendering-area-actual')).toBeInTheDocument();
  });

  test('KGV-SEC-001: should sanitize HTML in node and edge labels before passing to Cytoscape', () => {
    const xssNodePayload = "<b id='xss_test_marker'>XSS Node</b>";
    const sanitizedNodePayload = "XSS Node";
    const xssEdgePayload = "Edge with <script>alert(1)</script>";
    const sanitizedEdgePayload = "Edge with ";

    const maliciousGraphData = {
      nodes: [
        { id: 'n1', label: xssNodePayload, type: 'typeA' },
        { id: 'n2', label: 'Safe Node', type: 'typeB' },
      ],
      edges: [{ id: 'e1', source: 'n1', target: 'n2', label: xssEdgePayload }],
    };

    render(<GraphRenderingArea {...mockProps} graphData={maliciousGraphData} />);

    // Verify that the sanitized (plain text) version of the label is passed to Cytoscape
    const cytoscapeMock = require('cytoscape');
    const lastCallArgs = cytoscapeMock.mock.calls[cytoscapeMock.mock.calls.length - 1][0];
    
    const passedNodes = lastCallArgs.elements.nodes;
    const passedEdges = lastCallArgs.elements.edges;

    const nodeN1 = passedNodes.find(n => n.data.id === 'n1');
    const nodeN2 = passedNodes.find(n => n.data.id === 'n2');
    const edgeE1 = passedEdges.find(e => e.data.id === 'e1');

    expect(nodeN1.data.label).toBe(sanitizedNodePayload);
    expect(nodeN2.data.label).toBe('Safe Node');
    expect(edgeE1.data.label).toBe(sanitizedEdgePayload);

    // Also check the overall structure if needed, focusing on the labels
    expect(cytoscapeMock).toHaveBeenCalledWith(
      expect.objectContaining({
        elements: expect.objectContaining({
          nodes: expect.arrayContaining([
            expect.objectContaining({ data: expect.objectContaining({ id: 'n1', label: sanitizedNodePayload }) }),
            expect.objectContaining({ data: expect.objectContaining({ id: 'n2', label: 'Safe Node' }) }),
          ]),
          edges: expect.arrayContaining([
            expect.objectContaining({ data: expect.objectContaining({ id: 'e1', label: sanitizedEdgePayload }) })
          ])
        }),
      })
    );
  });

  describe('Advanced Label Sanitization (KGV-SEC-001 - New Iteration 3 - Cycle 2)', () => {
    const testCases = [
      // Malformed HTML and event handlers
      {
        name: 'should not properly sanitize malformed img tag with onerror',
        input: "Node <img src=x onerror=alert('XSS1')//>",
        expectedOutputAfterCurrentRegex: "Node alert('XSS1')//>", // Current regex removes <img src=x onerror=>
        expectedOutputAfterRobustSanitization: "Node ", // DOMPurify with no allowed tags strips the img tag and its content
      },
      {
        name: 'should not properly sanitize script tag with mixed case',
        input: "Text <ScRiPt>alert('XSS2')</ScRiPt> End",
        expectedOutputAfterCurrentRegex: "Text alert('XSS2') End",
        expectedOutputAfterRobustSanitization: "Text  End", // DOMPurify removes script tags AND their content
      },
      {
        name: 'should not properly sanitize unclosed tag with event handler',
        input: "Another <img src=x onerror=alert('XSS3')",
        expectedOutputAfterCurrentRegex: "Another alert('XSS3')", // Current regex removes <img src=x onerror=>
        expectedOutputAfterRobustSanitization: "Another ", // DOMPurify with no allowed tags strips the img tag
      },
      {
        name: 'should not properly sanitize javascript href',
        input: 'Link <a href="javascript:alert(\'XSS4\')">click</a>',
        expectedOutputAfterCurrentRegex: "Link click", // Current regex removes <a> tag
        expectedOutputAfterRobustSanitization: "Link click", // DOMPurify removes 'a' tag but keeps content
      },
      {
        name: 'should not properly sanitize tag with encoded chars that current regex might miss',
        input: "Encoded <IMG SRC=j&Tab;a&Tab;v&Tab;a&Tab;s&Tab;c&Tab;r&Tab;i&Tab;p&Tab;t:alert('XSS5')>",
        expectedOutputAfterCurrentRegex: "Encoded alert('XSS5')", // Current regex removes <IMG SRC=...>
        expectedOutputAfterRobustSanitization: "Encoded ", // DOMPurify with no allowed tags strips the img tag
      },
      {
        name: 'should not properly sanitize nested tags if regex is too greedy/simple',
        input: "Nested <div onclick='alert(\"XSS6\")'><p>Text</p></div>",
        expectedOutputAfterCurrentRegex: "Nested Text", // Current regex removes outer and inner tags
        expectedOutputAfterRobustSanitization: "Nested Text", // DOMPurify removes div and p tags but keeps content
      },
      {
        name: 'should not properly sanitize broken HTML comments that might execute',
        input: "Comment <!-- <script>alert('XSS7')</script> --!>",
        expectedOutputAfterCurrentRegex: "Comment --!>", // Current regex removes the script tag but leaves broken comment
        expectedOutputAfterRobustSanitization: "Comment ", // DOMPurify removes comment and its content, and trailing invalid comment part
      }
    ];

    testCases.forEach(tc => {
      test(`KGV-SEC-001_Advanced: ${tc.name}`, () => {
        const maliciousGraphData = {
          nodes: [{ id: 'n1', label: tc.input, type: 'typeA' }],
          edges: [{ id: 'e1', source: 'n1', target: 'n1', label: tc.input }], // Test edge label too
        };

        render(<GraphRenderingArea {...mockProps} graphData={maliciousGraphData} />);
        const cytoscapeMock = require('cytoscape');
        const lastCallArgs = cytoscapeMock.mock.calls[cytoscapeMock.mock.calls.length - 1][0];
        const passedNode = lastCallArgs.elements.nodes.find(n => n.data.id === 'n1');
        const passedEdge = lastCallArgs.elements.edges.find(e => e.data.id === 'e1');

        // This assertion demonstrates the current regex's inadequacy.
        // It shows that the output *still contains* the malicious parts.
        // Once a robust sanitizer is in place, this assertion might need to change
        // to tc.expectedOutputAfterRobustSanitization, and the test should PASS.
        // For now, we expect the current regex to produce its (inadequate) output.
        expect(passedNode.data.label).toBe(tc.expectedOutputAfterRobustSanitization);
        expect(passedEdge.data.label).toBe(tc.expectedOutputAfterRobustSanitization);

        // For demonstration, if we were to assert against a *truly* sanitized output,
        // these tests would currently FAIL, highlighting the vulnerability.
        // e.g., expect(passedNode.data.label).toBe(tc.expectedOutputAfterRobustSanitization);
      });
    });
  });

  // Further tests from original file can be adapted similarly:
  // - TC_KGV_GR_002, TC_KGV_GR_003: (Performance aspects are harder to test here, focus on correct data passing)
  // - TC_KGV_GR_005: Re-running layout (covered by layout change test)
  // - TC_KGV_CI_005: Multiple node selection (if component supports it, adapt 'tap' simulation)
  // - TC_KGV_CI_006, TC_KGV_CI_007: Hover interactions (would need 'mouseover'/'mouseout' event simulation)
  // - TC_KGV_CI_008: Node deselection (e.g., tap on background or re-tap node)
});