import React from 'react';
import { render, screen } from '@testing-library/react';
import InformationDisplayPanel from '../components/InformationDisplayPanel';

const mockSelectedNode = {
  id: 'n1',
  label: 'Selected Node Alpha',
  type: 'typeA',
  attributes: {
    description: 'This is a detailed description of Node Alpha.',
    createdDate: '2023-01-15',
    author: 'Test User',
  },
};

const mockSelectedEdge = {
  id: 'e1',
  source: 'n1',
  target: 'n2',
  type: 'relX',
  attributes: {
    relationshipType: 'Connects To',
    strength: 7,
    notes: 'Important connection',
  },
};

describe('InformationDisplayPanel Component', () => {
  test('TC_KGV_IDP_001: should render without crashing when no item is selected', () => {
    render(<InformationDisplayPanel selectedItem={null} itemType={null} />);
    expect(screen.getByText(/No item selected/i)).toBeInTheDocument();
  });

  test('TC_KGV_ID_001 & TC_KGV_ID_004: should display attributes for a selected node', () => {
    render(<InformationDisplayPanel selectedItem={mockSelectedNode} itemType="node" />);
    expect(screen.getByText(/Selected Node Alpha/i)).toBeInTheDocument(); // Label
    expect(screen.getByText(/Type: typeA/i)).toBeInTheDocument();
    expect(screen.getByText(/ID: n1/i)).toBeInTheDocument();
    
    // Check for attributes
    expect(screen.getByText(/Description:/i)).toBeInTheDocument();
    expect(screen.getByText(mockSelectedNode.attributes.description)).toBeInTheDocument();
    expect(screen.getByText(/Created Date:/i)).toBeInTheDocument();
    expect(screen.getByText(mockSelectedNode.attributes.createdDate)).toBeInTheDocument();
    expect(screen.getByText(/Author:/i)).toBeInTheDocument();
    expect(screen.getByText(mockSelectedNode.attributes.author)).toBeInTheDocument();
  });

  test('TC_KGV_ID_002 & TC_KGV_ID_004: should display attributes for a selected edge', () => {
    render(<InformationDisplayPanel selectedItem={mockSelectedEdge} itemType="edge" />);
    expect(screen.getByText(/Edge ID: e1/i)).toBeInTheDocument();
    expect(screen.getByText(/Type: relX/i)).toBeInTheDocument();
    expect(screen.getByText(/Source: n1/i)).toBeInTheDocument();
    expect(screen.getByText(/Target: n2/i)).toBeInTheDocument();

    // Check for attributes
    expect(screen.getByText(/Relationship Type:/i)).toBeInTheDocument();
    expect(screen.getByText(mockSelectedEdge.attributes.relationshipType)).toBeInTheDocument();
    expect(screen.getByText(/Strength:/i)).toBeInTheDocument();
    expect(screen.getByText(mockSelectedEdge.attributes.strength.toString())).toBeInTheDocument();
    expect(screen.getByText(/Notes:/i)).toBeInTheDocument();
    expect(screen.getByText(mockSelectedEdge.attributes.notes)).toBeInTheDocument();
  });

  test('TC_KGV_IDP_002: should update display when selected item changes from node to edge', () => {
    const { rerender } = render(<InformationDisplayPanel selectedItem={mockSelectedNode} itemType="node" />);
    expect(screen.getByText(/Selected Node Alpha/i)).toBeInTheDocument();
    
    rerender(<InformationDisplayPanel selectedItem={mockSelectedEdge} itemType="edge" />);
    expect(screen.queryByText(/Selected Node Alpha/i)).not.toBeInTheDocument();
    expect(screen.getByText(/Edge ID: e1/i)).toBeInTheDocument();
    expect(screen.getByText(/Relationship Type:/i)).toBeInTheDocument();
  });

  test('TC_KGV_IDP_003: should update display when selected item changes to null', () => {
    const { rerender } = render(<InformationDisplayPanel selectedItem={mockSelectedNode} itemType="node" />);
    expect(screen.getByText(/Selected Node Alpha/i)).toBeInTheDocument();
    
    rerender(<InformationDisplayPanel selectedItem={null} itemType={null} />);
    expect(screen.queryByText(/Selected Node Alpha/i)).not.toBeInTheDocument();
    expect(screen.getByText(/No item selected/i)).toBeInTheDocument();
  });

  test('TC_KGV_ID_003: should display concise information (attributes) as specified', () => {
    // This test is implicitly covered by TC_KGV_ID_001 and TC_KGV_ID_002.
    // The key is that all *specified* attributes are shown.
    // If "concise" means a subset, the mock data and assertions should reflect that.
    // For now, assuming all attributes of the mock are "specified" for display.
    render(<InformationDisplayPanel selectedItem={mockSelectedNode} itemType="node" />);
    Object.entries(mockSelectedNode.attributes).forEach(([key, value]) => {
      // A simple check that the value is present. More specific checks for labels are in TC_KGV_ID_001.
      expect(screen.getByText(new RegExp(value, 'i'))).toBeInTheDocument();
    });
  });
  
  // Add more tests for:
  // - Handling items with no attributes
  // - Handling very long attribute values (e.g., truncation, scrollability if applicable)
  // - Different data types for attributes (numbers, booleans, dates) and their formatting
  // - Accessibility of the displayed information (e.g., ARIA attributes)
});