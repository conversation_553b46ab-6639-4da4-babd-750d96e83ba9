# Research Report: References

This section lists the conceptual sources of information used throughout this research, primarily derived from interactions with the Perplexity AI MCP tool. Each entry refers to the AI search query performed and the key themes or data points extracted from the AI's synthesized response, as documented in the corresponding "Primary Findings" files.

**Note:** The AI search tool synthesizes information from multiple web sources. Specific URLs for each synthesized point are not always provided by the tool in a directly citable format for a formal bibliography. Thus, this reference list serves to document the information provenance at the level of AI search queries and the resulting summarized outputs.

1.  **Initial Broad Search (First Search):**
    *   **Query:** "state-of-the-art AI techniques for semantic relationships and conceptual linking in text documents and knowledge bases"
    *   **Key Information Derived:** Foundational concepts including Transformer models (BERT), Semantic Textual Similarity (STS), Knowledge Graphs, hybrid NLP approaches (LSA, LDA, NER), applications in plagiarism detection, content recommendation, and challenges like ambiguity and data bias.
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part1.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part1.md), [`.../02_secondary_findings_part1.md`](research/ai_linking_strategies_research/02_data_collection/02_secondary_findings_part1.md), [`.../03_expert_insights_part1.md`](research/ai_linking_strategies_research/02_data_collection/03_expert_insights_part1.md).

2.  **On-Device NLP Models (Second Search):**
    *   **Query:** "on-device NLP models for semantic similarity: techniques, performance, and libraries"
    *   **Key Information Derived:** Techniques like contrastive learning (SimCSE), distilled transformers (`all-MiniLM-L6-v2`), quantization; performance trade-offs; libraries like Sentence-Transformers, TensorFlow Lite, ONNX Runtime.
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part2.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part2.md).

3.  **Lightweight Knowledge Graph Libraries (Third Search):**
    *   **Query:** "efficient lightweight knowledge graph libraries for desktop/mobile applications (Python, JavaScript compatible): features, performance, and limitations"
    *   **Key Information Derived:** Python libraries (AmpliGraph for embeddings, Bokeh for viz), JavaScript libraries (vis.js, VivaGraphJS, Cytoscape.js for viz), cross-platform considerations, hybrid approaches.
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part3.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part3.md).

4.  **Integrating Embeddings with Local Graph DBs (Fourth Search):**
    *   **Query:** "integrating Sentence-Transformers with local graph databases (TinyDB, SQLite, RDFLib) for conceptual linking workflows and examples"
    *   **Key Information Derived:** General workflow (embedding, storage, similarity, link creation); database-specific strategies for TinyDB, SQLite, RDFLib; scalability challenges and ANN solutions (FAISS, HNSWLib).
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part4.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part4.md).

5.  **Typed Link Prediction Algorithms (Fifth Search):**
    *   **Query:** "algorithms for typed link prediction in text: methods, datasets, and evaluation"
    *   **Key Information Derived:** Methods (heuristic, content-based, GNNs, PLM+GNN hybrids); relevant datasets (TAG benchmarks, biomedical KGs); evaluation metrics (Hits@k, MRR, AUC, Precision/Recall/F1).
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part5.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part5.md).

6.  **Ranking Algorithms for Conceptual Links (Sixth Search):**
    *   **Query:** "ranking algorithms for conceptual links in knowledge graphs considering relevance and novelty: techniques, metrics, and challenges"
    *   **Key Information Derived:** Techniques (path-based analysis, hybrid authority metrics like INDEGREE, confidence scores, probabilistic relevance, context-aware novelty detection); metrics (structural, semantic, temporal, behavioral); challenges (data sparsity, complexity, novelty-relevance trade-off).
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part6.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part6.md).

7.  **On-Device Contradiction Detection (Seventh Search):**
    *   **Query:** "on-device NLP models for textual contradiction detection and inconsistency identification: lightweight approaches, performance, and libraries"
    *   **Key Information Derived:** Lightweight approaches (efficient pipelines, model compression like quantization/pruning, distilled NLI models like `cross-encoder/nli-MiniLM-L6-H768`); performance trade-offs; libraries (Hugging Face Transformers, TF Lite, ONNX, Core ML).
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part7.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part7.md).

8.  **Novelty Detection Algorithms (Eighth Search):**
    *   **Query:** "novelty detection algorithms in information retrieval and recommender systems adaptable to personal knowledge graphs and conceptual link suggestion: techniques, metrics, challenges"
    *   **Key Information Derived:** Techniques (semantic redundancy filtering, sentence-level analysis, ensembles, temporal dynamics, graph embeddings for unexplored regions); metrics (novelty ratio, semantic divergence); adaptation to PKGs; challenges (semantic vs. lexical novelty, scalability, user context).
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part8.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part8.md).

9.  **User-Configurable Ranking & Filtering in PKM (Ninth Search):**
    *   **Query:** "user-configurable ranking parameters and interactive filtering for conceptual links in personal knowledge management systems: design patterns, UI examples, and technical considerations"
    *   **Key Information Derived:** Design patterns (multi-dimensional ranking, dynamic filters); UI examples from tools like Obsidian, Tana, Scrintal; technical considerations (data modeling, performance optimization like dynamic indexing/lazy loading).
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part9.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part9.md).

10. **Multimodal AI for Conceptual Linking (Tenth Search):**
    *   **Query:** "multimodal AI for conceptual linking between text, images, and other media: techniques, models (e.g., CLIP, BLIP), and challenges"
    *   **Key Information Derived:** Core techniques (fusion strategies, alignment methods, contrastive learning); key models (CLIP, BLIP); implementation challenges (representational complexity, alignment precision, scalability, evaluation, data scarcity for specialized domains).
    *   **Documented in:** [`research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part10.md`](research/ai_linking_strategies_research/02_data_collection/01_primary_findings_part10.md).

This list serves as a high-level trace of the information gathered through the AI-assisted research process. The detailed findings documents contain further context and synthesized information from the AI tool's responses.