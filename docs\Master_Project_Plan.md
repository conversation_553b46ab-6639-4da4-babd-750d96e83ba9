# Master Project Plan: Personalized AI Knowledge Companion & PKM Web Clipper

**Version:** 1.4
**Date:** May 19, 2025 (GitHub Template Integration)
**Based on:**
*   Product Requirements Document (PRD) v1.0 ([`docs/PRD.md`](docs/PRD.md))
*   GitHub Template Research Report ([`docs/research/github_template_research_report.md`](docs/research/github_template_research_report.md))
*   Template Integration Guide ([`docs/template_integration_guide.md`](docs/template_integration_guide.md))

## 1. Introduction

This Master Project Plan outlines the initial project definition, scope, and high-level architecture for the "Personalized AI Knowledge Companion & PKM Web Clipper." It is derived from the PRD, the outputs of the initial research, feature specification, architectural design phases, and the integration of Jonghakseo's `chrome-extension-react-ts-boilerplate` GitHub template.

## 2. Project Vision & Goals

(Derived from PRD - Section 1 & 3)

**Vision:** To develop a smart, AI-powered tool that transforms how users capture, organize, and derive insights from digital web content, serving as a "second brain" with a core principle of user data privacy, ownership, and local-first storage.

**Primary Goals:**
*   Enable quick and reliable capture of web content.
*   Provide intelligent, automated organization of captured content.
*   Facilitate active interaction with the knowledge base (search, summarization, insights).
*   Ensure user data privacy, ownership, and local-first storage.
*   Build a system that learns and becomes more personalized over time.
*   Leverage a well-structured project template to accelerate development and ensure maintainability.

## 3. Key Features (Modules)

The project has been decomposed into the following primary modules, each with detailed specifications and high-level architecture defined. These modules will be implemented within the structure provided by the integrated GitHub template.

1.  **Web Content Capture Module:**
    *   **Specification:** [`docs/specs/Web_Content_Capture_Module_overview.md`](docs/specs/Web_Content_Capture_Module_overview.md)
    *   **Architecture:** [`docs/architecture/Web_Content_Capture_Module_architecture.md`](docs/architecture/Web_Content_Capture_Module_architecture.md)
    *   **Summary:** Provides browser extensions for capturing web content in various formats (full page, article, selection, bookmark, PDF), automatic metadata extraction, preview, and saving in configurable formats like Markdown. Architecture emphasizes local processing and clear component separation (Browser Extension, Content Processing Service, Storage Interface). To be adapted to the template's `apps/chrome-extension` structure.

2.  **Intelligent Capture & Organization Assistance Module:**
    *   **Specification:** [`docs/specs/Intelligent_Capture_Organization_Assistance_Module_overview.md`](docs/specs/Intelligent_Capture_Organization_Assistance_Module_overview.md)
    *   **Architecture:** [`docs/architecture/Intelligent_Capture_Organization_Assistance_Module_architecture.md`](docs/architecture/Intelligent_Capture_Organization_Assistance_Module_architecture.md)
    *   **Summary:** Offers AI-suggested tags, categories, and summaries (via Gemini) during capture. Allows user overrides, notes, highlights, and feedback. Architecture includes an Orchestration Service, AI Suggestion Service (local-first for tags/categories), and User Interaction/Feedback Handlers. Shared services may reside in the template's `packages/` directory.

3.  **Knowledge Base Interaction & Insights Module:**
    *   **Specification:** [`docs/specs/Knowledge_Base_Interaction_Insights_Module_overview.md`](docs/specs/Knowledge_Base_Interaction_Insights_Module_overview.md)
    *   **Architecture:** [`docs/architecture/Knowledge_Base_Interaction_Insights_Module_architecture.md`](docs/architecture/Knowledge_Base_Interaction_Insights_Module_architecture.md)
    *   **Summary:** Enables unified browsing, natural language semantic search, AI-powered Q&A, summarization, content transformation (via Gemini), and conceptual link suggestions. Architecture features a UI Manager, Query Understanding Engine, Search Service (with Vector Embeddings), KBAL, and dedicated AI service gateways, prioritizing local-first and offline access for core functions. The KBAL implementation now utilizes `lowdb` for local-first data persistence. UI components like [`KnowledgeBaseView.js`](src/components/KnowledgeBaseView.js:0) and [`DetailViewPane.js`](src/components/DetailViewPane.js:0) will be adapted to the template's React structure. List virtualization using `react-window` in `ContentList` remains a key performance feature.

4.  **Management & Configuration Module:**
    *   **Specification:** [`docs/specs/Management_Configuration_Module_overview.md`](docs/specs/Management_Configuration_Module_overview.md)
    *   **Architecture:** [`docs/architecture/Management_Configuration_Module_architecture.md`](docs/architecture/Management_Configuration_Module_architecture.md)
    *   **Summary:** Allows users to configure capture settings, manage custom clipping templates, and organize tags/categories. Architecture includes UI components, managers for settings/templates/tags, and a persistence service using local JSON files (or `chrome.storage.local` as provided by the template for settings).

## 4. GitHub Template Integration

*   **Template Used:** Jonghakseo's `chrome-extension-react-ts-boilerplate` ([https://github.com/Jonghakseo/chrome-extension-react-ts-boilerplate](https://github.com/Jonghakseo/chrome-extension-react-ts-boilerplate))
*   **Rationale:** Aligns with React, TypeScript, and Vite. Provides a monorepo structure (Turborepo) for browser extensions. See [`docs/research/github_template_research_report.md`](docs/research/github_template_research_report.md) for details.
*   **Integration Guide:** [`docs/template_integration_guide.md`](docs/template_integration_guide.md)
*   **Key Impacts on Project Structure:**
    *   The project will adopt the monorepo structure, with the main extension code in `apps/chrome-extension` and shared utilities in `packages/`.
    *   Build processes will leverage Vite and Turborepo as configured in the template.
    *   Initial storage for settings may use `chrome.storage.local` as per the template, with `lowdb` integrated for the main knowledge base.

## 5. Initial Research Phase Summary

*   **Lead:** `@ResearchPlanner_Strategic`
*   **Status:** "Initialization and Scoping" phase completed.
*   **Outputs:**
    *   Scope Definition: [`research/01_initial_queries/01_scope_definition.md`](research/01_initial_queries/01_scope_definition.md)
    *   Key Research Questions: [`research/01_initial_queries/02_key_questions_part1.md`](research/01_initial_queries/02_key_questions_part1.md), [`research/01_initial_queries/02_key_questions_part2.md`](research/01_initial_queries/02_key_questions_part2.md)
    *   Information Sources: [`research/01_initial_queries/03_information_sources.md`](research/01_initial_queries/03_information_sources.md)
*   **Key Finding/Status:** The Perplexity AI MCP tool, previously a blocker, is now operational. This tool is designated as a primary information gathering resource, and its availability unblocks the "Initial Data Collection" phase and subsequent research stages.

## 6. Overall Architectural Approach

The high-level architecture for the Personalized AI Knowledge Companion & PKM Web Clipper emphasizes:
*   **Modularity:** Clear separation of concerns through distinct feature modules, organized within the template's monorepo structure (`apps/` and `packages/`).
*   **Local-First Principle:** Prioritizing local storage (`lowdb` for knowledge base, `chrome.storage.local` for settings) and processing for user data, privacy, and offline access.
*   **User Control:** Ensuring users have final authority over AI suggestions and data management.
*   **Responsible AI Integration:** Secure and transparent use of external AI services (e.g., Gemini) with user consent, primarily for features that cannot be effectively performed locally.
*   **Openness:** Preference for open formats (e.g., Markdown) for data storage and export.
*   **Leveraging Template Structure:** Utilizing the `chrome-extension-react-ts-boilerplate` for a robust and maintainable codebase, including its Vite build system and Turborepo for monorepo management.

## 7. Key Documents Produced During Initialization

*   **Product Requirements Document:** [`docs/PRD.md`](docs/PRD.md)
*   **Research Documents:**
    *   [`research/01_initial_queries/01_scope_definition.md`](research/01_initial_queries/01_scope_definition.md)
    *   [`research/01_initial_queries/02_key_questions_part1.md`](research/01_initial_queries/02_key_questions_part1.md)
    *   [`research/01_initial_queries/02_key_questions_part2.md`](research/01_initial_queries/02_key_questions_part2.md)
    *   [`research/01_initial_queries/03_information_sources.md`](research/01_initial_queries/03_information_sources.md)
    *   [`docs/research/github_template_research_report.md`](docs/research/github_template_research_report.md)
*   **Template Integration Guide:** [`docs/template_integration_guide.md`](docs/template_integration_guide.md)
*   **Feature Specifications:**
    *   [`docs/specs/Web_Content_Capture_Module_overview.md`](docs/specs/Web_Content_Capture_Module_overview.md)
    *   [`docs/specs/Intelligent_Capture_Organization_Assistance_Module_overview.md`](docs/specs/Intelligent_Capture_Organization_Assistance_Module_overview.md)
    *   [`docs/specs/Knowledge_Base_Interaction_Insights_Module_overview.md`](docs/specs/Knowledge_Base_Interaction_Insights_Module_overview.md)
    *   [`docs/specs/Management_Configuration_Module_overview.md`](docs/specs/Management_Configuration_Module_overview.md)
*   **Architectural Designs:**
    *   [`docs/architecture/Web_Content_Capture_Module_architecture.md`](docs/architecture/Web_Content_Capture_Module_architecture.md)
    *   [`docs/architecture/Intelligent_Capture_Organization_Assistance_Module_architecture.md`](docs/architecture/Intelligent_Capture_Organization_Assistance_Module_architecture.md)
    *   [`docs/architecture/Knowledge_Base_Interaction_Insights_Module_architecture.md`](docs/architecture/Knowledge_Base_Interaction_Insights_Module_architecture.md)
    *   [`docs/architecture/Management_Configuration_Module_architecture.md`](docs/architecture/Management_Configuration_Module_architecture.md)
    *   [`docs/architecture/Advanced_Features_Architecture.md`](docs/architecture/Advanced_Features_Architecture.md)

## 8. Current Status (As of May 19, 2025)

*   **GitHub Template Integrated:** Jonghakseo's `chrome-extension-react-ts-boilerplate` has been adopted as the foundational structure for the project.
*   **Initial Feature Implementation & Integration Complete (Pre-Template):** The core functionalities represented by the four primary modules have been implemented and successfully integrated into the `main` branch conceptually. These will now be adapted to the template structure.
    *   Web Content Capture Module - Integrated: [`docs/reports/integration/archive/Integration_Status_Report_web-content-capture_to_main_re-attempt_already_up_to_date.md`](docs/reports/integration/archive/Integration_Status_Report_web-content-capture_to_main_re-attempt_already_up_to_date.md:0)
    *   Intelligent Capture & Organization Assistance Module - Integrated: [`docs/reports/integration/archive/Integration_Status_Report_intelligent-capture-org-assist_to_main_re-attempt_success.md`](docs/reports/integration/archive/Integration_Status_Report_intelligent-capture-org-assist_to_main_re-attempt_success.md:0)
    *   Knowledge Base Interaction & Insights Module - Integrated: [`docs/reports/integration/archive/Integration_Status_Report_knowledge-base-interaction-insights-module_to_main.md`](docs/reports/integration/archive/Integration_Status_Report_knowledge-base-interaction-insights-module_to_main.md:0)
    *   Management & Configuration Module - Integrated: [`docs/reports/integration/archive/Integration_Status_Report_management-config_to_main_success.md`](docs/reports/integration/archive/Integration_Status_Report_management-config_to_main_success.md:0)
*   **System Validation (Pre-Template):** Post-integration system tests (`npm test`) had passed, confirming the stability of the previously integrated codebase. Testing strategy will be updated to include Playwright.
*   **Documentation:** Core specifications, architecture documents, test plans, and integration reports for the initial modules are available. Template-related documentation has been added.
*   **Post-Integration Refinements Complete (Pre-Template):** Previous refinements are noted. New refinements will focus on template adaptation.
*   **UI Development & Hardening (Knowledge Base Interaction & Insights Module - Pre-Template):** Key UI components were developed and hardened. These will be migrated and adapted to the template's React structure.
*   **End-to-End (E2E) Testing Refinement Cycle Complete (Pre-Template):** Previous E2E testing (mock-based) was completed. This will be superseded by Playwright-based E2E testing as per template integration.
*   **Performance Optimization Cycle (ContentList Rendering & Search - Pre-Template):** `ContentList` virtualization with `react-window` remains a key implementation to be carried into the template structure.
*   **AI Linking Strategies Research Completed:** Research findings will guide development within the new template structure.

## 9. Next Steps (Post-Template Integration)

1.  **Phase 1: Template Adaptation & Core Setup**
    *   **Task 1.1: Integrate `lowdb` for Knowledge Base Storage**
        *   **Description:** Install `lowdb`. Design and implement the data schema. Develop a data access layer/service (e.g., in `packages/knowledge-base`) for `lowdb` interactions.
        *   **AI Verifiable End Result:** `lowdb` is integrated. A service in `packages/knowledge-base` can successfully read and write data to a local `db.json` file. Basic unit tests for the service pass.
    *   **Task 1.2: Set up Playwright for E2E Testing**
        *   **Description:** Install and configure Playwright. Adapt existing high-level acceptance tests or create new basic ones to run within Playwright, ensuring the extension loads correctly.
        *   **AI Verifiable End Result:** Playwright is installed and configured. A basic Playwright test script can successfully launch a browser, load the built extension, and interact with a basic UI element (e.g., popup title). The test run completes successfully.
    *   **Task 1.3: Establish Core Module Structure within Template**
        *   **Description:** Create dedicated directories for each of the four main project modules within `apps/chrome-extension/src/` (e.g., `src/capture/`, `src/organization/`, `src/knowledge-base-ui/`, `src/config-ui/`). Create placeholder components or files.
        *   **AI Verifiable End Result:** Directory structure for modules exists within `apps/chrome-extension/src/`. Each module directory contains at least one placeholder `.ts` or `.tsx` file. Project builds successfully with these additions.

2.  **Phase 2: Module Implementation & UI Adaptation**
    *   **Task 2.1: Adapt Web Content Capture Module**
        *   **Description:** Migrate/re-implement the Web Content Capture module's UI (popup, options) and logic within the template structure. Ensure it uses `chrome.storage.local` for its settings if appropriate and interacts with the future `lowdb` service for saving content.
        *   **AI Verifiable End Result:** Basic web content capture (e.g., bookmarking a URL and title) is functional using the extension popup. Captured data is intended for `lowdb` (actual saving can be mocked initially if `lowdb` service is not fully ready for this module). Relevant Playwright tests for basic capture pass.
    *   **Task 2.2: Adapt Intelligent Capture & Organization Assistance Module**
        *   **Description:** Migrate/re-implement UI elements for suggesting tags/categories. Integrate with AI services (can be mocked initially).
        *   **AI Verifiable End Result:** UI for tag/category suggestions is present during capture. Mocked suggestions are displayed.
    *   **Task 2.3: Adapt Knowledge Base Interaction & Insights Module UI**
        *   **Description:** Migrate/re-implement the UI for browsing, viewing, and searching saved content (e.g., `KnowledgeBaseView.js`, `DetailViewPane.js`, `ContentList.js` with `react-window`). Connect to the `lowdb` service.
        *   **AI Verifiable End Result:** User can view a list of (mocked or `lowdb`-sourced) items. `react-window` virtualization is confirmed to be working. Basic search/filter UI is present.
    *   **Task 2.4: Adapt Management & Configuration Module UI**
        *   **Description:** Migrate/re-implement UI for managing settings, templates, tags/categories. Connect to `chrome.storage.local` or `lowdb` as appropriate.
        *   **AI Verifiable End Result:** Basic settings page is accessible and displays mock/default settings.

3.  **Phase 3: Full Feature Integration & Testing**
    *   **Task 3.1: Complete `lowdb` Integration Across Modules**
        *   **Description:** Ensure all modules correctly use the `lowdb` service for persisting and retrieving knowledge base content and metadata.
        *   **AI Verifiable End Result:** All CRUD operations for knowledge items via `lowdb` are functional across relevant modules. Data consistency is maintained.
    *   **Task 3.2: Develop Comprehensive Playwright Acceptance Tests**
        *   **Description:** Write Playwright tests covering all high-level acceptance criteria from the [`docs/Master_Acceptance_Test_Plan.md`](docs/Master_Acceptance_Test_Plan.md), including `chrome.runtime.lastError` scenarios.
        *   **AI Verifiable End Result:** A suite of Playwright tests in `tests/acceptance/` (or similar, as per template conventions) covers all major user flows. All tests pass.
    *   **Task 3.3: Integrate AI Functionalities**
        *   **Description:** Implement actual calls to Gemini and other AI services. Develop local AI components.
        *   **AI Verifiable End Result:** AI-powered summarization, Q&A, and tagging features are functional with live or robustly mocked AI services.
    *   **Task 3.4: Refine UI/UX and Perform Security Hardening**
        *   **Description:** Ensure UI/UX consistency. Apply security best practices (XSS protection, etc.) as done previously.
        *   **AI Verifiable End Result:** UI is polished. Security scan (manual or automated) identifies no new critical vulnerabilities.

4.  **Phase 4: Documentation & Release Preparation**
    *   **Task 4.1: Update All Project Documentation**
        *   **Description:** Reflect all changes from template integration and subsequent development in [`README.md`](README.md:0), module READMEs, this Master Plan, and user guides.
        *   **AI Verifiable End Result:** All key documents in `docs/` are updated and accurately reflect the current project state.
    *   **Task 4.2: Final System Validation & Bug Fixing**
        *   **Description:** Conduct thorough end-to-end testing and address any remaining bugs.
        *   **AI Verifiable End Result:** All Playwright tests pass. No critical bugs reported from manual testing.

## 10. Identified Blockers & Risks

*   **Risk:** Complexity in adapting existing module logic to the monorepo structure and shared packages of the template.
*   **Risk:** Ensuring seamless data flow between `chrome.storage.local` (for settings/template state) and `lowdb` (for main knowledge base).
*   **Risk:** Time required to fully migrate/rewrite E2E tests to Playwright and ensure comprehensive coverage.
*   **Risk:** Potential latency or cost implications of external AI API (e.g., Gemini) usage.
*   **Risk:** Complexity in achieving seamless and highly accurate AI suggestions across diverse content.
*   **Risk:** Ensuring robust data privacy and security, especially with any cloud interaction.

---
This Master Project Plan will be updated as the project progresses.