import React from 'react';
import './CaptureTypeSelector.css';

const CAPTURE_TYPES = [
  { id: 'fullPage', label: 'Full Page' },
  { id: 'selection', label: 'Selection' },
  { id: 'bookmark', label: 'Bookmark' },
];

function CaptureTypeSelector({ selectedType, onTypeChange }) {
  return (
    <div className="capture-type-selector">
      <h2>Select Capture Type:</h2>
      {CAPTURE_TYPES.map((type) => (
        <label key={type.id} htmlFor={`capture-type-${type.id}`} className="capture-type-label">
          <input
            type="radio"
            id={`capture-type-${type.id}`}
            name="captureType"
            value={type.id}
            checked={selectedType === type.id}
            onChange={() => onTypeChange(type.id)}
            className="capture-type-radio"
          />
          {type.label}
        </label>
      ))}
    </div>
  );
}

export default CaptureTypeSelector;