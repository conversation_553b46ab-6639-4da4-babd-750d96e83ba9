import React, { useCallback, useMemo } from 'react';
import ReactFlow, {
  addEdge,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
} from 'reactflow';
import 'reactflow/dist/style.css';

import useStore from '../store/useStore';
import './KnowledgeGraphView.css';

const KnowledgeGraphView = () => {
  const selectedItemId = useStore((state) => state.selectedItemId);
  const items = useStore((state) => state.items);
  const searchResults = useStore((state) => state.searchResults);
  const selectedItem = selectedItemId ? (searchResults.find(item => item.id === selectedItemId) || items.find(item => item.id === selectedItemId)) : null;
  const conceptualLinks = useStore((state) => state.conceptualLinks);
  const selectItem = useStore((state) => state.selectItem);

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  const onConnect = useCallback((params) => setEdges((eds) => addEdge(params, eds)), [setEdges]);

  useMemo(() => {
    if (!selectedItem) {
      setNodes([]);
      setEdges([]);
      return;
    }

    const initialNodes = [];
    const initialEdges = [];

    // Central node for the selected item
    initialNodes.push({
      id: String(selectedItem.id),
      data: { label: selectedItem.title },
      position: { x: 250, y: 5 },
      type: 'input', // Or 'default'
      style: { background: '#D6D5E6', color: '#333', border: '1px solid #222138', width: 180 },
    });

    // Nodes for conceptual links
    conceptualLinks.forEach((link, index) => {
      initialNodes.push({
        id: String(link.id),
        data: { label: link.title },
        position: { x: index * 150, y: 100 }, // Basic positioning, can be improved
        style: { background: '#fff', color: '#333', border: '1px solid #ccc', width: 150 },
      });
      initialEdges.push({
        id: `e-${selectedItem.id}-${link.id}`,
        source: String(selectedItem.id),
        target: String(link.id),
        animated: true, // Optional: make edges animated
        // label: `Relevance: ${link.relevance_score}`, // Optional: show relevance
      });
    });

    setNodes(initialNodes);
    setEdges(initialEdges);
  }, [selectedItem, conceptualLinks, setNodes, setEdges]);

  const onNodeClick = useCallback(
    (event, node) => {
      // Assuming node.id is the ID of the item to select
      // We need to ensure the full item object is available or fetched if only ID is stored
      // For now, let's assume we can find it in conceptualLinks or it's the selectedItem itself
      if (node.id === String(selectedItem.id)) {
        console.log('Clicked on the currently selected item node:', selectedItem.title);
        return; // Already selected
      }

      const clickedLinkItem = conceptualLinks.find(link => String(link.id) === node.id);
      if (clickedLinkItem) {
        // To select an item, we typically need its full object or at least its ID
        // The `selectItem` action in the store might expect an ID or the full item
        // For this example, we'll call selectItem with the ID.
        // The store's selectItem action would then fetch the full item details if necessary.
        console.log(`Selecting item from graph: ${clickedLinkItem.title} (ID: ${clickedLinkItem.id})`);
        selectItem(clickedLinkItem.id); // Or selectItem(clickedLinkItem) if it expects the object
      } else {
        console.warn(`Clicked node ${node.id} not found in conceptual links or as selected item.`);
      }
    },
    [selectItem, selectedItemId, items, searchResults, conceptualLinks]
  );

  if (!selectedItem) {
    return <div className="knowledge-graph-container-empty">Select an item to see its knowledge graph.</div>;
  }

  return (
    <div className="knowledge-graph-container" style={{ height: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        fitView
        attributionPosition="bottom-left"
      >
        <MiniMap nodeStrokeColor={(n) => {
          if (n.style?.background) return n.style.background;
          if (n.type === 'input') return '#0041d0';
          if (n.type === 'output') return '#ff0072';
          if (n.type === 'default') return '#1a192b';
          return '#eee';
        }} nodeColor={(n) => {
          if (n.style?.background) return n.style.background;
          return '#fff';
        }} nodeBorderRadius={2} />
        <Controls />
        <Background color="#aaa" gap={16} />
      </ReactFlow>
    </div>
  );
};

export default KnowledgeGraphView;