# Diagnosis Report: Test Failure TC_WCC_MD_001 - Web Content Capture Module

**Feature Name:** Web Content Capture Module
**Failing Test Case:** `TC_WCC_MD_001` (Save captured article content as Markdown. Verify formatting)
**File Under Test:** [`src/web-content-capture/index.js`](src/web-content-capture/index.js)
**Test File:** [`src/web-content-capture/__tests__/webContentCapture.test.js`](src/web-content-capture/__tests__/webContentCapture.test.js)

## 1. Issue Overview

Test `TC_WCC_MD_001` consistently fails. The assertion at line 814 of [`src/web-content-capture/__tests__/webContentCapture.test.js`](src/web-content-capture/__tests__/webContentCapture.test.js:814) expects the Markdown output to contain `[link](http://example.com)`. However, the received output contains the unprocessed HTML: `<a href='http://example.com'>link</a>`.

```
Expected substring: "[link](http://example.com)"
Received string:    "---
title: Article for Markdown
url: http://example.com/article-md
captureDate: 2025-05-12T00:16:19.671Z
---·
# Test Article
This is a paragraph with a <a href='http://example.com'>link</a>.·
* Item 1
* Item 2
<img src='image.png' alt='Test Image'>"
```

## 2. Root Cause Analysis

The root cause of this test failure lies within the **mock implementation of the `convertToMarkdown` function** in the test file, [`src/web-content-capture/__tests__/webContentCapture.test.js`](src/web-content-capture/__tests__/webContentCapture.test.js:224-255).

Specifically, the regular expression used in the mock to convert HTML `<a>` tags to Markdown links is:
```javascript
.replace(/<a.*?href="(.*?)".*?>(.*?)<\/a>/gi, '[$2]($1)') // Line 232 in the test file
```
This regex is designed to match `href` attributes that use **double quotes** (e.g., `href="..."`).

However, the HTML input provided to this mock function within the failing test (`TC_WCC_MD_001`) uses **single quotes** for the `href` attribute:
```javascript
const articleHTML = "<h1>Test Article</h1><p>This is a paragraph with a <a href='http://example.com'>link</a>.</p><ul><li>Item 1</li><li>Item 2</li></ul><img src='image.png' alt='Test Image'>"; // Line 787 in the test file
```
As a result, the mock's regex fails to match and convert the `<a>` tag, leaving the original HTML in the output, which then causes the test assertion to fail.

It's important to note that the actual `convertToMarkdown` function in the module code ([`src/web-content-capture/index.js`](src/web-content-capture/index.js:218-277)) has a fallback mechanism. If the `turndown` library (its primary conversion tool) fails to load or initialize, it uses a set of internal regexps. The fallback regex for anchor tags in the actual module is:
```javascript
.replace(/<a\s+(?:[^>]*?\s+)?href=(["'])([^"']+?)\1(?:[^>]*?)>(.*?)<\/a>/gi, '[$3]($2)') // Line 232 in index.js
```
This regex (`(["'])... \1`) is more robust and correctly handles `href` attributes with either single or double quotes.

The test explicitly calls the mocked `convertToMarkdown` (line 807 in the test file):
```javascript
const markdownOutput = WebContentCaptureModule.convertToMarkdown(captureResult.content, { /* ... */ });
```
Thus, the failure is due to the discrepancy between the test's input HTML (single-quoted `href`) and the test mock's regex capability (double-quoted `href` only).

## 3. Diagnosis Summary

The test `TC_WCC_MD_001` is failing because the mock version of `convertToMarkdown` in [`src/web-content-capture/__tests__/webContentCapture.test.js`](src/web-content-capture/__tests__/webContentCapture.test.js) does not correctly process HTML anchor tags with single-quoted `href` attributes. The actual module code in [`src/web-content-capture/index.js`](src/web-content-capture/index.js) appears to have a more robust fallback for this scenario.

## 4. Suggested Actions / Potential Fix (for the test)

To resolve this test failure, the mock `convertToMarkdown` function in [`src/web-content-capture/__tests__/webContentCapture.test.js`](src/web-content-capture/__tests__/webContentCapture.test.js) should be updated to correctly handle anchor tags with single-quoted `href` attributes. This can be done by modifying its regex to be similar to the one used in the actual module's fallback:

**Current mock regex (problematic):**
```javascript
.replace(/<a.*?href="(.*?)".*?>(.*?)<\/a>/gi, '[$2]($1)')
```

**Suggested mock regex (improved):**
```javascript
.replace(/<a\s+(?:[^>]*?\s+)?href=(["'])([^"']+?)\1(?:[^>]*?)>(.*?)<\/a>/gi, '[$3]($2)')
```
Alternatively, ensure the input HTML in the test uses double quotes for `href` if the intention is to keep the mock regex simpler, though aligning the mock with the actual module's fallback capability is generally better for mock accuracy.

**Further Considerations:**
*   **Turndown in Jest:** While not the direct cause of *this* failure, it's worth verifying if the `turndown` library is correctly installed (`npm install --save-dev turndown` or `yarn add --dev turndown`) and accessible within the Jest environment. If `turndown` fails to load during tests, the actual module would rely on its fallback regexes. The current test setup, by mocking `convertToMarkdown` entirely, bypasses testing the `turndown` integration or the actual module's fallback logic. A more comprehensive test strategy might involve allowing the actual `convertToMarkdown` to run and mocking `turndown` itself if needed, or ensuring `turndown` works in the test environment.