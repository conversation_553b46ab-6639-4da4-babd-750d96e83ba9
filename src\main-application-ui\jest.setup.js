// Import required modules and configuration
import 'jest-canvas-mock'; // For Cytoscape rendering in tests
// jest.setup.js
require('@testing-library/jest-dom');

// Global setup configurations
// Mock fetch API for tests
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    statusText: 'OK',
    json: () => Promise.resolve({}),
  })
);

// Set NODE_ENV to 'test' for conditional logic in code
process.env.NODE_ENV = 'test';

// Mock window.location for URL construction
Object.defineProperty(window, 'location', {
  value: {
    origin: 'http://localhost',
  },
  writable: true,
});

// Mock TextEncoder and TextDecoder for jsdom
global.TextEncoder = require('util').TextEncoder;
global.TextDecoder = require('util').TextDecoder;