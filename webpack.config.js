const path = require('path');

module.exports = {
  entry: './src/index.js', // Assuming this is your main entry point
  output: {
    path: path.resolve(__dirname, 'dist'), // Output directory
    filename: 'bundle.js' // Output bundle file
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader'
        }
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'] // For handling CSS files
      }
    ]
  },
  resolve: {
    extensions: ['.js', '.jsx'] // Allow importing .js and .jsx files without specifying the extension
  }
};