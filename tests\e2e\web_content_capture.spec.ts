import { test, expect, chromium, type BrowserContext, type Page } from '@playwright/test';
import path from 'path';
// We will not directly mock KnowledgeBaseService here in the Playwright test.
// Verification will happen by checking side-effects (e.g., chrome.storage.local)
// or by observing logs/messages if the service interaction is mocked within the extension's background script.

const pathToExtension = path.join(__dirname, '..', '..', 'apps', 'chrome-extension', 'dist');
let extensionId: string;

test.describe('Web Content Capture E2E Tests', () => {
  let browserContext: BrowserContext;
  let serviceWorker: any; // Keep track of the service worker

  test.beforeAll(async () => {
    browserContext = await chromium.launchPersistentContext('', {
      headless: false, // Set to true for CI, false for local debugging
      args: [
        `--disable-extensions-except=${pathToExtension}`,
        `--load-extension=${pathToExtension}`,
      ],
      // Permissions needed if we interact with chrome.storage from the test page context
      // permissions: ['storage'], 
    });

    // Wait for the extension to load and find the service worker
    let attempts = 0;
    const maxAttempts = 10; 
    while (!serviceWorker && attempts < maxAttempts) {
      attempts++;
      const allServiceWorkers = browserContext.serviceWorkers();
      if (allServiceWorkers.length > 0) {
        console.log(`Attempt ${attempts}: Found ${allServiceWorkers.length} service worker(s):`);
        allServiceWorkers.forEach((sw, index) => {
          console.log(`  SW ${index}: ${sw.url()}`);
        });
      } else {
        console.log(`Attempt ${attempts}: No service workers found.`);
      }
      
      // Try to find the service worker associated with the extension
      // The URL might end with something like '/background.js' or be the root of the extension
      serviceWorker = allServiceWorkers.find(sw => sw.url().startsWith('chrome-extension://'));
      if (serviceWorker) {
        console.log(`Target service worker found: ${serviceWorker.url()}`);
        extensionId = serviceWorker.url().split('/')[2];
        console.log(`Extension ID: ${extensionId}`);
        break;
      }
      if (attempts < maxAttempts) {
        console.log(`Retrying in 1000ms... (Attempt ${attempts}/${maxAttempts})`);
        await new Promise(resolve => setTimeout(resolve, 1000)); 
      }
    }

    if (!serviceWorker) {
      const finalAllServiceWorkers = browserContext.serviceWorkers();
      if (finalAllServiceWorkers.length > 0) {
         console.error(`Final check: Found ${finalAllServiceWorkers.length} service worker(s) but none matched criteria:`);
         finalAllServiceWorkers.forEach((sw, index) => {
           console.error(`  SW ${index}: ${sw.url()}`);
         });
      } else {
         console.error("Final check: Still no service workers found.");
      }
      throw new Error("Extension service worker not found after multiple attempts. Ensure the extension is built, the manifest correctly specifies the background script, and the background script is functional.");
    }
  });

  test.afterAll(async () => {
    await browserContext.close();
  });

  test.beforeEach(async ({ page }) => {
    // Clear all entries from KnowledgeBaseService before each test
    if (serviceWorker && extensionId) {
      try {
        // We need a way to tell the background script to clear its data.
        // Let's assume the background script is modified to listen for a 'clearAllKnowledgeData' message.
        // This is a common pattern for E2E test setup.
        // For now, we'll attempt to directly clear chrome.storage.local,
        // knowing this is coupled to the ChromeStorageLocalAdapter implementation.
        // A better approach would be a dedicated message to the service.
        // The KnowledgeBaseService already has a clearDatabase method.
        // We need to expose this via a message if not already done.
        // Let's assume 'clearDatabase' is available via a global for testing or a message.
        // (self as any).kbService.clearDatabase() in background.
        
        // Attempt to clear via a direct call to kbService if exposed globally for tests in background.ts
        // (self as any).kbService = kbService;
        await serviceWorker.evaluate(async () => {
          // @ts-ignore
          if (self.kbService && typeof self.kbService.clearDatabase === 'function') {
            // @ts-ignore
            await self.kbService.clearDatabase();
            console.log("Cleared KnowledgeBase via serviceWorker self.kbService.clearDatabase()");
          } else {
            // Fallback: try to clear the default storage key directly if kbService is not exposed
            // This is brittle as it depends on the internal default key of KnowledgeBaseService
            // @ts-ignore
            await chrome.storage.local.remove('knowledgeBaseV1');
            console.warn("Cleared 'knowledgeBaseV1' directly. Consider exposing a clear method on kbService for tests.");
          }
        });
      } catch (e) {
        console.warn("Could not clear knowledge base via service worker:", e);
      }
    } else {
      console.warn("Service worker or extensionId not available in beforeEach, cannot clear storage.");
    }
    // Navigate to about:blank to ensure a clean state for the page context if needed
    await page.goto('about:blank');
  });

  test('should capture content from popup and verify it on options page', async ({ page }) => {
    const testPage = await browserContext.newPage();
    // Use a reliable, simple page for capture testing
    const testUrl = 'https://example.com/';
    const testTitle = 'Example Domain'; // Title of example.com
    await testPage.goto(testUrl); // Go to a page to capture
    await testPage.waitForLoadState('domcontentloaded');
    expect(await testPage.title()).toBe(testTitle);

    // Open the popup
    const popupPage = await browserContext.newPage();
    if (!extensionId) throw new Error("Extension ID not found.");
    const popupUrl = `chrome-extension://${extensionId}/popup.html?e2e_test_url=${encodeURIComponent(testUrl)}&e2e_test_title=${encodeURIComponent(testTitle)}`;
    await popupPage.goto(popupUrl);
    await popupPage.waitForLoadState('domcontentloaded');

    // Verify tab info in popup
    // Add console listener for popup page
    popupPage.on('console', msg => {
      if (msg.type() === 'error' || msg.type() === 'warn') {
        console.log(`POPUP PAGE CONSOLE [${msg.type().toUpperCase()}]: ${msg.text()}`);
      }
    });

    // Wait for the root div to have some content, indicating React might have mounted.
    await expect(popupPage.locator('#root')).not.toBeEmpty({ timeout: 10000 });
    
    // Wait for tab info to be loaded and not be fallback/loading data by polling
    const titleLocator = popupPage.locator('[data-testid="current-tab-title"]');
    const urlLocator = popupPage.locator('[data-testid="current-tab-url"]');

    // Wait for "Loading..." to disappear
    await expect(titleLocator).not.toHaveText('Loading...', { timeout: 10000 });
    await expect(urlLocator).not.toHaveText('Loading...', { timeout: 5000 });

    // Wait for fallback text to not be present
    await expect(titleLocator).not.toHaveText(/Example Domain \(/, { timeout: 5000 });
    // The following line is removed as testUrl itself is "https://example.com/",
    // so checking for "not /example\.com\//" would fail if E2E params work correctly.
    // The final toHaveText(testUrl) will confirm the correct URL.
    // await expect(urlLocator).not.toHaveText(/example\.com\//, { timeout: 5000 });

    // Now assert the exact text
    await expect(titleLocator).toHaveText(testTitle, { timeout: 5000 });
    await expect(urlLocator).toHaveText(testUrl, { timeout: 5000 });

    // Click capture button - ensure it has the data-testid
    await popupPage.locator('[data-testid="capture-bookmark-button"]').click();
    // Wait for success message in popup (optional, but good feedback)
    await expect(popupPage.locator('p:has-text("Bookmark captured successfully!")')).toBeVisible({ timeout: 5000 });
    
    await popupPage.close();
    await testPage.close();

    // Open options page
    const optionsPage = await browserContext.newPage();
    await optionsPage.goto(`chrome-extension://${extensionId}/options.html`);
    await optionsPage.waitForLoadState('domcontentloaded');
    
    // Wait for items to load in the options page (ContentList)
    // Check for the title of the captured item.
    // This assumes ContentList renders items with their titles visible.
    // The ContentList items are virtualized, so we might need to scroll or ensure the item is rendered.
    // For a small number of items (like 1 after clearing), it should be visible.
    // A more robust selector might target a specific data-testid or a combination of text.
    // Let's assume the title is within an h3 tag in the ContentList's Row component.
    
    // Wait for loading message to disappear
    await expect(optionsPage.locator('[data-testid="loading-items-message"]')).not.toBeVisible({ timeout: 10000 });
    
    // Now wait for the actual item to be visible
    // Use the data-testid for the list item row, then check for text within it.
    const capturedItemInOptionsList = optionsPage.locator(`[data-testid^="kb-item-"]:has-text("${testTitle}")`);
    await expect(capturedItemInOptionsList).toBeVisible({ timeout: 15000 });

    // Optionally, click it and verify details if DetailViewPane is part of this test scope
    await capturedItemInOptionsList.click();
    // Use data-testid for DetailViewPane elements
    await expect(optionsPage.locator('[data-testid="view-title"]')).toHaveText(testTitle);
    await expect(optionsPage.locator('[data-testid="view-url-link"]')).toHaveAttribute('href', testUrl);

    await optionsPage.close();
  });
});