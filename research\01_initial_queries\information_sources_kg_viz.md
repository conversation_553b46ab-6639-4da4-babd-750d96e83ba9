# Information Sources: Best Practices for Intuitive and Effective Visualization of Complex Knowledge Graphs

This document outlines the potential information sources that will be consulted during the research on best practices for visualizing complex knowledge graphs (KGs). The primary information gathering tool will be Perplexity AI, which itself draws from a wide array of web-based resources.

## 1. Primary Information Gathering Tool

*   **Perplexity AI:** Leveraged via the MCP tool for executing targeted search queries based on the [key questions document](key_questions_kg_viz_part1.md). Perplexity AI will be used to access:
    *   Academic papers and research articles.
    *   Technical blogs and articles from industry experts.
    *   Documentation for KG visualization tools and libraries.
    *   Conference proceedings and presentations.
    *   Case studies and white papers.
    *   Relevant books and chapters (summaries or excerpts).

## 2. Types of Sources to be Prioritized via Perplexity AI

When formulating queries for Perplexity AI, the aim will be to retrieve information from the following types of reputable sources:

*   **Academic Databases and Journals:**
    *   IEEE Xplore (e.g., IEEE Transactions on Visualization and Computer Graphics - TVCG)
    *   ACM Digital Library (e.g., ACM Transactions on Computer-Human Interaction - TOCHI, conferences like CHI, InfoVis, VAST)
    *   SpringerLink (e.g., Journal of Visual Languages and Computing)
    *   Elsevier ScienceDirect (e.g., Information Visualization journal)
    *   Google Scholar (for broad academic search)
    *   Semantic Scholar
    *   arXiv (for pre-prints, especially in CS and AI)

*   **Conference Proceedings:**
    *   IEEE VIS (InfoVis, VAST, SciVis)
    *   EuroVis
    *   PacificVis
    *   ACM CHI Conference on Human Factors in Computing Systems
    *   ACM SIGGRAPH
    *   International Semantic Web Conference (ISWC)
    *   The Web Conference (WWW)
    *   Knowledge Graph Conference (KGC)

*   **Technical Blogs and Expert Articles:**
    *   Blogs from companies developing KG visualization tools (e.g., Neo4j, Cambridge Intelligence/KeyLines, Tom Sawyer Software, Graphistry).
    *   Personal blogs of well-known researchers and practitioners in data visualization and graph theory.
    *   Articles on platforms like Medium, Towards Data Science, InfoQ, DZone, Smashing Magazine (for UI/UX aspects).

*   **Tool and Library Documentation:**
    *   Official documentation for tools like Gephi, Cytoscape.js, D3.js, Sigma.js, Vis.js, Graphviz, Neo4j Bloom, yFiles, etc.
    *   GitHub repositories for relevant open-source projects (for READMEs, wikis, and issue discussions).

*   **Books and Reference Works:**
    *   Key textbooks on information visualization, graph drawing, and human-computer interaction. (Perplexity AI might provide summaries or key insights from these).
    *   Examples: "Interactive Data Visualization for the Web" by Scott Murray, "The Grammar of Graphics" by Leland Wilkinson, "Information Visualization: Perception for Design" by Colin Ware, "Graph Drawing: Algorithms for the Visualization of Graphs" by Di Battista et al.

*   **Industry Reports and White Papers:**
    *   Reports from market research firms or consultancies focusing on data analytics, big data, and AI, where KG visualization is discussed.

## 3. Search Strategy

*   **Iterative Querying:** Start with broad queries based on the main sections of the [key questions document](key_questions_kg_viz_part1.md) and progressively refine them based on initial findings and identified gaps.
*   **Keyword Combinations:** Use combinations of keywords such as: "knowledge graph visualization," "complex graph visualization," "intuitive graph UI," "effective graph layout," "interaction techniques for graphs," "handling visual clutter graphs," "evaluating graph visualization," specific tool names + "best practices," specific techniques (e.g., "force-directed layout complexity," "edge bundling knowledge graph").
*   **Citation Chasing:** When Perplexity AI provides sources, especially academic papers, note key citations to potentially explore further (if directly accessible or if Perplexity can summarize them).
*   **Focus on "Best Practices," "Principles," "Guidelines," "Challenges," "Solutions," "Case Studies," "Comparative Analysis."**

## 4. Capturing Citations

A critical aspect of using Perplexity AI will be to ensure that, wherever possible, citations or links to the original sources are captured. This information will be vital for compiling the references section of the final report and for potential deeper dives if required.

By systematically querying these types of sources through Perplexity AI, the research aims to gather comprehensive and reliable information to address the key questions and fulfill the objectives outlined in the [scope definition](scope_definition_kg_viz.md).