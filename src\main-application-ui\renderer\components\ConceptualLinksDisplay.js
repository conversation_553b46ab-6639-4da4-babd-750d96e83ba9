import React from 'react';
import PropTypes from 'prop-types';

const ConceptualLinksDisplay = ({ links, onLinkClick, onHighlight }) => {
  // Test expects null if links is undefined or null.
  if (links === null || typeof links === 'undefined') {
    return null;
  }

  // If links is an empty array (either passed directly or was undefined and now defaulted by caller context if any)
  if (links.length === 0) {
    return <p>No conceptual links to display.</p>;
  }

  return (
    <div>
      <h2>Conceptual Links</h2>
      <ul>
        {links.map((link) => (
          <li key={link.id}>
            <h3
              onClick={() => onLinkClick(link.id)}
              style={{ cursor: 'pointer', textDecoration: 'underline' }}
              role="button" // Added for accessibility, though styling suggests link-like behavior
              tabIndex={0} // Make it focusable
              onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') onLinkClick(link.id); }} // Keyboard activation
            >
              {link.title}
            </h3>
            <p
              onMouseEnter={() => onHighlight(link.id, 'some supporting text segment for ' + link.id)} // Placeholder interaction
              onFocus={() => onHighlight(link.id, 'some supporting text segment for ' + link.id)} // Placeholder interaction
              tabIndex={0} // Make it focusable for keyboard users
            >
              {link.snippet}
            </p>
          </li>
        ))}
      </ul>
    </div>
  );
};

ConceptualLinksDisplay.propTypes = {
  links: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      snippet: PropTypes.string.isRequired,
    })
  ),
  onLinkClick: PropTypes.func.isRequired,
  onHighlight: PropTypes.func.isRequired, // Kept as required based on tests, even if placeholder
};

// ConceptualLinksDisplay.defaultProps = {
//  links: [],
// }; // Replaced with default parameter

export default ConceptualLinksDisplay;