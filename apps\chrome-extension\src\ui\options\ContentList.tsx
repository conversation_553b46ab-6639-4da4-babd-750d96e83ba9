import React, { CSSProperties } from 'react';
import { FixedSizeList as List, ListChildComponentProps } from 'react-window';
import { KnowledgeBaseEntry as KnowledgeBaseItem } from '@pkm-ai/knowledge-base-service/dist/types'; // Aliasing for consistency

interface ContentListProps {
  items: KnowledgeBaseItem[];
  onItemSelect: (item: KnowledgeBaseItem | string) => void; // Can pass ID or full item
  selectedItemId?: string | null;
  onDelete: (id: string) => void; // Added onDelete prop
}

const Row: React.FC<ListChildComponentProps<{
  items: KnowledgeBaseItem[];
  onItemSelect: (item: KnowledgeBaseItem | string) => void;
  selectedItemId?: string | null;
  onDelete: (id: string) => void;
}>> = ({ index, style, data }) => {
  const { items, onItemSelect, selectedItemId, onDelete } = data;
  const item = items[index];

  if (!item) {
    return null;
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering onItemSelect
    onDelete(item.id);
  };

  return (
    <div
      data-testid={`kb-item-${item.id}`}
      style={style as CSSProperties}
      onClick={() => onItemSelect(item)}
      className={`flex justify-between items-center p-3 hover:bg-gray-200 cursor-pointer border-b border-gray-200 ${selectedItemId === item.id ? 'bg-blue-100' : ''}`}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') onItemSelect(item);}}
      aria-pressed={selectedItemId === item.id}
    >
      <div className="flex-grow overflow-hidden">
        <h3 className="font-semibold text-sm text-gray-700 truncate">{item.title}</h3>
        <p className="text-xs text-gray-500 truncate">{item.content || 'No content preview'}</p>
      </div>
      <button
        data-testid={`delete-kb-item-${item.id}`}
        onClick={handleDelete}
        className="ml-2 px-2 py-1 text-xs text-red-500 hover:text-red-700 hover:bg-red-100 rounded"
        aria-label={`Delete ${item.title}`}
      >
        Delete
      </button>
    </div>
  );
};

const ContentList: React.FC<ContentListProps> = ({ items, onItemSelect, selectedItemId, onDelete }) => {
  if (!items || items.length === 0) {
    return <p className="p-4 text-gray-500">No items to display.</p>;
  }

  return (
    <div className="h-full w-full">
      <List
        height={600} // Adjust height as needed, or use AutoSizer
        itemCount={items.length}
        itemSize={70} // Increased item size to accommodate delete button
        width="100%"
        itemData={{ items, onItemSelect, selectedItemId, onDelete }}
      >
        {Row}
      </List>
    </div>
  );
};

export default ContentList;