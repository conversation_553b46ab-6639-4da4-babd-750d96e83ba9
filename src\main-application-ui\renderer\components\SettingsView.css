/* SettingsView.css */

.settings-view-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  background-color: #f4f7f9; /* Light background for the whole settings page */
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #dde3e7;
}

.settings-header h1 {
  font-size: 1.8em;
  color: #2c3e50;
  margin: 0;
}

.settings-back-button {
  background-color: #7f8c8d;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s ease;
}

.settings-back-button:hover {
  background-color: #95a5a6;
}

.settings-navigation {
  display: flex;
  margin-bottom: 25px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.settings-navigation button {
  flex-grow: 1;
  padding: 12px 15px;
  background-color: transparent;
  border: none;
  border-bottom: 3px solid transparent; /* For active indicator */
  cursor: pointer;
  font-size: 1em;
  color: #555;
  transition: color 0.2s ease, border-bottom-color 0.2s ease;
  text-align: center;
}

.settings-navigation button:hover {
  color: #3498db;
}

.settings-navigation button.active {
  color: #3498db;
  font-weight: 600;
  border-bottom-color: #3498db;
}

.settings-content {
  flex-grow: 1;
  background-color: #ffffff;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  overflow-y: auto; /* Allows content to scroll if it overflows */
}

.settings-section {
  margin-bottom: 30px;
}

.settings-section h2 {
  font-size: 1.5em;
  color: #34495e;
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ecf0f1;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.form-group input[type="text"],
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 0.95em;
}

.form-group input[type="text"]:read-only {
  background-color: #ecf0f1;
  cursor: not-allowed;
}

.form-group input[type="checkbox"] {
  margin-right: 8px;
  vertical-align: middle;
}

.form-group small {
  display: block;
  margin-top: 5px;
  font-size: 0.85em;
  color: #7f8c8d;
}

.settings-button-primary,
.settings-button-secondary,
.settings-button-danger {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  margin-right: 10px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.settings-button-primary {
  background-color: #3498db;
  color: white;
}
.settings-button-primary:hover {
  background-color: #2980b9;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.settings-button-secondary {
  background-color: #ecf0f1;
  color: #34495e;
  border: 1px solid #bdc3c7;
}
.settings-button-secondary:hover {
  background-color: #dde1e2;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.settings-button-danger {
  background-color: #e74c3c;
  color: white;
}
.settings-button-danger:hover {
  background-color: #c0392b;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.settings-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.settings-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ecf0f1;
}

.settings-list-item:last-child {
  border-bottom: none;
}

.settings-list-item span {
  color: #333;
}

.settings-list-item div button {
  margin-left: 8px;
}

.settings-search-input {
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 0.95em;
}

.settings-section p {
    line-height: 1.6;
    margin-bottom: 10px;
}

.settings-section a {
    color: #3498db;
    text-decoration: none;
}

.settings-section a:hover {
    text-decoration: underline;
}