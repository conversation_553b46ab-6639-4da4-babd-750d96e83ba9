import React, { useState, useEffect } from 'react';
import useStore from '../store/useStore';
import './CategoryManagement.css'; // We'll create this CSS file next

const CategoryManagement = () => {
  const {
    allCategories,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    categoryOperationLoading,
    categoryOperationError,
  } = useStore();

  const [newCategoryName, setNewCategoryName] = useState('');
  const [editingCategory, setEditingCategory] = useState(null); // { id: string, name: string }
  const [updatedCategoryName, setUpdatedCategoryName] = useState('');

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleCreateCategory = async (e) => {
    e.preventDefault();
    if (newCategoryName.trim()) {
      await createCategory(newCategoryName.trim());
      setNewCategoryName('');
    }
  };

  const handleEditCategory = (category) => {
    setEditingCategory(category);
    setUpdatedCategoryName(category.name);
  };

  const handleCancelEdit = () => {
    setEditingCategory(null);
    setUpdatedCategoryName('');
  };

  const handleUpdateCategory = async (e) => {
    e.preventDefault();
    if (editingCategory && updatedCategoryName.trim()) {
      await updateCategory(editingCategory.id, updatedCategoryName.trim());
      setEditingCategory(null);
      setUpdatedCategoryName('');
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      await deleteCategory(categoryId);
    }
  };

  return (
    <div className="settings-section category-management-section">
      <h2>Manage Categories</h2>
      {categoryOperationError && <p className="error-message">Error: {categoryOperationError}</p>}

      <form onSubmit={handleCreateCategory} className="category-form">
        <input
          type="text"
          value={newCategoryName}
          onChange={(e) => setNewCategoryName(e.target.value)}
          placeholder="Enter new category name"
          disabled={categoryOperationLoading}
        />
        <button type="submit" disabled={categoryOperationLoading || !newCategoryName.trim()}>
          {categoryOperationLoading ? 'Adding...' : 'Add Category'}
        </button>
      </form>

      {editingCategory && (
        <div className="edit-category-modal-overlay">
          <div className="edit-category-modal">
            <h3>Edit Category</h3>
            <form onSubmit={handleUpdateCategory}>
              <input
                type="text"
                value={updatedCategoryName}
                onChange={(e) => setUpdatedCategoryName(e.target.value)}
                placeholder="Enter updated category name"
                disabled={categoryOperationLoading}
              />
              <div className="edit-category-actions">
                <button type="submit" disabled={categoryOperationLoading || !updatedCategoryName.trim()}>
                  {categoryOperationLoading ? 'Saving...' : 'Save Changes'}
                </button>
                <button type="button" onClick={handleCancelEdit} disabled={categoryOperationLoading}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <ul className="category-list">
        {allCategories.length === 0 && !categoryOperationLoading && <li>No categories found.</li>}
        {allCategories.map((category) => (
          <li key={category.id} className="category-list-item">
            <span className="category-name">{category.name}</span>
            <div className="category-actions">
              <button onClick={() => handleEditCategory(category)} disabled={categoryOperationLoading}>
                Edit
              </button>
              <button onClick={() => handleDeleteCategory(category.id)} disabled={categoryOperationLoading} className="delete-button">
                Delete
              </button>
            </div>
          </li>
        ))}
      </ul>
      {categoryOperationLoading && <p>Loading categories...</p>}
    </div>
  );
};

export default CategoryManagement;