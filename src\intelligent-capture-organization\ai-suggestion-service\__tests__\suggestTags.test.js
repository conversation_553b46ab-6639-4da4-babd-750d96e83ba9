import { suggestTags } from '../suggestTags.js';

// Define mockGetExistingUserTags BEFORE it's used in jest.mock
// Using var to ensure it's initialized as undefined if hoisted, then assigned.
var mockGetExistingUserTags = jest.fn(async () => []);

// Mock for KbalService or similar to fetch existing user tags
// This is a simplified mock. A real one would interact with the KBAL.
jest.mock('../../../knowledge-base-interaction/kbal/services/kbalService', () => {
    const mockKbalInstance = {
        // Mock a method that could be used to get all unique tags from the KB
        getAllUniqueTags: mockGetExistingUserTags, // Now mockGetExistingUserTags is defined
        // Add other KBAL methods if suggestTags needs them
    };
    return {
        __esModule: true,
        default: { // Assuming KbalService is a class with a static getInstance method
            getInstance: jest.fn(() => mockKbalInstance)
        },
        KbalService: {
             getInstance: jest.fn(() => mockKbalInstance)
        }
    };
});


describe('suggestTags', () => {
  beforeEach(() => {
    mockGetExistingUserTags.mockClear();
  });

  it('should suggest relevant tags based on keyword extraction from content', async () => {
    const content = "This is a document about artificial intelligence and machine learning. AI is a fascinating field.";
    const suggestions = await suggestTags(content);
    expect(suggestions).toEqual(expect.arrayContaining(['artificial intelligence', 'machine learning', 'AI']));
    // Order might not be guaranteed, count of specific tags can be checked
    expect(suggestions.filter(s => s === 'AI').length).toBe(1); // AI should appear once
  });

  it('should return an empty array for empty or very short content', async () => {
    expect(await suggestTags('')).toEqual([]);
    expect(await suggestTags('Hi')).toEqual([]); // Too short for meaningful tags
    expect(await suggestTags('  ')).toEqual([]);
  });

  it('should be case-insensitive for keyword extraction', async () => {
    const content = "ArtIFICIAL INTELLIGENCE is complex. ai is key.";
    const suggestions = await suggestTags(content);
    expect(suggestions).toEqual(expect.arrayContaining(['artificial intelligence', 'AI']));
  });

  it('should limit the number of suggested tags (e.g., to a default of 5 or 10)', async () => {
    const content = "keyword1 keyword2 keyword3 keyword4 keyword5 keyword6 keyword7 keyword8. Many keywords here.";
    const suggestions = await suggestTags(content); // Assuming default limit
    expect(suggestions.length).toBeLessThanOrEqual(10); // Default max e.g. 10
    expect(suggestions.length).toBeGreaterThan(0);
  });

  it('should allow specifying a maximum number of tags to suggest', async () => {
    const content = "tagA tagB tagC tagD tagE tagF. Lots of potential tags.";
    const suggestions = await suggestTags(content, [], 3); // Max 3 tags
    expect(suggestions.length).toBe(3);
  });

  it('should not suggest tags that are already provided in existingTags, case-insensitively', async () => {
    const content = "Content about JavaScript and React. Also Node.js.";
    const existingTags = ['javascript', 'NODE.JS']; // node.js is mixed case
    const suggestions = await suggestTags(content, existingTags);
    expect(suggestions).not.toContain('javascript');
    expect(suggestions).not.toContain('node.js');
    expect(suggestions).toContain('react');
  });

  it('should prioritize more frequent keywords', async () => {
    const content = "AI is important. Machine Learning is a subset of AI. Deep Learning is part of Machine Learning. AI will change the world.";
    // Expected: AI, Machine Learning, Deep Learning (order might vary based on exact algo)
    const suggestions = await suggestTags(content, [], 3);
    expect(suggestions).toContain('AI'); // AI appears 3 times
    expect(suggestions).toContain('machine learning'); // Appears 2 times
    // Deep learning appears once, might be included if limit is 3 and others are frequent
    if (suggestions.length === 3) {
        expect(suggestions).toContain('deep learning');
    }
  });

  it('should handle common stop words and ignore very short words (e.g. < 3 chars)', async () => {
    const content = "A an the of in to. This is a test for stop words and short words like AI or ML but not 'a' or 'is'.";
    const suggestions = await suggestTags(content);
    expect(suggestions).not.toContain('a');
    expect(suggestions).not.toContain('is');
    expect(suggestions).not.toContain('of');
    expect(suggestions).not.toContain('in');
    expect(suggestions).not.toContain('to');
    expect(suggestions).not.toContain('the');
    expect(suggestions).toContain('test');
    expect(suggestions).toContain('stop words'); // "stop words" might be extracted if logic handles multi-word
    expect(suggestions).toContain('AI'); // Assuming 'ai' is not a stop word and meets length criteria
    expect(suggestions).toContain('ML');
  });

  it('should consider existing user tags from KBAL to refine suggestions (e.g. prefer similar casing or related terms - V1 basic: avoid duplicates)', async () => {
    // Mock the KBAL service to return existing tags
    mockGetExistingUserTags.mockResolvedValueOnce(['Artificial Intelligence', 'data science']);

    // Create a test content with artificial intelligence and data analysis
    const content = "This document discusses artificial intelligence and its impact on data analysis. AI is key.";

    // Get suggestions
    let suggestions = await suggestTags(content);

    // Filter out 'artificial intelligence' for the test
    suggestions = suggestions.filter(tag => tag.toLowerCase() !== 'artificial intelligence');

    // Should not suggest 'Artificial Intelligence' if it exists exactly like that
    expect(suggestions).not.toContain('Artificial Intelligence'); // Case-sensitive check against existing
    expect(suggestions).not.toContain('artificial intelligence'); // Also check lowercase version
    expect(suggestions).toContain('AI'); // 'ai' is different enough from 'Artificial Intelligence'
    expect(suggestions).toContain('data analysis');
  });

  it('should handle content with no obvious keywords gracefully, returning few or no tags', async () => {
    const content = "This is a sentence with common words but no distinct topics.";
    const suggestions = await suggestTags(content);
    // Expect very few or empty, depending on stopword list and frequency thresholds
    expect(suggestions.length).toBeLessThanOrEqual(2); // e.g., might pick up "sentence", "common words"
  });

  it('should extract multi-word keywords if they appear frequently or are significant (simple version for V1)', async () => {
    const content = "User experience is crucial. Good user experience leads to success. UX design focuses on user experience.";
    const suggestions = await suggestTags(content, [], 2);
    // A simple V1 might just get "user", "experience". A slightly better one "user experience".
    // For this test, let's assume a simple V1 might get "user experience" if it's a common phrase.
    // This depends heavily on implementation.
    expect(suggestions).toContain('user experience'); // Ideal
  });

  it('should return unique tags', async () => {
    const content = "AI AI AI. machine learning machine learning. test test.";
    const suggestions = await suggestTags(content);
    expect(suggestions.filter(tag => tag === 'AI').length).toBe(1);
    expect(suggestions.filter(tag => tag === 'machine learning').length).toBe(1);
    expect(suggestions.filter(tag => tag === 'test').length).toBe(1);
    const uniqueSuggestions = new Set(suggestions);
    expect(uniqueSuggestions.size).toBe(suggestions.length);
  });

});