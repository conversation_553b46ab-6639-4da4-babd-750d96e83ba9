import { createStore } from 'zustand/vanilla';
import { useStore } from 'zustand'; // Use the default export useStore

// Access the exposed electronAPI
const electronAPI = window.electronAPI;

// Define a basic store
const store = createStore((set, get) => ({
  // Example state: a list of items
  items: [
    { id: 'sample-1', name: 'Sample Item 1', description: 'This is the first sample item.' },
    { id: 'sample-2', name: 'Sample Item 2', description: 'Another item for demonstration.' },
    { id: 'sample-3', name: 'Sample Item 3', description: 'Yet another placeholder.' },
  ],
  selectedItemId: null,

  // Conceptual Links State
  conceptualLinks: [],
  conceptualLinksLoading: false,
  conceptualLinksError: null,

  // Manual Links State
  manualLinks: [],
  manualLinksLoading: false,
  manualLinksError: null,
  manualLinkCreating: false,
  manualLinkCreateError: null,
  manualLinkDeleting: false,
  manualLinkDeleteError: null,

  // Search State
  searchTerm: '',
  searchResults: [],
  searchLoading: false,
  searchError: null,

  // Advanced Filter States
  filterTags: ['tech', 'productivity', 'react', 'javascript', 'ai'], // Example available tags
  selectedTags: [],
  dateRangeFilter: { startDate: null, endDate: null }, // Example date filter
  allContentTypes: ['article', 'bookmark', 'pdf', 'selection', 'note'], // Example content types
  selectedContentTypes: [],
  filterCategories: ['work', 'personal', 'learning', 'research'], // Example categories
  selectedCategories: [],
  sourceFilter: '',
  tagFilterLogic: 'AND', // 'AND' or 'OR'

  // Tag Management State
  allTags: [], // Complete list of available tags for management
  tagOperationLoading: false,
  tagOperationError: null,

  // Category Management State
  allCategories: [], // Complete list of available categories for management
  categoryOperationLoading: false,
  categoryOperationError: null,

  // Capture Settings State
  captureSettings: {
    defaultFormat: 'markdown', // e.g., 'markdown', 'html', 'text'
    extractTitle: true,
    extractUrl: true,
    extractPublicationDate: true,
    enableAiAutoTagging: false,
  },
  captureSettingsLoading: false,
  captureSettingsError: null,
  captureSettingsSaving: false,
  captureSettingsSaveError: null,

  // Clipping Templates State
  clippingTemplates: [],
  clippingTemplatesLoading: false,
  clippingTemplatesError: null,
  clippingTemplateCreating: false,
  clippingTemplateCreateError: null,
  clippingTemplateUpdating: false,
  clippingTemplateUpdateError: null,
  clippingTemplateDeleting: false,
  clippingTemplateDeleteError: null,
  clippingTemplateSettingDefault: false,
  clippingTemplateSetDefaultError: null,

  // Data Management State
  exportInProgress: false,
  exportError: null,
  importInProgress: false,
  importError: null,

  // Actions for Search and Filter Execution
  performSearch: async (query) => {
    const { selectedTags, dateRangeFilter, selectedContentTypes, selectedCategories, sourceFilter, tagFilterLogic } = get();

    // If query is empty and no filters are active, clear results and return
    const noActiveFilters = (
      selectedTags.length === 0 &&
      !dateRangeFilter.startDate && !dateRangeFilter.endDate &&
      selectedContentTypes.length === 0 &&
      selectedCategories.length === 0 &&
      (!sourceFilter || sourceFilter.trim() === '')
    );

    if ((!query || query.trim() === '') && noActiveFilters) {
      set({ searchResults: [], searchTerm: query || '', searchLoading: false, searchError: null });
      return;
    }

    set({ searchLoading: true, searchError: null, searchTerm: query || '' });
    try {
      // Map store filters to API query criteria
      const filterParams = {};
      if (selectedTags.length > 0) {
        filterParams.tags = selectedTags; // Pass as array
        filterParams.tagLogic = tagFilterLogic; // Add tag logic
      }
      if (dateRangeFilter.startDate) {
        filterParams.createdAfter = dateRangeFilter.startDate;
      }
      if (dateRangeFilter.endDate) {
        filterParams.createdBefore = dateRangeFilter.endDate;
      }
      if (selectedContentTypes.length > 0) {
        // Assuming KBAL queryContent supports 'type' (singular) for now
        filterParams.type = selectedContentTypes[0]; // Taking the first type for now
      }
      if (selectedCategories.length > 0) {
        // Assuming KBAL queryContent supports 'category' (singular) for now
        filterParams.category = selectedCategories[0]; // Taking the first category for now
      }
      if (sourceFilter && sourceFilter.trim() !== '') {
        filterParams.source = sourceFilter.trim();
      }

      // Pass both query and filterParams to the IPC handler
      const results = await electronAPI.kbalService.queryContent({ query: query || '', ...filterParams });

      set({ searchResults: results || [], searchLoading: false });
    } catch (error) {
      console.error('Search failed:', error);
      set({ searchResults: [], searchLoading: false, searchError: error.message || 'Failed to perform search.' });
    }
  },
  clearSearch: () => set((state) => ({
    searchTerm: '',
    searchResults: [],
    searchLoading: false,
    searchError: null,
    // Decide if advanced filters should also be cleared here
    // For now, they persist until explicitly cleared
  })),

  // Actions for Q&A
  qaQuestion: '',
  qaAnswer: null,
  qaLoading: false,
  qaError: null,

  setQaQuestion: (question) => set({ qaQuestion: question, qaError: null }), // Clear error when question changes
  submitQaQuestion: async (itemId, question) => {
    if (!itemId || !question || question.trim() === '') {
      set({ qaAnswer: null, qaLoading: false, qaError: 'Item ID and question are required.' });
      return;
    }
    set({ qaLoading: true, qaError: null, qaAnswer: null });
    try {
      const answer = await electronAPI.kbalService.askQuestion(itemId, question); // Use IPC
      set({ qaAnswer: answer, qaLoading: false });
    } catch (error) {
      console.error('Q&A failed:', error);
      set({ qaAnswer: null, qaLoading: false, qaError: error.message || 'Failed to get an answer.' });
    }
  },
  clearQa: () => set({ qaQuestion: '', qaAnswer: null, qaLoading: false, qaError: null }),

  // Actions for Summarization
  summary: null,
  summaryLoading: false,
  summaryError: null,
  summaryGenerated: false, // Flag to indicate if a summary has been successfully generated

  fetchSummary: async (itemId) => {
    if (!itemId) {
      set({ summary: null, summaryLoading: false, summaryError: 'Item ID is required for summarization.', summaryGenerated: false });
      return;
    }
    set({ summaryLoading: true, summaryError: null, summary: null, summaryGenerated: false });
    try {
      const summaryText = await electronAPI.kbalService.summarizeItem(itemId); // Use IPC
      set({ summary: summaryText, summaryLoading: false, summaryGenerated: true });
    } catch (error) {
      console.error('Summarization failed:', error);
      set({ summary: null, summaryLoading: false, summaryError: error.message || 'Failed to generate summary.', summaryGenerated: false });
    }
  },
  clearSummary: () => set({ summary: null, summaryLoading: false, summaryError: null, summaryGenerated: false }),

  // Actions for Conceptual Links
  fetchConceptualLinks: async (itemId) => {
    if (!itemId) {
      set({ conceptualLinks: [], conceptualLinksLoading: false, conceptualLinksError: 'Item ID is required to fetch conceptual links.' });
      return;
    }
    set({ conceptualLinksLoading: true, conceptualLinksError: null });
    try {
      const links = await electronAPI.kbalService.getConceptualLinks(itemId); // Use IPC
      set({ conceptualLinks: links || [], conceptualLinksLoading: false });
    } catch (error) {
      console.error('Fetching conceptual links failed:', error);
      set({ conceptualLinks: [], conceptualLinksLoading: false, conceptualLinksError: error.message || 'Failed to fetch conceptual links.' });
    }
  },
  clearConceptualLinks: () => set({ conceptualLinks: [], conceptualLinksLoading: false, conceptualLinksError: null }),

  // Actions for Manual Links
  fetchManualLinks: async (itemId) => {
    if (!itemId) {
      set({ manualLinks: [], manualLinksLoading: false, manualLinksError: 'Item ID is required to fetch manual links.' });
      return;
    }
    set({ manualLinksLoading: true, manualLinksError: null });
    try {
      const links = await electronAPI.kbalService.getManualLinks(itemId); // Use IPC
      set({ manualLinks: links || [], manualLinksLoading: false });
    } catch (error) {
      console.error('Fetching manual links failed:', error);
      set({ manualLinks: [], manualLinksLoading: false, manualLinksError: error.message || 'Failed to fetch manual links.' });
    }
  },
  createManualLink: async (sourceItemId, targetItemId, linkType, description) => {
    if (!sourceItemId || !targetItemId) {
      set({ manualLinkCreating: false, manualLinkCreateError: 'Source and Target Item IDs are required.' });
      return;
    }
    set({ manualLinkCreating: true, manualLinkCreateError: null });
    try {
      const newLink = await electronAPI.kbalService.addManualLink(sourceItemId, { targetItemId, linkType, description }); // Use IPC
      set((state) => ({
        manualLinks: [...state.manualLinks, newLink], // Assuming the API returns the created link
        manualLinkCreating: false,
      }));
    } catch (error) {
      console.error('Creating manual link failed:', error);
      set({ manualLinkCreating: false, manualLinkCreateError: error.message || 'Failed to create manual link.' });
    }
  },
  deleteManualLink: async (sourceItemId, linkId) => {
    if (!sourceItemId || !linkId) {
      set({ manualLinkDeleting: false, manualLinkDeleteError: 'Source Item ID and Link ID are required.' });
      return;
    }
    set({ manualLinkDeleting: true, manualLinkDeleteError: null });
    try {
      await electronAPI.kbalService.removeManualLink(sourceItemId, linkId); // Use IPC
      set((state) => ({
        manualLinks: state.manualLinks.filter((link) => link.linkId !== linkId), // Assuming link object has linkId
        manualLinkDeleting: false,
      }));
    } catch (error) {
      console.error('Deleting manual link failed:', error);
      set({ manualLinkDeleting: false, manualLinkDeleteError: error.message || 'Failed to delete manual link.' });
    }
  },
  clearManualLinks: () => set({ manualLinks: [], manualLinksLoading: false, manualLinksError: null }),
  clearManualLinkStatus: () => set({ // Action to clear create/delete status/errors
    manualLinkCreating: false,
    manualLinkCreateError: null,
    manualLinkDeleting: false,
    manualLinkDeleteError: null,
  }),

  // Content Transformation State
  selectedTransformationType: null,
  transformedContent: null,
  transformationLoading: false,
  transformationError: null,

  // Content Transformation Actions
  setSelectedTransformationType: (type) => set({ selectedTransformationType: type, transformationError: null, transformedContent: null }), // Clear previous state
  transformItemContent: async (itemId, transformationType, selectedText = null) => {
    if (!itemId || !transformationType) {
      set({ transformedContent: null, transformationLoading: false, transformationError: 'Item ID and transformation type are required.' });
      return;
    }
    set({ transformationLoading: true, transformationError: null, transformedContent: null });
    try {
      // Pass selectedText if available, otherwise API should use full content
      const result = await electronAPI.kbalService.transformContent(itemId, transformationType, selectedText); // Use IPC
      set({ transformedContent: result.transformedContent, transformationLoading: false }); // Assuming API returns { transformedContent: '...' }
    } catch (error) {
      console.error('Content transformation failed:', error);
      set({ transformedContent: null, transformationLoading: false, transformationError: error.message || 'Failed to transform content.' });
    }
  },
  clearTransformationState: () => set({
    selectedTransformationType: null,
    transformedContent: null,
    transformationLoading: false,
    transformationError: null,
  }),

  // State for saving transformed content as a new item
  savingNewItemLoading: false,
  savingNewItemError: null,

  // Action to save transformed content as a new item
  saveTransformedContentAsNewItem: async () => {
    const { transformedContent, selectedItemId, items, clearTransformationState } = get();

    if (!transformedContent) {
      set({ savingNewItemError: 'No transformed content to save.', savingNewItemLoading: false });
      return;
    }

    // Prepare data for the new item
    const originalItem = items.find((item) => item.id === selectedItemId);
    let title = 'Transformed Content';
    let tags = [];
    if (originalItem) {
      title = `Transformed: ${originalItem.name || originalItem.title || 'Untitled'}`;
      tags = originalItem.tags || []; // Copy tags from original item
    }

    const newItemData = {
      title,
      content: transformedContent,
      tags,
      source: originalItem?.source || 'Transformed', // Add source information
      itemType: originalItem?.itemType || 'note', // Copy item type
    };

    set({ savingNewItemLoading: true, savingNewItemError: null });
    try {
      const createdItem = await electronAPI.kbalService.addContent(newItemData); // Use IPC
      set((state) => ({ savingNewItemLoading: false })); // Update loading state
      clearTransformationState(); // Clear transformation state after saving
      console.log('New item created successfully:', createdItem);
      // Consider adding a success message/toast for the user
    } catch (error) {
      console.error('Failed to save new item:', error);
      set({ savingNewItemLoading: false, savingNewItemError: error.message || 'Failed to save new item.' });
    }
  },
  clearSaveNewItemStatus: () => set({
    savingNewItemLoading: false,
    savingNewItemError: null,
  }),

  // Actions for Tag Management
  fetchTags: async () => {
    set({ tagOperationLoading: true, tagOperationError: null });
    try {
      const tags = await electronAPI.kbalService.fetchTags(); // Use IPC
      set({ allTags: tags || [], tagOperationLoading: false });
    } catch (error) {
      console.error('Fetching tags failed:', error);
      set({ allTags: [], tagOperationLoading: false, tagOperationError: error.message || 'Failed to fetch tags.' });
    }
  },
  createTag: async (name) => {
    set({ tagOperationLoading: true, tagOperationError: null });
    try {
      const newTag = await electronAPI.kbalService.createTag({ name }); // Use IPC
      set((state) => ({
        allTags: [...state.allTags, newTag],
        tagOperationLoading: false,
      }));
      // If the new tag is set as default, update others
      if (newTag.isDefault) {
        set((state) => ({
          allTags: state.allTags.map(tag => tag.id === newTag.id ? newTag : { ...tag, isDefault: false }),
        }));
      }
      return newTag;
    } catch (error) {
      console.error('Creating tag failed:', error);
      set({ tagOperationLoading: false, tagOperationError: error.message || 'Failed to create tag.' });
      throw error; // Re-throw to allow component to handle
    }
  },
  updateTag: async (tagId, newName) => {
    set({ tagOperationLoading: true, tagOperationError: null });
    try {
      const updatedTag = await electronAPI.kbalService.updateTag(tagId, { name: newName }); // Use IPC
      set((state) => ({
        allTags: state.allTags.map(tag => tag.id === tagId ? updatedTag : tag),
        tagOperationLoading: false,
      }));
      // If the updated tag is set as default, update others
      if (updatedTag.isDefault) {
        set((state) => ({
          allTags: state.allTags.map(t => t.id === updatedTag.id ? updatedTag : { ...t, isDefault: false }),
        }));
      }
      return updatedTag;
    } catch (error) {
      console.error('Updating tag failed:', error);
      set({ tagOperationLoading: false, tagOperationError: error.message || 'Failed to update tag.' });
      throw error; // Re-throw to allow component to handle
    }
  },
  deleteTag: async (tagId) => {
    set({ tagOperationLoading: true, tagOperationError: null });
    try {
      await electronAPI.kbalService.deleteTag(tagId); // Use IPC
      set((state) => ({
        allTags: state.allTags.filter(tag => tag.id !== tagId),
        tagOperationLoading: false,
      }));
    } catch (error) {
      console.error('Deleting tag failed:', error);
      set({ tagOperationLoading: false, tagOperationError: error.message || 'Failed to delete tag.' });
      throw error; // Re-throw to allow component to handle
    }
  },

  // Actions for Category Management
  fetchCategories: async () => {
    set({ categoryOperationLoading: true, categoryOperationError: null });
    try {
      const categories = await electronAPI.kbalService.fetchCategories(); // Use IPC
      set({ allCategories: categories || [], categoryOperationLoading: false });
    } catch (error) {
      console.error('Fetching categories failed:', error);
      set({ allCategories: [], categoryOperationLoading: false, categoryOperationError: error.message || 'Failed to fetch categories.' });
    }
  },
  createCategory: async (name) => {
    set({ categoryOperationLoading: true, categoryOperationError: null });
    try {
      const newCategory = await electronAPI.kbalService.createCategory({ name }); // Use IPC
      set((state) => ({
        allCategories: [...state.allCategories, newCategory],
        categoryOperationLoading: false,
      }));
      return newCategory;
    } catch (error) {
      console.error('Creating category failed:', error);
      set({ categoryOperationLoading: false, categoryOperationError: error.message || 'Failed to create category.' });
      throw error; // Re-throw to allow component to handle
    }
  },
  updateCategory: async (categoryId, newName) => {
    set({ categoryOperationLoading: true, categoryOperationError: null });
    try {
      const updatedCategory = await electronAPI.kbalService.updateCategory(categoryId, { name: newName }); // Use IPC
      set((state) => ({
        allCategories: state.allCategories.map(cat => cat.id === categoryId ? updatedCategory : cat),
        categoryOperationLoading: false,
      }));
      return updatedCategory;
    } catch (error) {
      console.error('Updating category failed:', error);
      set({ categoryOperationLoading: false, categoryOperationError: error.message || 'Failed to update category.' });
      throw error; // Re-throw to allow component to handle
    }
  },
  deleteCategory: async (categoryId) => {
    set({ categoryOperationLoading: true, categoryOperationError: null });
    try {
      await electronAPI.kbalService.deleteCategory(categoryId); // Use IPC
      set((state) => ({
        allCategories: state.allCategories.filter(cat => cat.id !== categoryId),
        categoryOperationLoading: false,
      }));
    } catch (error) {
      console.error('Deleting category failed:', error);
      set({ categoryOperationLoading: false, categoryOperationError: error.message || 'Failed to delete category.' });
      throw error; // Re-throw to allow component to handle
    }
  },

  // Actions for Capture Settings
  fetchCaptureSettings: async () => {
    set({ captureSettingsLoading: true, captureSettingsError: null });
    try {
      const settings = await electronAPI.kbalService.getCaptureSettings(); // Use IPC
      set({
        captureSettings: settings || get().captureSettings, // Use returned settings or fallback to current/default
        captureSettingsLoading: false,
      });
    } catch (error) {
      console.error('Fetching capture settings failed:', error);
      set({
        captureSettingsLoading: false,
        captureSettingsError: error.message || 'Failed to fetch capture settings.',
      });
    }
  },
  updateCaptureSetting: (settingKey, value) => {
    set((state) => ({
      captureSettings: {
        ...state.captureSettings,
        [settingKey]: value,
      },
    }));
  },
  saveCaptureSettings: async (settingsObject) => {
    // If settingsObject is not provided, use the current state
    const settingsToSave = settingsObject || get().captureSettings;
    set({ captureSettingsSaving: true, captureSettingsSaveError: null });
    try {
      const updatedSettings = await electronAPI.kbalService.putCaptureSettings(settingsToSave); // Use IPC
      set({
        captureSettings: updatedSettings || settingsToSave, // Use returned settings or fallback
        captureSettingsSaving: false,
      });
      // Optional: show a success message
    } catch (error) {
      console.error('Saving capture settings failed:', error);
      set({
        captureSettingsSaving: false,
        captureSettingsSaveError: error.message || 'Failed to save capture settings.',
      });
    }
  },
  clearCaptureSettingsStatus: () => set({
    captureSettingsLoading: false,
    captureSettingsError: null,
    captureSettingsSaving: false,
    captureSettingsSaveError: null,
  }),

  // Actions for Clipping Templates
  fetchClippingTemplates: async () => {
    set({ clippingTemplatesLoading: true, clippingTemplatesError: null });
    try {
      const templates = await electronAPI.kbalService.fetchClippingTemplates(); // Use IPC
      set({ clippingTemplates: templates || [], clippingTemplatesLoading: false });
    } catch (error) {
      console.error('Fetching clipping templates failed:', error);
      set({ clippingTemplates: [], clippingTemplatesLoading: false, clippingTemplatesError: error.message || 'Failed to fetch clipping templates.' });
    }
  },
  createClippingTemplate: async (templateData) => {
    set({ clippingTemplateCreating: true, clippingTemplateCreateError: null });
    try {
      const newTemplate = await electronAPI.kbalService.createClippingTemplate(templateData); // Use IPC
      set((state) => ({
        clippingTemplates: [...state.clippingTemplates, newTemplate],
        clippingTemplateCreating: false,
      }));
      // If the new template is set as default, update others
      if (newTemplate.isDefault) {
        set((state) => ({
          clippingTemplates: state.clippingTemplates.map(t => t.id === newTemplate.id ? newTemplate : { ...t, isDefault: false }),
        }));
      }
      return newTemplate;
    } catch (error) {
      console.error('Creating clipping template failed:', error);
      set({ clippingTemplateCreating: false, clippingTemplateCreateError: error.message || 'Failed to create clipping template.' });
      throw error; // Re-throw to allow component to handle
    }
  },
  updateClippingTemplate: async (templateId, templateData) => {
    set({ clippingTemplateUpdating: true, clippingTemplateUpdateError: null });
    try {
      const updatedTemplate = await electronAPI.kbalService.updateClippingTemplate(templateId, templateData); // Use IPC
      set((state) => ({
        clippingTemplates: state.clippingTemplates.map(t => t.id === templateId ? updatedTemplate : t),
        clippingTemplateUpdating: false,
      }));
      // If the updated template is set as default, update others
      if (updatedTemplate.isDefault) {
        set((state) => ({
          clippingTemplates: state.clippingTemplates.map(t => t.id === updatedTemplate.id ? updatedTemplate : { ...t, isDefault: false }),
        }));
      }
      return updatedTemplate;
    } catch (error) {
      console.error('Updating clipping template failed:', error);
      set({ clippingTemplateUpdating: false, clippingTemplateUpdateError: error.message || 'Failed to update clipping template.' });
      throw error; // Re-throw to allow component to handle
    }
  },
  deleteClippingTemplate: async (templateId) => {
    set({ clippingTemplateDeleting: true, clippingTemplateDeleteError: null });
    try {
      await electronAPI.kbalService.deleteClippingTemplate(templateId); // Use IPC
      set((state) => ({
        clippingTemplates: state.clippingTemplates.filter(t => t.id !== templateId),
        clippingTemplateDeleting: false,
      }));
    } catch (error) {
      console.error('Deleting clipping template failed:', error);
      set({ clippingTemplateDeleting: false, clippingTemplateDeleteError: error.message || 'Failed to delete clipping template.' });
      throw error; // Re-throw to allow component to handle
    }
  },
  setDefaultClippingTemplate: async (templateId) => {
    set({ clippingTemplateSettingDefault: true, clippingTemplateSetDefaultError: null });
    try {
      // The API is assumed to handle setting isDefault to true for the specified ID
      // and false for all others. We update the local state to reflect this.
      await electronAPI.kbalService.setDefaultClippingTemplate(templateId); // Use IPC
      set((state) => ({
        clippingTemplates: state.clippingTemplates.map(t => ({
          ...t,
          isDefault: t.id === templateId,
        })),
        clippingTemplateSettingDefault: false,
      }));
    } catch (error) {
      console.error('Setting default clipping template failed:', error);
      set({ clippingTemplateSettingDefault: false, clippingTemplateSetDefaultError: error.message || 'Failed to set default template.' });
      throw error; // Re-throw to allow component to handle
    }
  },
  clearClippingTemplateStatus: () => set({
    clippingTemplatesLoading: false,
    clippingTemplatesError: null,
    clippingTemplateCreating: false,
    clippingTemplateCreateError: null,
    clippingTemplateUpdating: false,
    clippingTemplateUpdateError: null,
    clippingTemplateDeleting: false,
    clippingTemplateDeleteError: null,
    clippingTemplateSettingDefault: false,
    clippingTemplateSetDefaultError: null,
  }),

  // Actions for Data Management
  exportData: async () => {
    set({ exportInProgress: true, exportError: null });
    try {
      // Open a save dialog using Electron IPC
      const filePath = await window.electronAPI.ipcRenderer.invoke('show-save-dialog', {
        title: 'Export Knowledge Base',
        defaultPath: 'knowledge-base-export.zip', // Suggest a default filename and extension
        filters: [ // Define allowed file types
          { name: 'ZIP Archives', extensions: ['zip'] }, // Adjust as per actual export format
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'SQLite Databases', extensions: ['sqlite', 'db'] },
          { name: 'All Files', extensions: ['*'] },
        ],
      });

      if (filePath) {
        // Call the API to trigger the export process, passing the selected file path
        // The backend/main process will handle writing the file to this path
        await electronAPI.kbalService.triggerExport(filePath); // Use IPC
        set({ exportInProgress: false });
        // User will be notified by the main process or a component about success/failure
      } else {
        // User cancelled the dialog
        set({ exportInProgress: false });
      }
    } catch (error) {
      console.error('Export data failed:', error);
      set({ exportInProgress: false, exportError: error.message || 'Failed to export data.' });
      throw error; // Re-throw to allow component to handle
    }
  },
  importData: async () => {
    set({ importInProgress: true, importError: null });
    try {
      // Open an open dialog using Electron IPC to select the file to import
      const filePaths = await window.electronAPI.ipcRenderer.invoke('show-open-dialog', {
        title: 'Import Knowledge Base',
        properties: ['openFile'], // Allow selecting files
        filters: [ // Define allowed file types
          { name: 'Supported Formats', extensions: ['zip', 'json', 'sqlite', 'db'] }, // Adjust as per supported import formats
          { name: 'All Files', extensions: ['*'] },
        ],
      });

      if (filePaths && filePaths.length > 0) {
        const filePath = filePaths[0]; // Get the first selected file path
        // Ask for user confirmation before potentially overwriting data
        const confirmed = window.confirm('Importing will replace existing data. Are you sure you want to proceed?');
        if (confirmed) {
          // Call the API to trigger the import process, passing the selected file path
          // The backend/main process will handle reading the file from this path
          await electronAPI.kbalService.triggerImport(filePath); // Use IPC
          set({ importInProgress: false });
          // User will be notified by the main process or a component about success/failure
          // After successful import, you might want to refresh the item list
          // get().performSearch(''); // Refresh the list
        } else {
          // User cancelled the confirmation
          set({ importInProgress: false });
        }
      } else {
        // User cancelled the dialog
        set({ importInProgress: false });
      }
    } catch (error) {
      console.error('Import data failed:', error);
      set({ importInProgress: false, importError: error.message || 'Failed to import data.' });
      throw error; // Re-throw for component to catch
    }
  },
  clearDataManagementStatus: () => set({
    exportInProgress: false,
    exportError: null,
    importInProgress: false,
    importError: null,
  }),

}));

// Export the hook to be used in React components
// Changed: Renamed useStore to useStoreHook to avoid conflict with import { useStore } from 'zustand'
// Changed: useStoreHook = (selector) => useZustandReactHook(store, selector);
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the default export useStore
// Changed: useStoreHook = (selector) => useStore(store, selector); // Use the
