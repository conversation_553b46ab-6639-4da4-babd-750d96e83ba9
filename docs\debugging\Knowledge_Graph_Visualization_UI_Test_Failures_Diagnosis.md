# Diagnosis Report: Knowledge Graph Visualization (KGV) UI Test Failures

**Date:** 2025-05-15
**Feature Name:** Knowledge Graph Visualization (KGV) UI
**Debugger:** <PERSON><PERSON> (AI Debugger)

## 1. Overview

This report details the diagnosis of 11 test failures encountered during the development of the Knowledge Graph Visualization (KGV) UI feature. The Coder (@Coder_Test_Driven) had attempted implementation using Cytoscape.js, and the failures primarily stem from mismatches between test expectations (often based on insufficient mocks or outdated assumptions) and the actual component behavior, especially concerning Cytoscape.js integration and component property changes.

The analysis confirms the Coder's assessment that significant test refactoring is required, particularly for tests interacting with the Cytoscape.js canvas.

## 2. Summary of Failing Test Suites

*   **[`GraphRenderingArea.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:0): 8 failures**
    *   Primarily due to tests expecting mock DOM elements or interacting with a superficial mock of Cytoscape.js, instead of correctly interacting with the Cytoscape.js instance's API and event model. Layout name expectation issues also contribute if tests hardcode 'force-directed' when 'cose' might be in effect via props.
*   **[`ControlPanel.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js:0): 2 failures**
    *   Due to generic or inaccurate text/role-based queries for selecting interactive elements (e.g., buttons, input labels).
*   **[`KnowledgeGraphVisualizationContainer.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js:0): 1 failure**
    *   The mock for `GraphRenderingArea` (child component) is checked for an initial layout prop of 'force-directed', but the container component now defaults to and passes 'cose'.

## 3. Detailed Diagnosis and Recommendations per File

### 3.1. [`GraphRenderingArea.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/GraphRenderingArea.test.js:0) (8 failures)

*   **Component:** [`GraphRenderingArea.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/GraphRenderingArea.js:0)
*   **Root Cause Hypothesis:**
    *   The tests heavily rely on an inadequate `graphInstanceRef` mock that does not accurately represent the Cytoscape.js API or its canvas-based rendering.
    *   Tests attempt direct DOM event simulation (`fireEvent`) on the container for interactions like zoom/pan, which are internally handled by Cytoscape.js.
    *   Assertions for node/edge rendering and selection are based on the flawed mock or incorrect assumptions about DOM structure.
    *   Layout tests might be failing due to incorrect mock interaction or assertion logic.
*   **Recommendations:**
    1.  **Refactor Mocking Strategy:**
        *   Implement a comprehensive mock for the `cytoscape` module itself using `jest.mock('cytoscape')`. This mock should accurately simulate the Cytoscape API methods used by the component (e.g., `elements()`, `style()`, `layout().run()`, event handling `on()`, `trigger()`, `zoom()`, `pan()`). This allows for inspecting calls and, to some extent, internal state.
        *   Ensure `jest-canvas-mock` is properly configured in `jest.setup.js` to support JSDOM rendering for Cytoscape.
    2.  **Test via Cytoscape API:**
        *   **Event Handling:** Trigger events (node taps, zoom, pan) using the mocked Cytoscape instance's API (e.g., `mockedCyInstance.nodes().first().trigger('tap')`) and assert that the component's callback props (`onNodeSelect`, `onCanvasInteraction`, etc.) are invoked correctly.
        *   **Rendering/Styling:** Verify that nodes/edges are added and styles are applied by checking calls to the mocked Cytoscape instance's methods (e.g., `mockedCyInstance.add()`, `mockedCyInstance.style()`). Query the mocked instance for element counts (`mockedCyInstance.elements('node').length`).
        *   **Layout:** Verify layout application by checking calls to `mockedCyInstance.layout({ name: '...' }).run()`.
    3.  **Align with Component Logic:** Ensure tests reflect how `GraphRenderingArea.js` actually uses Cytoscape.js (e.g., how it attaches event listeners, applies styles, and runs layouts).

### 3.2. [`ControlPanel.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/ControlPanel.test.js:0) (2 failures)

*   **Component:** [`ControlPanel.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/ControlPanel.js:0)
*   **Root Cause Hypothesis:**
    *   **Failure 1 (Likely `TC_KGV_CP_003` - Attribute Filters):** The test uses `screen.getByRole('button', { name: /Apply Filters/i })`. The actual button in `ControlPanel.js` has the text "Apply Defined Filters" and `aria-label="Apply Defined Filters Button"`. The test's regex is too generic and doesn't match the specific button accurately.
    *   **Failure 2 (Likely `TC_KGV_CP_001` - Rendering):** The test `TC_KGV_CP_001` uses `expect(screen.getByText(/Filters/i))`. The actual heading in the component is "Define Attribute Filters". This mismatch will cause the test to fail.
*   **Recommendations:**
    1.  **Use Precise Selectors:**
        *   For the filter application button in `TC_KGV_CP_003`, update the selector to be specific: `screen.getByRole('button', { name: /Apply Defined Filters/i })` or, even better, `screen.getByRole('button', { name: "Apply Defined Filters Button" })`.
        *   For the heading check in `TC_KGV_CP_001`, change `expect(screen.getByText(/Filters/i))` to `expect(screen.getByText(/Define Attribute Filters/i))`.
    2.  **Consider `data-testid`:** For critical interactive elements, add `data-testid` attributes in `ControlPanel.js` and use `screen.getByTestId()` in tests to make them more resilient to text changes.
    3.  **Review All Text-Based Queries:** Systematically verify all text-based queries in the test file against the actual rendered output of `ControlPanel.js`.

### 3.3. [`KnowledgeGraphVisualizationContainer.test.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/__tests__/KnowledgeGraphVisualizationContainer.test.js:0) (1 failure)

*   **Component:** [`KnowledgeGraphVisualizationContainer.js`](src/main-application-ui/renderer/features/knowledge-graph-visualization/components/KnowledgeGraphVisualizationContainer.js:0)
*   **Root Cause Hypothesis (Test `TC_KGV_CNT_002`):**
    *   The `KnowledgeGraphVisualizationContainer.js` component initializes its `layout` state to `'cose'` (as per line 12: `useState('cose')`).
    *   The test `TC_KGV_CNT_002` asserts that the mocked `GraphRenderingArea` initially displays "Layout: force-directed" (line 86 of the test file: `expect(within(graphArea).getByText(/Layout: force-directed/i)).toBeInTheDocument();`).
    *   Since the container passes `layout='cose'` to the mock, the mock renders "Layout: cose", causing the assertion to fail.
*   **Recommendations:**
    1.  **Update Test Expectation:** Modify the assertion in `TC_KGV_CNT_002` (line 86) to reflect the correct default layout:
        ```javascript
        expect(within(graphArea).getByText(/Layout: cose/i)).toBeInTheDocument();
        ```

## 4. Confirmation of Coder's Assessment

The Coder's assessment that the test suite, especially for `GraphRenderingArea.test.js`, needs significant refactoring is **accurate and confirmed**. The primary issue across multiple failures is the inadequacy of the current testing strategy (especially mocking) when dealing with a complex third-party library like Cytoscape.js that manages its own rendering canvas and event system. The other failures are due to outdated expectations or imprecise test selectors.

## 5. General Recommendations for Test Suite Improvement

1.  **Robust Mocking for Cytoscape.js:** Invest time in creating a solid mock for Cytoscape.js or adopt testing strategies that allow interaction with a near-real instance within the JSDOM environment (potentially more complex).
2.  **Test Behavior, Not Implementation Details:** Where possible, focus tests on verifying that the correct props are called (e.g., `onNodeSelect`) with the correct arguments after simulating user interactions (via the Cytoscape API if applicable), rather than asserting deeply on the internal DOM structure of mocked components if those mocks are too simplistic.
3.  **Use `data-testid`:** For less brittle selectors, especially in `ControlPanel` and other form-like components, use `data-testid` attributes.
4.  **Keep Tests Updated:** Ensure tests are updated promptly when component defaults, prop names, or key UI text/labels change.
5.  **Integration Testing with Real Instances:** For testing the integration between `GraphRenderingArea` and `KnowledgeGraphVisualizationContainer`, consider if some tests could run with a real (or more deeply mocked) `GraphRenderingArea` to ensure the props flow correctly and Cytoscape is initialized as expected by the container. This is more complex than unit testing with shallow mocks.

This diagnosis should provide a clear path to resolving the current test failures and improving the overall quality of the KGV UI test suite.