# Installation Guide

This guide provides step-by-step instructions for installing the application and its browser extension.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- Node.js (LTS version recommended)
- npm or yarn package manager

## Application Installation

1.  **Clone the repository:**
    ```bash
    git clone <repository_url>
    cd <repository_directory>
    ```
    (Replace `<repository_url>` and `<repository_directory>` with the actual repository details.)

2.  **Install dependencies:**
    Using npm:
    ```bash
    npm install
    ```
    Using yarn:
    ```bash
    yarn install
    ```

3.  **Build the application:**
    ```bash
    npm run build
    ```
    or
    ```bash
    yarn build
    ```
    This will build the main application and the browser extension.

4.  **Run the application:**
    ```bash
    npm start
    ```
    or
    ```bash
    yarn start
    ```
    The main application window should open.

## Browser Extension Installation

The browser extension is built as part of the application build process (`npm run build` or `yarn build`).

1.  **Open your browser's extension management page:**
    - Chrome: `chrome://extensions/`
    - Firefox: `about:addons` (then click the gear icon and select "Install Add-on From File...")
    - Edge: `edge://extensions/`

2.  **Enable Developer Mode:**
    Toggle the "Developer mode" switch (usually in the top-right corner of the extensions page).

3.  **Load the unpacked extension:**
    - Click "Load unpacked" or "Load Temporary Add-on...".
    - Navigate to the application's build directory (usually `dist` or `build` within the repository).
    - Select the directory containing the browser extension files (e.g., `dist/browser-extension`).

4.  The browser extension should now be installed and visible in your browser's toolbar.