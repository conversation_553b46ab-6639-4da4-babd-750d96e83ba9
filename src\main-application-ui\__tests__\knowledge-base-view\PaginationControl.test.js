import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import PaginationControl from '../../renderer/components/knowledge-base-view/PaginationControl';

describe('PaginationControl', () => {
  const mockOnPageChange = jest.fn();

  beforeEach(() => {
    mockOnPageChange.mockClear();
  });

  test('does not render if totalPages is 1 or less', () => {
    const { container: container1 } = render(
      <PaginationControl
        currentPage={1}
        totalPages={1}
        onPageChange={mockOnPageChange}
        itemsPerPage={10}
        totalItems={10}
      />
    );
    expect(container1.firstChild).toBeNull();

    const { container: container0 } = render(
      <PaginationControl
        currentPage={1}
        totalPages={0}
        onPageChange={mockOnPageChange}
        itemsPerPage={10}
        totalItems={0}
      />
    );
    expect(container0.firstChild).toBeNull();
  });

  test('renders correctly for multiple pages', () => {
    render(
      <PaginationControl
        currentPage={1}
        totalPages={5}
        onPageChange={mockOnPageChange}
        itemsPerPage={10}
        totalItems={45}
      />
    );

    expect(screen.getByLabelText('Go to previous page')).toBeDisabled();
    expect(screen.getByLabelText('Go to next page')).not.toBeDisabled();
    expect(screen.getByText('1')).toHaveAttribute('aria-current', 'page');
    expect(screen.getByText('2')).not.toHaveAttribute('aria-current');
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('Showing 1-10 of 45 items')).toBeInTheDocument();
  });

  test('calls onPageChange with correct page number when a page button is clicked', () => {
    render(
      <PaginationControl
        currentPage={2}
        totalPages={5}
        onPageChange={mockOnPageChange}
        itemsPerPage={10}
        totalItems={45}
      />
    );

    fireEvent.click(screen.getByText('3'));
    expect(mockOnPageChange).toHaveBeenCalledTimes(1);
    expect(mockOnPageChange).toHaveBeenCalledWith(3);
  });

  test('calls onPageChange for "Next" and "Previous" buttons', () => {
    render(
      <PaginationControl
        currentPage={3}
        totalPages={5}
        onPageChange={mockOnPageChange}
        itemsPerPage={10}
        totalItems={45}
      />
    );

    fireEvent.click(screen.getByLabelText('Go to next page'));
    expect(mockOnPageChange).toHaveBeenCalledTimes(1);
    expect(mockOnPageChange).toHaveBeenCalledWith(4);

    mockOnPageChange.mockClear();
    fireEvent.click(screen.getByLabelText('Go to previous page'));
    expect(mockOnPageChange).toHaveBeenCalledTimes(1);
    expect(mockOnPageChange).toHaveBeenCalledWith(2);
  });

  test('"Previous" button is disabled on the first page', () => {
    render(
      <PaginationControl
        currentPage={1}
        totalPages={5}
        onPageChange={mockOnPageChange}
        itemsPerPage={10}
        totalItems={45}
      />
    );
    expect(screen.getByLabelText('Go to previous page')).toBeDisabled();
  });

  test('"Next" button is disabled on the last page', () => {
    render(
      <PaginationControl
        currentPage={5}
        totalPages={5}
        onPageChange={mockOnPageChange}
        itemsPerPage={10}
        totalItems={45}
      />
    );
    expect(screen.getByLabelText('Go to next page')).toBeDisabled();
  });

  test('does not call onPageChange if current page button is clicked', () => {
    render(
      <PaginationControl
        currentPage={2}
        totalPages={5}
        onPageChange={mockOnPageChange}
        itemsPerPage={10}
        totalItems={45}
      />
    );
    fireEvent.click(screen.getByText('2')); // Button for current page
    expect(mockOnPageChange).not.toHaveBeenCalled();
  });

  test('displays correct item range information', () => {
    render(
      <PaginationControl
        currentPage={2}
        totalPages={3}
        onPageChange={mockOnPageChange}
        itemsPerPage={5}
        totalItems={12}
      />
    );
    expect(screen.getByText('Showing 6-10 of 12 items')).toBeInTheDocument();

    render( // Last page, not full
      <PaginationControl
        currentPage={3}
        totalPages={3}
        onPageChange={mockOnPageChange}
        itemsPerPage={5}
        totalItems={12}
      />
    );
    expect(screen.getByText('Showing 11-12 of 12 items')).toBeInTheDocument();
  });

  // Tests for pagination number display logic (ellipsis, first/last page links)
  describe('Pagination Number Display Logic', () => {
    test('shows all page numbers if totalPages <= maxPagesToShow (5)', () => {
      render(<PaginationControl currentPage={1} totalPages={5} onPageChange={mockOnPageChange} itemsPerPage={10} totalItems={50} />);
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
      expect(screen.getByText('4')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.queryByText('...')).not.toBeInTheDocument();
    });

    test('shows leading ellipsis and last page if current page is far from start (e.g. 10 pages, current 7)', () => {
      render(<PaginationControl currentPage={7} totalPages={10} onPageChange={mockOnPageChange} itemsPerPage={10} totalItems={100} />);
      // Expected: 1 ... 5 6 7 8 9 ... 10 (simplified for test, actual logic is more complex)
      // With maxPagesToShow = 5, current = 7: startPage = 5, endPage = 9
      // So it should show: 1 ... 5 6 7 8 9 10
      expect(screen.getByLabelText('Go to page 1')).toBeInTheDocument();
      expect(screen.getAllByText('...').length).toBeGreaterThanOrEqual(1); // Could be one or two depending on exact position
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getByText('6')).toBeInTheDocument();
      expect(screen.getByText('7')).toHaveAttribute('aria-current', 'page');
      expect(screen.getByText('8')).toBeInTheDocument();
      expect(screen.getByText('9')).toBeInTheDocument();
      expect(screen.getByLabelText('Go to page 10')).toBeInTheDocument();
    });
    
    test('shows trailing ellipsis and first page if current page is near start (e.g. 10 pages, current 3)', () => {
        render(<PaginationControl currentPage={3} totalPages={10} onPageChange={mockOnPageChange} itemsPerPage={10} totalItems={100} />);
        // With maxPagesToShow = 5, current = 3: startPage = 1, endPage = 5
        // So it should show: 1 2 3 4 5 ... 10
        expect(screen.getByLabelText('Go to page 1')).toBeInTheDocument();
        expect(screen.getByText('2')).toBeInTheDocument();
        expect(screen.getByText('3')).toHaveAttribute('aria-current', 'page');
        expect(screen.getByText('4')).toBeInTheDocument();
        expect(screen.getByText('5')).toBeInTheDocument();
        expect(screen.getAllByText('...').length).toBe(1);
        expect(screen.getByLabelText('Go to page 10')).toBeInTheDocument();
      });


    test('shows both ellipses if current page is in the middle of many pages (e.g. 20 pages, current 10)', () => {
      render(<PaginationControl currentPage={10} totalPages={20} onPageChange={mockOnPageChange} itemsPerPage={10} totalItems={200} />);
      // With maxPagesToShow = 5, current = 10: startPage = 8, endPage = 12
      // So it should show: 1 ... 8 9 10 11 12 ... 20
      expect(screen.getByLabelText('Go to page 1')).toBeInTheDocument();
      expect(screen.getAllByText('...').length).toBe(2);
      expect(screen.getByText('8')).toBeInTheDocument();
      expect(screen.getByText('9')).toBeInTheDocument();
      expect(screen.getByText('10')).toHaveAttribute('aria-current', 'page');
      expect(screen.getByText('11')).toBeInTheDocument();
      expect(screen.getByText('12')).toBeInTheDocument();
      expect(screen.getByLabelText('Go to page 20')).toBeInTheDocument();
    });

    test('handles edge case: current page is 1, many pages', () => {
        render(<PaginationControl currentPage={1} totalPages={10} onPageChange={mockOnPageChange} itemsPerPage={10} totalItems={100} />);
        // maxPagesToShow = 5, current = 1: startPage = 1, endPage = 5
        // So: 1 2 3 4 5 ... 10
        expect(screen.getByRole('button', {name: 'Go to page 1', current: 'page'})).toBeInTheDocument();
        expect(screen.getByRole('button', {name: 'Go to page 2'})).toBeInTheDocument();
        expect(screen.getByRole('button', {name: 'Go to page 3'})).toBeInTheDocument();
        expect(screen.getByRole('button', {name: 'Go to page 4'})).toBeInTheDocument();
        expect(screen.getByRole('button', {name: 'Go to page 5'})).toBeInTheDocument();
        expect(screen.getAllByText('...').length).toBe(1);
        expect(screen.getByRole('button', {name: 'Go to page 10'})).toBeInTheDocument();
        // Check that there isn't a *second* "Go to page 1" button (the one rendered when startPage > 1)
        const pageOneButtons = screen.getAllByRole('button', {name: 'Go to page 1'});
        expect(pageOneButtons.length).toBe(1);
      });
  
      test('handles edge case: current page is last page, many pages', () => {
        render(<PaginationControl currentPage={10} totalPages={10} onPageChange={mockOnPageChange} itemsPerPage={10} totalItems={100} />);
        // maxPagesToShow = 5, current = 10: startPage = 6, endPage = 10
        // So: 1 ... 6 7 8 9 10
        expect(screen.getByRole('button', {name: 'Go to page 1'})).toBeInTheDocument();
        expect(screen.getAllByText('...').length).toBe(1);
        expect(screen.getByRole('button', {name: 'Go to page 6'})).toBeInTheDocument();
        expect(screen.getByRole('button', {name: 'Go to page 7'})).toBeInTheDocument();
        expect(screen.getByRole('button', {name: 'Go to page 8'})).toBeInTheDocument();
        expect(screen.getByRole('button', {name: 'Go to page 9'})).toBeInTheDocument();
        expect(screen.getByRole('button', {name: 'Go to page 10', current: 'page'})).toBeInTheDocument();
        // Check that there isn't a *second* "Go to page 10" button (the one rendered when endPage < totalPages)
        const pageTenButtons = screen.getAllByRole('button', {name: 'Go to page 10'});
        expect(pageTenButtons.length).toBe(1);
      });
  });
});