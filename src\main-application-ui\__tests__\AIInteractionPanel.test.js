import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import AIInteractionPanel from '../renderer/components/AIInteractionPanel';

describe('AIInteractionPanel', () => {
  const mockOnSubmit = jest.fn();
  const mockOnFeedback = jest.fn();

  beforeEach(() => {
    mockOnSubmit.mockClear();
    mockOnFeedback.mockClear();
  });

  test('renders input area, output area, and placeholders', () => {
    render(<AIInteractionPanel onSubmit={mockOnSubmit} onFeedback={mockOnFeedback} />);
    expect(screen.getByPlaceholderText(/Ask a question or provide input.../i)).toBeInTheDocument();
    expect(screen.getByTestId('ai-output-area')).toBeInTheDocument();
    expect(screen.getByText(/Source Attribution Placeholder/i)).toBeInTheDocument();
    expect(screen.getByText(/Feedback:/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Good/i })).toBeInTheDocument();
  });

  test('handles input change', () => {
    render(<AIInteractionPanel onSubmit={mockOnSubmit} onFeedback={mockOnFeedback} />);
    const inputArea = screen.getByPlaceholderText(/Ask a question or provide input.../i);
    fireEvent.change(inputArea, { target: { value: 'Test question' } });
    expect(inputArea.value).toBe('Test question');
  });

  test('calls onSubmit when submit button is clicked with input', () => {
    render(<AIInteractionPanel onSubmit={mockOnSubmit} onFeedback={mockOnFeedback} />);
    const inputArea = screen.getByPlaceholderText(/Ask a question or provide input.../i);
    const submitButton = screen.getByRole('button', { name: /Submit/i });
    fireEvent.change(inputArea, { target: { value: 'Test question' } });
    fireEvent.click(submitButton);
    expect(mockOnSubmit).toHaveBeenCalledWith('Test question');
  });

  test('does not call onSubmit if input is empty', () => {
    render(<AIInteractionPanel onSubmit={mockOnSubmit} onFeedback={mockOnFeedback} />);
    const submitButton = screen.getByRole('button', { name: /Submit/i });
    fireEvent.click(submitButton);
    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  test('displays AI response in output area', () => {
    render(<AIInteractionPanel 
             onSubmit={mockOnSubmit} 
             onFeedback={mockOnFeedback} 
             aiResponse="This is an AI answer." 
           />);
    expect(screen.getByTestId('ai-output-area')).toHaveTextContent('This is an AI answer.');
  });

  test('calls onFeedback when a feedback button is clicked', () => {
    render(<AIInteractionPanel onSubmit={mockOnSubmit} onFeedback={mockOnFeedback} />);
    const goodFeedbackButton = screen.getByRole('button', { name: /Good/i });
    fireEvent.click(goodFeedbackButton);
    expect(mockOnFeedback).toHaveBeenCalledWith('good');
  });

  test('clears input after submission', () => {
    render(<AIInteractionPanel onSubmit={mockOnSubmit} onFeedback={mockOnFeedback} />);
    const inputArea = screen.getByPlaceholderText(/Ask a question or provide input.../i);
    const submitButton = screen.getByRole('button', { name: /Submit/i });
    fireEvent.change(inputArea, { target: { value: 'Test question' } });
    fireEvent.click(submitButton);
    expect(inputArea.value).toBe('');
  });

});