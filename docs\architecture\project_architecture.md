# High-Level Architecture: Personalized AI Knowledge Companion & PKM Web Clipper

**Version:** 2.0
**Date:** May 19, 2025
**Reason for Update:** Integration of Jonghakseo's `chrome-extension-react-ts-boilerplate` GitHub template.

## 1. Introduction

This document defines the high-level architecture for the Personalized AI Knowledge Companion & PKM Web Clipper project. It is based on the project vision, goals, and modular decomposition outlined in the **updated** [`docs/Master_Project_Plan.md`](docs/Master_Project_Plan.md) (v1.4) and designed to support the successful completion of the AI verifiable tasks and the high-level acceptance criteria defined in the **updated** [`docs/Master_Acceptance_Test_Plan.md`](docs/Master_Acceptance_Test_Plan.md) (v1.1).

This version (2.0) specifically reflects the architectural changes resulting from the integration of **Jonghakseo's `chrome-extension-react-ts-boilerplate` GitHub template**. This template introduces a monorepo structure managed by Turborepo, utilizes Vite as the build tool, and provides a foundation based on React and TypeScript.

The architecture continues to emphasize modularity, a local-first approach for data privacy and ownership (now leveraging `lowdb` for the main knowledge base and `chrome.storage.local` for settings), user control over AI features, responsible AI integration, and openness through the use of standard formats.

## 2. Overall System Architecture (Post-Template Integration)

The system remains a user-centric tool for knowledge capture and management, with enhanced AI capabilities.

```mermaid
C4Context
    title System Context Diagram for Personalized AI Knowledge Companion

    Person(user, "User", "The individual who captures, organizes, and interacts with their knowledge.")

    System(ai_companion, "Personalized AI Knowledge Companion (Chrome Extension)", "The core application for capturing, organizing, and interacting with web content. Built using a monorepo structure.")

    System_Ext(external_ai_service, "External AI Service (e.g., Gemini)", "Provides advanced AI capabilities like summarization, Q&A, and content transformation.")

    Rel(user, ai_companion, "Uses")
    Rel(ai_companion, external_ai_service, "Interacts with", "API (with user consent)")
```

## 3. Container Diagram (Post-Template Integration)

The system's container diagram now reflects the monorepo structure introduced by the `chrome-extension-react-ts-boilerplate`.

```mermaid
C4Container
    title Container Diagram for Personalized AI Knowledge Companion (Post-Template Integration)

    Person(user, "User", "The individual who captures, organizes, and interacts with their knowledge.")

    System_Boundary(monorepo, "Personalized AI Knowledge Companion (Monorepo with Turborepo)") {
        Container(chrome_extension_app, "Chrome Extension App", "apps/chrome-extension (React, TypeScript, Vite)", "Main application logic, UI (Popup, Options, Content Scripts), and module implementations.") {
            Component(popup_ui, "Popup UI", "React", "UI for quick capture and interaction.")
            Component(options_ui, "Options UI", "React", "UI for settings and knowledge base management.")
            Component(content_scripts, "Content Scripts", "JavaScript/TypeScript", "Interact with web pages for capture.")
            Component(background_service, "Background Service Worker", "JavaScript/TypeScript", "Handles core extension logic, communication, event handling, and orchestrates module tasks.")
            Component(wcc_module_logic, "Web Content Capture Logic", "TypeScript", "Logic within chrome_extension_app for capture features (e.g., in apps/chrome-extension/src/capture).")
            Component(ico_module_logic, "Intelligent Capture & Org. Logic", "TypeScript", "Logic for AI-assisted tagging/categorization (e.g., in apps/chrome-extension/src/organization).")
            Component(kbi_module_logic, "Knowledge Base Interaction Logic", "TypeScript", "Logic for search, display, and AI insights on KB (e.g., in apps/chrome-extension/src/knowledge-base-ui).")
            Component(mc_module_logic, "Management & Config Logic", "TypeScript", "Logic for managing settings and user preferences (e.g., in apps/chrome-extension/src/config-ui).")
        }

        System_Boundary(packages_boundary, "Shared Packages (packages/)") {
            Container(kbal_service_pkg, "Knowledge Base Service (KBAL)", "packages/knowledge-base-service (TypeScript, LowDB)", "Provides API for LowDB-based knowledge base storage and retrieval.")
            Container(shared_utils_pkg, "Shared Utilities", "packages/shared-utils (TypeScript)", "Common utility functions and types used across the monorepo.")
            Container(ai_services_pkg, "AI Services Integration", "packages/ai-services (TypeScript)", "Manages communication with External AI Services and local AI utilities.")
        }

        ContainerDb(lowdb_storage, "Knowledge Base Data", "LowDB (local db.json file)", "Stores captured content, metadata, tags, links, and other knowledge items.")
        ContainerDb(chrome_storage, "Extension Settings & State", "chrome.storage.local API", "Stores user preferences, extension operational state, and temporary data.")
    }

    System_Ext(external_ai_service, "External AI Service (e.g., Gemini)", "Provides advanced AI capabilities like summarization, Q&A.")

    Rel(user, popup_ui, "Uses")
    Rel(user, options_ui, "Uses")
    Rel(user, content_scripts, "Interacts with (implicitly via page interaction)")

    Rel(popup_ui, background_service, "Communicates with")
    Rel(options_ui, background_service, "Communicates with")
    Rel(content_scripts, background_service, "Communicates with")

    Rel(background_service, wcc_module_logic, "Delegates to/Uses")
    Rel(background_service, ico_module_logic, "Delegates to/Uses")
    Rel(background_service, kbi_module_logic, "Delegates to/Uses")
    Rel(background_service, mc_module_logic, "Delegates to/Uses")
    Rel(background_service, kbal_service_pkg, "Uses", "For KB operations")
    Rel(background_service, ai_services_pkg, "Uses", "For AI operations")


    Rel(wcc_module_logic, kbal_service_pkg, "Uses", "To save content")
    Rel(ico_module_logic, kbal_service_pkg, "Uses", "To save organized content")
    Rel(ico_module_logic, ai_services_pkg, "Uses", "For suggestions")
    Rel(kbi_module_logic, kbal_service_pkg, "Uses", "To query/retrieve content")
    Rel(kbi_module_logic, ai_services_pkg, "Uses", "For Q&A, summary")
    Rel(mc_module_logic, chrome_storage, "Manages settings in")
    Rel(mc_module_logic, kbal_service_pkg, "Uses", "To manage tags/categories in KB")


    Rel(kbal_service_pkg, lowdb_storage, "Reads/Writes")
    Rel(ai_services_pkg, external_ai_service, "Communicates with", "API")

    Rel(chrome_extension_app, shared_utils_pkg, "Uses")
```

## 4. Component Breakdown and Responsibilities (Post-Template Integration)

The project adopts a **monorepo structure** managed by Turborepo.
-   **`apps/chrome-extension`**: Contains the primary Chrome extension application, including its UI (React), background scripts, content scripts, and the core logic for the feature modules.
-   **`packages/`**: Contains shared code, utilities, and services that can be used by the `chrome-extension` app or potentially other future apps.

### 4.1. User Interface (UI) - within `apps/chrome-extension`

-   **Components:**
    -   **Popup UI (`apps/chrome-extension/src/ui/popup`):** React components for quick capture, interaction, and status display.
    -   **Options UI (`apps/chrome-extension/src/ui/options`):** React components for managing settings, browsing the knowledge base (e.g., [`KnowledgeBaseView.js`](docs/architecture/project_architecture.md:0), [`DetailViewPane.js`](docs/architecture/project_architecture.md:0)), and other advanced configurations.
    -   **Content Scripts UI (if any):** UI elements injected into web pages.
-   **Responsibilities:**
    -   Provide user interaction points for all features.
    -   Display captured content, search results, and AI insights.
    -   Allow configuration of settings.
    -   Handle user input and communicate with the Background Service Worker.
-   **Alignment:** Directly supports user interaction aspects of all High-Level Acceptance Tests (H-LATs 1-10). List virtualization in `KnowledgeBaseView` (using `react-window`) remains crucial for H-LAT 3.1, 4.1, 9.1.

### 4.2. Background Service Worker - within `apps/chrome-extension`

-   **Location:** `apps/chrome-extension/src/background/index.ts` (example path)
-   **Responsibilities:**
    -   Handles core extension logic, event management (e.g., browser actions, messages from UI/content scripts).
    -   Orchestrates tasks by delegating to specific module logic.
    -   Manages communication between different parts of the extension (popup, options, content scripts).
    -   Interacts with shared services from `packages/` (e.g., KBAL, AI Services).
-   **Alignment:** Central to processing and data flow for all H-LATs.

### 4.3. Content Scripts - within `apps/chrome-extension`

-   **Location:** `apps/chrome-extension/src/content` (example path)
-   **Responsibilities:**
    -   Interact directly with web page DOM for content selection and extraction.
    -   Communicate selected content and page metadata to the Background Service Worker.
-   **Alignment:** Essential for H-LAT 1.1 (Capture and Store Web Content).

### 4.4. Web Content Capture Module Logic - within `apps/chrome-extension`

-   **Location:** `apps/chrome-extension/src/capture` (example path, as per MPP Task 1.3)
-   **Responsibilities:**
    -   Process captured web content (article, full page, selection, etc.) received via Background Service Worker.
    -   Extract initial metadata (title, URL, date).
    -   Prepare content for storage via the KBAL service.
-   **Alignment:** Directly supports H-LAT 1.1.

### 4.5. Intelligent Capture & Organization Assistance Module Logic - within `apps/chrome-extension`

-   **Location:** `apps/chrome-extension/src/organization` (example path, as per MPP Task 1.3)
-   **Responsibilities:**
    -   Receive captured content for analysis.
    -   Utilize the `AI Services Integration` package for generating suggestions (tags, categories, summaries).
    -   Handle user interaction for reviewing and applying suggestions.
    -   Prepare organized content for storage via the KBAL service.
-   **Alignment:** Directly supports H-LAT 2.1.

### 4.6. Knowledge Base Interaction & Insights Module Logic - within `apps/chrome-extension`

-   **Location:** `apps/chrome-extension/src/knowledge-base-ui` (example path, as per MPP Task 1.3, for UI-related logic) and potentially `apps/chrome-extension/src/search` for backend search logic.
-   **Responsibilities:**
    -   Handle user queries for search and retrieval via KBAL.
    -   Implement Query Understanding Engine logic.
    -   Utilize the `AI Services Integration` package for Q&A, summarization, content transformation, and conceptual linking.
    -   Manage data presentation in the Options UI.
-   **Alignment:** Supports H-LATs 3.1, 4.1, 5.1, 6.1, 7.1, 8.1, 9.1.

### 4.7. Management & Configuration Module Logic - within `apps/chrome-extension`

-   **Location:** `apps/chrome-extension/src/config-ui` (example path, as per MPP Task 1.3, for UI-related logic) and potentially `apps/chrome-extension/src/settings` for backend settings logic.
-   **Responsibilities:**
    -   Manage user settings, custom capture templates.
    -   Organize tags and categories (interacting with KBAL if stored there).
    -   Persist settings to `chrome.storage.local`.
-   **Alignment:** Supports H-LAT 10.1.

### 4.8. Knowledge Base Service (KBAL) - within `packages/`

-   **Location:** `packages/knowledge-base-service` (example path, as per MPP Task 1.1)
-   **Technology:** TypeScript, `lowdb`.
-   **Responsibilities:**
    -   Provide a clean, consistent API (e.g., `IKbalService`) for CRUD operations on the knowledge base.
    -   Abstract the `lowdb` storage mechanism.
    -   Handle data modeling, validation, and indexing for content items stored in `lowdb_storage`.
-   **Alignment:** Foundational for local-first data persistence. Supports H-LATs 1.1, 2.1, 3.1, 4.1, 5.1, 6.1, 7.1, 8.1, 9.1, and aspects of 10.1.

### 4.9. AI Services Integration - within `packages/`

-   **Location:** `packages/ai-services` (example path)
-   **Responsibilities:**
    -   Provide a unified interface for accessing both external AI services (e.g., Gemini) and any local AI model utilities.
    -   Handle API key management, request formatting, and response parsing for external services.
-   **Alignment:** Supports H-LATs 2.1, 5.1, 6.1, 7.1, 8.1.

### 4.10. Shared Utilities - within `packages/`

-   **Location:** `packages/shared-utils` (example path)
-   **Responsibilities:**
    -   Provide common functions, type definitions, constants, etc., used across `apps/chrome-extension` and other `packages/`.
-   **Alignment:** Promotes code reuse and consistency.

### 4.11. Storage Layer

-   **Components:**
    -   **`lowdb_storage` (Knowledge Base Data):** A local `db.json` file managed by `lowdb` via the KBAL service. Stores the main user-captured content, metadata, tags, etc.
    -   **`chrome_storage` (Extension Settings & State):** Utilizes the `chrome.storage.local` API. Stores user preferences, extension operational state, and potentially temporary data. Managed by the Management & Configuration Module logic.
-   **Responsibilities:**
    -   Persistently store user data and application settings locally.
    -   Ensure data integrity and availability for offline use.
-   **Alignment:** Core to the local-first principle. `lowdb_storage` is critical for H-LATs involving persistent knowledge items. `chrome_storage` is critical for H-LAT 10.1.

## 5. Data Flow (Post-Template Integration)

-   **Capture Workflow:**
    1.  User interacts with **Popup UI** or **Content Scripts**.
    2.  Data sent to **Background Service Worker**.
    3.  BSW uses **Web Content Capture Logic** to process.
    4.  (Optional) **Intelligent Capture & Org. Logic** (via BSW) uses **AI Services Integration** for suggestions.
    5.  Finalized content and metadata sent by BSW to **KBAL Service (`packages/knowledge-base-service`)**.
    6.  KBAL Service writes to **`lowdb_storage` (`db.json`)**.
-   **Knowledge Base Interaction Workflow (e.g., Search in Options UI):**
    1.  User interacts with **Options UI**.
    2.  Request sent to **Background Service Worker**.
    3.  BSW uses **Knowledge Base Interaction Logic**.
    4.  KB Interaction Logic queries **KBAL Service**.
    5.  KBAL Service reads from **`lowdb_storage`**.
    6.  (Optional for AI features) KB Interaction Logic uses **AI Services Integration**.
    7.  Results returned to BSW, then to **Options UI**.
-   **Configuration Workflow:**
    1.  User interacts with **Options UI**.
    2.  Settings changes sent to **Background Service Worker**.
    3.  BSW uses **Management & Config Logic**.
    4.  MC Logic writes to **`chrome_storage` (`chrome.storage.local`)**. Settings are read similarly.

## 6. Technology Considerations (Post-Template Integration)

-   **Monorepo:** Turborepo.
-   **Build Tool:** Vite (for `apps/chrome-extension`).
-   **Language:** TypeScript.
-   **UI Framework:** React.
-   **Browser Extension APIs:** Standard Chrome Extension APIs.
-   **Local Knowledge Base Storage:** `lowdb` (managing a local `db.json` file).
-   **Local Settings Storage:** `chrome.storage.local` API.
-   **E2E Testing:** Playwright.
-   **Unit/Integration Testing:** Jest, React Testing Library (RTL).
-   **AI:** Integration with external AI APIs (e.g., Gemini) via `packages/ai-services`. Potential for local AI models in the future.

## 7. Alignment with Master Project Plan (v1.4) and High-Level Acceptance Tests (v1.1)

This updated architecture directly supports the revised Master Project Plan (MPP v1.4) and Master Acceptance Test Plan (MATP v1.1), which incorporate the GitHub template.

-   **MPP Alignment:**
    -   The monorepo structure (`apps/`, `packages/`) aligns with the template's structure adopted in the MPP.
    -   The phased tasks in MPP v1.4 (e.g., Task 1.1 Integrate `lowdb`, Task 1.2 Set up Playwright, Task 1.3 Establish Core Module Structure) are directly supported by this architecture. For instance, the KBAL service in `packages/knowledge-base-service` using `lowdb` addresses Task 1.1. The module logic components within `apps/chrome-extension/src/` address Task 1.3.
    -   AI verifiable tasks in the MPP are supported by clear component responsibilities and data flows.
-   **MATP Alignment:**
    -   The architecture facilitates Playwright testing by providing clear UI interaction points (Popup, Options) and backend logic that can be verified through data persistence in `lowdb` and `chrome.storage.local`.
    -   H-LAT 1.1 (Capture and Store Web Content via Popup & `lowdb`): Directly supported by Popup UI, WCC Logic, KBAL, and `lowdb_storage`.
    -   H-LAT 1.2 (Popup Initialization Error Handling): Handled by Popup UI and Background Service Worker.
    -   H-LAT 2.1 (Automatic Tagging): Supported by ICO Logic, AI Services Integration, KBAL.
    -   H-LAT 3.1 (Browse and View Saved Content from `lowdb`): Supported by Options UI, KBI Logic, KBAL, `lowdb_storage`.
    -   H-LAT 10.1 (Configure Capture Settings via `chrome.storage.local`): Supported by Options UI, MC Logic, `chrome_storage`.
    -   Other AI-related H-LATs are supported by KBI Logic and AI Services Integration.

## 8. Potential Issues and Considerations (Post-Template Integration)

-   **Monorepo Complexity:** Managing dependencies and build configurations with Turborepo requires careful attention.
-   **Build Times:** While Vite is fast, large monorepos can still face build/CI time challenges; Turborepo's caching helps mitigate this.
-   **State Management:** Consistent state management strategy within the React UIs (Popup, Options) and synchronization with the Background Service Worker and `chrome.storage.local` needs careful design.
-   **Data Migration:** If schema changes occur in `lowdb` or `chrome.storage.local`, migration strategies will be needed.
-   **Performance:** Search performance on large `lowdb` instances. Latency of external AI calls.
-   **Security:** Securely managing API keys for external AI services. Ensuring data sanitization and protection against XSS in UI components.
-   **Testing Complexity:** Setting up and maintaining Playwright tests for a browser extension, including mocking `chrome.*` APIs where necessary for certain test scenarios if not fully covered by Playwright's extension support.

## 9. Foundational Architectural Step & Scaffolding Needs

This document, updated for template integration, represents a critical foundational architectural step. It refines the system's structure to align with the chosen `chrome-extension-react-ts-boilerplate`, providing a clear blueprint for development.

**Immediate Scaffolding Needs (aligning with MPP v1.4, Phase 1):**

1.  **Initialize Monorepo:** Set up the project using the chosen GitHub template.
2.  **`packages/knowledge-base-service` (KBAL with `lowdb`):** (MPP Task 1.1)
    -   Create the package directory.
    -   Install `lowdb`.
    -   Define the `IKbalService` interface.
    -   Implement basic CRUD operations for `lowdb` (targeting `db.json`).
    -   Add basic unit tests.
3.  **`packages/shared-utils` & `packages/ai-services`:**
    -   Create placeholder packages.
4.  **Playwright Setup:** (MPP Task 1.2)
    -   Install and configure Playwright.
    -   Create a basic test to load the extension and interact with the default popup.
5.  **Core Module Structure in `apps/chrome-extension`:** (MPP Task 1.3)
    -   Create directories: `src/capture`, `src/organization`, `src/knowledge-base-ui`, `src/config-ui`, `src/ui/popup`, `src/ui/options`, `src/background`, `src/content`.
    -   Add placeholder files (e.g., `index.ts` or a basic React component) in each.
6.  **Basic Communication Setup:**
    -   Establish basic message passing between Popup UI, Options UI, and Background Service Worker.
7.  **Build & Lint Configuration:**
    -   Ensure Vite and Turborepo configurations are correctly set up for development and builds.
    -   Integrate ESLint, Prettier as provided by the template.

This architecture provides the necessary guidance for these scaffolding tasks, ensuring the resulting codebase is structured for iterative development and supports the AI verifiable outcomes and high-level acceptance tests defined in the project's SPARC specifications.

## 10. Review of Other Architecture Documents

The following module-specific architecture documents should be reviewed and updated in subsequent tasks to ensure full alignment with this overall project architecture (v2.0), particularly regarding their new locations within `apps/chrome-extension/src/`, their interaction with shared `packages/` like KBAL, and the use of `lowdb` and `chrome.storage.local`:

*   [`docs/architecture/Knowledge_Base_Interaction_Insights_Module_architecture.md`](docs/architecture/Knowledge_Base_Interaction_Insights_Module_architecture.md)
*   [`docs/architecture/Web_Content_Capture_Module_architecture.md`](docs/architecture/Web_Content_Capture_Module_architecture.md)
*   [`docs/architecture/Intelligent_Capture_Organization_Assistance_Module_architecture.md`](docs/architecture/Intelligent_Capture_Organization_Assistance_Module_architecture.md)
*   [`docs/architecture/Management_Configuration_Module_architecture.md`](docs/architecture/Management_Configuration_Module_architecture.md)
*   [`docs/architecture/Advanced_Features_Architecture.md`](docs/architecture/Advanced_Features_Architecture.md)

Key aspects to update in those documents would include:
-   Revised component diagrams reflecting their place within `apps/chrome-extension` and usage of shared packages.
-   Updated data flow diagrams to show interaction with the KBAL (`lowdb`) and `chrome.storage.local`.
-   Confirmation of technology choices like React, Vite, and Playwright where relevant to the module.