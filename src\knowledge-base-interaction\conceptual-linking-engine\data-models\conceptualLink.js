// src/knowledge-base-interaction/conceptual-linking-engine/data-models/conceptualLink.js

/**
 * @file Defines the data model for a Conceptual Link.
 */

/**
 * Represents a conceptual link between two pieces of content.
 */
class ConceptualLink {
    /**
     * Creates an instance of ConceptualLink.
     * @param {Object} linkData - The data for the conceptual link.
     * @param {string} linkData.id - Unique identifier for the link.
     * @param {string} linkData.sourceItemId - Identifier of the source content item.
     * @param {string} linkData.targetItemId - Identifier of the target content item.
     * @param {string} linkData.type - Type of conceptual link (e.g., "relatedConcept", "contradiction").
     * @param {number} [linkData.strength=0.5] - Confidence or strength of the link (0.0 to 1.0).
     * @param {Array<Object>} [linkData.supportingEvidence=[]] - Text segments supporting the link.
     *        Each evidence object should have: { itemId: string, segmentText: string, offset: number, length: number }
     * @param {string} [linkData.explanation=''] - Human-readable explanation for the link.
     * @param {Object} [linkData.metadata={}] - Additional metadata (e.g., timestamps, generation source).
     */
    constructor({
        id,
        sourceItemId,
        targetItemId,
        type,
        strength = 0.5,
        supportingEvidence = [],
        explanation = '',
        metadata = {}
    }) {
        if (!id || !sourceItemId || !targetItemId || !type) {
            throw new Error('Missing required fields for ConceptualLink: id, sourceItemId, targetItemId, type.');
        }

        this.id = id;
        this.sourceItemId = sourceItemId;
        this.targetItemId = targetItemId;
        this.type = type;
        this.strength = strength;
        this.supportingEvidence = supportingEvidence; // Should validate structure of each evidence item
        this.explanation = explanation;
        this.metadata = {
            createdAt: new Date().toISOString(),
            ...metadata
        };

        // Validate supportingEvidence structure (basic check)
        if (!Array.isArray(this.supportingEvidence) ||
            !this.supportingEvidence.every(ev => ev && typeof ev.itemId === 'string' && typeof ev.segmentText === 'string')) {
            console.warn(`ConceptualLink (id: ${this.id}): supportingEvidence might have an invalid structure.`);
        }
    }

    /**
     * Validates the conceptual link data.
     * @returns {boolean} True if the link data is valid, false otherwise.
     */
    isValid() {
        // Add more comprehensive validation logic here
        return !!(this.id && this.sourceItemId && this.targetItemId && this.type &&
                  typeof this.strength === 'number' && this.strength >= 0 && this.strength <= 1);
    }

    /**
     * Converts the ConceptualLink instance to a plain JavaScript object.
     * @returns {Object}
     */
    toObject() {
        return {
            id: this.id,
            sourceItemId: this.sourceItemId,
            targetItemId: this.targetItemId,
            type: this.type,
            strength: this.strength,
            supportingEvidence: this.supportingEvidence,
            explanation: this.explanation,
            metadata: this.metadata,
        };
    }
}

// AI Verifiable: Existence of this file and the ConceptualLink class.
// Further AI verification can check for constructor parameters and methods like isValid or toObject.

export { ConceptualLink };