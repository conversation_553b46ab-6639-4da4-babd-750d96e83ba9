// main-application-ui/renderer/components/ItemListPane.js
import React from "react";
import './ItemListPane.css';
// Removed getItems as all item fetching is now handled by the store's performSearch action
import useStore from '../store/useStore'; // Import Zustand store

// Removed activeTagFilters, activeCategoryFilters from props
function ItemListPane({ className, onSelectItem }) {
  // Removed local state for items, loading, error as these are now from the store via searchResults

  const searchTerm = useStore((state) => state.searchTerm);
  const searchResults = useStore((state) => state.searchResults);
  const searchLoading = useStore((state) => state.searchLoading);
  const searchError = useStore((state) => state.searchError);

  // Removed useEffect that was fetching items locally.
  // All items are now populated into searchResults by the performSearch action in the store,
  // which is triggered by NavigationFiltersPane.

  // itemsToDisplay will always be derived from searchResults
  const itemsToDisplay = React.useMemo(() => {
    return (searchResults || []).map(item => {
      let snippet = '';
      if (item.content) {
        snippet = item.content.length > 150 ? `${item.content.substring(0, 150)}...` : item.content;
      }
      let displayDate = item.date; // Use original date string if parsing fails or not provided
      if (item.date) {
        try {
          const parsedDate = new Date(item.date);
          if (!isNaN(parsedDate.getTime())) {
            displayDate = parsedDate.toLocaleDateString();
          }
        } catch (e) { /* ignore date parsing error, keep original */ }
      }
      return { ...item, snippet, displayDate };
    });
  }, [searchResults]);


  if (searchLoading) {
    // Display a generic loading message if a search/filter operation is in progress
    return <div className={`${className} item-list-pane-component`}>Loading items...</div>;
  }

  if (searchError) {
    return <div className={`${className} item-list-pane-component`}>Error: {searchError}</div>;
  }

  return (
    <div className={`${className} item-list-pane-component`}>
      <h3>{searchTerm && searchTerm.trim() !== '' ? `Results for "${searchTerm}"` : 'Knowledge Items'}</h3>
      {itemsToDisplay.length === 0 ? (
        <p>No items found.</p>
      ) : (
        <ul className="item-list">
          {itemsToDisplay.map(item => (
            <li key={item.id} className="item-list-entry" onClick={() => onSelectItem(item)}>
              <h4 className="item-title">{item.title || item.name || 'Untitled'}</h4>
              <p className="item-snippet">{item.snippet}</p>
              {item.displayDate && <p className="item-date">{item.displayDate}</p>}
              {item.type && <p className="item-type">Type: {item.type}</p>}
              {item.tags && item.tags.length > 0 && (
                <div className="item-tags-list">
                  {item.tags.map((tag, index) => (
                    <span key={index} className="item-tag">{tag}</span>
                  ))}
                </div>
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default ItemListPane;