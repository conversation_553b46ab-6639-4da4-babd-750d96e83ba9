body {
    font-family: sans-serif;
    margin: 10px;
    width: 350px; /* Adjust as needed */
    background-color: #f4f4f4;
    color: #333;
}

.container {
    background-color: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    font-size: 1.5em;
    color: #007bff;
    text-align: center;
    margin-top: 0;
    margin-bottom: 15px;
}

h2 {
    font-size: 1.1em;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
    margin-top: 20px;
    margin-bottom: 10px;
}

.section {
    margin-bottom: 15px;
}

.capture-modes button,
#saveButton {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 12px;
    margin: 5px 2px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.capture-modes button:hover,
#saveButton:hover {
    background-color: #0056b3;
}

.capture-modes button.active {
    background-color: #0056b3;
    font-weight: bold;
}


#metadataDisplay p {
    margin: 5px 0;
    font-size: 0.9em;
}

#metadataDisplay strong {
    color: #555;
}

#contentPreview {
    border: 1px solid #ddd;
    padding: 10px;
    min-height: 50px;
    max-height: 150px;
    overflow-y: auto;
    background-color: #f9f9f9;
    font-size: 0.85em;
    border-radius: 4px;
}

#saveFormat {
    padding: 6px;
    border-radius: 4px;
    border: 1px solid #ccc;
    margin-left: 5px;
}

.status-message {
    margin-top: 10px;
    font-size: 0.9em;
    padding: 8px;
    border-radius: 4px;
}

.status-message.success {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

.status-message.info {
    color: #0c5460;
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
}