import { test, expect, type BrowserContext, type Worker } from '@playwright/test';

let serviceWorker: Worker; // Declare serviceWorker at the top level, use Worker type

test.beforeEach(async ({ context: fixtureContext }) => {
  console.log('Starting beforeEach hook: Waiting for service worker...');
  try {
    // Wait for the service worker to be available.
    // This will ensure the extension is loaded and its service worker is registered.
    serviceWorker = await fixtureContext.waitForEvent('serviceworker', { timeout: 60000 });
    console.log('Service worker detected.');

    // Wait for a specific console message from the service worker indicating it's fully activated and ready.
    // This is more reliable than waiting for a 'state' event on the Worker object directly.
    const serviceWorkerReadyPromise = new Promise<void>((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        fixtureContext.off('console', onConsoleMessage); // Clean up listener
        reject(new Error('Service worker activation message timed out.'));
      }, 60000); // 60 seconds timeout for activation message

      const onConsoleMessage = (message: any) => {
        if (message.url().includes(serviceWorker.url()) && message.text().includes('Background script: Service Worker activated and clients claimed.')) {
          console.log('Service worker activated and clients claimed message received.');
          clearTimeout(timeoutId);
          fixtureContext.off('console', onConsoleMessage); // Clean up listener
          resolve();
        }
      };
      fixtureContext.on('console', onConsoleMessage);
    });
    await serviceWorkerReadyPromise;

    // Evaluate code in the service worker context to clear storage
    console.log('Attempting to clear chrome.storage.local...');
    await serviceWorker.evaluate(async () => {
      // @ts-ignore - chrome is available in the service worker context
      await chrome.storage.local.clear();
      // Add logic here to clear or mock lowdb if necessary for test isolation
      // Example: await self.kbService.clearDatabase(); // Assuming kbService is exposed globally in the service worker
    });
    console.log('chrome.storage.local cleared successfully.');
  } catch (error) {
    console.error(`Error in beforeEach: ${error.message}`);
    throw error; // Re-throw to fail the test if setup fails
  }
});

test.describe('Minimal Extension Load Test', () => {
  test('should detect its service worker', async ({ context }) => { // Re-added context fixture
    console.log('Starting test: should detect its service worker');
    // The service worker should already be available and activated from the beforeEach hook
    expect(serviceWorker).toBeDefined();
    expect(serviceWorker.url()).toContain('background.js');
    console.log(`Service Worker URL: ${serviceWorker.url()}`);

    // Optionally, try to open the popup to confirm it's loaded
    const extensionId = serviceWorker.url().split('/')[2];
    const popupPage = await context.newPage();
    console.log(`Navigating to popup.html for extension ID: ${extensionId}`);
    await popupPage.goto(`chrome-extension://${extensionId}/popup.html`);
    await expect(popupPage.locator('body')).toBeVisible(); // Check if popup content is visible
    console.log('Popup page loaded successfully.');
  });
});
