// src/knowledge-base-interaction/features/content-summarization/query-understanding-engine/queryUnderstandingEngine.js

import { getIntent as recognizeIntent, Intent } from '../../../query-understanding-engine/intent-recognition/getIntent';
import { summarizeContentWithAIService } from '../ai-services-gateway/aiServiceGateway';
import { logError, logInfo } from '../utils/logger';
import { convertHtmlToText, extractPdfText } from '../utils/contentProcessor'; // Assuming these utilities will be created

const MAX_CONTENT_LENGTH = 10000;

// Local getIntent function is removed, using the imported recognizeIntent

/**
 * Processes the summarization request from the UI Layer,
 * interacts with the AI Services Gateway, and returns the result.
 *
 * @param {object} requestPayload - The payload from the UI Layer.
 *   Expected structure: { query: string, content: string, contentType: string, options: object }
 * @returns {Promise<object>} A promise that resolves with the summarization result or an error object.
 */
export async function sendToQueryUnderstandingEngine(requestPayload) {
  logInfo('Query Understanding Engine: Received request.', requestPayload);

  const { query, content, contentType, options } = requestPayload;

  const intent = await recognizeIntent(query); // Use the imported, async version

  if (intent !== Intent.SUMMARIZATION) {
    logError('Query Understanding Engine: Intent is not SUMMARIZATION.', { intent });
    return { error: 'Request is not for summarization.', summary: null };
  }

  let processedContent = content;

  // Content processing based on contentType
  try {
    if (contentType === 'text/html') {
      logInfo('Query Understanding Engine: Converting HTML to text.');
      processedContent = await convertHtmlToText(content);
    } else if (contentType === 'application/pdf') {
      logInfo('Query Understanding Engine: Extracting text from PDF.');
      // Assuming extractPdfText might need the raw content (e.g., ArrayBuffer or path)
      // This part might need adjustment based on how PDF content is passed and handled.
      processedContent = await extractPdfText(content);
    }
  } catch (error) {
    logError('Query Understanding Engine: Error processing content.', { contentType, error });
    return { error: `Failed to process ${contentType} content.`, summary: null, details: error.message };
  }


  if (processedContent.length > MAX_CONTENT_LENGTH) {
    logInfo(`Query Understanding Engine: Content exceeds ${MAX_CONTENT_LENGTH} characters. Truncating.`);
    processedContent = processedContent.substring(0, MAX_CONTENT_LENGTH);
  }

  const aiServicePayload = {
    content: processedContent,
    contentType: 'text/plain', // After processing, content is plain text
    options,
  };

  try {
    logInfo('Query Understanding Engine: Sending request to AI Services Gateway.', aiServicePayload);
    // The response from summarizeContentWithAIService should match the structure:
    // { summary: "The summarized content", contentType: "text/plain", model: "gemini" }
    // or an error object like { error: "AI service error", message: "Failed to summarize content" }
    const result = await summarizeContentWithAIService(aiServicePayload);
    logInfo('Query Understanding Engine: Received response from AI Services Gateway.', result);
    return result;
  } catch (error) {
    logError('Query Understanding Engine: Error communicating with AI Services Gateway.', error);
    return { error: 'Failed to get summary from AI service.', summary: null, details: error.message };
  }
}

// Example Usage (for testing purposes)
/*
async function testQueryEngine() {
  const requestFromUI = {
    query: "Summarize this document.",
    content: "<p>This is <b>HTML</b> content that needs to be summarized.</p>".repeat(500), // Test truncation
    contentType: "text/html",
    options: { summaryLength: "medium" }
  };

  const result = await sendToQueryUnderstandingEngine(requestFromUI);

  if (result.error) {
    console.error("Q-Engine - Summarization failed:", result.error, result.details || '');
  } else {
    console.log("Q-Engine - Summary:", result.summary, "Model:", result.model);
  }
}

// testQueryEngine();
*/