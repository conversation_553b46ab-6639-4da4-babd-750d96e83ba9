// Mock dependencies
// Mock fs.promises instead of fs
jest.mock('fs/promises', () => {
  return {
    promises: {
      access: jest.fn(),
      mkdir: jest.fn(),
      readFile: jest.fn(),
      writeFile: jest.fn().mockResolvedValue(undefined)
    }
  };
});
// Mock other modules this one interacts with
jest.mock('../../web-content-capture'); // Adjust path as needed
jest.mock('../../intelligent-capture-organization'); // Adjust path as needed

// Import modules/functions to test
const {
  getSettings,
  saveSettings,
  getTemplates,
  saveTemplate,
  deleteTemplate,
  getTags,
  saveTag,
  deleteTag,
  mergeTags,
  getCategories,
  saveCategory,
  deleteCategory,
} = require('../index');

// Mock implementation details
const fs = require('fs/promises');
const path = require('path');
const webContentCapture = require('../../web-content-capture');
const intelligentCapture = require('../../intelligent-capture-organization');

// Define expected paths to match the implementation
const DATA_DIR = path.join(__dirname, '..', 'data');
const SETTINGS_FILE_PATH_EXPECTED = path.join(DATA_DIR, 'settings.json');
const TEMPLATES_FILE_PATH_EXPECTED = path.join(DATA_DIR, 'templates.json');
const TAGS_FILE_PATH_EXPECTED = path.join(DATA_DIR, 'tags.json');
const CATEGORIES_FILE_PATH_EXPECTED = path.join(DATA_DIR, 'categories.json');

describe('Management & Configuration Module', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.resetAllMocks();
    // Set default mock implementation for readFile
    fs.promises.readFile.mockResolvedValue('[]');
  });

  // Setup mocks for other modules
  beforeEach(() => {
    // Mocks for Web Content Capture
    webContentCapture.retrieveDefaultSettings = jest.fn().mockResolvedValue({ mode: 'Full Page', format: 'Markdown' });
    webContentCapture.findMatchingTemplates = jest.fn().mockResolvedValue([]);
    intelligentCapture.getAllTags = jest.fn().mockResolvedValue([]);
    intelligentCapture.getAllCategories = jest.fn().mockResolvedValue([]);
    intelligentCapture.addTag = jest.fn().mockResolvedValue(true);
    intelligentCapture.addCategory = jest.fn().mockResolvedValue(true);
  });

  // --- Functional Tests ---

  describe('TC-MC-FUNC-001: Capture Settings Configuration', () => {
    test('should load default settings correctly', async () => {
      const mockSettings = { defaultMode: 'Article', defaultFormat: 'HTML' };
      fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists for this read
      fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(mockSettings));
      const settings = await getSettings();
      // Use the exact path expected by the implementation
      expect(fs.promises.readFile).toHaveBeenCalledWith(SETTINGS_FILE_PATH_EXPECTED, 'utf-8');
      // Check against combined defaults and loaded settings
      expect(settings).toEqual({ defaultMode: 'Article', defaultFormat: 'HTML' });
    });

    test('should save settings correctly', async () => {
      const newSettings = { defaultMode: 'Selection', defaultFormat: 'PlainText' };
      await saveSettings(newSettings);
      expect(fs.promises.writeFile).toHaveBeenCalledWith(
        SETTINGS_FILE_PATH_EXPECTED, // Use the exact path
        JSON.stringify(newSettings, null, 2),
        'utf-8' // Add encoding argument
      );
    });

    test('should return default settings if file is empty or invalid', async () => {
      fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
      fs.promises.readFile.mockResolvedValueOnce(''); // Simulate empty file content
      const settings = await getSettings();
      // Expect default settings when file is empty
      expect(settings).toEqual({ defaultMode: 'Full Page', defaultFormat: 'Markdown' });
    });
  });

  describe('TC-MC-FUNC-002 & 003: Custom Clipping Templates', () => {
    describe('CRUD Operations', () => {
      test('should create/save a new template', async () => {
        const newTemplate = { id: 't1', name: 'Blog Post', matchUrl: 'blog.example.com', selectors: { title: 'h1' } };
        // Mock file doesn't exist initially
        fs.promises.access.mockRejectedValueOnce({ code: 'ENOENT' });
        // readJsonFile will return the default value [] in this case
        await saveTemplate(newTemplate);
        expect(fs.promises.writeFile).toHaveBeenCalledWith(
          TEMPLATES_FILE_PATH_EXPECTED,
          JSON.stringify([newTemplate], null, 2),
          'utf-8' // Add encoding argument
        );
      });

      test('should update an existing template', async () => {
        const existingTemplate = { id: 't1', name: 'Old Name', matchUrl: 'old.url', selectors: { title: 'h2' } };
        const updatedTemplate = { id: 't1', name: 'New Name', matchUrl: 'new.url', selectors: { title: 'h1.entry-title' } };
        fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
        fs.promises.readFile.mockResolvedValueOnce(JSON.stringify([existingTemplate]));
        await saveTemplate(updatedTemplate);
        expect(fs.promises.writeFile).toHaveBeenCalledWith(
          TEMPLATES_FILE_PATH_EXPECTED, // Use the exact path
          JSON.stringify([updatedTemplate], null, 2),
          'utf-8' // Add encoding argument
        );
      });

      test('should retrieve all templates', async () => {
        const mockTemplates = [{ id: 't1', name:'Template 1' }, { id: 't2', name: 'Template 2' }];
        fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
        fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(mockTemplates)); // Mock specific return value
        const templates = await getTemplates();
        expect(templates).toEqual(mockTemplates); // Compare with the mocked data
        expect(fs.promises.readFile).toHaveBeenCalledWith(TEMPLATES_FILE_PATH_EXPECTED, 'utf-8'); // Use the exact path
      });

      test('should delete a template', async () => {
        const templates = [{ id: 't1' }, { id: 't2' }];
        fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
        fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(templates));
        await deleteTemplate('t1');
        expect(fs.promises.writeFile).toHaveBeenCalledWith(
          TEMPLATES_FILE_PATH_EXPECTED, // Use the exact path
          JSON.stringify([{ id: 't2' }], null, 2), // Only t2 remains
          'utf-8' // Add encoding argument
        );
      });

       test.skip('Negative: should handle creating template with missing required fields', () => {});
       test.skip('Negative: should handle creating template with invalid selectors/URL patterns', () => {});
    });

    describe('Application Logic (Placeholders/Integration)', () => {
       test.skip('should identify matching templates for a URL (Integration with Web Capture)', () => {
         // This logic might be primarily in Web Capture, tested via mock calls
       });
       test.skip('should apply template selectors (Integration with Web Capture)', () => {
         // This logic might be primarily in Web Capture, tested via mock calls
       });
    });
  });

  describe('TC-MC-FUNC-004 & 005: Tag Management', () => {
    describe('CRUD Operations', () => {
      test('should retrieve all tags', async () => {
        const mockTags = [{ id: 'tag1', name: 'Tech' }, { id: 'tag2', name: 'AI' }];
        fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
        fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(mockTags)); // Mock specific return value
        const tags = await getTags();
        expect(tags).toEqual(mockTags); // Compare with the mocked data
        expect(fs.promises.readFile).toHaveBeenCalledWith(TAGS_FILE_PATH_EXPECTED, 'utf-8'); // Use the exact path
      });

      test('should save a new tag', async () => {
          const newTag = { id: 'tag3', name: 'NewTag' };
          const initialTags = [{ id: 'tag1', name: 'Tech' }];
          fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
          fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(initialTags)); // Provide specific initial state
          await saveTag(newTag);
          expect(fs.promises.writeFile).toHaveBeenCalledWith(
              TAGS_FILE_PATH_EXPECTED,
              JSON.stringify([{ id: 'tag1', name: 'Tech' }, newTag], null, 2),
              'utf-8' // Add encoding argument
          );
      });

       test('should update (rename) an existing tag', async () => {
          const existingTag = { id: 'tag1', name: 'OldName' };
          const updatedTag = { id: 'tag1', name: 'NewName' };
          fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
          fs.promises.readFile.mockResolvedValueOnce(JSON.stringify([existingTag]));
          await saveTag(updatedTag); // Assuming save handles updates based on ID
          expect(fs.promises.writeFile).toHaveBeenCalledWith(
              TAGS_FILE_PATH_EXPECTED, // Use the exact path
              JSON.stringify([updatedTag], null, 2),
              'utf-8' // Add encoding argument
          );
       });

       test('should delete a tag', async () => {
          const initialTags = [{ id: 'tag1', name: 'Tech' }, { id: 'tag2', name: 'AI' }];
          fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
          fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(initialTags)); // Provide specific initial state
          await deleteTag('tag1');
          expect(fs.promises.writeFile).toHaveBeenCalledWith(
              TAGS_FILE_PATH_EXPECTED,
              JSON.stringify([{ id: 'tag2', name: 'AI' }], null, 2),
              'utf-8' // Add encoding argument
          );
       });

       test.skip('Negative: should prevent creating tags with duplicate names', () => {});
    });

    describe('Merge Functionality', () => {
      test('should merge multiple tags into one target tag', async () => {
        const tags = [{ id: 't1', name: 'Source1' }, { id: 't2', name: 'Source2' }, { id: 't3', name: 'Keep' }];
        fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
        fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(tags)); // Mock specific return value

        const targetTag = { id: 't4', name: 'MergedTag' };
        const result = await mergeTags(['t1', 't2'], targetTag);

        // Verify the result is the target tag
        expect(result).toEqual(targetTag);

        // Verify writeFile was called with the expected arguments
        expect(fs.promises.writeFile).toHaveBeenCalledWith(
          TAGS_FILE_PATH_EXPECTED,
          expect.stringContaining('MergedTag'), // Should contain the target tag name
          'utf-8'
        );
      });

      test('should handle merging a single tag', async () => {
        const tags = [{ id: 't1', name: 'Source1' }, { id: 't3', name: 'Keep' }];
        fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
        fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(tags)); // Mock specific return value

        const targetTag = { id: 't4', name: 'MergedTag' };
        const result = await mergeTags(['t1'], targetTag);

        // Verify the result is the target tag
        expect(result).toEqual(targetTag);

        // Verify writeFile was called with the expected arguments
        expect(fs.promises.writeFile).toHaveBeenCalledWith(
          TAGS_FILE_PATH_EXPECTED,
          expect.stringContaining('MergedTag'), // Should contain the target tag name
          'utf-8'
        );
      });
    });
  });

  describe('TC-MC-FUNC-006 & 007: Category/Project Management', () => {
    describe('CRUD Operations', () => {
       test('should retrieve all categories', async () => {
         const mockCategories = [{ id: 'cat1', name: 'Work' }, { id: 'cat2', name: 'Personal' }];
         fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
         fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(mockCategories)); // Mock specific return value
         const categories = await getCategories();
         expect(categories).toEqual(mockCategories); // Compare with the mocked data
         expect(fs.promises.readFile).toHaveBeenCalledWith(CATEGORIES_FILE_PATH_EXPECTED, 'utf-8'); // Use the exact path
       });

       test('should save a new category', async () => {
          const newCategory = { id: 'cat3', name: 'NewCategory' };
          const initialCategories = [{ id: 'cat1', name: 'Work' }];
          fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
          fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(initialCategories)); // Provide specific initial state
          await saveCategory(newCategory);
          expect(fs.promises.writeFile).toHaveBeenCalledWith(
              CATEGORIES_FILE_PATH_EXPECTED,
              JSON.stringify([{ id: 'cat1', name: 'Work' }, newCategory], null, 2),
              'utf-8' // Add encoding argument
          );
       });

       test('should update an existing category', async () => {
          const existingCategory = { id: 'cat1', name: 'OldName' };
          const updatedCategory = { id: 'cat1', name: 'NewName' };
          fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
          fs.promises.readFile.mockResolvedValueOnce(JSON.stringify([existingCategory]));
          await saveCategory(updatedCategory);
          expect(fs.promises.writeFile).toHaveBeenCalledWith(
              CATEGORIES_FILE_PATH_EXPECTED, // Use the exact path
              JSON.stringify([updatedCategory], null, 2),
              'utf-8' // Add encoding argument
          );
       });

       test('should delete a category', async () => {
          const initialCategories = [{ id: 'cat1', name: 'Work' }, { id: 'cat2', name: 'Personal' }];
          fs.promises.access.mockResolvedValueOnce(undefined); // Mock file exists
          fs.promises.readFile.mockResolvedValueOnce(JSON.stringify(initialCategories)); // Provide specific initial state
          await deleteCategory('cat1');
          expect(fs.promises.writeFile).toHaveBeenCalledWith(
              CATEGORIES_FILE_PATH_EXPECTED,
              JSON.stringify([{ id: 'cat2', name: 'Personal' }], null, 2),
              'utf-8' // Add encoding argument
          );
       });
    });

    describe('Hierarchy Management', () => {
      test.skip('should allow creating nested categories', () => {});
      test.skip('should allow moving categories within the hierarchy', () => {});
    });
  });

  // --- Integration Tests ---

  describe('TC-MC-INT: Integration Tests', () => {
    describe('TC-MC-INT-001: Persistence Service Integration', () => {
      // These are implicitly tested by the CRUD operations above using fs mock
      test('should use persistence (fs mock) for loading settings', async () => {
        // Mock access to return success for this specific call within getSettings
        fs.promises.access.mockResolvedValueOnce(undefined);
        await getSettings();
        expect(fs.promises.readFile).toHaveBeenCalledWith(SETTINGS_FILE_PATH_EXPECTED, 'utf-8'); // Use the exact path
      });
       test('should use persistence (fs mock) for saving settings', async () => {
        await saveSettings({ mode: 'test', format: 'test'});
        // Check all args for writeFile
        expect(fs.promises.writeFile).toHaveBeenCalledWith(
            SETTINGS_FILE_PATH_EXPECTED, // Use the exact path
            JSON.stringify({ mode: 'test', format: 'test'}, null, 2),
            'utf-8' // Add encoding argument
        );
      });
      // Add similar checks for templates, tags, categories load/save using exact paths and all args
      test('should use persistence (fs mock) for loading templates', async () => {
        fs.promises.access.mockResolvedValueOnce(undefined);
        await getTemplates();
        expect(fs.promises.readFile).toHaveBeenCalledWith(TEMPLATES_FILE_PATH_EXPECTED, 'utf-8');
      });
      test('should use persistence (fs mock) for saving templates', async () => {
        await saveTemplate({ id: 't-int', name: 'Integration Test' });
        expect(fs.promises.writeFile).toHaveBeenCalledWith(
            TEMPLATES_FILE_PATH_EXPECTED,
            expect.any(String), // Content check done in functional tests
            'utf-8'
        );
      });
      test('should use persistence (fs mock) for loading tags', async () => {
        fs.promises.access.mockResolvedValueOnce(undefined);
        await getTags();
        expect(fs.promises.readFile).toHaveBeenCalledWith(TAGS_FILE_PATH_EXPECTED, 'utf-8');
      });
       test('should use persistence (fs mock) for saving tags', async () => {
        await saveTag({ id: 'tag-int', name: 'Integration Test' });
        expect(fs.promises.writeFile).toHaveBeenCalledWith(
            TAGS_FILE_PATH_EXPECTED,
            expect.any(String),
            'utf-8'
        );
      });
       test('should use persistence (fs mock) for loading categories', async () => {
        fs.promises.access.mockResolvedValueOnce(undefined);
        await getCategories();
        expect(fs.promises.readFile).toHaveBeenCalledWith(CATEGORIES_FILE_PATH_EXPECTED, 'utf-8');
      });
      test('should use persistence (fs mock) for saving categories', async () => {
        await saveCategory({ id: 'cat-int', name: 'Integration Test' });
        expect(fs.promises.writeFile).toHaveBeenCalledWith(
            CATEGORIES_FILE_PATH_EXPECTED,
            expect.any(String),
            'utf-8'
        );
      });
    });

    describe('TC-MC-INT-002: Web Content Capture Integration', () => {
        test('should allow Web Content Capture to retrieve settings', async () => {
            // Assuming Web Content Capture module calls getSettings or a dedicated function
            // This test might live in the Web Content Capture tests, verifying its call to this module.
            // Here, we ensure the mock is set up.
            const settings = await webContentCapture.retrieveDefaultSettings(); // Mocked call
            expect(settings).toEqual({ mode: 'Full Page', format: 'Markdown' });
        });

        test('should allow Web Content Capture to find matching templates', async () => {
             // Assuming Web Content Capture module calls findMatchingTemplates or similar
             const url = 'https://example.com/article';
             const matching = await webContentCapture.findMatchingTemplates(url); // Mocked call
             expect(matching).toEqual([]); // Based on default mock
             // Could add tests where findMatchingTemplates mock returns specific templates
        });
    });

    describe('TC-MC-INT-003: Intelligent Capture Integration', () => {
        test('should allow Intelligent Capture to retrieve tags', async () => {
            const tags = await intelligentCapture.getAllTags(); // Mocked call
            expect(tags).toEqual([]); // Based on default mock
        });

        test('should allow Intelligent Capture to retrieve categories', async () => {
             const categories = await intelligentCapture.getAllCategories(); // Mocked call
             expect(categories).toEqual([]); // Based on default mock
        });

        test('should allow Intelligent Capture to add a tag', async () => {
            const result = await intelligentCapture.addTag({ name: 'SuggestedTag' }); // Mocked call
            expect(result).toBe(true);
            // We could verify if this mock call triggers saveTag internally if designed that way
            // expect(fs.writeFileSync).toHaveBeenCalledWith(expect.stringContaining('tags.json'), expect.any(String));
        });

         test('should allow Intelligent Capture to add a category', async () => {
            const result = await intelligentCapture.addCategory({ name: 'SuggestedCategory' }); // Mocked call
            expect(result).toBe(true);
            // expect(fs.writeFileSync).toHaveBeenCalledWith(expect.stringContaining('categories.json'), expect.any(String));
        });
    });
  });

  // --- Non-Functional Tests (Placeholders) ---

  describe('TC-MC-NF: Non-Functional Placeholder Tests', () => {
    test.skip('TC-MC-NF-001: Usability placeholder (Manual Test Recommended)', () => {});
    test.skip('TC-MC-NF-002: Privacy & Security placeholder (Manual Check Recommended - Local Storage, Network)', () => {});
    test.skip('TC-MC-NF-003: Reliability & Persistence placeholder (Manual Check Recommended - Restart, File Corruption)', () => {});
  });

});