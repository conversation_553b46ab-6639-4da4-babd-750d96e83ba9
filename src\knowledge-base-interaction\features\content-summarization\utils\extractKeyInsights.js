require('dotenv').config();
import { GoogleGenerativeAI } from '@google/generative-ai';
import { logInfo, logError } from './logger';

const DEFAULT_NUM_INSIGHTS = 3;

// Initialize Gemini AI
const API_KEY = process.env.GEMINI_API_KEY; // Use a specific environment variable for Gemini API Key

if (!API_KEY) {
  logError("GEMINI_API_KEY environment variable is not set. Please set your Gemini API key.");
  // In a real application, you might want to throw an error or handle this more gracefully
  // For now, we'll proceed with a dummy model or return empty results if API_KEY is missing.
}

const genAI = API_KEY ? new GoogleGenerativeAI(API_KEY) : null;
const model = genAI ? genAI.getGenerativeModel({ model: "gemini-1.5-pro" }) : null;

/**
 * Parses the AI's response to extract key insights.
 * Assumes the AI might return insights in a list format, e.g., "1. Insight A, 2. Insight B" or "Insights: A, B, C".
 * @param {string} aiResponse - The raw response string from the AI.
 * @returns {string[]} An array of extracted insight strings.
 */
function parseAiInsights(aiResponse) {
  const insights = new Set();
  if (!aiResponse) return [];

  // Attempt to find insights in common list formats
  const commaSeparated = aiResponse.split(/,\s*/);
  const numberedList = aiResponse.split(/\d+\.\s*/).filter(Boolean);
  const bulletList = aiResponse.split(/-\s*/).filter(Boolean);

  [commaSeparated, numberedList, bulletList].forEach(parts => {
    parts.forEach(part => {
      const cleanedPart = part.trim();
      if (cleanedPart.length > 0) {
        insights.add(cleanedPart.split('\n')[0].trim()); // Take only the first line if multi-line
      }
    });
  });

  return Array.from(insights).filter(insight => insight.length > 5); // Filter out very short or empty strings
}

/**
 * Extracts key insights from a given text content using Gemini AI.
 *
 * @param {string} content - The text content from which to extract insights.
 * @param {object} [options] - Optional parameters for insight extraction (e.g., number of insights).
 * @param {number} [options.numInsights=DEFAULT_NUM_INSIGHTS] - The maximum number of insights to extract.
 * @returns {Promise<string[]>} A promise that resolves to an array of key insight strings.
 */
export async function extractKeyInsights(content, options = {}) {
  const numInsights = options.numInsights || DEFAULT_NUM_INSIGHTS;
  logInfo('Content Summarization: Extracting key insights using Gemini AI.', { contentSnippet: content.substring(0, 100), numInsights });

  if (!model) {
    logError('Content Summarization: Gemini AI model not initialized. API_KEY might be missing or invalid.');
    return [];
  }

  if (!content || content.trim().length < 100) { // Increased minimum content length for meaningful AI insights
    logInfo('Content Summarization: Content too short for meaningful AI insight extraction.');
    return [];
  }

  try {
    const prompt = `Given the following content, extract up to ${numInsights} key insights. Provide only the insights, each as a concise sentence or phrase, separated by commas. Content: "${content}"`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const aiResponseText = response.text();

    if (aiResponseText) {
      const aiSuggestedInsights = parseAiInsights(aiResponseText);
      const finalInsights = new Set();

      for (const insight of aiSuggestedInsights) {
        finalInsights.add(insight);
        if (finalInsights.size >= numInsights) {
          break;
        }
      }

      const result = Array.from(finalInsights);
      logInfo('Content Summarization: Generated key insights from Gemini AI.', { insights: result });
      return result;
    } else {
      logError('Content Summarization: No valid response from Gemini AI for key insight extraction.');
      return [];
    }
  } catch (error) {
    logError('Content Summarization: Error extracting key insights with Gemini AI.', error);
    // Log more details about the error if available
    if (error.status) {
      logError(`HTTP Status: ${error.status}`);
    }
    if (error.message) {
      logError(`Error Message: ${error.message}`);
    }
    return [];
  }
}