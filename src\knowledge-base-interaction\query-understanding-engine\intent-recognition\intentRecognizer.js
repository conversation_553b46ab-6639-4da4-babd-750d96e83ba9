// AI-VERIFIABLE: Placeholder for IntentRecognizer logic.
// This class will identify the user's intent (e.g., search, Q&A).

class IntentRecognizer {
    constructor() {
        // Initialization logic, e.g., loading models or rules.
        console.log('[IntentRecognizer] Initialized.');
        // Define known intents for AI verifiability and basic operation
        this.knownIntents = {
            SEARCH: 'search',
            QUESTION_ANSWERING: 'question_answering',
            SUMMARIZE: 'summarize',
            CONTENT_TRANSFORMATION: 'content_transformation',
            CONCEPTUAL_LINKING: 'conceptual_linking',
            UNKNOWN: 'unknown',
        };
    }

    /**
     * Recognizes the intent from a parsed query.
     * @param {object} parsedQuery - The structured output from QueryParser.
     *                               Should contain `original`, `cleanedQuery`, `tokens`, and `keywords`.
     * @returns {Promise<object>} An object representing the recognized intent,
     *                                 e.g., { type: 'search', confidence: 0.9, details: '...' }.
     */
    async recognizeIntent(parsedQuery) {
        if (!parsedQuery || typeof parsedQuery !== 'object' || !parsedQuery.keywords || !parsedQuery.tokens || !parsedQuery.original) {
            throw new Error('Parsed query object with original, tokens, and keywords is required for intent recognition.');
        }

        console.log('[IntentRecognizer] Recognizing intent for:', parsedQuery);

        let intentType = this.knownIntents.UNKNOWN;
        let confidence = 0.3; // Base confidence for unknown
        let details = 'No specific intent pattern matched.';

        const { keywords, tokens, original } = parsedQuery; // Use keywords for more precise matching

        // Rule-based intent recognition. Order matters for overlapping patterns.
        // More specific rules should come first.

        // Question Answering
        const qaKeywords = ['what', 'who', 'where', 'when', 'why', 'how', 'explain', 'define'];
        const isQuestionMarkPresent = original.includes('?');
        if (qaKeywords.some(kw => keywords.includes(kw)) || isQuestionMarkPresent) {
            intentType = this.knownIntents.QUESTION_ANSWERING;
            confidence = isQuestionMarkPresent ? 0.85 : 0.75;
            details = `Matched Q&A pattern (keywords: ${qaKeywords.filter(kw => keywords.includes(kw)).join(', ') || 'N/A'}, question_mark: ${isQuestionMarkPresent}).`;
        }

        // Summarize
        const summarizeKeywords = ['summarize', 'summary', 'tl;dr', 'tldr', 'gist', 'overview', 'abstract', 'points'];
        if (summarizeKeywords.some(kw => keywords.includes(kw))) {
            // If it's also a question, Q&A might be more specific (e.g., "what is the summary of X?")
            // However, "summarize X" is clearly a summarize intent.
            // Let's give summarize higher precedence if its keywords are strong.
            const currentConfidence = summarizeKeywords.filter(kw => keywords.includes(kw)).length * 0.2 + 0.5;
            if (currentConfidence > confidence || intentType === this.knownIntents.UNKNOWN) {
                intentType = this.knownIntents.SUMMARIZE;
                confidence = Math.min(0.9, currentConfidence);
                details = `Matched SUMMARIZE pattern (keywords: ${summarizeKeywords.filter(kw => keywords.includes(kw)).join(', ')}).`;
            }
        }
        
        // Content Transformation (could be more specific later, e.g., extract facts, convert format)
        const transformKeywords = ['transform', 'change', 'convert', 'extract', 'rewrite', 'rephrase'];
        if (transformKeywords.some(kw => keywords.includes(kw))) {
            const currentConfidence = transformKeywords.filter(kw => keywords.includes(kw)).length * 0.15 + 0.6;
            if (currentConfidence > confidence && intentType !== this.knownIntents.SUMMARIZE) { // Summarize is a type of transformation but handled separately
                intentType = this.knownIntents.CONTENT_TRANSFORMATION;
                confidence = Math.min(0.85, currentConfidence);
                details = `Matched CONTENT_TRANSFORMATION pattern (keywords: ${transformKeywords.filter(kw => keywords.includes(kw)).join(', ')}).`;
            }
        }

        // Conceptual Linking
        const linkingKeywords = ['link', 'connect', 'relate', 'relationship', 'association', 'between'];
        if (linkingKeywords.some(kw => keywords.includes(kw))) {
            const currentConfidence = linkingKeywords.filter(kw => keywords.includes(kw)).length * 0.2 + 0.55;
            if (currentConfidence > confidence) {
                intentType = this.knownIntents.CONCEPTUAL_LINKING;
                confidence = Math.min(0.8, currentConfidence);
                details = `Matched CONCEPTUAL_LINKING pattern (keywords: ${linkingKeywords.filter(kw => keywords.includes(kw)).join(', ')}).`;
            }
        }

        // Search (more general, so checked after more specific intents)
        const searchKeywords = ['find', 'search', 'look', 'locate', 'show me', 'get me']; // 'for' is a stopword
        // If no other strong intent, or if search keywords are very explicit.
        if (intentType === this.knownIntents.UNKNOWN ||
            (searchKeywords.some(kw => keywords.includes(kw)) && confidence < 0.7)) {
            if (searchKeywords.some(kw => keywords.includes(kw)) || keywords.length > 0) { // If there are any keywords, it's likely a search
                 const currentConfidence = searchKeywords.filter(kw => keywords.includes(kw)).length * 0.1 + 0.6;
                if (currentConfidence > confidence || intentType === this.knownIntents.UNKNOWN) {
                    intentType = this.knownIntents.SEARCH;
                    confidence = Math.min(0.8, Math.max(0.5, currentConfidence)); // Ensure search has at least 0.5 if keywords exist
                    details = `Matched SEARCH pattern (keywords: ${searchKeywords.filter(kw => keywords.includes(kw)).join(', ') || 'general keywords present'}).`;
                }
            }
        }
        
        // Default to SEARCH if still UNKNOWN but has keywords
        if (intentType === this.knownIntents.UNKNOWN && keywords.length > 0) {
            intentType = this.knownIntents.SEARCH;
            confidence = 0.5; // Lower confidence for implicit search
            details = 'Defaulted to SEARCH intent as keywords are present but no specific pattern matched.';
        }


        const recognizedResult = {
            type: intentType,
            confidence: parseFloat(confidence.toFixed(2)),
            details: details,
        };

        console.log('[IntentRecognizer] Recognized intent:', recognizedResult);
        return recognizedResult;
    }
}

export default IntentRecognizer;
// AI-VERIFIABLE: End of IntentRecognizer.js