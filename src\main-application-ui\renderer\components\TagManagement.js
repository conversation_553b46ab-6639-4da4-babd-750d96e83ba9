import React, { useState, useEffect } from 'react';
import useStore from '../store/useStore';
import './TagManagement.css'; // We'll create this CSS file next

const TagManagement = () => {
  const {
    allTags,
    fetchTags,
    createTag,
    updateTag,
    deleteTag,
    tagOperationLoading,
    tagOperationError,
  } = useStore();

  const [newTagName, setNewTagName] = useState('');
  const [editingTag, setEditingTag] = useState(null); // { id: string, name: string }
  const [updatedTagName, setUpdatedTagName] = useState('');

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  const handleCreateTag = async (e) => {
    e.preventDefault();
    if (newTagName.trim()) {
      await createTag(newTagName.trim());
      setNewTagName('');
    }
  };

  const handleEditTag = (tag) => {
    setEditingTag(tag);
    setUpdatedTagName(tag.name);
  };

  const handleCancelEdit = () => {
    setEditingTag(null);
    setUpdatedTagName('');
  };

  const handleUpdateTag = async (e) => {
    e.preventDefault();
    if (editingTag && updatedTagName.trim()) {
      await updateTag(editingTag.id, updatedTagName.trim());
      setEditingTag(null);
      setUpdatedTagName('');
    }
  };

  const handleDeleteTag = async (tagId) => {
    if (window.confirm('Are you sure you want to delete this tag? This action cannot be undone.')) {
      await deleteTag(tagId);
    }
  };

  return (
    <div className="settings-section tag-management-section">
      <h2>Manage Tags</h2>
      {tagOperationError && <p className="error-message">Error: {tagOperationError}</p>}

      <form onSubmit={handleCreateTag} className="tag-form">
        <input
          type="text"
          value={newTagName}
          onChange={(e) => setNewTagName(e.target.value)}
          placeholder="Enter new tag name"
          disabled={tagOperationLoading}
        />
        <button type="submit" disabled={tagOperationLoading || !newTagName.trim()}>
          {tagOperationLoading ? 'Adding...' : 'Add Tag'}
        </button>
      </form>

      {editingTag && (
        <div className="edit-tag-modal-overlay">
          <div className="edit-tag-modal">
            <h3>Edit Tag</h3>
            <form onSubmit={handleUpdateTag}>
              <input
                type="text"
                value={updatedTagName}
                onChange={(e) => setUpdatedTagName(e.target.value)}
                placeholder="Enter updated tag name"
                disabled={tagOperationLoading}
              />
              <div className="edit-tag-actions">
                <button type="submit" disabled={tagOperationLoading || !updatedTagName.trim()}>
                  {tagOperationLoading ? 'Saving...' : 'Save Changes'}
                </button>
                <button type="button" onClick={handleCancelEdit} disabled={tagOperationLoading}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <ul className="tag-list">
        {allTags.length === 0 && !tagOperationLoading && <li>No tags found.</li>}
        {allTags.map((tag) => (
          <li key={tag.id} className="tag-list-item">
            <span className="tag-name">{tag.name}</span>
            <div className="tag-actions">
              <button onClick={() => handleEditTag(tag)} disabled={tagOperationLoading}>
                Edit
              </button>
              <button onClick={() => handleDeleteTag(tag.id)} disabled={tagOperationLoading} className="delete-button">
                Delete
              </button>
            </div>
          </li>
        ))}
      </ul>
      {tagOperationLoading && <p>Loading tags...</p>}
    </div>
  );
};

export default TagManagement;