// __tests__/knowledgeBaseInteraction.test.js
import * as KBI from '../knowledgeBaseInteraction';

// Mock the AI helper function
jest.mock('../aiHelpers', () => ({
  getAnswerFromContextAI: jest.fn()
}));

// Import the mocked function
import { getAnswerFromContextAI } from '../aiHelpers';

// In KBI_FUNC_QA_BUG_REPRO_001 test:
it('KBI_FUNC_QA_BUG_REPRO_001: should call the explicitly passed getItemDetailsFn when invoked via askQuestion', async () => {
    const question = "What is item1?";
    const itemId = 'item1';
    const mockItemDetails = { id: itemId, title: 'Mock Title', content: 'Mock Content from specific mock' };
    const mockAIResponse = { answer: "Mock AI answer", sources: [itemId] };

    // Create a specific mock function for this test case
    const passedMockGetItemDetailsFn = jest.fn().mockResolvedValue(mockItemDetails);

    // Mock the AI helper
    getAnswerFromContextAI.mockResolvedValue(mockAIResponse);

    // Call askQuestion, passing the specific mock function
    await KBI.askQuestion(question, [itemId], passedMockGetItemDetailsFn);

    // Assertion: Check if the passed mock for getItemDetailsFn was called.
    expect(passedMockGetItemDetailsFn).toHaveBeenCalledTimes(1);
    expect(passedMockGetItemDetailsFn).toHaveBeenCalledWith(itemId);

    // Check that the AI helper was called with the content from the passed mock
    expect(getAnswerFromContextAI).toHaveBeenCalledTimes(1);
    expect(getAnswerFromContextAI).toHaveBeenCalledWith(question, [{ id: itemId, content: 'Mock Content from specific mock' }]);

    // Ensure the module-level KBI.getItemDetails mock (if any was set up for other tests) was NOT called here
    // if KBI.getItemDetails was also mocked (e.g. KBI.getItemDetails = jest.fn())
    if (jest.isMockFunction(KBI.getItemDetails) && KBI.getItemDetails !== passedMockGetItemDetailsFn) {
        expect(KBI.getItemDetails).not.toHaveBeenCalled();
    }
});