/* DetailViewPane.css - Refined for a cleaner, modern look */
.detail-view-pane-component {
  display: flex;
  flex-direction: column;
  padding: 25px; /* Increased padding */
  overflow-y: auto;
  background-color: #fcfcfc; /* Even lighter background */
  border-left: 1px solid #e0e0e0; /* Lighter border */
  height: 100%;
  box-sizing: border-box;
}

.detail-view-pane-component h2 { /* Main Item Title */
  margin-top: 0;
  color: #2c3e50; /* Consistent dark blue-gray */
  font-size: 1.7em; /* Slightly reduced for balance */
  font-weight: 600;
  border-bottom: 1px solid #e8e8e8; /* Lighter separator */
  padding-bottom: 12px;
  margin-bottom: 20px; /* More space below title */
}

.item-date,
.item-source {
  font-size: 0.85em; /* Slightly smaller for metadata */
  color: #555; /* Darker gray for better readability */
  margin-bottom: 6px;
  line-height: 1.5;
}
.item-source a {
  color: #007bff;
  text-decoration: none;
}
.item-source a:hover {
  text-decoration: underline;
}


.item-content {
  margin-top: 20px; /* More space above content */
  margin-bottom: 20px; /* Space below content */
  padding: 15px;
  background-color: #fff;
  border: 1px solid #e8e8e8; /* Lighter border */
  border-radius: 6px; /* Slightly more rounded */
  line-height: 1.65; /* Improved readability */
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Helvetica Neue', Arial, sans-serif; /* Modern sans-serif */
  font-size: 1em; /* Standard content size */
  color: #333;
}

.item-tags {
  margin-top: 20px;
  margin-bottom: 15px;
}

.item-tags strong {
  color: #34495e; /* Consistent with other titles */
  font-weight: 600;
  font-size: 0.95em;
  margin-bottom: 8px;
  display: block;
}

.item-tags ul {
  list-style-type: none;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px; /* Consistent gap */
  margin-top: 0;
}

.item-tags li {
  background-color: #e9ecef; /* Consistent with ItemListPane tags */
  color: #495057;
  padding: 5px 12px; /* Adjusted padding */
  border-radius: 16px; /* More rounded */
  font-size: 0.85em; /* Consistent size */
  font-weight: 500;
}

/* Tab Styles */
.detail-view-tabs {
  display: flex;
  margin-bottom: 25px; /* More space below tabs */
  border-bottom: 1px solid #d0d0d0; /* Slightly darker tab underline */
}

.detail-view-tabs button {
  padding: 12px 18px; /* Increased padding */
  cursor: pointer;
  border: none;
  background-color: transparent;
  font-size: 1.05em; /* Slightly larger tab text */
  font-weight: 500; /* Medium weight for tabs */
  color: #495057; /* Muted color for inactive tabs */
  border-bottom: 3px solid transparent;
  margin-right: 12px;
  transition: color 0.2s ease, border-bottom-color 0.2s ease;
}

.detail-view-tabs button.active {
  color: #007bff;
  border-bottom-color: #007bff;
  font-weight: 600; /* Bolder active tab */
}

.detail-view-tabs button:hover:not(.active) {
  color: #0056b3; /* Darker hover for inactive tabs */
}

/* Section Styles (Q&A, Summarization, etc.) */
.qa-section,
.summarization-section,
.conceptual-links-section,
.content-transformation-section,
.manual-links-section {
  margin-top: 20px; /* Consistent top margin */
  margin-bottom: 20px; /* Add bottom margin for spacing between sections */
  padding: 20px; /* Increased padding */
  background-color: #fff; /* White background for sections */
  border: 1px solid #e0e0e0; /* Lighter border */
  border-radius: 6px; /* Consistent rounding */
  box-shadow: 0 2px 4px rgba(0,0,0,0.04); /* Softer shadow */
}

.qa-section h3,
.summarization-section h3,
.conceptual-links-section h3,
.content-transformation-section h3,
.manual-links-section h3 {
  margin-top: 0;
  color: #34495e; /* Consistent section title color */
  font-size: 1.25em; /* Slightly larger section titles */
  font-weight: 600;
  border-bottom: 1px solid #e8e8e8; /* Lighter separator */
  padding-bottom: 10px;
  margin-bottom: 20px; /* More space below section titles */
}

/* General Button Styles (Primary Actions) */
.qa-input-area button,
.summarize-button,
.transform-button,
.manual-link-form button[type="submit"] {
  padding: 10px 18px; /* Adjusted padding */
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px; /* Slightly more rounded */
  cursor: pointer;
  font-size: 0.95em; /* Standardized button font size */
  font-weight: 500;
  transition: background-color 0.15s ease, box-shadow 0.15s ease;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.qa-input-area button:hover,
.summarize-button:hover,
.transform-button:hover,
.manual-link-form button[type="submit"]:hover {
  background-color: #0069d9; /* Slightly darker blue on hover */
  box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.qa-input-area button:disabled,
.summarize-button:disabled,
.transform-button:disabled,
.manual-link-form button[type="submit"]:disabled {
  background-color: #adb5bd; /* Muted disabled color */
  cursor: not-allowed;
  box-shadow: none;
}

/* Input Fields (Textarea, Select) */
.qa-input-area textarea,
.transformation-controls select,
.manual-link-form input[type="text"],
.manual-link-form select,
.manual-link-form textarea {
  padding: 10px 12px;
  border: 1px solid #ced4da; /* Standard input border */
  border-radius: 5px;
  font-size: 0.95em;
  color: #495057;
  background-color: #fff;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.qa-input-area textarea:focus,
.transformation-controls select:focus,
.manual-link-form input[type="text"]:focus,
.manual-link-form select:focus,
.manual-link-form textarea:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}


/* Q&A Specific Styles */
.qa-input-area {
  display: flex;
  gap: 12px; /* Increased gap */
  align-items: flex-start;
}

.qa-input-area textarea {
  flex-grow: 1;
  min-height: 70px;
  resize: vertical;
}

/* Error and Info Messages */
.qa-error,
.summary-error,
.conceptual-links-section .error-message,
.transformation-error,
.save-new-item-error,
.manual-links-section .error-message {
  color: #721c24; /* Darker red for text */
  margin-top: 12px;
  padding: 10px 15px; /* More padding */
  background-color: #f8d7da; /* Lighter red background */
  border: 1px solid #f5c6cb; /* Matching border */
  border-radius: 5px;
  font-size: 0.9em;
}

.save-new-item-error {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.clear-error-button {
  padding: 4px 10px;
  font-size: 0.85em;
  background-color: #dc3545; /* Bootstrap danger */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
  transition: background-color 0.15s ease;
}
.clear-error-button:hover {
  background-color: #c82333; /* Darker danger */
}

/* Display Areas for AI Content */
.qa-answer-area,
.summary-display-area,
.transformed-content-display-area {
  margin-top: 20px; /* More space above */
  padding: 15px; /* Increased padding */
  background-color: #f8f9fa; /* Very light gray for differentiation */
  border: 1px solid #e9ecef; /* Lighter border */
  border-radius: 5px;
  line-height: 1.6;
}

.qa-answer-area h4,
.summary-display-area h4,
.transformed-content-display-area h4 {
  margin-top: 0;
  margin-bottom: 10px; /* Space below heading */
  color: #343a40; /* Dark gray for heading */
  font-size: 1.15em; /* Slightly larger */
  font-weight: 600;
}

/* Conceptual Links Specific Styles */
.conceptual-links-list {
  list-style-type: none;
  padding: 0;
  margin-top: 0; /* Remove margin if section has padding */
}

.conceptual-link-item {
  padding: 10px 15px; /* Increased padding */
  border-bottom: 1px solid #f1f1f1; /* Very light separator */
  cursor: pointer;
  transition: background-color 0.15s ease;
  color: #007bff; /* Link color */
  font-weight: 500;
}

.conceptual-link-item:last-child {
  border-bottom: none;
}

.conceptual-link-item:hover {
  background-color: #e9f5ff; /* Light blue hover */
  color: #0056b3;
}

.relevance-score {
  font-size: 0.8em; /* Smaller relevance score */
  color: #6c757d; /* Muted gray */
  margin-left: 12px;
  font-weight: 400;
}

/* Knowledge Graph Tab Content */
.knowledge-graph-tab-content {
  margin-top: 20px;
  height: 500px;
  border: 1px solid #e0e0e0; /* Consistent border */
  background-color: #fff;
  border-radius: 6px; /* Consistent rounding */
  overflow: hidden; /* Ensure graph fits */
}

/* Selected Text Info */
.selected-text-info {
  margin-top: 15px;
  margin-bottom: 15px; /* Add bottom margin */
  padding: 10px 12px;
  background-color: #e6f7ff; /* Lighter blue */
  border: 1px solid #91d5ff; /* Softer blue border */
  border-radius: 5px;
  font-size: 0.9em;
  color: #005288; /* Darker blue text */
}

.selected-text-info em {
  font-style: normal; /* Remove italic for a cleaner look */
  font-weight: 600; /* Bolder selected text */
}

/* Content Transformation Specific Styles */
.transformation-controls {
  display: flex;
  gap: 12px; /* Consistent gap */
  align-items: center;
  margin-bottom: 20px; /* More space below controls */
}

.transformation-controls select {
  flex-grow: 1;
}

.transformed-content-actions {
  margin-top: 15px;
  display: flex;
  gap: 12px; /* Consistent gap */
  flex-wrap: wrap; /* Allow buttons to wrap on smaller screens if needed */
}

/* Secondary Action Buttons (Copy, Replace, Save New) */
.transformed-content-actions button,
.save-new-item-button, /* Apply consistent secondary styling */
.add-manual-link-button,
.delete-manual-link-button {
  padding: 8px 15px; /* Adjusted padding */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: 500;
  transition: background-color 0.15s ease, box-shadow 0.15s ease;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.transformed-content-actions button { /* Specific for copy, replace */
  background-color: #28a745; /* Green */
}
.transformed-content-actions button:hover {
  background-color: #218838; /* Darker Green */
  box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.save-new-item-button {
  background-color: #17a2b8; /* Teal/Info */
}
.save-new-item-button:hover {
  background-color: #138496; /* Darker Teal */
  box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.transformed-content-actions button:disabled,
.save-new-item-button:disabled,
.add-manual-link-button:disabled,
.delete-manual-link-button:disabled {
  background-color: #adb5bd; /* Muted disabled color */
  cursor: not-allowed;
  box-shadow: none;
}


/* Manual Links Section Specific Styles */
.add-manual-link-button {
  background-color: #28a745; /* Green for add */
  margin-bottom: 20px; /* More space */
}
.add-manual-link-button:hover {
  background-color: #218838;
}

.manual-link-form {
  padding: 20px; /* Increased padding */
  background-color: #f8f9fa; /* Consistent light bg */
  border: 1px solid #e9ecef; /* Lighter border */
  border-radius: 6px;
  margin-bottom: 25px; /* More space */
  display: flex;
  flex-direction: column;
  gap: 15px; /* Increased gap */
}

.manual-link-form div { /* Container for label + input */
  display: flex;
  flex-direction: column;
  gap: 6px; /* Space between label and input */
}

.manual-link-form label {
  font-weight: 600; /* Bolder labels */
  color: #495057; /* Darker gray for labels */
  font-size: 0.9em;
}

.manual-link-form input[type="text"],
.manual-link-form select,
.manual-link-form textarea {
  width: 100%;
  box-sizing: border-box;
}

.manual-link-form textarea {
  resize: vertical;
  min-height: 60px;
}

.manual-link-form button[type="submit"] {
  align-self: flex-start;
  margin-top: 5px; /* Add a bit of top margin */
}

.manual-links-list {
  list-style-type: none;
  padding: 0;
  margin-top: 0; /* Remove margin if section has padding */
}

.manual-link-item {
  padding: 15px; /* Increased padding */
  border-bottom: 1px solid #f1f1f1; /* Very light separator */
  background-color: #fff;
  border-radius: 5px; /* Consistent rounding */
  margin-bottom: 10px; /* Space between items */
  box-shadow: 0 1px 3px rgba(0,0,0,0.03); /* Softer shadow */
  display: flex;
  flex-direction: column;
  gap: 8px; /* Increased gap */
}
.manual-link-item:last-child {
  border-bottom: none;
}

.manual-link-target-title {
  font-weight: 600; /* Bolder title */
  color: #007bff;
  cursor: pointer;
  font-size: 1.0em; /* Slightly smaller for balance */
}
.manual-link-target-title:hover {
  text-decoration: underline;
  color: #0056b3;
}

.manual-link-type {
  font-size: 0.8em; /* Smaller type */
  color: #6c757d; /* Muted gray */
  font-style: normal; /* Cleaner look */
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block; /* So background fits content */
}

.manual-link-description {
  font-size: 0.9em;
  color: #495057; /* Darker gray for description */
  margin-top: 4px;
  line-height: 1.5;
}

.delete-manual-link-button {
  background-color: #dc3545; /* Bootstrap danger */
  align-self: flex-end;
  margin-top: 8px;
}
.delete-manual-link-button:hover {
  background-color: #c82333; /* Darker danger */
}

.manual-links-section .small-error {
    font-size: 0.85em;
    padding: 6px 10px;
    margin-top: 8px;
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}

/* General placeholder style for empty states */
.detail-view-pane-component > p:first-child, /* "Select an item..." */
.conceptual-links-section p:not(.error-message), /* "No related items..." */
.manual-links-section p:not(.error-message) { /* "No manual links..." */
  padding: 20px;
  text-align: center;
  color: #6c757d; /* Muted gray */
  font-size: 1em;
  font-style: italic;
}