# Query Understanding Engine - Tests

This directory contains all unit and integration tests for the Query Understanding Engine (QUE) and its sub-components. The testing strategy emphasizes Test-Driven Development (TDD).

## Test Structure:

Tests are organized to mirror the structure of the `src` code. For each component in the `core/`, `intent-recognition/`, `entity-extraction/`, and `request-routing/` directories, there should be a corresponding test file in this `tests/` directory.

-   **[`queryUnderstandingEngine.test.js`](queryUnderstandingEngine.test.js:1)**: Tests for the main `QueryUnderstandingEngine` orchestrator. These will likely be integration-style tests, verifying the interaction between the different sub-modules.
-   **[`queryParser.test.js`](queryParser.test.js:1)**: Unit tests for the `QueryParser` component.
-   **[`intentRecognizer.test.js`](intentRecognizer.test.js:1)**: Unit tests for the `IntentRecognizer` component.
-   **[`entityExtractor.test.js`](entityExtractor.test.js:1)**: Unit tests for the `EntityExtractor` component.
-   **[`requestRouter.test.js`](requestRouter.test.js:1)**: Unit tests for the `RequestRouter` component.

## Running Tests:

(Instructions on how to run these tests will be added here, e.g., using Jest, Mocha, or another testing framework.)

## AI Verifiability:

-   Existence of this `README.md` file.
-   Existence of placeholder test files for each major component:
    -   `queryUnderstandingEngine.test.js`
    -   `queryParser.test.js`
    -   `intentRecognizer.test.js`
    -   `entityExtractor.test.js`
    -   `requestRouter.test.js`

---
*AI-VERIFIABLE: README.md for QUE tests directory created.*