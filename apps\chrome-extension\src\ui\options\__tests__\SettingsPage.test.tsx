import React from 'react';
import { render, screen } from '@testing-library/react';
import SettingsPage from '../SettingsPage';
import { describe } from '@jest/globals';
describe('SettingsPage', () => {
  it('renders the settings page title', () => {
    render(<SettingsPage />);
    expect(screen.getByText('Capture Settings')).toBeInTheDocument();
  });

  it('displays mock capture mode setting', () => {
    render(<SettingsPage />);
    expect(screen.getByText('Default Capture Mode:')).toBeInTheDocument();
    expect(screen.getByText('Full Page')).toBeInTheDocument();
  });

  it('displays mock content format setting', () => {
    render(<SettingsPage />);
    expect(screen.getByText('Preferred Content Format:')).toBeInTheDocument();
    expect(screen.getByText('Markdown')).toBeInTheDocument();
  });
});