{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ES2020", "WebWorker"], "module": "ESNext", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@pkm-ai/knowledge-base-service": ["../../packages/knowledge-base-service/dist"]}, "types": ["vite/client", "chrome", "react", "react-dom", "jest"], "composite": true, "declaration": true, "declarationMap": true, "experimentalDecorators": true}, "include": ["src", "__mocks__"], "exclude": ["src/**/*.test.ts", "src/**/*.test.tsx", "src/**/__tests__/*"], "references": [{"path": "./tsconfig.node.json"}, {"path": "../../packages/knowledge-base-service"}]}