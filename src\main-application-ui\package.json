{"name": "main-application-ui", "version": "0.1.0", "description": "Main Application UI for PKM AI", "main": "main.js", "scripts": {"start": "npm run build:react && electron .", "build": "echo 'Build script not implemented yet'", "dev:react": "webpack --mode development --watch", "build:react": "webpack --mode production", "test": "jest"}, "keywords": ["electron", "react", "zustand", "pkm"], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "babel-jest": "^29.7.0", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "electron": "^28.0.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "style-loader": "^4.0.0", "webpack": "^5.99.8", "webpack-cli": "^6.0.1"}, "dependencies": {"@reactflow/minimap": "", "@google/generative-ai": "0.24.1", "dotenv": "16.5.0", "dompurify": "^3.2.5", "lowdb": "^7.0.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-window": "^1.8.11", "reactflow": "^11.11.4", "turndown": "^7.2.0", "uuid": "^11.1.0", "zustand": "^4.4.7", "zustand-react": "^4.4.7"}}