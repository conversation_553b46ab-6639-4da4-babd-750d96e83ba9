/// <reference types="chrome" />

console.log('Background script: Starting evaluation (top level)...');

// Import the electronApi service
import { electronApi } from '../services/electronApi';

console.log('Background script: electronApi imported.');

declare const self: ServiceWorkerGlobalScope;

// In-memory storage for settings (for demonstration; ideally use chrome.storage.sync)
let captureSettings = {
  captureMode: 'fullPage', // Default
  contentFormat: 'markdown', // Default
};

self.addEventListener('install', (event: ExtendableEvent) => {
  console.log('Background script: Service Worker installing...');
  event.waitUntil(
    (async () => {
      console.log('Background script: Service Worker install complete.');
      self.skipWaiting(); // Force the waiting service worker to become active.
      console.log('Background script: skipWaiting() called.');
    })()
  );
});

self.addEventListener('activate', (event: ExtendableEvent) => {
  console.log('Background script: Service Worker activating...');
  event.waitUntil(self.clients.claim()); // Claim clients immediately to ensure all pages are controlled by the new service worker.
  console.log('Background script: Service Worker activated and clients claimed.');
});

// Listen for messages from the popup or content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background script received message:', message);

  // All knowledge base operations are now routed through electronApi
  switch (message.action) {
    case 'getKnowledgeBaseEntries':
      electronApi.getAllEntries()
        .then(entries => sendResponse({ success: true, entries }))
        .catch(error => {
          console.error('Error getting all knowledge base entries via electronApi:', error);
          sendResponse({ success: false, error: error.message });
        });
      return true; // Indicates async response

    case 'getKnowledgeBaseEntryById':
      if (!message.data || !message.data.id) {
        console.error('getKnowledgeBaseEntryById: Missing id');
        sendResponse({ success: false, error: 'Missing id in message data' });
        return false;
      }
      electronApi.getEntryById(message.data.id)
        .then(entry => sendResponse({ success: true, entry }))
        .catch(error => {
          console.error(`Error getting knowledge base entry by id ${message.data.id} via electronApi:`, error);
          sendResponse({ success: false, error: error.message });
        });
      return true;

    case 'addBookmark': // Kept for popup compatibility
    case 'createKnowledgeBaseEntry': // Unified handling for creation
      if (!message.data) {
        console.error(`${message.action}: Missing data for new entry`);
        sendResponse({ success: false, error: 'Missing data for new entry' });
        return false;
      }
      
      const entryDataToCreate = { ...message.data };
      // Ensure type is 'bookmark' if action is 'addBookmark'
      if (message.action === 'addBookmark' && !entryDataToCreate.type) {
        entryDataToCreate.type = 'bookmark';
      }

      electronApi.createEntry(entryDataToCreate)
        .then(entry => {
          console.log(`${message.action}: Entry added/created via electronApi:`, entry);
          sendResponse({ success: true, entry });
        })
        .catch(error => {
          console.error(`Error in ${message.action} via electronApi:`, error);
          sendResponse({ success: false, error: error.message });
        });
      return true;

    case 'updateKnowledgeBaseEntry':
      if (!message.data || !message.data.id || !message.data.updateData) {
        console.error('updateKnowledgeBaseEntry: Missing id or updateData');
        sendResponse({ success: false, error: 'Missing id or updateData in message' });
        return false;
      }
      electronApi.updateEntry(message.data.id, message.data.updateData)
        .then(entry => {
          if (entry) {
            sendResponse({ success: true, entry });
          } else {
            sendResponse({ success: false, error: `Entry with id ${message.data.id} not found for update via electronApi.` });
          }
        })
        .catch(error => {
          console.error(`Error updating knowledge base entry ${message.data.id} via electronApi:`, error);
          sendResponse({ success: false, error: error.message });
        });
      return true;

    case 'deleteKnowledgeBaseEntry':
      if (!message.data || !message.data.id) {
        console.error('deleteKnowledgeBaseEntry: Missing id');
        sendResponse({ success: false, error: 'Missing id in message data' });
        return false;
      }
      electronApi.deleteEntry(message.data.id)
        .then(success => {
          if (success) {
            sendResponse({ success: true, id: message.data.id });
          } else {
            sendResponse({ success: false, error: `Entry with id ${message.data.id} not found for deletion or delete failed via electronApi.` });
          }
        })
        .catch(error => {
          console.error(`Error deleting knowledge base entry ${message.data.id} via electronApi:`, error);
          sendResponse({ success: false, error: error.message });
        });
      return true;

    case 'getCurrentTabInfo':
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (chrome.runtime.lastError) {
          console.error('Error querying tabs:', chrome.runtime.lastError.message);
          sendResponse({ success: false, error: chrome.runtime.lastError.message });
          return;
        }
        if (tabs && tabs[0] && tabs[0].title && tabs[0].url) {
          sendResponse({ success: true, tab: { title: tabs[0].title, url: tabs[0].url } });
        } else {
          sendResponse({ success: false, error: 'Could not get current tab information.' });
        }
      });
      return true; // Indicates async response

    case 'getAISuggestions':
      // Placeholder for AI suggestion logic
      // In a real scenario, this would involve sending content to an AI service
      console.log('Generating mock AI suggestions for content:', message.data?.content);
      const mockSuggestions = {
        tags: ['mock-tag-1', 'mock-tag-2', 'mock-tag-3'],
        categories: ['Mock Category A', 'Mock Category B'],
        summary: 'This is a mock summary generated by the AI service for the provided content. It highlights key points and themes.',
      };
      sendResponse({ success: true, suggestions: mockSuggestions });
      return false; // Synchronous response for mock data

    case 'getCaptureSettings':
      sendResponse({ success: true, settings: captureSettings });
      return false; // Synchronous response

    case 'saveCaptureSettings':
      if (!message.data) {
        sendResponse({ success: false, error: 'Missing settings data.' });
        return false;
      }
      captureSettings = { ...captureSettings, ...message.data };
      // In a real extension, you would save this to chrome.storage.sync
      // chrome.storage.sync.set({ captureSettings: captureSettings }, () => {
      //   if (chrome.runtime.lastError) {
      //     console.error('Error saving settings:', chrome.runtime.lastError.message);
      //     sendResponse({ success: false, error: chrome.runtime.lastError.message });
      //   } else {
      //     sendResponse({ success: true });
      //   }
      // });
      sendResponse({ success: true }); // For now, just update in-memory
      return false; // Synchronous response

    default:
      console.warn('Unknown message action received:', message.action);
      sendResponse({ success: false, error: `Unknown action: ${message.action}` });
      return false; // Not handled, synchronous response
  }
});

chrome.runtime.onInstalled.addListener(details => {
  console.log('Background script: onInstalled event triggered. Reason:', details.reason);
  if (details.reason === 'install') {
    console.log('Background script: Extension installed.');
    (async () => {
      const sampleEntries = [
        { title: 'Sample Entry 1', url: 'https://example.com/sample1', content: 'This is a sample entry for testing.', tags: ['sample', 'test'], type: 'bookmark' },
        { title: 'Sample Entry 2', url: 'https://example.com/sample2', content: 'Another sample entry with different content.', tags: ['sample'], type: 'note' }
      ];
      console.log('Background script: Adding sample entries via electronApi...');
      await Promise.all(sampleEntries.map(entry => electronApi.createEntry(entry)))
        .then(() => console.log('Background script: Sample entries added to knowledge base via electronApi'))
        .catch(error => console.error('Background script: Error adding sample entries via electronApi:', error));
    })();
  } else if (details.reason === 'update') {
    console.log('Background script: Extension updated to version', chrome.runtime.getManifest().version);
  }
});

console.log('Background script: Evaluation complete (bottom of file).');
