# Known Issues: Web Content Capture Module UI Test Failures

This document details known persistent test failures within the Web Content Capture Module UI, providing context for developers who may encounter or attempt to resolve these issues.

## Persistent Failure: `should display error and not initialize fully if POPUP_INIT fails due to lastError`

*   **Failing Test Case:** [`should display error and not initialize fully if P<PERSON>UP_INIT fails due to lastError`](src/browser-extension-ui/__tests__/popup.test.js) in [`src/browser-extension-ui/__tests__/popup.test.js`](src/browser-extension-ui/__tests__/popup.test.js).
*   **Suspected Cause:** The primary issue is believed to stem from limitations or inaccuracies in the Jest/JSDOM test environment's simulation of browser extension APIs, specifically concerning the persistence and behavior of `chrome.runtime.lastError`. Additionally, there are challenges in reliably asserting timely DOM updates within the test environment, particularly those triggered during the `DOMContentLoaded` event cycle for the popup's initialization path when an error occurs. The production code's error handling logic (for both `lastError` and application-level errors) is thought to be correct, but the test environment prevents reliable verification of the `lastError` scenario.
*   **Attempted Fixes:** Several attempts have been made to address this failure, including modifications to [`src/browser-extension-ui/popup.js`](src/browser-extension-ui/popup.js) to snapshot `chrome.runtime.lastError` and adjustments to test mocks within the test file.
*   **Current Status:** The test case remains failing. Further investigation and potential workarounds or alternative strategies focusing on the test environment itself, rather than the production code, may be required to achieve a stable test result for this specific scenario.