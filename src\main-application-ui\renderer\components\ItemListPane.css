/* Styles for ItemListPane - Refined for a cleaner, modern look */
.item-list-pane-component {
  background-color: #fdfdfd; /* Slightly off-white for a softer look */
  box-sizing: border-box;
  padding: 15px; /* Add some padding to the pane itself */
}

.item-list-pane-component h3 {
  margin-top: 0;
  margin-bottom: 15px; /* More space below the title */
  color: #2c3e50; /* Darker, more modern blue-gray */
  font-size: 1.2em; /* Slightly larger title */
  font-weight: 600; /* Bolder title */
}

.item-list-pane-component .item-list {
  list-style-type: none;
  padding-left: 0;
  margin-top: 0; /* Remove margin if pane has padding */
}

.item-list-pane-component .item-list-entry {
  padding: 15px 10px; /* Increased padding for better touch/click targets and spacing */
  border-bottom: 1px solid #e8e8e8; /* Lighter border */
  cursor: pointer;
  transition: background-color 0.15s ease-in-out, border-left-color 0.15s ease-in-out;
  border-left: 3px solid transparent; /* For active/hover indication */
}

.item-list-pane-component .item-list-entry:hover {
  background-color: #f5f7fa; /* Lighter, bluish hover */
  border-left-color: #007bff; /* Accent color on hover */
}

/* Consider adding an .active class if selection needs to persist visually */
/* .item-list-pane-component .item-list-entry.active {
  background-color: #e9f5ff;
  border-left-color: #0056b3;
} */

.item-list-pane-component .item-list-entry:last-child {
  border-bottom: none;
}

.item-list-pane-component .item-title {
  font-size: 1.05em; /* Slightly adjusted size */
  font-weight: 600; /* Medium weight for title */
  color: #34495e; /* Slightly softer dark blue-gray */
  margin-bottom: 5px; /* Consistent margin */
}

.item-list-pane-component .item-snippet {
  font-size: 0.88em; /* Adjusted for readability */
  color: #555; /* Darker gray for better contrast */
  margin-bottom: 8px; /* More space before date/tags */
  line-height: 1.4; /* Improved line spacing for snippets */
  /* Keep truncation for snippets */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.item-list-pane-component .item-date,
.item-list-pane-component .item-type {
  font-size: 0.8em;
  color: #7f8c8d; /* Consistent muted color */
  margin-bottom: 4px; /* Space between date and type if both present */
}
.item-list-pane-component .item-type {
  margin-top: 0; /* Remove top margin if date is also present */
}


.item-list-pane-component .item-tags-list {
  margin-top: 8px; /* More space above tags */
  display: flex;
  flex-wrap: wrap;
  gap: 6px; /* Slightly increased gap */
}

.item-list-pane-component .item-tag {
  background-color: #e9ecef; /* Lighter, more modern tag background */
  color: #495057; /* Darker text for better contrast on light bg */
  padding: 4px 10px; /* Slightly more padding */
  border-radius: 15px; /* More rounded tags */
  font-size: 0.78em; /* Adjusted for clarity */
  font-weight: 500;
}

/* Loading and Error States */
.item-list-pane-component > div:first-child:not(.item-list) { /* Target direct div children that are not the list itself for loading/error */
  padding: 20px;
  text-align: center;
  color: #555;
  font-size: 1.1em;
}