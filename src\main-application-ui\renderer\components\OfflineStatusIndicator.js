import React from 'react';
import PropTypes from 'prop-types';

// Basic styling, can be moved to a CSS module
const styles = {
  indicator: {
    padding: '5px 10px',
    borderRadius: '4px',
    fontWeight: 'bold',
    display: 'inline-block',
  },
  online: {
    backgroundColor: '#4CAF50', // Green
    color: 'white',
  },
  offline: {
    backgroundColor: '#f44336', // Red
    color: 'white',
  },
};

const OfflineStatusIndicator = ({ isOnline = true }) => {
  const statusText = isOnline ? 'Online' : 'Offline';
  const statusClass = isOnline ? 'online' : 'offline';
  const combinedStyle = {
    ...styles.indicator,
    ...(isOnline ? styles.online : styles.offline),
  };

  return (
    <div
      data-testid="offline-status-indicator"
      className={`status-indicator ${statusClass}`} // For testing class names
      style={combinedStyle} // For visual styling
    >
      Status: {statusText}
    </div>
  );
};

OfflineStatusIndicator.propTypes = {
  isOnline: PropTypes.bool,
};

// Default prop handled by default parameter in function signature
// OfflineStatusIndicator.defaultProps = {
// isOnline: true,
// };

export default OfflineStatusIndicator;